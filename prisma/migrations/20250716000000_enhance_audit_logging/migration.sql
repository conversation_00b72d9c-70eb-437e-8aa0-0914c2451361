-- Enhanced Audit Logging Migration
-- This migration adds new audit log types and enhances the AuditLog model

-- Add new audit log types to the enum
ALTER TYPE "AuditLogType" ADD VALUE 'LOGIN_ATTEMPT';
ALTER TYPE "AuditLogType" ADD VALUE 'LOGOUT_SUCCESS';
ALTER TYPE "AuditLogType" ADD VALUE 'SESSION_EXPIRED';
ALTER TYPE "AuditLogType" ADD VALUE 'PASSWORD_CHANGED';
ALTER TYPE "AuditLogType" ADD VALUE 'TWO_FACTOR_ENABLED';
ALTER TYPE "AuditLogType" ADD VALUE 'TWO_FACTOR_DISABLED';
ALTER TYPE "AuditLogType" ADD VALUE 'ACCOUNT_LOCKED';
ALTER TYPE "AuditLogType" ADD VALUE 'ACCOUNT_UNLOCKED';

-- SPV Management Events
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_CREATED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_UPDATED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_DELETED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_VERIFIED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_REJECTED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_DOCUMENT_UPLOADED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_DOCUMENT_VERIFIED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_DOCUMENT_REJECTED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_USER_CREATED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_USER_UPDATED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_USER_DELETED';
ALTER TYPE "AuditLogType" ADD VALUE 'SPV_USER_ROLE_CHANGED';

-- Role and Permission Events
ALTER TYPE "AuditLogType" ADD VALUE 'USER_ROLE_CHANGED';
ALTER TYPE "AuditLogType" ADD VALUE 'USER_PERMISSIONS_UPDATED';
ALTER TYPE "AuditLogType" ADD VALUE 'ORGANIZATION_ROLE_ASSIGNED';
ALTER TYPE "AuditLogType" ADD VALUE 'ORGANIZATION_ROLE_REMOVED';

-- Document Management Events
ALTER TYPE "AuditLogType" ADD VALUE 'DOCUMENT_DOWNLOADED';
ALTER TYPE "AuditLogType" ADD VALUE 'DOCUMENT_DELETED';
ALTER TYPE "AuditLogType" ADD VALUE 'DOCUMENT_ACCESS_GRANTED';
ALTER TYPE "AuditLogType" ADD VALUE 'DOCUMENT_ACCESS_REVOKED';

-- System Events
ALTER TYPE "AuditLogType" ADD VALUE 'SYSTEM_BACKUP_CREATED';
ALTER TYPE "AuditLogType" ADD VALUE 'SYSTEM_MAINTENANCE_STARTED';
ALTER TYPE "AuditLogType" ADD VALUE 'SYSTEM_MAINTENANCE_COMPLETED';
ALTER TYPE "AuditLogType" ADD VALUE 'CONFIGURATION_CHANGED';
ALTER TYPE "AuditLogType" ADD VALUE 'SECURITY_POLICY_UPDATED';

-- Add new columns to AuditLog table
ALTER TABLE "AuditLog" ADD COLUMN "sessionId" TEXT;
ALTER TABLE "AuditLog" ADD COLUMN "requestId" TEXT;
ALTER TABLE "AuditLog" ADD COLUMN "severity" TEXT DEFAULT 'INFO';
ALTER TABLE "AuditLog" ADD COLUMN "category" TEXT;
ALTER TABLE "AuditLog" ADD COLUMN "source" TEXT;
ALTER TABLE "AuditLog" ADD COLUMN "duration" INTEGER;
ALTER TABLE "AuditLog" ADD COLUMN "success" BOOLEAN;
ALTER TABLE "AuditLog" ADD COLUMN "errorCode" TEXT;
ALTER TABLE "AuditLog" ADD COLUMN "tags" TEXT[];

-- Create performance indexes
CREATE INDEX "AuditLog_severity_idx" ON "AuditLog"("severity");
CREATE INDEX "AuditLog_category_idx" ON "AuditLog"("category");
CREATE INDEX "AuditLog_success_idx" ON "AuditLog"("success");
CREATE INDEX "AuditLog_sessionId_idx" ON "AuditLog"("sessionId");

-- Composite indexes for common queries
CREATE INDEX "AuditLog_organizationId_type_idx" ON "AuditLog"("organizationId", "type");
CREATE INDEX "AuditLog_organizationId_createdAt_idx" ON "AuditLog"("organizationId", "createdAt");
CREATE INDEX "AuditLog_userId_type_idx" ON "AuditLog"("userId", "type");
CREATE INDEX "AuditLog_userId_createdAt_idx" ON "AuditLog"("userId", "createdAt");
CREATE INDEX "AuditLog_type_createdAt_idx" ON "AuditLog"("type", "createdAt");
CREATE INDEX "AuditLog_organizationId_type_createdAt_idx" ON "AuditLog"("organizationId", "type", "createdAt");
CREATE INDEX "AuditLog_spvId_type_createdAt_idx" ON "AuditLog"("spvId", "type", "createdAt");
CREATE INDEX "AuditLog_projectId_type_createdAt_idx" ON "AuditLog"("projectId", "type", "createdAt");

-- Full-text search index for description field
CREATE INDEX "AuditLog_description_idx" ON "AuditLog"("description");

-- Add GIN index for tags array for efficient array operations
CREATE INDEX "AuditLog_tags_gin_idx" ON "AuditLog" USING GIN ("tags");

-- Add partial indexes for common filtered queries
CREATE INDEX "AuditLog_recent_errors_idx" ON "AuditLog"("createdAt", "organizationId") 
WHERE "success" = false AND "createdAt" > NOW() - INTERVAL '30 days';

CREATE INDEX "AuditLog_recent_auth_events_idx" ON "AuditLog"("createdAt", "userId") 
WHERE "category" = 'AUTHENTICATION' AND "createdAt" > NOW() - INTERVAL '7 days';

-- Add constraint to ensure severity values are valid
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_severity_check" 
CHECK ("severity" IN ('INFO', 'WARN', 'ERROR', 'CRITICAL'));

-- Add constraint to ensure category values are valid
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_category_check" 
CHECK ("category" IN ('AUTHENTICATION', 'AUTHORIZATION', 'DATA', 'SYSTEM', 'SPV', 'DOCUMENT', 'ROLE', 'API', 'TRANSACTION', 'WALLET'));

-- Add constraint to ensure source values are valid
ALTER TABLE "AuditLog" ADD CONSTRAINT "AuditLog_source_check" 
CHECK ("source" IN ('WEB', 'API', 'MOBILE', 'SYSTEM', 'CLI'));
