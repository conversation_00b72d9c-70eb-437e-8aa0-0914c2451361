-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "AuditLogType" ADD VALUE 'DATA_SUBMITTED_FOR_VERIFICATION';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_PM_VERIFIED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_PM_REJECTED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_SPV_ADMIN_VERIFIED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_SPV_ADMIN_REJECTED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_SUBMITTED_TO_ORG_ADMIN';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_ORG_APPROVED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_ORG_REJECTED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_SUBMITTED_TO_VVB';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_VVB_VERIFIED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_VVB_REJECTED';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_SENT_BACK_FOR_CORRECTION';
ALTER TYPE "AuditLogType" ADD VALUE 'DATA_EDITED_AND_RESUBMITTED';
ALTER TYPE "AuditLogType" ADD VALUE 'VERIFICATION_STATUS_CHANGED';
ALTER TYPE "AuditLogType" ADD VALUE 'VERIFICATION_WORKFLOW_COMPLETED';
ALTER TYPE "AuditLogType" ADD VALUE 'VERIFICATION_WORKFLOW_FAILED';
