-- Add subtype field to Project table
ALTER TABLE "Project" ADD COLUMN "subtype" TEXT;

-- Add solarEnergyData field to Project table  
ALTER TABLE "Project" ADD COLUMN "solarEnergyData" JSONB;

-- Add customFields field to Project table
ALTER TABLE "Project" ADD COLUMN "customFields" JSONB;

-- Add customFieldsConfig field to Organization table
ALTER TABLE "Organization" ADD COLUMN "customFieldsConfig" JSONB;

-- Add indexes for better performance on JSON queries
CREATE INDEX IF NOT EXISTS "Project_subtype_idx" ON "Project"("subtype");
CREATE INDEX IF NOT EXISTS "Project_solarEnergyData_idx" ON "Project" USING GIN ("solarEnergyData");
CREATE INDEX IF NOT EXISTS "Project_customFields_idx" ON "Project" USING GIN ("customFields");
CREATE INDEX IF NOT EXISTS "Organization_customFieldsConfig_idx" ON "Organization" USING GIN ("customFieldsConfig");
