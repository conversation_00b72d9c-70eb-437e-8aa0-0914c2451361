import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";
import { hashPassword } from "@/lib/auth/password";
import { emailService } from "@/lib/email";

// Validation schema for SPV user creation by SPV admin
const spvUserCreationSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required"),
  role: z.enum(["SPV_ADMIN", "PROJECT_MANAGER", "SITE_WORKER"], {
    required_error: "Role is required",
  }),
  jobTitle: z.string().optional(),
  phoneNumber: z.string().optional(),
  password: z.string().min(8, "Password must be at least 8 characters"),
  spvId: z.string().optional(), // Optional - will be determined based on user context
  projectIds: z.array(z.string()).optional(), // Optional immediate project assignment
});

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get SPV user info
    const spvUser = await db.sPVUser.findFirst({
      where: { userId: session.user.id },
      include: {
        spv: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Only SPV admins can view all users, others can only see themselves
    if (spvUser.role !== "SPV_ADMIN") {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const role = searchParams.get("role") || "";
    const status = searchParams.get("status") || "";

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {
      spvId: spvUser.spvId, // Only show users from the same SPV
      NOT: {
        userId: session.user.id, // Exclude the current user
      },
    };

    if (search) {
      where.OR = [
        {
          user: {
            name: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          user: {
            email: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
      ];
    }

    if (role && role !== "ALL") {
      where.role = role;
    }

    if (status && status !== "ALL") {
      where.isActive = status === "ACTIVE";
    }

    // Get users with pagination
    const [users, totalCount] = await Promise.all([
      db.sPVUser.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              jobTitle: true,
              phoneNumber: true,
              departmentName: true,
            },
          },
          spv: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
          projectAssignments: {
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: {
          createdAt: "desc",
        },
      }),
      db.sPVUser.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("SPV users retrieved", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      spvId: spvUser.spvId,
      count: users.length,
      totalCount,
      page,
    });

    return NextResponse.json({
      users,
      totalCount,
      page,
      totalPages,
      limit,
    });
  } catch (error) {
    console.error("Error fetching SPV users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/spv/users
 * Create a new SPV user (SPV admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Check permissions - allow SPV admins and organization admins
    let spvUser = null;
    let userOrganizationId = null;

    if (session.user.role === "ORGANIZATION_ADMIN") {
      // Organization admins can create SPV users for their organization's SPVs
      userOrganizationId = session.user.organizationId;
      if (!userOrganizationId) {
        return NextResponse.json(
          { error: "Organization admin must have an organization" },
          { status: 400 }
        );
      }
    } else {
      // For other users, check if they are SPV admins
      spvUser = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          isActive: true,
        },
        include: {
          spv: {
            include: {
              organization: true,
            },
          },
        },
      });

      if (!spvUser) {
        return NextResponse.json(
          { error: "SPV user not found" },
          { status: 404 }
        );
      }

      if (spvUser.role !== "SPV_ADMIN") {
        return NextResponse.json(
          { error: "Access denied. SPV admin role required." },
          { status: 403 }
        );
      }
    }

    const body = await request.json();
    const validatedData = spvUserCreationSchema.parse(body);

    // Determine SPV ID if not provided
    let targetSpvId = validatedData.spvId;

    if (!targetSpvId) {
      if (session.user.role === "ORGANIZATION_ADMIN") {
        // For organization admins, use the first active SPV from their organization
        const orgSpv = await db.sPV.findFirst({
          where: {
            organizationId: userOrganizationId,
            status: "ACTIVE",
          },
        });

        if (!orgSpv) {
          return NextResponse.json(
            { error: "No active SPV found for your organization" },
            { status: 404 }
          );
        }

        targetSpvId = orgSpv.id;
      } else if (spvUser) {
        // For SPV users, use their SPV
        targetSpvId = spvUser.spvId;
      } else {
        return NextResponse.json(
          { error: "SPV ID is required" },
          { status: 400 }
        );
      }
    }

    // Validate SPV access
    const targetSpv = await db.sPV.findUnique({
      where: { id: targetSpvId },
      include: { organization: true }
    });

    if (!targetSpv) {
      return NextResponse.json(
        { error: "SPV not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this SPV
    if (session.user.role === "ORGANIZATION_ADMIN") {
      if (targetSpv.organizationId !== userOrganizationId) {
        return NextResponse.json(
          { error: "Access denied. You can only create users for your organization's SPVs." },
          { status: 403 }
        );
      }
    } else if (spvUser && targetSpv.id !== spvUser.spvId) {
      return NextResponse.json(
        { error: "Access denied. You can only create users for your SPV." },
        { status: 403 }
      );
    }

    // Check if user with this email already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);

    // Create user and SPV user in a transaction
    const result = await db.$transaction(async (tx) => {
      // Create the user
      const user = await tx.user.create({
        data: {
          email: validatedData.email,
          name: validatedData.name,
          password: hashedPassword,
          role: "SPV_USER",
          jobTitle: validatedData.jobTitle,
          phoneNumber: validatedData.phoneNumber,
          organizationId: targetSpv.organizationId,
        },
      });

      // Create the SPV user
      const newSpvUser = await tx.sPVUser.create({
        data: {
          userId: user.id,
          spvId: targetSpvId,
          role: validatedData.role,
          permissions: {},
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              jobTitle: true,
              phoneNumber: true,
              createdAt: true,
            },
          },
          spv: {
            select: {
              id: true,
              name: true,
              purpose: true,
              jurisdiction: true,
              status: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                  legalName: true,
                },
              },
            },
          },
        },
      });

      // If project IDs are provided, create project assignments
      if (validatedData.projectIds && validatedData.projectIds.length > 0) {
        // Verify that all projects belong to the SPV
        const projects = await tx.project.findMany({
          where: {
            id: { in: validatedData.projectIds },
            spvId: targetSpvId,
          },
        });

        if (projects.length !== validatedData.projectIds.length) {
          throw new Error("Some projects do not belong to this SPV or do not exist");
        }

        // Create project assignments
        await tx.projectAssignment.createMany({
          data: validatedData.projectIds.map(projectId => ({
            projectId,
            spvUserId: newSpvUser.id,
            assignedBy: session.user.id,
            permissions: {},
          })),
        });

        // Update projects' spvId to link them to the SPV
        await tx.project.updateMany({
          where: { id: { in: validatedData.projectIds } },
          data: { spvId: targetSpvId },
        });
      }

      return newSpvUser;
    });

    logger.info("SPV user created by SPV admin", {
      createdBy: session.user.id,
      spvUserId: result.id,
      userId: result.userId,
      spvId: targetSpvId,
      role: validatedData.role,
      projectsAssigned: validatedData.projectIds?.length || 0,
    });

    // Send credentials email to the new SPV user
    try {
      await emailService.sendCredentials({
        email: validatedData.email,
        password: validatedData.password,
        name: validatedData.name,
        role: validatedData.role,
        organizationName: result.spv.organization.name || result.spv.organization.legalName,
        spvName: result.spv.name,
        loginUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/spv/login`
      });
      logger.info(`SPV user credentials email sent to ${validatedData.email}`);
    } catch (emailError) {
      logger.error(`Failed to send SPV user credentials email to ${validatedData.email}:`, emailError);
      // Don't fail the entire operation if email fails
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: "SPV user created successfully! Credentials have been sent via email.",
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error creating SPV user", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get SPV user info to verify permissions
    const spvUser = await db.sPVUser.findFirst({
      where: { userId: session.user.id },
    });

    if (!spvUser || spvUser.role !== "SPV_ADMIN") {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const userIdToDelete = searchParams.get("userId");

    if (!userIdToDelete) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Find the SPV user to delete
    const spvUserToDelete = await db.sPVUser.findFirst({
      where: {
        userId: userIdToDelete,
        spvId: spvUser.spvId, // Ensure it's from the same SPV
      },
    });

    if (!spvUserToDelete) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Prevent deleting yourself
    if (spvUserToDelete.userId === session.user.id) {
      return NextResponse.json(
        { error: "Cannot delete yourself" },
        { status: 400 }
      );
    }

    // Delete the SPV user (this will also remove project assignments due to cascade)
    await db.sPVUser.delete({
      where: { id: spvUserToDelete.id },
    });

    logger.info("SPV user deleted", {
      deletedBy: session.user.id,
      deletedUserId: userIdToDelete,
      spvId: spvUser.spvId,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting SPV user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
