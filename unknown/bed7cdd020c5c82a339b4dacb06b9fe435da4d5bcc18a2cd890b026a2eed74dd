import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { determineVerificationStatus } from "@/lib/utils/verification-status";
import { ProjectAuditService } from "@/lib/audit/project-audit";
import { z } from "zod";

// Validation schema for unit log creation
const unitLogCreationSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
  logDate: z.string().datetime("Invalid date format"),
  frequency: z.enum(["REAL_TIME", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]),
  unitType: z.string().min(1, "Unit type is required"),
  quantity: z.number().positive("Quantity must be positive"),
  dataSource: z.enum(["MANUAL", "CSV_UPLOAD", "API_INTEGRATION", "IOT_DEVICE"]).default("MANUAL"),
  sourceFile: z.string().optional(),
  apiSource: z.string().optional(),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
  attachments: z.array(z.object({
    name: z.string(),
    url: z.string().url(),
    type: z.string(),
    size: z.number().optional(),
  })).optional(),
});

// Validation schema for unit log update
const unitLogUpdateSchema = unitLogCreationSchema.partial().omit({ projectId: true });

/**
 * GET /api/spv/unit-logs
 * Get unit logs for SPV user's assigned projects
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const projectId = url.searchParams.get('projectId');
    const status = url.searchParams.get('status');
    const ownOnly = url.searchParams.get('ownOnly') === 'true';

    const offset = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      projectId: { in: assignedProjectIds },
    };

    if (projectId && assignedProjectIds.includes(projectId)) {
      whereClause.projectId = projectId;
    }

    if (status) {
      whereClause.verificationStatus = status;
    }

    if (ownOnly) {
      whereClause.loggerId = session.user.id;
    }

    // Get unit logs with pagination
    const [unitLogs, totalCount] = await Promise.all([
      db.unitLog.findMany({
        where: whereClause,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              type: true,
              status: true,
            },
          },
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verificationLogs: {
            include: {
              verifier: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                },
              },
            },
            orderBy: {
              createdAt: 'desc',
            },
            take: 5,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: offset,
        take: limit,
      }),
      db.unitLog.count({ where: whereClause }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("SPV unit logs fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      page,
      limit,
      totalCount,
      filters: { projectId, status, ownOnly },
    });

    return NextResponse.json({
      success: true,
      data: {
        unitLogs,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasMore: page < totalPages,
        },
        userRole: spvUser.role,
      },
    });

  } catch (error) {
    logger.error("Error fetching SPV unit logs:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/spv/unit-logs
 * Create new unit log entry
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const validatedData = unitLogCreationSchema.parse(body);

    // Check if user has access to the project
    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);
    if (!assignedProjectIds.includes(validatedData.projectId)) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Verify project exists and is active
    const project = await db.project.findUnique({
      where: { id: validatedData.projectId },
      select: { id: true, status: true, name: true },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    // Allow unit logs for ACTIVE, COMPLETED, and PENDING projects
    // Prevent unit logs for SUSPENDED or CANCELLED projects
    if (project.status === "SUSPENDED" || project.status === "CANCELLED") {
      return NextResponse.json(
        { error: `Cannot create unit logs for ${project.status.toLowerCase()} projects` },
        { status: 400 }
      );
    }

    // Determine appropriate verification status based on user role
    logger.info("Determining verification status", {
      userId: session.user.id,
      projectId: validatedData.projectId,
    });

    // Re-enable proper verification status determination
    const verificationStatus = await determineVerificationStatus(session.user.id, validatedData.projectId);

    logger.info("Using simple verification status", {
      userId: session.user.id,
      projectId: validatedData.projectId,
      verificationStatus,
    });

    // Create unit log
    logger.info("Creating unit log with data", {
      projectId: validatedData.projectId,
      loggedBy: session.user.id,
      quantity: validatedData.quantity,
      verificationStatus,
    });

    const unitLog = await db.unitLog.create({
      data: {
        projectId: validatedData.projectId,
        loggedBy: session.user.id,
        logDate: new Date(validatedData.logDate),
        frequency: validatedData.frequency,
        unitType: validatedData.unitType,
        quantity: validatedData.quantity,
        dataSource: validatedData.dataSource,
        sourceFile: validatedData.sourceFile,
        apiSource: validatedData.apiSource,
        notes: validatedData.notes,
        metadata: validatedData.metadata || {},
        verificationStatus: verificationStatus,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
            status: true,
          },
        },
        logger: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Handle attachments if provided
    if (validatedData.attachments && validatedData.attachments.length > 0) {
      await Promise.all(
        validatedData.attachments.map(attachment =>
          db.document.create({
            data: {
              name: attachment.name,
              type: attachment.type,
              url: attachment.url,
              size: attachment.size,
              status: "PENDING",
              unitLogId: unitLog.id,
            },
          })
        )
      );
    }

    logger.info("Unit log created", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: unitLog.id,
      projectId: validatedData.projectId,
      projectName: project.name,
    });

    // Log audit event for unit log creation
    await ProjectAuditService.logUnitLogActivity({
      unitLogId: unitLog.id,
      projectId: validatedData.projectId,
      userId: session.user.id,
      organizationId: project.organizationId,
      spvId: spvUser.spvId, // Include SPV context
      action: "CREATED",
      dataSource: validatedData.dataSource,
      newData: {
        unitType: validatedData.unitType,
        quantity: validatedData.quantity,
        logDate: validatedData.logDate,
        frequency: validatedData.frequency,
        verificationStatus: verificationStatus,
      },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      userAgent: request.headers.get('user-agent') || undefined,
    });

    return NextResponse.json({
      success: true,
      data: unitLog,
      message: "Unit log created successfully",
    }, { status: 201 });

  } catch (error) {
    logger.error("Error creating unit log:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        error: "Internal server error",
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
