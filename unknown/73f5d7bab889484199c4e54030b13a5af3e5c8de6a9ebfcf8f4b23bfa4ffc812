import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { ProjectAuditService } from "@/lib/audit/project-audit";
import { z } from "zod";

// Validation schema for submission
const submitSchema = z.object({
  comments: z.string().optional(),
  priority: z.enum(["LOW", "MEDIUM", "HIGH"]).optional(),
});

/**
 * POST /api/spv/unit-logs/[id]/submit
 * Submit unit log for verification
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const unitLogId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // Get existing unit log
    const existingUnitLog = await db.unitLog.findUnique({
      where: { id: unitLogId },
      select: {
        id: true,
        projectId: true,
        loggerId: true,
        verificationStatus: true,
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!existingUnitLog) {
      return NextResponse.json(
        { error: "Unit log not found" },
        { status: 404 }
      );
    }

    // Check access permissions
    if (!assignedProjectIds.includes(existingUnitLog.projectId)) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Check ownership (only owner can submit)
    if (existingUnitLog.loggerId !== session.user.id) {
      return NextResponse.json(
        { error: "Access denied. You can only submit your own entries." },
        { status: 403 }
      );
    }

    // Check if unit log can be submitted (DRAFT or rejected entries)
    if (!['DRAFT', 'REJECTED', 'SPV_REJECTED', 'ORG_REJECTED'].includes(existingUnitLog.verificationStatus)) {
      return NextResponse.json(
        { error: "Cannot submit unit log. Only draft or rejected entries can be submitted for verification." },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = submitSchema.parse(body);

    // Determine the next status based on current status
    const currentStatus = existingUnitLog.verificationStatus;
    let nextStatus = "SUBMITTED_FOR_VERIFICATION"; // Default for new submissions

    // If it's a resubmission after rejection, determine appropriate next status
    if (currentStatus === "REJECTED") {
      nextStatus = "SUBMITTED_FOR_VERIFICATION"; // Back to PM for review
    } else if (currentStatus === "SPV_REJECTED") {
      nextStatus = "PM_VERIFIED"; // Skip PM review, go to SPV admin
    } else if (currentStatus === "ORG_REJECTED") {
      nextStatus = "SPV_ADMIN_VERIFIED"; // Skip PM and SPV admin, go to org admin
    }

    // Update unit log status and create verification log
    const result = await db.$transaction(async (tx) => {
      // Update unit log status
      const updatedUnitLog = await tx.unitLog.update({
        where: { id: unitLogId },
        data: {
          verificationStatus: nextStatus as any,
          submittedAt: new Date(),
          updatedAt: new Date(),
          // Clear rejection fields on resubmission
          rejectedBy: null,
          rejectedAt: null,
          rejectionReason: null,
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              type: true,
              status: true,
            },
          },
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      // Create verification log entry
      const verificationLog = await tx.dataVerificationLog.create({
        data: {
          unitLogId: unitLogId,
          verifierId: session.user.id,
          fromStatus: currentStatus,
          toStatus: nextStatus,
          verificationNotes: validatedData.comments,
          priority: validatedData.priority || "MEDIUM",
          metadata: {
            submittedBy: session.user.id,
            submittedAt: new Date().toISOString(),
            userRole: spvUser.role,
            isResubmission: currentStatus !== "DRAFT",
          },
        },
      });

      return { updatedUnitLog, verificationLog };
    });

    logger.info("Unit log submitted for verification", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: unitLogId,
      projectId: existingUnitLog.projectId,
      projectName: existingUnitLog.project.name,
      fromStatus: currentStatus,
      toStatus: nextStatus,
      isResubmission: currentStatus !== "DRAFT",
    });

    // Log audit event for submission
    await ProjectAuditService.logVerificationAction(
      unitLogId,
      existingUnitLog.projectId,
      session.user.id,
      existingUnitLog.project.organizationId,
      currentStatus !== "DRAFT" ? "EDIT_AND_RESUBMIT" : "SUBMIT_FOR_VERIFICATION",
      currentStatus,
      nextStatus,
      spvUser.role,
      validatedData.comments,
      undefined,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      request.headers.get('user-agent') || undefined
    );

    return NextResponse.json({
      success: true,
      data: result.updatedUnitLog,
      message: "Unit log submitted for verification successfully",
    });

  } catch (error) {
    logger.error("Error submitting unit log for verification:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
