import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { UserRole } from "@prisma/client";
import { z } from "zod";

// Schema for unit log query parameters
const unitLogQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 50),
  frequency: z.enum(["REAL_TIME", "HOURLY", "DAILY", "WEEKLY", "MONTHLY", "QUARTERLY", "YEARLY"]).optional(),
  unitType: z.string().optional(),
  dataSource: z.enum(["MANUAL", "CSV_UPLOAD", "API_INTEGRATION", "IOT_DEVICE"]).optional(),
  verificationStatus: z.enum([
    "DRAFT", 
    "SUBMITTED_FOR_VERIFICATION", 
    "PM_VERIFIED",
    "SPV_ADMIN_VERIFIED",
    "VERIFIED", 
    "REJECTED", 
    "SPV_APPROVED",
    "SPV_REJECTED",
    "ORG_APPROVED",
    "ORG_REJECTED",
    "SUBMITTED_TO_ORG_ADMIN",
    "SUBMITTED_TO_VVB",
    "VVB_VERIFIED",
    "VVB_REJECTED"
  ]).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

/**
 * GET /api/spv/projects/[id]/unit-logs
 * Get unit logs for a specific SPV project
 */
async function getProjectUnitLogsHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to access this resource",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      throw new ApiError(
        "Access denied. SPV user role required.",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const projectId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      throw new ApiError(
        "SPV user not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if user has access to this project
    const assignedProjectIds = spvUser.projectAssignments.map(assignment => assignment.projectId);
    if (!assignedProjectIds.includes(projectId)) {
      throw new ApiError(
        "Access denied. Project not assigned to you.",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedParams = unitLogQuerySchema.parse(queryParams);

    const {
      page,
      limit,
      frequency,
      unitType,
      dataSource,
      verificationStatus,
      startDate,
      endDate,
    } = validatedParams;

    // Build where clause
    const where: any = {
      projectId: projectId,
    };

    if (frequency) where.frequency = frequency;
    if (unitType) where.unitType = { contains: unitType, mode: 'insensitive' };
    if (dataSource) where.dataSource = dataSource;
    if (verificationStatus) where.verificationStatus = verificationStatus;
    if (startDate) where.logDate = { ...where.logDate, gte: new Date(startDate) };
    if (endDate) where.logDate = { ...where.logDate, lte: new Date(endDate) };

    const skip = (page - 1) * limit;

    // Get unit logs with pagination
    const [unitLogs, totalCount] = await Promise.all([
      db.unitLog.findMany({
        where,
        include: {
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          logDate: 'desc',
        },
        skip,
        take: limit,
      }),
      db.unitLog.count({ where }),
    ]);

    // Calculate summary statistics
    const [totalGeneration, verifiedCount, draftCount, rejectedCount] = await Promise.all([
      db.unitLog.aggregate({
        where: { projectId: projectId },
        _sum: { quantity: true },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId, 
          verificationStatus: { 
            in: ['VERIFIED', 'SPV_APPROVED', 'ORG_APPROVED', 'VVB_VERIFIED'] 
          } 
        },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId, 
          verificationStatus: 'DRAFT' 
        },
      }),
      db.unitLog.count({
        where: { 
          projectId: projectId, 
          verificationStatus: { 
            in: ['REJECTED', 'SPV_REJECTED', 'ORG_REJECTED', 'VVB_REJECTED'] 
          } 
        },
      }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info(`SPV user ${session.user.id} fetched ${unitLogs.length} unit logs for project ${projectId}`);

    return NextResponse.json({
      unitLogs,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      summary: {
        totalGeneration: Number(totalGeneration._sum.quantity || 0),
        verifiedCount,
        draftCount,
        rejectedCount,
        pendingCount: totalCount - verifiedCount - draftCount - rejectedCount,
      },
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid query parameters",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    logger.error(`Error fetching unit logs for SPV project ${params.id}:`, error);
    
    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while fetching unit logs",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getProjectUnitLogsHandler);
