import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

/**
 * Simple test endpoint to verify API functionality
 */
export async function GET(request: NextRequest) {
  try {
    logger.info("Test API called");
    
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    logger.info("User authenticated", {
      userId: session.user.id,
      userRole: session.user.role,
      organizationId: session.user.organizationId,
    });

    // Test database connection
    const userCount = await db.user.count();
    
    logger.info("Database test successful", {
      userCount,
    });

    return NextResponse.json({
      success: true,
      message: "API is working",
      user: {
        id: session.user.id,
        role: session.user.role,
        organizationId: session.user.organizationId,
      },
      database: {
        userCount,
      },
    });

  } catch (error) {
    logger.error("Test API error:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });

    return NextResponse.json(
      { error: "Internal server error", details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    );
  }
}
