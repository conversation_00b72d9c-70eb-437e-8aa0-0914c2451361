"use client";

import { useState } from "react";
import { Plus, TrendingUp, Search, Filter, MoreHorizontal, Edit, Eye, Calendar } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useBrokerTransactions } from "@/hooks/use-broker-transactions";
import { BrokerTransactionForm } from "@/components/broker/broker-transaction-form";
import { BrokerTransactionDetails } from "@/components/broker/broker-transaction-details";

type ViewMode = "list" | "create" | "edit" | "view";

export default function BrokerTransactionsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [dateFromFilter, setDateFromFilter] = useState<string>("");
  const [dateToFilter, setDateToFilter] = useState<string>("");

  const {
    transactions,
    pagination,
    summary,
    isLoading,
    error,
    refetch,
    updateFilters,
  } = useBrokerTransactions({
    search: searchTerm,
    status: statusFilter === "all" ? undefined : statusFilter,
    transactionType: typeFilter === "all" ? undefined : typeFilter,
    dateFrom: dateFromFilter || undefined,
    dateTo: dateToFilter || undefined,
  });

  const handleCreateTransaction = () => {
    setSelectedTransaction(null);
    setViewMode("create");
  };

  const handleEditTransaction = (transaction: any) => {
    setSelectedTransaction(transaction);
    setViewMode("edit");
  };

  const handleViewTransaction = (transaction: any) => {
    setSelectedTransaction(transaction);
    setViewMode("view");
  };

  const handleFormSuccess = () => {
    setViewMode("list");
    refetch();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "CARBON_CREDIT_SALE":
        return "bg-green-100 text-green-800";
      case "CARBON_CREDIT_PURCHASE":
        return "bg-blue-100 text-blue-800";
      case "PROJECT_INVESTMENT":
        return "bg-purple-100 text-purple-800";
      case "CONSULTATION":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatTransactionType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  if (viewMode === "create") {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Create Transaction</h1>
            <p className="text-gray-600 mt-1">Record a new transaction for a client.</p>
          </div>
          <Button variant="outline" onClick={() => setViewMode("list")}>
            Back to Transactions
          </Button>
        </div>
        <BrokerTransactionForm onSuccess={handleFormSuccess} onCancel={() => setViewMode("list")} />
      </div>
    );
  }

  if (viewMode === "edit" && selectedTransaction) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Transaction</h1>
            <p className="text-gray-600 mt-1">Update transaction information.</p>
          </div>
          <Button variant="outline" onClick={() => setViewMode("list")}>
            Back to Transactions
          </Button>
        </div>
        <BrokerTransactionForm 
          transaction={selectedTransaction}
          onSuccess={handleFormSuccess} 
          onCancel={() => setViewMode("list")} 
        />
      </div>
    );
  }

  if (viewMode === "view" && selectedTransaction) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Transaction Details</h1>
            <p className="text-gray-600 mt-1">View transaction information and history.</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={() => handleEditTransaction(selectedTransaction)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Transaction
            </Button>
            <Button variant="outline" onClick={() => setViewMode("list")}>
              Back to Transactions
            </Button>
          </div>
        </div>
        <BrokerTransactionDetails transaction={selectedTransaction} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Transaction Management</h1>
          <p className="text-gray-600 mt-1">
            Track and manage all client transactions and commissions.
          </p>
        </div>
        <Button onClick={handleCreateTransaction}>
          <Plus className="h-4 w-4 mr-2" />
          New Transaction
        </Button>
      </div>

      {/* Summary Stats */}
      {summary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${summary.totalAmount.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                From completed transactions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Commission</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${summary.totalCommission.toLocaleString()}</div>
              <p className="text-xs text-muted-foreground">
                Commission earned
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed Transactions</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.completedTransactions}</div>
              <p className="text-xs text-muted-foreground">
                Successfully processed
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search transactions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="PROCESSING">Processing</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="FAILED">Failed</SelectItem>
                <SelectItem value="CANCELLED">Cancelled</SelectItem>
              </SelectContent>
            </Select>

            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="CARBON_CREDIT_SALE">Credit Sale</SelectItem>
                <SelectItem value="CARBON_CREDIT_PURCHASE">Credit Purchase</SelectItem>
                <SelectItem value="PROJECT_INVESTMENT">Project Investment</SelectItem>
                <SelectItem value="CONSULTATION">Consultation</SelectItem>
                <SelectItem value="OTHER">Other</SelectItem>
              </SelectContent>
            </Select>

            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="date"
                placeholder="From Date"
                value={dateFromFilter}
                onChange={(e) => setDateFromFilter(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="date"
                placeholder="To Date"
                value={dateToFilter}
                onChange={(e) => setDateToFilter(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Transactions Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Transactions</CardTitle>
              <CardDescription>
                {pagination?.totalCount || 0} total transactions
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Spinner size="lg" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-600">
              <p>Error loading transactions: {error}</p>
              <Button onClick={refetch} className="mt-4">
                Try Again
              </Button>
            </div>
          ) : transactions && transactions.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Client</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Commission</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {transactions.map((transaction) => (
                  <TableRow key={transaction.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{transaction.brokerClient?.clientName || "Unknown"}</p>
                        <p className="text-sm text-gray-600">{transaction.brokerClient?.clientEmail}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getTypeColor(transaction.transactionType)}>
                        {formatTransactionType(transaction.transactionType)}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      ${transaction.amount.toLocaleString()}
                    </TableCell>
                    <TableCell className="text-green-600 font-medium">
                      +${transaction.commissionAmount.toLocaleString()}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(transaction.status)}>
                        {transaction.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(transaction.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewTransaction(transaction)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditTransaction(transaction)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No transactions found</h3>
              <p className="text-gray-600 mb-4">Get started by creating your first transaction.</p>
              <Button onClick={handleCreateTransaction}>
                <Plus className="h-4 w-4 mr-2" />
                Create Transaction
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
