import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";
import { UserRole } from "@prisma/client";

// Schema for project document creation
const projectDocumentSchema = z.object({
  name: z.string().min(1, "Document name is required"),
  type: z.string().min(1, "Document type is required"),
  url: z.string().url("Valid URL is required"),
  notes: z.string().optional(),
});

/**
 * GET /api/spv/projects/[id]/documents
 * Get documents for a specific project (SPV users only)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const projectId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { 
            projectId: projectId,
            isActive: true 
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    if (spvUser.projectAssignments.length === 0) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Get project documents
    const documents = await db.projectDocument.findMany({
      where: {
        projectId: projectId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    logger.info(`SPV user ${session.user.id} retrieved documents for project ${projectId}`);

    return NextResponse.json({
      documents,
      message: "Documents retrieved successfully",
    });
  } catch (error) {
    logger.error("Error retrieving project documents:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving documents" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/spv/projects/[id]/documents
 * Add a document to a project (SPV users only)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const projectId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { 
            projectId: projectId,
            isActive: true 
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    if (spvUser.projectAssignments.length === 0) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Check if user has permission to manage documents
    const canManageDocuments = spvUser.role === "SPV_ADMIN" || spvUser.role === "PROJECT_MANAGER";
    
    if (!canManageDocuments) {
      return NextResponse.json(
        { error: "Access denied. You don't have permission to upload documents." },
        { status: 403 }
      );
    }

    // Verify project exists
    const project = await db.project.findUnique({
      where: { id: projectId },
      select: { id: true, name: true },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const documentData = projectDocumentSchema.parse(body);

    // Create the document
    const document = await db.projectDocument.create({
      data: {
        name: documentData.name,
        type: documentData.type as any, // Cast to ProjectDocumentType enum
        url: documentData.url,
        notes: documentData.notes,
        status: "PENDING",
        project: {
          connect: {
            id: projectId,
          },
        },
      },
    });

    logger.info(`SPV user ${session.user.id} added document ${document.id} to project ${projectId}`);

    return NextResponse.json({
      document,
      message: "Document added successfully",
    });
  } catch (error) {
    logger.error("Error adding project document:", error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid document data provided", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while adding the document" },
      { status: 500 }
    );
  }
}
