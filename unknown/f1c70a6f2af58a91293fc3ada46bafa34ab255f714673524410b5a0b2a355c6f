import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';
import { ALL_PERMISSIONS } from '@/lib/rbac/permissions';

/**
 * GET /api/rbac/permissions
 * Get all available permissions grouped by category
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canView = await hasPermission('view:permission', context);
    if (!canView) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Get permissions from database to ensure they exist
    const dbPermissions = await db.permission.findMany({
      select: {
        name: true,
        displayName: true,
        description: true,
        category: true,
      },
      orderBy: [
        { category: 'asc' },
        { displayName: 'asc' },
      ],
    });

    // Group permissions by category
    const groupedPermissions = dbPermissions.reduce((acc, permission) => {
      const category = permission.category;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(permission);
      return acc;
    }, {} as Record<string, typeof dbPermissions>);

    // Get permission usage statistics
    const permissionUsage = await db.rolePermission.groupBy({
      by: ['permissionId'],
      _count: {
        roleId: true,
      },
    });

    const permissionUsageMap = new Map(
      permissionUsage.map(usage => [usage.permissionId, usage._count.roleId])
    );

    // Add usage count to permissions
    const permissionsWithUsage = Object.entries(groupedPermissions).reduce((acc, [category, permissions]) => {
      acc[category] = permissions.map(permission => {
        const dbPermission = dbPermissions.find(p => p.name === permission.name);
        const usageCount = dbPermission ? permissionUsageMap.get(dbPermission.name) || 0 : 0;
        
        return {
          ...permission,
          usageCount,
        };
      });
      return acc;
    }, {} as Record<string, Array<typeof dbPermissions[0] & { usageCount: number }>>);

    return NextResponse.json({
      success: true,
      data: {
        permissions: permissionsWithUsage,
        totalPermissions: dbPermissions.length,
        categories: Object.keys(groupedPermissions),
      },
    });
  } catch (error) {
    logger.error('Error fetching permissions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/rbac/permissions/sync
 * Sync permissions from code to database
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin (only admins can sync permissions)
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { role: true, organizationId: true },
    });

    if (user?.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Sync permissions from code to database
    const syncResults = {
      created: 0,
      updated: 0,
      errors: [] as string[],
    };

    for (const permission of ALL_PERMISSIONS) {
      try {
        await db.permission.upsert({
          where: { name: permission.name },
          update: {
            displayName: permission.displayName,
            description: permission.description,
            category: permission.category,
          },
          create: {
            name: permission.name,
            displayName: permission.displayName,
            description: permission.description,
            category: permission.category,
          },
        });

        // Check if it was created or updated
        const existingPermission = await db.permission.findUnique({
          where: { name: permission.name },
          select: { createdAt: true, updatedAt: true },
        });

        if (existingPermission) {
          if (existingPermission.createdAt.getTime() === existingPermission.updatedAt.getTime()) {
            syncResults.created++;
          } else {
            syncResults.updated++;
          }
        }
      } catch (error) {
        syncResults.errors.push(`Failed to sync permission ${permission.name}: ${error}`);
      }
    }

    logger.info('Permissions synced successfully', {
      created: syncResults.created,
      updated: syncResults.updated,
      errors: syncResults.errors.length,
      syncedBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      data: syncResults,
    });
  } catch (error) {
    logger.error('Error syncing permissions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
