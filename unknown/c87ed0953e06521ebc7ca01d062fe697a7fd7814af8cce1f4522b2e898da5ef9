import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { DataVerificationStatus, NotificationType, NotificationPriority } from "@prisma/client";
import { z } from "zod";
import { NotificationService } from "@/lib/notification-service";
import { ProjectAuditService } from "@/lib/audit/project-audit";

// Validation schema for verification action
const verificationActionSchema = z.object({
  unitLogId: z.string().min(1, "Unit log ID is required"),
  action: z.enum([
    "SUBMIT_FOR_VERIFICATION",
    "PM_VERIFY",           // Project Manager verification
    "PM_REJECT",           // Project Manager rejection
    "SPV_ADMIN_VERIFY",    // SPV Admin verification
    "SPV_ADMIN_REJECT",    // SPV Admin rejection
    "SPV_ADMIN_VERIFY_AND_SUBMIT", // SPV Admin verify and submit to org admin in one step
    "VERIFY",              // Final verification (org admin)
    "REJECT",              // Reject at any stage
    "SPV_APPROVE",         // Legacy - keeping for compatibility
    "SPV_REJECT",          // Legacy - keeping for compatibility
    "ORG_APPROVE",
    "ORG_REJECT",
    "SUBMIT_TO_ORG_ADMIN", // Submit to org admin for final approval
    "SUBMIT_TO_VVB",
    "SEND_BACK_FOR_CORRECTION", // Send rejected entry back for correction
    "EDIT_AND_RESUBMIT"    // Edit and resubmit rejected entry
  ]),
  notes: z.string().max(1000, "Notes must be less than 1000 characters").optional(),
  rejectionReason: z.string().max(500, "Rejection reason must be less than 500 characters").optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Get available actions based on user role and current verification status
 */
function getAvailableActions(currentStatus: string, userRole: string, isOwner: boolean): string[] {
  const actions: string[] = [];

  switch (userRole) {
    case "SITE_WORKER":
      if (isOwner) {
        if (currentStatus === "DRAFT") {
          actions.push("SUBMIT_FOR_VERIFICATION");
        }
        // Site workers can resubmit if their entry was rejected by PM
        if (currentStatus === "REJECTED") {
          actions.push("SUBMIT_FOR_VERIFICATION");
        }
        // Site workers can resubmit if their entry was rejected by SPV admin
        if (currentStatus === "SPV_REJECTED") {
          actions.push("SUBMIT_FOR_VERIFICATION");
        }
      }
      break;

    case "PROJECT_MANAGER":
      // Project managers cannot verify their own entries
      if (!isOwner) {
        if (currentStatus === "DRAFT" || currentStatus === "SUBMITTED_FOR_VERIFICATION") {
          actions.push("PM_VERIFY", "PM_REJECT");
        }
        // PMs can resubmit to SPV admin if they previously verified but SPV admin rejected
        if (currentStatus === "PM_VERIFIED") {
          actions.push("PM_VERIFY"); // Allow resubmission to SPV admin
        }
        // PMs can re-verify entries that were rejected by SPV admin
        if (currentStatus === "SPV_REJECTED") {
          actions.push("PM_VERIFY", "PM_REJECT");
        }
      }
      break;

    case "SPV_ADMIN":
      // SPV admins cannot verify their own entries
      if (!isOwner) {
        if (currentStatus === "PM_VERIFIED") {
          actions.push("SPV_ADMIN_VERIFY", "SPV_ADMIN_REJECT", "SPV_ADMIN_VERIFY_AND_SUBMIT");
        }
        // SPV admins can also verify entries that are already SPV_ADMIN_VERIFIED (for re-review)
        if (currentStatus === "SPV_ADMIN_VERIFIED") {
          actions.push("SPV_ADMIN_VERIFY");
        }
      }
      // SPV admins can submit verified entries to org admin
      if (currentStatus === "SPV_ADMIN_VERIFIED") {
        actions.push("SUBMIT_TO_ORG_ADMIN");
      }
      // SPV admins can handle org-rejected entries
      if (currentStatus === "ORG_REJECTED") {
        if (isOwner) {
          // If SPV admin owns the entry, they can edit and resubmit
          actions.push("EDIT_AND_RESUBMIT");
        } else {
          // If entry belongs to someone else, SPV admin can either:
          // 1. Send back to original logger for corrections
          // 2. Edit the data themselves and resubmit
          actions.push("SEND_BACK_FOR_CORRECTION", "EDIT_AND_RESUBMIT");
        }
      }
      break;
  }

  return actions;
}

/**
 * Send verification notification to relevant users
 */
async function sendVerificationNotification(
  action: string,
  unitLog: any,
  verifierName: string,
  rejectionReason?: string
) {
  try {
    let title = "";
    let message = "";
    let targetUserId = "";
    let actionUrl = "";

    switch (action) {
      case "PM_VERIFY":
        // Notify SPV admin that data is ready for their review
        title = "Data Ready for SPV Admin Review";
        message = `Data entry for ${unitLog.project.name} has been verified by PM ${verifierName} and is ready for your review.`;
        // Find SPV admin for this project
        const spvAdmin = await db.sPVUser.findFirst({
          where: {
            role: "SPV_ADMIN",
            projectAssignments: {
              some: {
                projectId: unitLog.projectId,
                isActive: true,
              },
            },
            isActive: true,
          },
        });
        if (spvAdmin) {
          targetUserId = spvAdmin.userId;
        }
        actionUrl = `/spv/verification`;
        break;

      case "PM_REJECT":
        // Notify site worker that their data was rejected by PM
        title = "Data Entry Rejected by Project Manager";
        message = `Your data entry for ${unitLog.project.name} was rejected by PM ${verifierName}. Reason: ${rejectionReason || "No reason provided"}`;
        targetUserId = unitLog.loggedBy;
        actionUrl = `/spv/projects/${unitLog.projectId}/data-entry`;
        break;

      case "SPV_ADMIN_VERIFY":
        // Notify organization admin that data is fully verified
        title = "Data Entry Fully Verified";
        message = `Data entry for ${unitLog.project.name} has been fully verified and approved by SPV Admin ${verifierName}.`;
        // For now, we'll notify the project manager as well
        const pm = await db.sPVUser.findFirst({
          where: {
            role: "PROJECT_MANAGER",
            projectAssignments: {
              some: {
                projectId: unitLog.projectId,
                isActive: true,
              },
            },
            isActive: true,
          },
        });
        if (pm) {
          targetUserId = pm.userId;
        }
        actionUrl = `/spv/projects/${unitLog.projectId}/monitoring`;
        break;

      case "SPV_ADMIN_REJECT":
        // Notify project manager that data was rejected by SPV admin
        title = "Data Entry Rejected by SPV Admin";
        message = `Data entry for ${unitLog.project.name} was rejected by SPV Admin ${verifierName}. Reason: ${rejectionReason || "No reason provided"}`;
        const projectManager = await db.sPVUser.findFirst({
          where: {
            role: "PROJECT_MANAGER",
            projectAssignments: {
              some: {
                projectId: unitLog.projectId,
                isActive: true,
              },
            },
            isActive: true,
          },
        });
        if (projectManager) {
          targetUserId = projectManager.userId;
        }
        actionUrl = `/spv/verification`;
        break;

      case "SUBMIT_TO_ORG_ADMIN":
      case "SPV_ADMIN_VERIFY_AND_SUBMIT":
        // Notify organization admin that data is ready for their review
        title = "Data Submitted for Organization Admin Review";
        message = `Data entry for ${unitLog.project.name} has been submitted by SPV Admin ${verifierName} and is ready for organization admin review.`;
        const orgAdmin = await db.user.findFirst({
          where: {
            role: "ORGANIZATION_ADMIN",
            organizationId: unitLog.project.organizationId,
          },
        });
        if (orgAdmin) {
          targetUserId = orgAdmin.id;
        }
        actionUrl = `/dashboard/verification`;
        break;

      case "SEND_BACK_FOR_CORRECTION":
        // Notify original logger that entry needs correction
        title = "Data Entry Sent Back for Correction";
        message = `Your data entry for ${unitLog.project.name} has been sent back by SPV Admin ${verifierName} for correction. Please review and resubmit.`;
        targetUserId = unitLog.loggedBy;
        actionUrl = `/spv/data-entry`;
        break;

      case "EDIT_AND_RESUBMIT":
        // Notify organization admin that edited data is ready for review
        title = "Edited Data Resubmitted for Review";
        message = `Data entry for ${unitLog.project.name} has been edited and resubmitted by SPV Admin ${verifierName} for your review.`;
        const orgAdminForEdit = await db.user.findFirst({
          where: {
            role: "ORGANIZATION_ADMIN",
            organizationId: unitLog.project.organizationId,
          },
        });
        if (orgAdminForEdit) {
          targetUserId = orgAdminForEdit.id;
        }
        actionUrl = `/dashboard/verification`;
        break;
    }

    if (targetUserId && title && message) {
      await NotificationService.createNotification({
        userId: targetUserId,
        title,
        message,
        type: NotificationType.VERIFICATION,
        priority: NotificationPriority.NORMAL,
        actionUrl,
        actionLabel: "View Details",
        metadata: {
          unitLogId: unitLog.id,
          projectId: unitLog.projectId,
          action,
          verifierName,
          rejectionReason,
        },
      });

      logger.info("Verification notification sent", {
        action,
        targetUserId,
        unitLogId: unitLog.id,
        projectId: unitLog.projectId,
      });
    }
  } catch (error) {
    logger.error("Error sending verification notification", error);
    // Don't throw error as notification failure shouldn't break the verification process
  }
}

/**
 * GET /api/spv/verification
 * Get verification queue for current SPV user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const projectId = searchParams.get("projectId");

    const skip = (page - 1) * limit;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Get project IDs based on user role
    let projectIds: string[] = [];

    if (spvUser.role === "SPV_ADMIN") {
      // SPV admins can access all projects within their SPV
      const spvProjects = await db.project.findMany({
        where: { spvId: spvUser.spvId },
        select: { id: true },
      });
      projectIds = spvProjects.map(p => p.id);
    } else {
      // Other roles need specific project assignments
      projectIds = spvUser.projectAssignments.map(a => a.projectId);
    }

    if (projectIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          unitLogs: [],
          pagination: {
            page,
            limit,
            totalCount: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
          userRole: spvUser.role,
        },
      });
    }

    // Build where clause
    const where: any = {
      projectId: { in: projectIds },
    };

    if (projectId) {
      where.projectId = projectId;
    }

    // Filter by verification status based on user role
    if (spvUser.role === "SITE_WORKER") {
      // Site workers see their own entries
      where.loggedBy = session.user.id;
      if (status) {
        where.verificationStatus = status;
      } else {
        // Show all their entries regardless of status
        where.verificationStatus = { in: ["DRAFT", "PM_VERIFIED", "SPV_ADMIN_VERIFIED", "SUBMITTED_TO_ORG_ADMIN", "VERIFIED", "REJECTED", "SPV_REJECTED", "SUBMITTED_FOR_VERIFICATION", "ORG_APPROVED", "ORG_REJECTED", "SUBMITTED_TO_VVB", "VVB_VERIFIED", "VVB_REJECTED"] };
      }
    } else if (spvUser.role === "PROJECT_MANAGER") {
      // Project managers see all entries in their projects for tracking
      // They can verify others' entries but only track their own
      if (status) {
        where.verificationStatus = status;
      } else {
        where.verificationStatus = { in: ["DRAFT", "PM_VERIFIED", "SPV_ADMIN_VERIFIED", "SUBMITTED_TO_ORG_ADMIN", "VERIFIED", "REJECTED", "SPV_REJECTED", "SUBMITTED_FOR_VERIFICATION", "ORG_APPROVED", "ORG_REJECTED", "SUBMITTED_TO_VVB", "VVB_VERIFIED", "VVB_REJECTED"] };
      }
    } else if (spvUser.role === "SPV_ADMIN") {
      // SPV admins see all entries in their SPV for tracking
      // They can verify others' entries but only track their own
      if (status) {
        where.verificationStatus = status;
      } else {
        where.verificationStatus = { in: ["PM_VERIFIED", "SPV_ADMIN_VERIFIED", "SUBMITTED_TO_ORG_ADMIN", "VERIFIED", "REJECTED", "SPV_REJECTED", "ORG_APPROVED", "ORG_REJECTED", "SUBMITTED_TO_VVB", "VVB_VERIFIED", "VVB_REJECTED"] };
      }
    }

    // Get unit logs with verification data
    const [unitLogs, totalCount] = await Promise.all([
      db.unitLog.findMany({
        where,
        include: {
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          verifier: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
              type: true,
              status: true,
            },
          },
          verificationLogs: {
            include: {
              verifier: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                  role: true,
                },
              },
            },
            orderBy: { createdAt: "desc" },
            take: 5,
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      db.unitLog.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("Verification queue retrieved", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      role: spvUser.role,
      count: unitLogs.length,
      totalCount,
    });

    // Add available actions for each unit log
    const unitLogsWithActions = unitLogs.map(unitLog => {
      const availableActions = getAvailableActions(unitLog.verificationStatus, spvUser.role, unitLog.loggedBy === session.user.id);
      console.log(`=== AVAILABLE ACTIONS DEBUG ===`);
      console.log(`Unit Log ID: ${unitLog.id}`);
      console.log(`Status: ${unitLog.verificationStatus}`);
      console.log(`User Role: ${spvUser.role}`);
      console.log(`Is Owner: ${unitLog.loggedBy === session.user.id}`);
      console.log(`Available Actions: ${JSON.stringify(availableActions)}`);
      console.log(`===============================`);
      return {
        ...unitLog,
        availableActions,
      };
    });

    return NextResponse.json({
      success: true,
      data: {
        unitLogs: unitLogsWithActions,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        userRole: spvUser.role,
      },
    });

  } catch (error) {
    logger.error("Error retrieving verification queue", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/spv/verification
 * Perform verification action on unit log
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const body = await request.json();

    const validatedData = verificationActionSchema.parse(body);

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Get the unit log
    const unitLog = await db.unitLog.findUnique({
      where: { id: validatedData.unitLogId },
      include: { project: true },
    });

    if (!unitLog) {
      return NextResponse.json(
        { error: "Unit log not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    // SPV admins can access all projects within their SPV
    // Other roles need specific project assignments
    let hasAccess = false;

    if (spvUser.role === "SPV_ADMIN") {
      // SPV admins can access any project within their SPV
      hasAccess = unitLog.project.spvId === spvUser.spvId;
    } else {
      // Other roles need specific project assignments
      hasAccess = spvUser.projectAssignments.some(
        a => a.projectId === unitLog.projectId
      );
    }

    if (!hasAccess) {
      return NextResponse.json(
        { error: "Access denied. You don't have access to this project." },
        { status: 403 }
      );
    }

    // Validate action based on current status and user role
    const canPerformAction = validateVerificationAction(
      validatedData.action,
      unitLog.verificationStatus,
      spvUser.role,
      unitLog.loggedBy === session.user.id
    );

    if (!canPerformAction.allowed) {
      return NextResponse.json(
        { error: canPerformAction.reason },
        { status: 400 }
      );
    }

    // Perform the verification action in a transaction
    const result = await db.$transaction(async (tx) => {
      try {
        // Determine new status and update fields
        const updateData = getVerificationUpdateData(
          validatedData.action,
          session.user.id,
          validatedData.notes,
          validatedData.rejectionReason
        );

        // Update the unit log
        const updatedUnitLog = await tx.unitLog.update({
          where: { id: validatedData.unitLogId },
          data: updateData,
          include: {
            logger: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            verifier: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            project: {
              select: {
                id: true,
                name: true,
                type: true,
                status: true,
              },
            },
          },
        });

        // Create verification log entry
        await tx.dataVerificationLog.create({
          data: {
            unitLogId: validatedData.unitLogId,
            fromStatus: unitLog.verificationStatus,
            toStatus: updateData.verificationStatus,
            verifiedBy: session.user.id,
            verificationNotes: validatedData.rejectionReason || validatedData.notes,
            metadata: {
              ...validatedData.metadata,
              action: validatedData.action,
              rejectionReason: validatedData.rejectionReason,
            },
          },
        });

        return updatedUnitLog;
      } catch (error) {
        // Rollback transaction on error
        throw error;
      }
    });

    logger.info("Verification action performed", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      unitLogId: validatedData.unitLogId,
      action: validatedData.action,
      fromStatus: unitLog.verificationStatus,
      toStatus: result.verificationStatus,
    });

    // Log audit event for verification action
    await ProjectAuditService.logVerificationAction(
      validatedData.unitLogId,
      unitLog.projectId,
      session.user.id,
      unitLog.project.organizationId,
      validatedData.action,
      unitLog.verificationStatus,
      result.verificationStatus,
      spvUser.role,
      validatedData.notes,
      validatedData.rejectionReason,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      request.headers.get('user-agent') || undefined,
      spvUser.spvId // Include SPV context
    );

    // Send notification for verification actions that affect other users
    if (["PM_VERIFY", "PM_REJECT", "SPV_ADMIN_VERIFY", "SPV_ADMIN_REJECT", "SPV_ADMIN_VERIFY_AND_SUBMIT", "SUBMIT_TO_ORG_ADMIN", "SEND_BACK_FOR_CORRECTION", "EDIT_AND_RESUBMIT"].includes(validatedData.action)) {
      await sendVerificationNotification(
        validatedData.action,
        result,
        session.user.name || "Unknown User",
        validatedData.rejectionReason
      );
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: `${validatedData.action.replace('_', ' ').toLowerCase()} completed successfully`,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error performing verification action", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to validate if an action can be performed
function validateVerificationAction(
  action: string,
  currentStatus: string,
  userRole: string,
  isOwner: boolean
): { allowed: boolean; reason?: string } {
  const transitions: Record<string, { allowedRoles: string[]; fromStatuses: string[]; requiresOwnership?: boolean; prohibitsOwnership?: boolean }> = {
    SUBMIT_FOR_VERIFICATION: {
      allowedRoles: ["SITE_WORKER", "PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["DRAFT", "REJECTED", "SPV_REJECTED"],
      requiresOwnership: true,
    },
    PM_VERIFY: {
      allowedRoles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["DRAFT", "SUBMITTED_FOR_VERIFICATION", "SPV_REJECTED"],
      prohibitsOwnership: true, // Cannot verify own entries
    },
    PM_REJECT: {
      allowedRoles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["DRAFT", "SUBMITTED_FOR_VERIFICATION", "SPV_REJECTED"],
      prohibitsOwnership: true, // Cannot reject own entries
    },
    SPV_ADMIN_VERIFY: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["PM_VERIFIED"],
      prohibitsOwnership: true, // Cannot verify own entries
    },
    SPV_ADMIN_REJECT: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["PM_VERIFIED"],
      prohibitsOwnership: true, // Cannot reject own entries
    },
    SPV_ADMIN_VERIFY_AND_SUBMIT: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["PM_VERIFIED"],
      prohibitsOwnership: true, // Cannot verify own entries
    },
    VERIFY: {
      allowedRoles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["SPV_ADMIN_VERIFIED", "SUBMITTED_FOR_VERIFICATION"],
      prohibitsOwnership: true, // Cannot verify own entries
    },
    REJECT: {
      allowedRoles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      fromStatuses: ["DRAFT", "PM_VERIFIED", "SPV_ADMIN_VERIFIED", "SUBMITTED_FOR_VERIFICATION", "VERIFIED"],
      prohibitsOwnership: true, // Cannot reject own entries
    },
    SPV_APPROVE: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["VERIFIED"],
    },
    SPV_REJECT: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["VERIFIED"],
    },
    ORG_APPROVE: {
      allowedRoles: ["ORGANIZATION_ADMIN"],
      fromStatuses: ["SPV_ADMIN_VERIFIED"],
      prohibitsOwnership: true, // Cannot approve own entries
    },
    ORG_REJECT: {
      allowedRoles: ["ORGANIZATION_ADMIN"],
      fromStatuses: ["SPV_ADMIN_VERIFIED"],
      prohibitsOwnership: true, // Cannot reject own entries
    },
    SUBMIT_TO_ORG_ADMIN: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["SPV_ADMIN_VERIFIED"],
      prohibitsOwnership: false, // SPV admin can submit their verified entries
    },
    SEND_BACK_FOR_CORRECTION: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["ORG_REJECTED"],
      prohibitsOwnership: true, // Can only send back others' entries
    },
    EDIT_AND_RESUBMIT: {
      allowedRoles: ["SPV_ADMIN"],
      fromStatuses: ["ORG_REJECTED"],
      prohibitsOwnership: false, // Can edit any rejected entry
    },
    SUBMIT_TO_VVB: {
      allowedRoles: ["ORGANIZATION_ADMIN"],
      fromStatuses: ["ORG_APPROVED", "VERIFIED"],
    },
  };

  const transition = transitions[action];
  if (!transition) {
    return { allowed: false, reason: "Invalid action" };
  }

  if (!transition.allowedRoles.includes(userRole)) {
    return { allowed: false, reason: "Insufficient permissions for this action" };
  }

  if (!transition.fromStatuses.includes(currentStatus)) {
    return { allowed: false, reason: `Cannot ${action} from current status: ${currentStatus}` };
  }

  if (transition.requiresOwnership && !isOwner) {
    return { allowed: false, reason: "You can only perform this action on your own entries" };
  }

  if (transition.prohibitsOwnership && isOwner) {
    return { allowed: false, reason: "You cannot verify or reject your own entries" };
  }

  return { allowed: true };
}

// Helper function to get update data for verification action
function getVerificationUpdateData(action: string, userId: string, notes?: string, rejectionReason?: string): any {
  const now = new Date();

  switch (action) {
    case "SUBMIT_FOR_VERIFICATION":
      return {
        verificationStatus: DataVerificationStatus.SUBMITTED_FOR_VERIFICATION,
      };
    case "PM_VERIFY":
      return {
        verificationStatus: DataVerificationStatus.PM_VERIFIED,
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
      };
    case "PM_REJECT":
      return {
        verificationStatus: DataVerificationStatus.REJECTED, // Set to REJECTED status
        verifiedBy: null, // Clear verification fields
        verifiedAt: null,
        verificationNotes: null,
        rejectedBy: userId,
        rejectedAt: now,
        rejectionReason: rejectionReason || notes,
        updatedAt: now,
      };
    case "SPV_ADMIN_VERIFY":
      return {
        verificationStatus: DataVerificationStatus.SPV_ADMIN_VERIFIED,
        spvApprovedBy: userId,
        spvApprovedAt: now,
        spvApprovalNotes: notes,
      };
    case "SPV_ADMIN_REJECT":
      return {
        verificationStatus: DataVerificationStatus.SPV_REJECTED, // Set to SPV_REJECTED status
        spvApprovedBy: null, // Clear SPV approval fields
        spvApprovedAt: null,
        spvApprovalNotes: null,
        rejectedBy: userId,
        rejectedAt: now,
        rejectionReason: rejectionReason || notes,
        updatedAt: now,
      };
    case "SPV_ADMIN_VERIFY_AND_SUBMIT":
      return {
        verificationStatus: "SUBMITTED_TO_ORG_ADMIN" as any,
        spvApprovedBy: userId,
        spvApprovedAt: now,
        spvApprovalNotes: notes,
        // Track who submitted to org admin
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
        updatedAt: now,
      };
    case "VERIFY":
      return {
        verificationStatus: DataVerificationStatus.VERIFIED,
        orgApprovedBy: userId,
        orgApprovedAt: now,
        orgApprovalNotes: notes,
      };
    case "REJECT":
      return {
        verificationStatus: DataVerificationStatus.REJECTED,
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
        rejectedBy: userId,
        rejectedAt: now,
        rejectionReason: rejectionReason || notes,
      };
    case "SPV_APPROVE":
      return {
        verificationStatus: DataVerificationStatus.SPV_APPROVED,
        spvApprovedBy: userId,
        spvApprovedAt: now,
        spvApprovalNotes: notes,
      };
    case "SPV_REJECT":
      return {
        verificationStatus: DataVerificationStatus.SPV_REJECTED,
        spvApprovedBy: userId,
        spvApprovedAt: now,
        spvApprovalNotes: notes,
        rejectedBy: userId,
        rejectedAt: now,
        rejectionReason: rejectionReason || notes,
      };
    case "ORG_APPROVE":
      return {
        verificationStatus: DataVerificationStatus.VERIFIED,
        orgApprovedBy: userId,
        orgApprovedAt: now,
        orgApprovalNotes: notes,
      };
    case "ORG_REJECT":
      return {
        verificationStatus: DataVerificationStatus.ORG_REJECTED, // Set to ORG_REJECTED so SPV admin can see it
        orgApprovedBy: null,
        orgApprovedAt: null,
        orgApprovalNotes: null,
        rejectedBy: userId,
        rejectedAt: now,
        rejectionReason: rejectionReason || notes,
        updatedAt: now,
      };
    case "SUBMIT_TO_ORG_ADMIN":
      return {
        verificationStatus: "SUBMITTED_TO_ORG_ADMIN" as any,
        // Track who submitted to org admin
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
        updatedAt: now,
      };
    case "SEND_BACK_FOR_CORRECTION":
      return {
        verificationStatus: DataVerificationStatus.REJECTED, // Set to REJECTED so original logger can see it in rejected entries
        // Clear all verification data so original logger can re-enter
        verifiedBy: null,
        verifiedAt: null,
        verificationNotes: null,
        orgApprovedBy: null,
        orgApprovedAt: null,
        orgApprovalNotes: null,
        // Keep rejection info for reference
        rejectedBy: userId, // SPV admin who sent it back
        rejectedAt: now,
        rejectionReason: rejectionReason || notes || "Sent back for correction by SPV Admin",
        updatedAt: now,
      };
    case "EDIT_AND_RESUBMIT":
      return {
        verificationStatus: "SUBMITTED_TO_ORG_ADMIN" as any,
        // Clear rejection data and resubmit with SPV admin as verifier
        rejectedBy: null,
        rejectedAt: null,
        rejectionReason: null,
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes || "Edited and resubmitted by SPV Admin",
        updatedAt: now,
      };
    case "SUBMIT_TO_VVB":
      return {
        verificationStatus: DataVerificationStatus.SUBMITTED_TO_VVB,
        // Use existing fields for VVB submission tracking
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
      };
    default:
      throw new Error(`Unknown action: ${action}`);
  }
}
