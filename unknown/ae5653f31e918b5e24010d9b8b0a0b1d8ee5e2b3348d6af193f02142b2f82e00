"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CustomFieldsManager } from "@/components/admin/custom-fields-manager";
import { 
  Settings, 
  Plus, 
  Info,
  AlertCircle,
  Lightbulb
} from "lucide-react";
import { motion } from "framer-motion";
import { Alert, AlertDescription } from "@/components/ui/alert";

const PROJECT_TYPES = [
  {
    id: "RENEWABLE_ENERGY",
    name: "Renewable Energy",
    subtypes: [
      { id: "SOLAR_PV", name: "Solar Energy" },
      { id: "WIND_POWER", name: "Wind Energy" },
      { id: "HYBRID_RENEWABLE", name: "Hybrid (Solar + Wind) Energy" },
    ]
  },
  {
    id: "FORESTRY",
    name: "Forestry & Conservation",
    subtypes: [
      { id: "REFORESTATION", name: "Reforestation" },
      { id: "FOREST_CONSERVATION", name: "Forest Conservation" },
      { id: "AGROFORESTRY", name: "Agroforestry" },
    ]
  },
  {
    id: "WASTE_MANAGEMENT",
    name: "Waste Management",
    subtypes: [
      { id: "WASTE_TO_ENERGY", name: "Waste-to-Energy" },
      { id: "RECYCLING", name: "Recycling Programs" },
      { id: "WASTE_REDUCTION", name: "Waste Reduction" },
    ]
  },
];

export default function CustomFieldsPage() {
  const { data: session } = useSession();
  const [selectedProjectType, setSelectedProjectType] = useState<string>("RENEWABLE_ENERGY");
  const [selectedProjectSubtype, setSelectedProjectSubtype] = useState<string>("SOLAR_PV");

  // Check if user is organization admin
  const isOrganizationAdmin = session?.user?.role === "ORGANIZATION_ADMIN";

  if (!isOrganizationAdmin) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Access denied. Only organization administrators can manage custom fields.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const selectedType = PROJECT_TYPES.find(type => type.id === selectedProjectType);
  const availableSubtypes = selectedType?.subtypes || [];

  // Reset subtype when type changes
  const handleTypeChange = (typeId: string) => {
    setSelectedProjectType(typeId);
    const newType = PROJECT_TYPES.find(type => type.id === typeId);
    if (newType?.subtypes.length) {
      setSelectedProjectSubtype(newType.subtypes[0].id);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-4"
      >
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <Settings className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Custom Fields Management</h1>
            <p className="text-gray-600">Add custom fields to project creation forms for your organization</p>
          </div>
        </div>

        {/* Info Alert */}
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            Custom fields allow you to collect additional information specific to your organization's needs. 
            These fields will appear in the project creation forms for all users in your organization.
          </AlertDescription>
        </Alert>
      </motion.div>

      {/* Project Type Selection */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Select Project Type
            </CardTitle>
            <p className="text-sm text-muted-foreground">
              Choose the project type and subtype to manage custom fields for
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Project Type</label>
                <Select value={selectedProjectType} onValueChange={handleTypeChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select project type" />
                  </SelectTrigger>
                  <SelectContent>
                    {PROJECT_TYPES.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Project Subtype</label>
                <Select 
                  value={selectedProjectSubtype} 
                  onValueChange={setSelectedProjectSubtype}
                  disabled={!availableSubtypes.length}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select project subtype" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableSubtypes.map((subtype) => (
                      <SelectItem key={subtype.id} value={subtype.id}>
                        {subtype.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {selectedProjectType && selectedProjectSubtype && (
              <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start gap-2">
                  <Lightbulb className="h-4 w-4 text-blue-600 mt-0.5" />
                  <div className="text-sm">
                    <p className="font-medium text-blue-900">
                      Managing custom fields for: {selectedType?.name} → {availableSubtypes.find(s => s.id === selectedProjectSubtype)?.name}
                    </p>
                    <p className="text-blue-700 mt-1">
                      These fields will appear in the project creation form when users select this project type and subtype.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Custom Fields Manager */}
      {selectedProjectType && selectedProjectSubtype && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <CustomFieldsManager
            projectType={selectedProjectType}
            projectSubtype={selectedProjectSubtype}
          />
        </motion.div>
      )}

      {/* Help Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              How to Use Custom Fields
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">Field Types Available:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>Text:</strong> Single line text input</li>
                  <li>• <strong>Number:</strong> Numeric input with validation</li>
                  <li>• <strong>Date:</strong> Date picker</li>
                  <li>• <strong>Select:</strong> Dropdown with predefined options</li>
                  <li>• <strong>Textarea:</strong> Multi-line text input</li>
                  <li>• <strong>File:</strong> File upload functionality</li>
                  <li>• <strong>Checkbox:</strong> Yes/no or true/false input</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Best Practices:</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Use clear, descriptive field labels</li>
                  <li>• Add helpful placeholder text</li>
                  <li>• Include help text for complex fields</li>
                  <li>• Only mark fields as required if truly necessary</li>
                  <li>• Group related fields in the same step</li>
                  <li>• Test the form after adding new fields</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
