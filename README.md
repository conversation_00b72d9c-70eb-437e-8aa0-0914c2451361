# CarbonX - Carbon Credit Trading Platform

A comprehensive B2B carbon credit trading platform built with Next.js, TypeScript, and PostgreSQL.

## 🚀 Quick Start

```bash
# Install dependencies
pnpm install

# Start PostgreSQL (if using local installation)
sudo systemctl start postgresql

# Setup database
pnpm exec prisma db push
pnpm prisma:seed:comprehensive

# Start development server
pnpm run dev
```

Visit http://localhost:3000 (or the port shown in terminal)

## 📁 Project Structure

```
├── docs/                    # 📚 All documentation
├── scripts/                 # 🔧 Automation scripts
├── src/                     # 💻 Source code
├── prisma/                  # 🗄️ Database schema
├── public/                  # 🌐 Static assets
└── ...                      # Configuration files
```

## 📚 Documentation

**All documentation has been organized in the [`/docs`](./docs/) folder:**

- **[📖 Main Documentation](./docs/README.md)** - Complete platform documentation
- **[🔑 Login Credentials](./docs/LOGIN_CREDENTIALS.md)** - Test account credentials
- **[🏗️ Architecture Guide](./docs/architecture-documentation.md)** - System architecture
- **[🚀 Deployment Guide](./docs/deployment-guide.md)** - Deployment instructions
- **[🔐 RBAC System](./docs/rbac-system.md)** - Role-based access control
- **[📊 API Integration](./docs/API_INTEGRATION_GUIDE.md)** - API documentation

## 🛠️ Scripts

**All automation scripts are in the [`/scripts`](./scripts/) folder:**

- **Development**: `./scripts/run-docker.sh` (option 1)
- **Production**: `./scripts/run-docker.sh` (option 2)
- **Database**: `./scripts/reset-database.sh`
- **Seeding**: `./scripts/seed-comprehensive.ts`

## 🔧 Configuration

Key configuration files in the root:
- `package.json` - Dependencies and scripts
- `next.config.ts` - Next.js configuration
- `tailwind.config.ts` - Tailwind CSS configuration
- `tsconfig.json` - TypeScript configuration
- `.env` - Environment variables

## 🏃‍♂️ Development

```bash
# Local development (recommended)
pnpm run dev

# Docker development
./scripts/run-docker.sh
# Select option 1 for development mode

# Production build
./scripts/run-docker.sh
# Select option 2 for production mode
```

## 🧪 Testing

```bash
# Run tests
pnpm test

# Test specific functionality
node scripts/test-api-direct.js
```

## 📦 Tech Stack

- **Frontend**: Next.js 15, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Authentication**: NextAuth.js
- **Blockchain**: Alchemy SDK, Ethers.js
- **Email**: Nodemailer with SMTP
- **Deployment**: Docker, Docker Compose

## 🤝 Contributing

1. Check the [documentation](./docs/) for detailed guides
2. Follow the existing code structure
3. Run tests before submitting changes
4. Update documentation when adding features

## 📄 License

This project is proprietary software. All rights reserved.

---

**For detailed documentation, visit the [`/docs`](./docs/) folder.**
