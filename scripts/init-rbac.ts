#!/usr/bin/env tsx

/**
 * RBAC Initialization Script
 * 
 * This script initializes the RBAC system and optionally runs tests
 * 
 * Usage:
 *   npx tsx scripts/init-rbac.ts
 *   npx tsx scripts/init-rbac.ts --test
 *   npx tsx scripts/init-rbac.ts --test --cleanup
 */

import { initializeRbacSystem, isRbacInitialized } from '../src/lib/rbac/init-rbac';
import { logger } from '../src/lib/logger';

async function main() {
  console.log('🚀 RBAC System Initialization');
  console.log('================================');

  try {
    // Check if already initialized
    const isInitialized = await isRbacInitialized();

    if (isInitialized) {
      console.log('✅ RBAC system is already initialized');
    } else {
      console.log('🔧 Initializing RBAC system...');
      await initializeRbacSystem();
      console.log('✅ RBAC system initialized successfully');
    }

    console.log('\n🎉 RBAC system is ready!');
    console.log('\nNext steps:');
    console.log('1. Start your application');
    console.log('2. Log in as an Organization Admin');
    console.log('3. Navigate to "Role & Access Management"');
    console.log('4. Create custom roles and manage users');

  } catch (error) {
    console.error('❌ Error during RBAC initialization:', error);
    logger.error('RBAC initialization failed:', error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the script
main().catch((error) => {
  console.error('Script execution failed:', error);
  process.exit(1);
});
