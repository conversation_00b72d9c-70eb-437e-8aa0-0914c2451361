const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seed() {
  console.log('🌱 Starting basic database seed...');
  
  try {
    // Create a test organization
    const org = await prisma.organization.upsert({
      where: { id: 'test-org-id' },
      update: {},
      create: {
        id: 'test-org-id',
        name: 'Test Organization',
        primaryContactEmail: '<EMAIL>',
        verificationStatus: 'VERIFIED',
        country: 'US',
        website: 'https://test.example.com'
      }
    });
    
    console.log('✅ Created organization:', org.name);
    
    // Create a test user
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test Admin',
        role: 'ADMIN',
        organizationId: org.id
      }
    });
    
    console.log('✅ Created user:', user.name);
    console.log('🎉 Basic seed completed successfully!');
  } catch (error) {
    console.error('❌ Seed failed:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

seed();
