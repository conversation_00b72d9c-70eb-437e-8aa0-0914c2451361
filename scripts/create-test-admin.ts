#!/usr/bin/env tsx

/**
 * Create Test Organization Admin User
 * 
 * This script creates a test Organization Admin user for RBAC testing
 */

import { db } from '../src/lib/db';
import bcrypt from 'bcryptjs';
import { logger } from '../src/lib/logger';

async function createTestAdmin() {
  try {
    console.log('🔧 Creating Test Organization Admin User...');

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Test user already exists!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: password123');
      console.log('👤 Role:', existingUser.role);
      return;
    }

    // Create or find a test organization
    let testOrg = await db.organization.findFirst({
      where: { name: 'Test Organization' }
    });

    if (!testOrg) {
      testOrg = await db.organization.create({
        data: {
          name: 'Test Organization',
          description: 'Test organization for RBAC testing',
          type: 'CORPORATION',
          status: 'ACTIVE',
          country: 'United States',
          industry: 'Technology',
          size: 'SMALL',
          foundedYear: 2024,
          primaryContact: 'Test Admin',
          primaryContactEmail: '<EMAIL>',
          primaryContactPhone: '+1234567890',
          legalName: 'Test Organization Inc.',
          registrationNumber: 'TEST123456',
          taxId: '12-3456789',
          address: '123 Test Street',
          city: 'Test City',
          state: 'CA',
          postalCode: '12345',
        }
      });
      console.log('✅ Created test organization:', testOrg.name);
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('password123', 12);

    // Create the test user
    const testUser = await db.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test Organization Admin',
        password: hashedPassword,
        role: 'ORGANIZATION_ADMIN',
        emailVerified: new Date(),
        jobTitle: 'Test Administrator',
        departmentName: 'Administration',
        phoneNumber: '+1234567890',
        organizationId: testOrg.id,
      }
    });

    console.log('✅ Successfully created test Organization Admin user!');
    console.log('');
    console.log('🎯 LOGIN CREDENTIALS:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: password123');
    console.log('👤 Role: ORGANIZATION_ADMIN');
    console.log('🏢 Organization:', testOrg.name);
    console.log('');
    console.log('🚀 You can now login and test the RBAC system!');
    console.log('   1. Login with the credentials above');
    console.log('   2. Look for "Role & Access Management" in the sidebar');
    console.log('   3. Navigate to /rbac to access the RBAC dashboard');

  } catch (error) {
    console.error('❌ Error creating test admin user:', error);
    logger.error('Failed to create test admin user:', error);
    process.exit(1);
  }
}

// Also create a simple admin user if needed
async function createSimpleAdmin() {
  try {
    // Check if any admin user exists
    const existingAdmin = await db.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (existingAdmin) {
      console.log('ℹ️  Platform admin already exists:', existingAdmin.email);
      return;
    }

    // Create platform organization if it doesn't exist
    let platformOrg = await db.organization.findFirst({
      where: { name: 'Carbon Exchange Platform' }
    });

    if (!platformOrg) {
      platformOrg = await db.organization.create({
        data: {
          name: 'Carbon Exchange Platform',
          description: 'Platform organization for system administration',
          type: 'PLATFORM',
          status: 'ACTIVE',
          country: 'United States',
          industry: 'Technology',
          size: 'LARGE',
          foundedYear: 2024,
          primaryContact: 'Platform Admin',
          primaryContactEmail: '<EMAIL>',
          primaryContactPhone: '+1234567890',
          legalName: 'Carbon Exchange Platform Inc.',
          registrationNumber: 'PLATFORM123',
          taxId: '99-9999999',
          address: '123 Platform Street',
          city: 'Platform City',
          state: 'CA',
          postalCode: '99999',
        }
      });
    }

    const hashedPassword = await bcrypt.hash('admin123', 12);

    const adminUser = await db.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Platform Administrator',
        password: hashedPassword,
        role: 'ADMIN',
        emailVerified: new Date(),
        jobTitle: 'Platform Administrator',
        organizationId: platformOrg.id,
      }
    });

    console.log('✅ Created platform admin user:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');

  } catch (error) {
    console.error('Error creating admin user:', error);
  }
}

async function main() {
  console.log('🚀 Setting up test users for RBAC...');
  console.log('=====================================');

  await createTestAdmin();
  await createSimpleAdmin();

  console.log('');
  console.log('🎉 Setup complete!');
  
  await db.$disconnect();
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// Run the script
main().catch((error) => {
  console.error('Script execution failed:', error);
  process.exit(1);
});
