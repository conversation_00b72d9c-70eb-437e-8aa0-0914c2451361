const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addRecentAuditLogs() {
  try {
    console.log('🔄 Adding recent audit logs for testing...\n');

    // Get the main organization and user
    const org = await prisma.organization.findFirst({
      where: { name: 'GreenTech Solutions' }
    });

    const user = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!org || !user) {
      console.log('❌ Organization or user not found');
      return;
    }

    console.log(`📍 Using organization: ${org.name} (${org.id})`);
    console.log(`👤 Using user: ${user.name} (${user.id})\n`);

    const now = new Date();
    const auditLogs = [];

    // Create various types of recent audit logs
    const logTypes = [
      { type: 'USER_LOGIN', description: 'User logged in successfully', severity: 'INFO', category: 'AUTHENTICATION' },
      { type: 'PROJECT_CREATED', description: 'New project created', severity: 'INFO', category: 'DATA' },
      { type: 'CARBON_CREDIT_LISTED', description: 'Carbon credit listed on marketplace', severity: 'INFO', category: 'DATA' },
      { type: 'WALLET_TRANSACTION', description: 'Wallet transaction completed', severity: 'INFO', category: 'SYSTEM' },
      { type: 'DOCUMENT_UPLOADED', description: 'Project document uploaded', severity: 'INFO', category: 'DOCUMENT' },
      { type: 'VERIFICATION_COMPLETED', description: 'Project verification completed', severity: 'INFO', category: 'SPV' },
      { type: 'API_REQUEST', description: 'API request processed', severity: 'INFO', category: 'SYSTEM' },
      { type: 'PERMISSION_DENIED', description: 'Access denied to restricted resource', severity: 'WARN', category: 'AUTHORIZATION' },
      { type: 'LOGIN_FAILED', description: 'Failed login attempt', severity: 'ERROR', category: 'AUTHENTICATION' },
      { type: 'SYSTEM_ERROR', description: 'System error occurred', severity: 'CRITICAL', category: 'SYSTEM' }
    ];

    // Create logs for the last 24 hours
    for (let i = 0; i < 50; i++) {
      const logType = logTypes[i % logTypes.length];
      const createdAt = new Date(now.getTime() - Math.random() * 24 * 60 * 60 * 1000); // Random time in last 24 hours
      
      auditLogs.push({
        type: logType.type,
        description: logType.description,
        userId: user.id,
        organizationId: org.id,
        severity: logType.severity,
        category: logType.category,
        success: logType.severity !== 'ERROR' && logType.severity !== 'CRITICAL',
        duration: Math.floor(Math.random() * 1000) + 50, // Random duration 50-1050ms
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Test Browser)',
        metadata: {
          testData: true,
          randomValue: Math.random()
        },
        createdAt,
        updatedAt: createdAt
      });
    }

    // Insert all logs
    const result = await prisma.auditLog.createMany({
      data: auditLogs
    });

    console.log(`✅ Created ${result.count} recent audit logs`);

    // Verify the data
    const totalCount = await prisma.auditLog.count({
      where: { organizationId: org.id }
    });

    const recentCount = await prisma.auditLog.count({
      where: {
        organizationId: org.id,
        createdAt: {
          gte: new Date(now.getTime() - 24 * 60 * 60 * 1000)
        }
      }
    });

    console.log(`📊 Total logs for ${org.name}: ${totalCount}`);
    console.log(`🕒 Recent logs (24h): ${recentCount}`);

  } catch (error) {
    console.error('❌ Error adding audit logs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addRecentAuditLogs();
