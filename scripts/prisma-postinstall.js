#!/usr/bin/env node

/**
 * Prisma postinstall script to handle engine binary issues in production
 * This script ensures Prisma engines are properly installed and accessible
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Running Prisma postinstall script...');

try {
  // Set environment variables for Prisma
  process.env.PRISMA_CLI_BINARY_TARGETS = 'debian-openssl-3.0.x';
  process.env.PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING = '1';
  
  // Check if we're in a production environment
  const isProduction = process.env.NODE_ENV === 'production';
  
  if (isProduction) {
    console.log('📦 Production environment detected');
    
    // Generate Prisma client with specific options for production
    console.log('🔄 Generating Prisma client for production...');
    execSync('npx prisma generate --no-engine', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        PRISMA_CLI_BINARY_TARGETS: 'debian-openssl-3.0.x',
        PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING: '1'
      }
    });
    
    console.log('✅ Prisma client generated successfully for production');
  } else {
    console.log('🔄 Development environment - generating Prisma client...');
    execSync('npx prisma generate', { stdio: 'inherit' });
    console.log('✅ Prisma client generated successfully for development');
  }
  
  // Verify Prisma client is accessible
  try {
    const { PrismaClient } = require('@prisma/client');
    console.log('✅ Prisma client is accessible');
  } catch (error) {
    console.warn('⚠️  Warning: Prisma client verification failed:', error.message);
  }
  
} catch (error) {
  console.error('❌ Prisma postinstall failed:', error.message);
  
  // In production, we don't want to fail the build completely
  if (process.env.NODE_ENV === 'production') {
    console.log('🔄 Attempting fallback Prisma generation...');
    try {
      execSync('npx prisma generate --skip-generate', { stdio: 'inherit' });
      console.log('✅ Fallback Prisma generation completed');
    } catch (fallbackError) {
      console.error('❌ Fallback also failed:', fallbackError.message);
      process.exit(1);
    }
  } else {
    process.exit(1);
  }
}

console.log('🎉 Prisma postinstall script completed');
