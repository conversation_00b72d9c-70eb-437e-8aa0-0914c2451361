const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkDataEntryLogs() {
  try {
    console.log('🔍 Checking data entry logs...\n');

    // Get all audit logs
    const allLogs = await prisma.auditLog.findMany({
      select: {
        type: true,
        description: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 50
    });

    console.log(`📊 Total audit logs: ${allLogs.length}\n`);

    // Group by type
    const logsByType = {};
    allLogs.forEach(log => {
      if (!logsByType[log.type]) {
        logsByType[log.type] = 0;
      }
      logsByType[log.type]++;
    });

    console.log('📋 Logs by type:');
    Object.entries(logsByType).forEach(([type, count]) => {
      console.log(`  ${type}: ${count}`);
    });

    // Check for data entry specific logs
    const dataEntryTypes = [
      'DATA_ENTRY_MANUAL',
      'DATA_ENTRY_CSV',
      'DATA_ENTRY_API',
      'DATA_ENTRY_IOT',
      'DATA_EDITED',
      'DATA_DELETED',
      'DATA_APPROVED',
      'DATA_REJECTED',
      'DATA_VERIFIED',
      'DATA_CORRECTION_REQUESTED',
      'DATA_CORRECTION_APPROVED',
      'DATA_CORRECTION_REJECTED',
      'MONITORING_DATA_CREATED',
      'MONITORING_DATA_UPDATED',
      'MONITORING_DATA_DELETED',
      'MONITORING_DATA_VERIFIED',
      'MONITORING_DATA_REJECTED',
      'DATA_SUBMITTED_FOR_VERIFICATION',
      'DATA_PM_VERIFIED',
      'DATA_PM_REJECTED',
      'DATA_SPV_ADMIN_VERIFIED',
      'DATA_SPV_ADMIN_REJECTED',
      'DATA_SUBMITTED_TO_ORG_ADMIN',
      'DATA_ORG_APPROVED',
      'DATA_ORG_REJECTED',
      'DATA_SUBMITTED_TO_VVB',
      'DATA_VVB_VERIFIED',
      'DATA_VVB_REJECTED',
      'DATA_SENT_BACK_FOR_CORRECTION',
      'DATA_EDITED_AND_RESUBMITTED'
    ];

    const dataEntryLogs = allLogs.filter(log => dataEntryTypes.includes(log.type));
    
    console.log(`\n🎯 Data entry logs found: ${dataEntryLogs.length}`);
    
    if (dataEntryLogs.length > 0) {
      console.log('\n📝 Data entry logs:');
      dataEntryLogs.forEach(log => {
        console.log(`  ${log.type} - ${log.description} (${log.createdAt.toISOString()})`);
      });
    } else {
      console.log('\n❌ No data entry logs found!');
    }

    // Test the filter query
    console.log('\n🧪 Testing data entry filter query...');
    
    const filteredLogs = await prisma.auditLog.findMany({
      where: {
        type: {
          in: dataEntryTypes
        }
      },
      select: {
        type: true,
        description: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`✅ Filter query returned: ${filteredLogs.length} logs`);
    
    if (filteredLogs.length > 0) {
      console.log('📋 Filtered results:');
      filteredLogs.forEach(log => {
        console.log(`  ${log.type} - ${log.description}`);
      });
    }

  } catch (error) {
    console.error('❌ Error checking data entry logs:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkDataEntryLogs();
