#!/usr/bin/env tsx

/**
 * Check Existing Users
 * 
 * This script checks what users exist in the database
 */

import { db } from '../src/lib/db';

async function checkUsers() {
  try {
    console.log('🔍 Checking existing users in database...');
    console.log('=========================================');

    // Get all users
    const users = await db.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        organizationId: true,
        organization: {
          select: {
            name: true
          }
        },
        createdAt: true,
      },
      orderBy: {
        role: 'asc'
      }
    });

    if (users.length === 0) {
      console.log('❌ No users found in database!');
      console.log('');
      console.log('💡 To create test users, run:');
      console.log('   npx tsx scripts/create-test-admin.ts');
      return;
    }

    console.log(`✅ Found ${users.length} user(s):`);
    console.log('');

    // Group users by role
    const usersByRole = users.reduce((acc, user) => {
      if (!acc[user.role]) {
        acc[user.role] = [];
      }
      acc[user.role].push(user);
      return acc;
    }, {} as Record<string, typeof users>);

    // Display users by role
    Object.entries(usersByRole).forEach(([role, roleUsers]) => {
      console.log(`👤 ${role} (${roleUsers.length}):`);
      roleUsers.forEach(user => {
        console.log(`   📧 ${user.email}`);
        console.log(`   👤 ${user.name}`);
        if (user.organization) {
          console.log(`   🏢 ${user.organization.name}`);
        }
        console.log(`   📅 Created: ${user.createdAt.toLocaleDateString()}`);
        console.log('');
      });
    });

    // Show Organization Admins specifically
    const orgAdmins = users.filter(u => u.role === 'ORGANIZATION_ADMIN');
    if (orgAdmins.length > 0) {
      console.log('🎯 ORGANIZATION ADMIN ACCOUNTS (for RBAC testing):');
      console.log('================================================');
      orgAdmins.forEach(admin => {
        console.log(`📧 Email: ${admin.email}`);
        console.log(`👤 Name: ${admin.name}`);
        if (admin.organization) {
          console.log(`🏢 Organization: ${admin.organization.name}`);
        }
        console.log('🔑 Password: (check seed scripts or create new user)');
        console.log('');
      });
    } else {
      console.log('⚠️  No ORGANIZATION_ADMIN users found!');
      console.log('');
      console.log('💡 To create an Organization Admin for RBAC testing:');
      console.log('   npx tsx scripts/create-test-admin.ts');
    }

    // Check if any users have known passwords from seed scripts
    const knownTestEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    const knownUsers = users.filter(u => knownTestEmails.includes(u.email));
    if (knownUsers.length > 0) {
      console.log('🔑 KNOWN TEST ACCOUNTS:');
      console.log('======================');
      knownUsers.forEach(user => {
        console.log(`📧 ${user.email}`);
        console.log(`👤 ${user.role}`);
        
        // Show likely passwords based on email
        if (user.email === '<EMAIL>') {
          console.log('🔑 Password: password123');
        } else if (user.email.includes('greentech') || user.email.includes('carbontrading')) {
          console.log('🔑 Password: Password123!');
        } else if (user.email.includes('admin@carbonexchange')) {
          console.log('🔑 Password: Admin123!');
        } else if (user.email === '<EMAIL>') {
          console.log('🔑 Password: password123');
        }
        console.log('');
      });
    }

  } catch (error) {
    console.error('❌ Error checking users:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Run the script
checkUsers().catch((error) => {
  console.error('Script execution failed:', error);
  process.exit(1);
});
