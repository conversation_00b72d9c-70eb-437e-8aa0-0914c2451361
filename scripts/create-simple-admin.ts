#!/usr/bin/env tsx

/**
 * Create Simple Test Organization Admin User
 */

import { db } from '../src/lib/db';
import bcrypt from 'bcryptjs';

async function createSimpleAdmin() {
  try {
    console.log('🔧 Creating Simple Test Organization Admin User...');

    // Check if user already exists
    const existingUser = await db.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (existingUser) {
      console.log('✅ Test user already exists!');
      console.log('📧 Email: <EMAIL>');
      console.log('🔑 Password: password123');
      console.log('👤 Role:', existingUser.role);
      return;
    }

    // Use an existing organization (GreenTech Solutions)
    const existingOrg = await db.organization.findFirst({
      where: { name: 'GreenTech Solutions' }
    });

    if (!existingOrg) {
      console.log('❌ No existing organization found. Creating a simple one...');
      
      // Create minimal organization
      const newOrg = await db.organization.create({
        data: {
          name: 'Test Organization',
          description: 'Test organization for RBAC testing',
          status: 'ACTIVE',
          country: 'United States',
          industry: 'Technology',
          primaryContact: 'Test Admin',
          primaryContactEmail: '<EMAIL>',
        }
      });
      
      console.log('✅ Created test organization:', newOrg.name);
    }

    // Hash the password
    const hashedPassword = await bcrypt.hash('password123', 12);

    // Create the test user
    const testUser = await db.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Test Organization Admin',
        password: hashedPassword,
        role: 'ORGANIZATION_ADMIN',
        emailVerified: new Date(),
        jobTitle: 'Test Administrator',
        organizationId: existingOrg?.id,
      }
    });

    console.log('✅ Successfully created test Organization Admin user!');
    console.log('');
    console.log('🎯 LOGIN CREDENTIALS:');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: password123');
    console.log('👤 Role: ORGANIZATION_ADMIN');
    console.log('');
    console.log('🚀 You can now login and test the RBAC system!');

  } catch (error) {
    console.error('❌ Error creating test admin user:', error);
    process.exit(1);
  } finally {
    await db.$disconnect();
  }
}

// Run the script
createSimpleAdmin().catch((error) => {
  console.error('Script execution failed:', error);
  process.exit(1);
});
