#!/bin/bash

# Fix Prisma engine permission issues in production builds
# This script addresses the EACCES permission denied error when copying libquery-engine

set -e

echo "🔧 Fixing Prisma engine permission issues..."

# Set environment variables
export PRISMA_CLI_BINARY_TARGETS="debian-openssl-3.0.x"
export PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING=1

# Function to check if running in Docker
is_docker() {
    [ -f /.dockerenv ] || grep -q 'docker\|lxc' /proc/1/cgroup 2>/dev/null
}

# Function to fix permissions
fix_permissions() {
    echo "📁 Fixing file permissions..."
    
    # Create necessary directories
    mkdir -p ~/.cache/prisma
    mkdir -p node_modules/.prisma
    mkdir -p node_modules/@prisma
    
    # Set proper permissions
    if is_docker; then
        echo "🐳 Docker environment detected"
        # In Docker, ensure the nextjs user owns the directories
        chown -R nextjs:nodejs ~/.cache/prisma 2>/dev/null || true
        chown -R nextjs:nodejs node_modules/.prisma 2>/dev/null || true
        chown -R nextjs:nodejs node_modules/@prisma 2>/dev/null || true
    else
        echo "🖥️  Host environment detected"
        # On host, ensure current user owns the directories
        chmod -R 755 ~/.cache/prisma 2>/dev/null || true
        chmod -R 755 node_modules/.prisma 2>/dev/null || true
        chmod -R 755 node_modules/@prisma 2>/dev/null || true
    fi
}

# Function to clean Prisma cache
clean_prisma_cache() {
    echo "🧹 Cleaning Prisma cache..."
    rm -rf ~/.cache/prisma/* 2>/dev/null || true
    rm -rf node_modules/.prisma/* 2>/dev/null || true
}

# Function to regenerate Prisma client
regenerate_client() {
    echo "🔄 Regenerating Prisma client..."
    
    # Try different generation strategies
    if command -v pnpm >/dev/null 2>&1; then
        echo "📦 Using pnpm..."
        pnpm prisma generate --no-engine || pnpm prisma generate
    elif command -v npm >/dev/null 2>&1; then
        echo "📦 Using npm..."
        npm run prisma:generate || npx prisma generate
    else
        echo "❌ No package manager found"
        exit 1
    fi
}

# Main execution
main() {
    echo "🚀 Starting Prisma engine fix process..."
    
    # Step 1: Fix permissions
    fix_permissions
    
    # Step 2: Clean cache
    clean_prisma_cache
    
    # Step 3: Regenerate client
    regenerate_client
    
    echo "✅ Prisma engine fix completed successfully!"
    
    # Verify the fix
    echo "🔍 Verifying Prisma client..."
    if node -e "require('@prisma/client')" 2>/dev/null; then
        echo "✅ Prisma client is working correctly"
    else
        echo "⚠️  Warning: Prisma client verification failed"
        exit 1
    fi
}

# Run the main function
main "$@"
