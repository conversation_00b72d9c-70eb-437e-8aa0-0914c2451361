const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAuditData() {
  try {
    console.log('🔍 Checking audit log data...\n');

    // Get total audit logs
    const totalLogs = await prisma.auditLog.count();
    console.log(`📊 Total audit logs: ${totalLogs}`);

    // Get logs by organization
    const logsByOrg = await prisma.auditLog.groupBy({
      by: ['organizationId'],
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } }
    });

    console.log('\n📈 Logs by Organization:');
    for (const org of logsByOrg) {
      const orgName = await prisma.organization.findUnique({
        where: { id: org.organizationId || '' },
        select: { name: true }
      });
      console.log(`  ${orgName?.name || 'Unknown'} (${org.organizationId}): ${org._count.id} logs`);
    }

    // Get recent logs
    const recentLogs = await prisma.auditLog.findMany({
      take: 5,
      orderBy: { createdAt: 'desc' },
      include: {
        user: { select: { name: true, email: true } },
        organization: { select: { name: true } }
      }
    });

    console.log('\n🕒 Recent audit logs:');
    for (const log of recentLogs) {
      console.log(`  ${log.createdAt.toISOString()} - ${log.type} by ${log.user?.name || 'Unknown'} (${log.organization?.name || 'No Org'})`);
    }

    // Check date range for analytics
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    const recentCount = await prisma.auditLog.count({
      where: {
        createdAt: {
          gte: yesterday,
          lte: now
        }
      }
    });

    console.log(`\n📅 Logs in last 24 hours: ${recentCount}`);

    // Get the main organization ID
    const mainOrg = await prisma.organization.findFirst({
      where: { name: 'GreenTech Solutions' },
      select: { id: true, name: true }
    });

    if (mainOrg) {
      const orgLogsRecent = await prisma.auditLog.count({
        where: {
          organizationId: mainOrg.id,
          createdAt: {
            gte: yesterday,
            lte: now
          }
        }
      });
      console.log(`\n🏢 ${mainOrg.name} logs in last 24 hours: ${orgLogsRecent}`);
    }

  } catch (error) {
    console.error('❌ Error checking audit data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAuditData();
