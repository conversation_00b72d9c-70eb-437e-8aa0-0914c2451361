#!/bin/bash

# Docker Build Optimization Script
# This script optimizes Docker builds for faster development and production builds

set -e

echo "🚀 Docker Build Optimization Script"
echo "=================================="

# Function to check Docker buildx support
check_buildx() {
    if docker buildx version >/dev/null 2>&1; then
        echo "✅ Docker Buildx is available"
        return 0
    else
        echo "❌ Docker Buildx not available - using standard docker build"
        return 1
    fi
}

# Function to clean up Docker to free space
cleanup_docker() {
    echo "🧹 Cleaning up Docker to free space..."
    
    # Remove unused containers
    docker container prune -f >/dev/null 2>&1 || true
    
    # Remove unused images
    docker image prune -f >/dev/null 2>&1 || true
    
    # Remove unused volumes
    docker volume prune -f >/dev/null 2>&1 || true
    
    # Remove build cache (keep recent)
    docker builder prune -f --filter until=24h >/dev/null 2>&1 || true
    
    echo "✅ Docker cleanup completed"
}

# Function to build with optimizations
build_optimized() {
    local target=${1:-"app"}
    local use_cache=${2:-"true"}
    
    echo "🔨 Building Docker image: $target"
    echo "📊 Build optimizations enabled:"
    echo "   - Multi-stage caching"
    echo "   - Buildx parallel builds"
    echo "   - Layer caching"
    echo "   - Optimized context"
    
    if check_buildx && [ "$use_cache" = "true" ]; then
        echo "🚀 Using Docker Buildx with advanced caching..."
        
        # Build with buildx and advanced caching
        docker buildx build \
            --target "$target" \
            --cache-from type=local,src=/tmp/.buildx-cache \
            --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max \
            --platform linux/amd64 \
            --load \
            -t "carbonx-$target:latest" \
            .
        
        # Move cache to avoid cache invalidation
        rm -rf /tmp/.buildx-cache
        mv /tmp/.buildx-cache-new /tmp/.buildx-cache 2>/dev/null || true
        
    else
        echo "🔨 Using standard Docker build..."
        
        # Standard build with basic optimizations
        docker build \
            --target "$target" \
            --build-arg BUILDKIT_INLINE_CACHE=1 \
            -t "carbonx-$target:latest" \
            .
    fi
    
    echo "✅ Build completed successfully!"
}

# Function to show build statistics
show_stats() {
    echo ""
    echo "📊 Build Statistics:"
    echo "==================="
    
    # Show image size
    local image_size=$(docker images carbonx-app:latest --format "table {{.Size}}" | tail -n 1)
    echo "📦 Final image size: $image_size"
    
    # Show layer count
    local layer_count=$(docker history carbonx-app:latest --quiet | wc -l)
    echo "📚 Number of layers: $layer_count"
    
    # Show cache usage
    echo "💾 Docker cache usage:"
    docker system df
}

# Function to run performance test
test_performance() {
    echo ""
    echo "⚡ Performance Test:"
    echo "==================="
    
    # Test container startup time
    echo "🕐 Testing container startup time..."
    start_time=$(date +%s.%N)
    
    # Start container in background
    container_id=$(docker run -d -p 3001:3000 carbonx-app:latest)
    
    # Wait for container to be ready (max 30 seconds)
    timeout=30
    counter=0
    while [ $counter -lt $timeout ]; do
        if curl -s http://localhost:3001/api/health >/dev/null 2>&1; then
            break
        fi
        sleep 1
        counter=$((counter + 1))
    done
    
    end_time=$(date +%s.%N)
    startup_time=$(echo "$end_time - $start_time" | bc -l)
    
    # Clean up test container
    docker stop "$container_id" >/dev/null 2>&1
    docker rm "$container_id" >/dev/null 2>&1
    
    echo "⚡ Container startup time: ${startup_time}s"
}

# Main execution
main() {
    local command=${1:-"build"}
    local target=${2:-"app"}
    local skip_cleanup=${3:-"false"}
    
    case $command in
        "clean")
            cleanup_docker
            ;;
        "build")
            if [ "$skip_cleanup" != "true" ]; then
                cleanup_docker
            fi
            build_optimized "$target"
            show_stats
            ;;
        "test")
            test_performance
            ;;
        "full")
            cleanup_docker
            build_optimized "$target"
            show_stats
            test_performance
            ;;
        *)
            echo "Usage: $0 [clean|build|test|full] [target] [skip_cleanup]"
            echo ""
            echo "Commands:"
            echo "  clean     - Clean up Docker cache and unused resources"
            echo "  build     - Build optimized Docker image"
            echo "  test      - Test container performance"
            echo "  full      - Run complete optimization cycle"
            echo ""
            echo "Examples:"
            echo "  $0 build app"
            echo "  $0 full"
            echo "  $0 clean"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
