const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testSPVAuditLogging() {
  try {
    console.log('🔍 Testing SPV Audit Logging...\n');

    // Get the main organization
    const org = await prisma.organization.findFirst({
      where: { name: 'GreenTech Solutions' },
      include: {
        spvs: {
          include: {
            spvUsers: {
              include: {
                user: true
              }
            }
          }
        }
      }
    });

    if (!org) {
      console.log('❌ Organization not found');
      return;
    }

    console.log(`🏢 Organization: ${org.name} (${org.id})`);
    console.log(`📊 SPVs in organization: ${org.spvs.length}\n`);

    // Check SPV users
    for (const spv of org.spvs) {
      console.log(`🏗️ SPV: ${spv.name} (${spv.id})`);
      console.log(`👥 SPV Users: ${spv.spvUsers.length}`);
      
      for (const spvUser of spv.spvUsers) {
        console.log(`  - ${spvUser.user.name} (${spvUser.user.email}) - Role: ${spvUser.role}`);
      }
      console.log('');
    }

    // Check audit logs for the organization
    const orgAuditLogs = await prisma.auditLog.findMany({
      where: { organizationId: org.id },
      include: {
        user: { select: { name: true, email: true } },
        spv: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 20
    });

    console.log(`📋 Total audit logs for organization: ${orgAuditLogs.length}`);
    console.log('\n🕒 Recent audit logs:');
    
    for (const log of orgAuditLogs) {
      const spvInfo = log.spv ? ` [SPV: ${log.spv.name}]` : '';
      console.log(`  ${log.createdAt.toISOString()} - ${log.type} by ${log.user?.name || 'Unknown'}${spvInfo}`);
    }

    // Check SPV-specific logs
    const spvLogs = await prisma.auditLog.findMany({
      where: { 
        organizationId: org.id,
        spvId: { not: null }
      },
      include: {
        user: { select: { name: true, email: true } },
        spv: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`\n🏗️ SPV-specific logs for organization: ${spvLogs.length}`);
    for (const log of spvLogs) {
      console.log(`  ${log.createdAt.toISOString()} - ${log.type} by ${log.user?.name} [SPV: ${log.spv?.name}]`);
    }

    // Check recent logs by SPV users
    const spvUserIds = org.spvs.flatMap(spv => spv.spvUsers.map(su => su.userId));
    
    const spvUserLogs = await prisma.auditLog.findMany({
      where: { 
        userId: { in: spvUserIds },
        organizationId: org.id
      },
      include: {
        user: { select: { name: true, email: true } },
        spv: { select: { name: true } }
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    console.log(`\n👤 Logs by SPV users: ${spvUserLogs.length}`);
    for (const log of spvUserLogs) {
      const spvInfo = log.spv ? ` [SPV: ${log.spv.name}]` : '';
      console.log(`  ${log.createdAt.toISOString()} - ${log.type} by ${log.user?.name}${spvInfo}`);
    }

    // Test: Create a sample audit log for SPV user
    const spvUser = org.spvs[0]?.spvUsers[0];
    if (spvUser) {
      console.log(`\n🧪 Creating test audit log for SPV user: ${spvUser.user.name}`);
      
      const testLog = await prisma.auditLog.create({
        data: {
          type: 'DATA_ENTRY_CREATED',
          description: `Test data entry created by SPV user ${spvUser.user.name}`,
          userId: spvUser.userId,
          organizationId: org.id,
          spvId: spvUser.spvId,
          severity: 'INFO',
          category: 'DATA',
          success: true,
          metadata: {
            testEntry: true,
            spvRole: spvUser.role,
            timestamp: new Date().toISOString()
          },
          ipAddress: '*************',
          userAgent: 'Test Script'
        }
      });

      console.log(`✅ Created test audit log: ${testLog.id}`);
      
      // Verify it appears in organization logs
      const verifyLog = await prisma.auditLog.findFirst({
        where: {
          id: testLog.id,
          organizationId: org.id
        }
      });

      if (verifyLog) {
        console.log(`✅ Test log is visible to organization admin`);
      } else {
        console.log(`❌ Test log is NOT visible to organization admin`);
      }
    }

    // Summary
    console.log('\n📊 Summary:');
    console.log(`- Organization: ${org.name}`);
    console.log(`- Total audit logs: ${orgAuditLogs.length}`);
    console.log(`- SPV-specific logs: ${spvLogs.length}`);
    console.log(`- SPV user logs: ${spvUserLogs.length}`);

  } catch (error) {
    console.error('❌ Error testing SPV audit logging:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testSPVAuditLogging();
