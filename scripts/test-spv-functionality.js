/**
 * Test script to verify SPV creation and listing functionality
 */

const BASE_URL = 'http://localhost:3001';

async function testSPVFunctionality() {
  console.log('🧪 Testing SPV Functionality...\n');

  try {
    // Test 1: Get SPVs (should work with authentication)
    console.log('1. Testing GET /api/organizations/spvs');
    const getResponse = await fetch(`${BASE_URL}/api/organizations/spvs`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (getResponse.ok) {
      const getData = await getResponse.json();
      console.log('✅ GET SPVs successful');
      console.log('   Organization:', getData.organization?.name || 'Not found');
      console.log('   SPVs count:', getData.spvs?.length || 0);
      if (getData.spvs?.length > 0) {
        console.log('   First SPV:', getData.spvs[0].name);
      }
    } else {
      console.log('❌ GET SPVs failed:', getResponse.status, getResponse.statusText);
      const errorData = await getResponse.json().catch(() => ({}));
      console.log('   Error:', errorData.message || errorData.error || 'Unknown error');
    }

    console.log('\n2. Testing POST /api/organizations/spvs (SPV Creation)');
    
    // Test 2: Create a new SPV
    const testSPVData = {
      name: `Test SPV ${Date.now()}`,
      purpose: 'Testing SPV creation functionality for carbon credit projects',
      jurisdiction: 'Delaware, USA',
      legalStructure: 'LLC',
      registrationNumber: 'TEST-' + Math.random().toString(36).substr(2, 9),
      taxId: '12-' + Math.random().toString(36).substr(2, 7),
      address: '123 Test Street, Test City, Test State 12345',
      description: 'This is a test SPV created by the automated test script',
      establishedDate: new Date('2023-01-01').toISOString(),
    };

    const createResponse = await fetch(`${BASE_URL}/api/organizations/spvs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testSPVData),
    });

    if (createResponse.ok) {
      const createData = await createResponse.json();
      console.log('✅ POST SPV creation successful');
      console.log('   Created SPV ID:', createData.spv?.id);
      console.log('   Created SPV Name:', createData.spv?.name);
      console.log('   Message:', createData.message);

      // Test 3: Verify the SPV appears in the list
      console.log('\n3. Testing if new SPV appears in GET request');
      const verifyResponse = await fetch(`${BASE_URL}/api/organizations/spvs`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (verifyResponse.ok) {
        const verifyData = await verifyResponse.json();
        const newSPV = verifyData.spvs?.find(spv => spv.id === createData.spv?.id);
        
        if (newSPV) {
          console.log('✅ New SPV found in list');
          console.log('   SPV Name:', newSPV.name);
          console.log('   SPV Purpose:', newSPV.purpose);
          console.log('   SPV Jurisdiction:', newSPV.jurisdiction);
        } else {
          console.log('❌ New SPV NOT found in list');
          console.log('   Available SPVs:', verifyData.spvs?.map(spv => spv.name) || []);
        }
      } else {
        console.log('❌ Verification GET request failed:', verifyResponse.status);
      }

    } else {
      console.log('❌ POST SPV creation failed:', createResponse.status, createResponse.statusText);
      const errorData = await createResponse.json().catch(() => ({}));
      console.log('   Error:', errorData.message || errorData.error || 'Unknown error');
      
      if (errorData.details) {
        console.log('   Details:', errorData.details);
      }
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }

  console.log('\n🏁 SPV functionality test completed');
}

// Run the test
testSPVFunctionality();
