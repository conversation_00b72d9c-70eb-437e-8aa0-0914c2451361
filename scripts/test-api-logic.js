// Simple test script to verify the API logic works
const { z } = require('zod');

// Schema for unit log creation (same as in API)
const createUnitLogSchema = z.object({
  logDate: z.string().datetime(),
  frequency: z.enum(["REAL_TIME", "HOURLY", "<PERSON>AIL<PERSON>", "WEEKL<PERSON>", "<PERSON>ONTH<PERSON><PERSON>", "QUARTERLY", "YEARLY"]),
  unitType: z.string().min(1),
  quantity: z.number().positive(),
  dataSource: z.enum(["MANUAL", "CSV_UPLOAD", "API_INTEGRATION", "IOT_DEVICE"]).default("MANUAL"),
  notes: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// Test data (same format as sent from frontend)
const testData = {
  logDate: new Date().toISOString(),
  frequency: "MONTHLY",
  unitType: "kWh",
  quantity: 1250.5,
  dataSource: "MANUAL",
  notes: "Test entry from manual form",
};

console.log("Testing API validation logic...");
console.log("Input data:", testData);

try {
  const validatedData = createUnitLogSchema.parse(testData);
  console.log("✅ Validation successful!");
  console.log("Validated data:", validatedData);
  
  // Simulate database save
  const unitLog = {
    id: `test-${Date.now()}`,
    ...validatedData,
    logDate: new Date(validatedData.logDate),
    projectId: "test-project-123",
    loggedBy: "test-user",
    verificationStatus: "PENDING",
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  
  console.log("✅ Mock database save successful!");
  console.log("Saved unit log:", unitLog);
  
} catch (error) {
  console.error("❌ Validation failed:");
  if (error instanceof z.ZodError) {
    console.error("Validation errors:", error.errors);
  } else {
    console.error("Error:", error.message);
  }
}

// Test with invalid data
console.log("\n" + "=".repeat(50));
console.log("Testing with invalid data...");

const invalidData = {
  logDate: "invalid-date",
  frequency: "INVALID_FREQUENCY",
  unitType: "",
  quantity: -100,
  dataSource: "MANUAL",
};

console.log("Invalid input data:", invalidData);

try {
  const validatedData = createUnitLogSchema.parse(invalidData);
  console.log("❌ This should have failed!");
} catch (error) {
  console.log("✅ Validation correctly failed!");
  if (error instanceof z.ZodError) {
    console.log("Validation errors:");
    error.errors.forEach((err, index) => {
      console.log(`  ${index + 1}. ${err.path.join('.')}: ${err.message}`);
    });
  }
}
