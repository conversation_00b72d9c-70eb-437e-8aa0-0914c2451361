const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function getProjectId() {
  try {
    const project = await prisma.project.findFirst({
      select: {
        id: true,
        name: true,
        type: true,
        organizationId: true
      }
    });
    
    if (project) {
      console.log('Found project:');
      console.log(`ID: ${project.id}`);
      console.log(`Name: ${project.name}`);
      console.log(`Type: ${project.type}`);
      console.log(`Organization ID: ${project.organizationId}`);
    } else {
      console.log('No projects found');
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

getProjectId();
