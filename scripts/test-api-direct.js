// Direct test of the project creation API logic
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testProjectCreation() {
  try {
    console.log("Testing project creation logic...");
    
    // Get the organization admin user
    const user = await prisma.user.findFirst({
      where: {
        email: '<EMAIL>'
      },
      include: {
        organization: true
      }
    });
    
    if (!user) {
      console.error("❌ User not found. Make sure deployment seed was run.");
      return;
    }
    
    console.log(`✅ Found user: ${user.email} (${user.id})`);
    console.log(`✅ Organization: ${user.organization.name} (${user.organizationId})`);
    
    // Test project data
    const projectData = {
      name: "Test Solar Project",
      type: "RENEWABLE_ENERGY",
      description: "Test solar project for debugging",
      country: "JP",
      location: "Tokyo",
      coordinates: "35.6762,139.6503",
      standard: "Verra VCS",
      methodology: "VM0025",
      verifier: "DNV",
      startDate: "2025-06-10T00:00:00.000Z",
      endDate: "2025-06-25T00:00:00.000Z",
      estimatedReductions: 5000,
      budget: 2500000,
      roi: -97,
      metadata: {
        projectSubType: "SOLAR_PV",
        templateId: "template-1",
        spvId: null,
        summary: "Test project",
        baselineMethodology: "Grid emission factor",
        emissionReductionTarget: 5000,
        monitoringFrequency: "MONTHLY",
        registrySelection: "Verra",
        additionalityProof: "Financial additionality",
        creditingPeriod: "10 years",
        estimatedPrice: 15,
      },
    };
    
    // Create project directly
    const project = await prisma.project.create({
      data: {
        ...projectData,
        status: "PENDING",
        verificationStatus: "PENDING",
        organization: {
          connect: {
            id: user.organizationId,
          },
        },
      },
    });
    
    console.log(`✅ Project created successfully: ${project.name} (${project.id})`);
    console.log("Project details:", JSON.stringify(project, null, 2));
    
    // Clean up - delete the test project
    await prisma.project.delete({
      where: { id: project.id }
    });
    console.log("✅ Test project cleaned up");
    
  } catch (error) {
    console.error("❌ Error:", error.message);
    if (error.code) {
      console.error("Error code:", error.code);
    }
  } finally {
    await prisma.$disconnect();
  }
}

testProjectCreation();
