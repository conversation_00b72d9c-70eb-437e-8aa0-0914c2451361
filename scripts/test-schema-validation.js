// Test script to validate project data against the backend schema
const { z } = require('zod');

// Copy of the backend schema
const projectSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  type: z.enum([
    "RENEWABLE_ENERGY",
    "FORESTRY",
    "METHANE_REDUCTION",
    "ENERGY_EFFICIENCY",
    "WASTE_MANAGEMENT",
    "AGRICULTURE",
    "TRANSPORTATION",
    "INDUSTRIAL",
    "OTHER"
  ]),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  location: z.string().optional(),
  country: z.string().optional(),
  coordinates: z.string().optional(),
  area: z.number().positive().optional(),
  externalProjectId: z.string().optional(),
  registryId: z.string().optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  methodologyVersion: z.string().optional(),
  estimatedReductions: z.number().positive().optional(),
  actualReductions: z.number().positive().optional(),
  verifier: z.string().optional(),
  validator: z.string().optional(),
  images: z.array(z.string().url()).optional(),
  budget: z.number().positive().optional(),
  roi: z.number().optional(),
  sdgs: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

// Sample data based on the form submission from the screenshot
const testData = {
  name: "Solar PV Energy Generation Project",
  type: "RENEWABLE_ENERGY",
  description: "Solar PV project reducing carbon emissions through clean electricity generation.",
  country: "JP",
  location: "Tokyo",
  coordinates: "35.6762,139.6503",
  standard: "Verra VCS",
  methodology: "VM0025",
  verifier: "DNV",
  startDate: "2025-06-10T00:00:00.000Z",
  endDate: "2025-06-25T00:00:00.000Z",
  estimatedReductions: 5000,
  budget: 2500000,
  roi: -97,
  metadata: {
    projectSubType: "SOLAR_PV",
    templateId: "template-1",
    spvId: null,
    summary: "Test project",
    baselineMethodology: "Grid emission factor",
    emissionReductionTarget: 5000,
    monitoringFrequency: "MONTHLY",
    registrySelection: "Verra",
    additionalityProof: "Financial additionality",
    creditingPeriod: "10 years",
    estimatedPrice: 15,
  },
};

console.log("Testing project data validation...");
console.log("Data to validate:", JSON.stringify(testData, null, 2));

try {
  const result = projectSchema.parse(testData);
  console.log("✅ Validation successful!");
  console.log("Parsed data:", JSON.stringify(result, null, 2));
} catch (error) {
  console.error("❌ Validation failed:");
  if (error.errors) {
    error.errors.forEach(err => {
      console.error(`- ${err.path.join('.')}: ${err.message}`);
    });
  } else {
    console.error(error.message);
  }
}
