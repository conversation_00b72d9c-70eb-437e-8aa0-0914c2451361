// Simple test script to debug project creation API
const testData = {
  name: "Test Solar Project",
  type: "RENEWABLE_ENERGY",
  description: "Test solar project for debugging",
  country: "JP",
  location: "Tokyo",
  coordinates: "35.6762,139.6503",
  standard: "Verra VCS",
  methodology: "VM0025",
  verifier: "DNV",
  startDate: "2025-06-10T00:00:00.000Z",
  endDate: "2025-06-25T00:00:00.000Z",
  estimatedReductions: 5000,
  budget: 2500000,
  roi: -97,
  metadata: {
    projectSubType: "SOLAR_PV",
    templateId: "template-1",
    spvId: null,
    summary: "Test project",
    baselineMethodology: "Grid emission factor",
    emissionReductionTarget: 5000,
    monitoringFrequency: "MONTHLY",
    registrySelection: "Verra",
    additionalityProof: "Financial additionality",
    creditingPeriod: "10 years",
    estimatedPrice: 15,
  },
};

async function testProjectCreation() {
  try {
    console.log("Testing project creation API...");
    console.log("Data to send:", JSON.stringify(testData, null, 2));
    
    const response = await fetch("http://localhost:3001/api/projects", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Note: This won't work without proper authentication
      },
      body: JSON.stringify(testData),
    });

    const responseData = await response.json();
    
    console.log("Response status:", response.status);
    console.log("Response data:", JSON.stringify(responseData, null, 2));
    
    if (!response.ok) {
      console.error("API Error:", responseData.error);
    } else {
      console.log("Success!");
    }
  } catch (error) {
    console.error("Network error:", error.message);
  }
}

testProjectCreation();
