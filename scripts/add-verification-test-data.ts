import { PrismaClient, DataVerificationStatus } from '@prisma/client';

const prisma = new PrismaClient();

async function addVerificationTestData() {
  console.log('Adding test data for verification queue...');

  try {
    // Get SPV projects and users
    const spvProjects = await prisma.project.findMany({
      where: {
        name: {
          in: ['Solar Farm Alpha', 'Wind Farm Beta', 'Solar Farm Gamma']
        }
      },
      include: {
        organization: true
      }
    });

    const spvUsers = await prisma.user.findMany({
      where: {
        email: {
          in: [
            '<EMAIL>',
            '<EMAIL>', 
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
          ]
        }
      }
    });

    if (spvProjects.length === 0 || spvUsers.length === 0) {
      console.log('No SPV projects or users found. Please run the comprehensive seed first.');
      return;
    }

    console.log(`Found ${spvProjects.length} SPV projects and ${spvUsers.length} SPV users`);

    // Get specific users
    const siteWorker = spvUsers.find(u => u.email === '<EMAIL>');
    const projectManager = spvUsers.find(u => u.email === '<EMAIL>');
    const spvAdmin = spvUsers.find(u => u.email === '<EMAIL>');

    if (!siteWorker || !projectManager || !spvAdmin) {
      console.log('Required SPV users not found');
      return;
    }

    // Create unit logs with different verification statuses
    const testData = [
      {
        projectId: spvProjects[0].id, // Solar Farm Alpha
        loggedBy: siteWorker.id,
        verificationStatus: DataVerificationStatus.ORG_REJECTED,
        quantity: 601,
        notes: 'Data entry that was rejected by org admin',
        logDate: new Date(),
      },
      {
        projectId: spvProjects[0].id, // Solar Farm Alpha  
        loggedBy: spvAdmin.id,
        verificationStatus: DataVerificationStatus.ORG_APPROVED,
        quantity: 700,
        notes: 'Data entry approved by org admin',
        logDate: new Date(),
      },
      {
        projectId: spvProjects[2].id, // Solar Farm Gamma
        loggedBy: spvAdmin.id,
        verificationStatus: DataVerificationStatus.SUBMITTED_TO_ORG_ADMIN,
        quantity: 900,
        notes: 'Data entry submitted to org admin for review',
        logDate: new Date(),
      },
      {
        projectId: spvProjects[1].id, // Wind Farm Beta
        loggedBy: siteWorker.id,
        verificationStatus: DataVerificationStatus.DRAFT,
        quantity: 450,
        notes: 'Draft data entry by site worker',
        logDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
      },
      {
        projectId: spvProjects[1].id, // Wind Farm Beta
        loggedBy: projectManager.id,
        verificationStatus: DataVerificationStatus.PM_VERIFIED,
        quantity: 520,
        notes: 'Data entry verified by project manager',
        logDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      },
      {
        projectId: spvProjects[0].id, // Solar Farm Alpha
        loggedBy: siteWorker.id,
        verificationStatus: DataVerificationStatus.REJECTED,
        quantity: 380,
        notes: 'Data entry rejected by PM',
        logDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      }
    ];

    console.log('Creating test unit logs...');

    for (const data of testData) {
      const unitLog = await prisma.unitLog.create({
        data: {
          projectId: data.projectId,
          loggedBy: data.loggedBy,
          logDate: data.logDate,
          frequency: 'MONTHLY',
          unitType: 'MW',
          quantity: data.quantity,
          dataSource: 'MANUAL',
          verificationStatus: data.verificationStatus,
          notes: data.notes,
          metadata: {
            testData: true,
            createdForVerificationTesting: true
          }
        },
        include: {
          project: {
            select: { name: true }
          },
          logger: {
            select: { name: true, email: true }
          }
        }
      });

      console.log(`Created unit log: ${unitLog.quantity} MW for ${unitLog.project.name} by ${unitLog.logger.name} (${data.verificationStatus})`);
    }

    console.log('\n✅ Test data for verification queue created successfully!');
    console.log('\nYou can now test the verification queue functionality with:');
    console.log('- ORG_REJECTED entries (should show SEND_BACK_FOR_CORRECTION and EDIT_AND_RESUBMIT actions)');
    console.log('- SUBMITTED_TO_ORG_ADMIN entries (should show appropriate actions)');
    console.log('- Other statuses for testing different verification flows');

  } catch (error) {
    console.error('Error adding verification test data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
addVerificationTestData();
