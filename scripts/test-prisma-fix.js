#!/usr/bin/env node

/**
 * Test script to verify Prisma configuration fixes
 * This script tests the Prisma engine permission fix
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 Testing Prisma configuration fixes...');

// Set environment variables for testing
process.env.PRISMA_CLI_BINARY_TARGETS = 'debian-openssl-3.0.x';
process.env.PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING = '1';

try {
  // Test 1: Check if Prisma schema is valid
  console.log('📋 Test 1: Validating Prisma schema...');
  execSync('npx prisma validate', { stdio: 'inherit' });
  console.log('✅ Prisma schema is valid');

  // Test 2: Generate Prisma client
  console.log('🔄 Test 2: Generating Prisma client...');
  execSync('npx prisma generate', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      PRISMA_CLI_BINARY_TARGETS: 'debian-openssl-3.0.x',
      PRISMA_ENGINES_CHECKSUM_IGNORE_MISSING: '1'
    }
  });
  console.log('✅ Prisma client generated successfully');

  // Test 3: Verify Prisma client can be imported
  console.log('📦 Test 3: Testing Prisma client import...');
  try {
    const { PrismaClient } = require('@prisma/client');
    console.log('✅ Prisma client can be imported successfully');
    
    // Test 4: Create Prisma client instance (without connecting)
    console.log('🔗 Test 4: Creating Prisma client instance...');
    const prisma = new PrismaClient();
    console.log('✅ Prisma client instance created successfully');
    
  } catch (importError) {
    console.error('❌ Prisma client import failed:', importError.message);
    throw importError;
  }

  console.log('🎉 All Prisma tests passed! The configuration should work in production.');

} catch (error) {
  console.error('❌ Prisma test failed:', error.message);
  console.log('\n🔧 Suggested fixes:');
  console.log('1. Run: npm install @prisma/client prisma');
  console.log('2. Run: npx prisma generate');
  console.log('3. Check if DATABASE_URL is set correctly');
  console.log('4. Verify Prisma schema syntax');
  
  process.exit(1);
}

console.log('\n📊 Test Summary:');
console.log('- Prisma schema validation: ✅');
console.log('- Prisma client generation: ✅');
console.log('- Prisma client import: ✅');
console.log('- Prisma client instantiation: ✅');
console.log('\n🚀 Ready for production deployment!');
