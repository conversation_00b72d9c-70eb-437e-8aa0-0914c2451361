---
type: "manual"
---

Create new docs always in docs folder .
create new scripts, and other files by maintaining the directory structure dont add them ar root 
always remove test script or redundant files having same functionality
always start implementation from backend to frontend and while creating api , check schema for consistent field name 
during developing fix all typescript errors , syntax errors , import errors and all other type of error make it error free 
always follow industry standard for development 
