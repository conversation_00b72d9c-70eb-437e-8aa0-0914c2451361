# RBAC Implementation Guide

## Overview

This document provides a comprehensive guide to the Role-Based Access Control (RBAC) system implementation. The RBAC system provides fine-grained access control across the entire application, allowing Organization Admins to manage users, roles, and permissions.

## Architecture

### Core Components

1. **Permissions**: Atomic access rights (e.g., `create:role`, `read:user`)
2. **Roles**: Collections of permissions (e.g., `ORGANIZATION_ADMIN`, `CARBON_SPECIALIST`)
3. **Users**: Individuals with assigned roles and permissions
4. **Organizations**: Isolation boundaries for multi-tenant access control

### Database Schema

```
Users
├── System Role (ADMIN, ORGANIZATION_ADMIN, ORGANIZATION_USER)
├── Organization Association
└── Custom Role Assignments

CustomRoles
├── Organization-specific or System-wide
├── Permission Assignments
└── Role Hierarchy (Parent/Child relationships)

Permissions
├── Categorized by module (USER_MANAGEMENT, CARBON_CREDIT, etc.)
├── Granular access rights
└── Usage tracking

Audit Trail
├── Permission Usage Logs
├── Role Assignment Changes
└── User Activity Tracking
```

## Features Implemented

### 1. User Management
- ✅ Create, edit, and remove users
- ✅ Bulk user operations
- ✅ User search and filtering
- ✅ Role assignment interface
- ✅ User activity tracking

### 2. Role Management
- ✅ Create custom roles with permissions
- ✅ Edit existing roles
- ✅ Role templates for quick setup
- ✅ Role hierarchy support
- ✅ Permission visualization

### 3. Permission System
- ✅ 60+ predefined permissions across 10 categories
- ✅ Permission grouping and categorization
- ✅ Usage statistics and analytics
- ✅ Permission sync from code to database

### 4. Audit & Compliance
- ✅ Comprehensive audit logging
- ✅ Permission usage tracking
- ✅ Role assignment history
- ✅ User activity monitoring

### 5. Security Features
- ✅ Organization-level isolation
- ✅ Session-based permission checking
- ✅ Input validation and sanitization
- ✅ Rate limiting on sensitive operations

## API Endpoints

### Users
- `GET /api/rbac/users` - List organization users
- `POST /api/rbac/users` - Create new user
- `GET /api/rbac/users/[id]` - Get user details
- `PATCH /api/rbac/users/[id]` - Update user
- `DELETE /api/rbac/users/[id]` - Remove user from organization

### Roles
- `GET /api/rbac/roles` - List organization roles
- `POST /api/rbac/roles` - Create new role
- `GET /api/rbac/roles/[id]` - Get role details
- `PATCH /api/rbac/roles/[id]` - Update role
- `DELETE /api/rbac/roles/[id]` - Delete role

### Role Assignment
- `GET /api/rbac/users/[id]/roles` - Get user's roles
- `POST /api/rbac/users/[id]/roles` - Assign roles to user
- `DELETE /api/rbac/users/[id]/roles` - Revoke roles from user

### Permissions
- `GET /api/rbac/permissions` - List all permissions
- `POST /api/rbac/permissions` - Sync permissions (Admin only)

### Audit
- `GET /api/rbac/audit` - Get audit logs

### System
- `GET /api/rbac/init` - Check initialization status
- `POST /api/rbac/init` - Initialize RBAC system (Admin only)
- `POST /api/rbac/test` - Run system tests (Admin only)

## Frontend Components

### Pages
- `/rbac` - RBAC Dashboard
- `/rbac/users` - User Management
- `/rbac/roles` - Role Management
- `/rbac/permissions` - Permission Browser
- `/rbac/audit` - Audit Log Viewer

### Reusable Components
- `UsersManagementClient` - Complete user management interface
- `RolesManagementClient` - Role creation and editing
- `PermissionsManagementClient` - Permission browser
- `AuditLogClient` - Audit trail viewer
- `CreateUserDialog` - User creation form
- `EditUserDialog` - User editing form
- `AssignRolesDialog` - Role assignment interface
- `CreateRoleDialog` - Role creation wizard
- `EditRoleDialog` - Role editing interface

## Permission Categories

1. **ORGANIZATION** - Organization management
2. **USER_MANAGEMENT** - User operations
3. **CARBON_CREDIT** - Carbon credit operations
4. **WALLET** - Financial operations
5. **TEAM** - Team management
6. **ADMIN** - Platform administration
7. **COMPLIANCE** - Compliance operations
8. **ENTERPRISE** - Enterprise features
9. **REPORTING** - Analytics and reporting
10. **SETTINGS** - Configuration management

## Default Roles

### System Roles
- **ADMIN** - Platform administrator (full access)
- **ORGANIZATION_ADMIN** - Organization administrator
- **ORGANIZATION_USER** - Standard organization user

### Predefined Custom Roles
- **Department Manager** - Departmental oversight
- **Project Manager** - Project-level management
- **Carbon Credit Specialist** - Carbon operations
- **Financial Analyst** - Financial operations
- **Compliance Officer** - Regulatory compliance
- **Team Member** - Standard operational access
- **Viewer** - Read-only access

## Setup Instructions

### 1. Initialize the System

```bash
# Run database migrations (if not already done)
npx prisma migrate dev

# Initialize RBAC system (via API or directly)
curl -X POST /api/rbac/init
```

### 2. Access the RBAC Interface

1. Log in as an Organization Admin
2. Navigate to "Role & Access Management" in the sidebar
3. Start by creating custom roles or using predefined templates
4. Add users and assign appropriate roles

### 3. Test the Implementation

```bash
# Run RBAC tests (Admin only)
curl -X POST /api/rbac/test -d '{"cleanup": true}'
```

## Usage Examples

### Creating a Custom Role

```typescript
const roleData = {
  name: "CARBON_ANALYST",
  displayName: "Carbon Analyst",
  description: "Analyzes carbon credit data and generates reports",
  permissions: [
    "read:carbon_credit",
    "create:carbon_credit",
    "read:analytics",
    "export:reports"
  ]
};

const response = await fetch('/api/rbac/roles', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(roleData)
});
```

### Checking Permissions in Code

```typescript
import { hasPermission } from '@/lib/rbac/rbac-service';

const context = {
  userId: user.id,
  organizationId: user.organizationId,
};

const canCreateRole = await hasPermission('create:role', context);
if (canCreateRole) {
  // User can create roles
}
```

### Using Permission Gates in Components

```tsx
import { PermissionGate } from '@/components/rbac/permission-gate';

<PermissionGate permission="create:user">
  <Button>Add User</Button>
</PermissionGate>
```

## Security Considerations

1. **Organization Isolation**: Users can only access data within their organization
2. **Permission Validation**: All API endpoints validate permissions before execution
3. **Audit Logging**: All permission checks and role changes are logged
4. **Input Sanitization**: All user inputs are validated and sanitized
5. **Session Security**: Permission checks are tied to authenticated sessions

## Performance Optimizations

1. **Permission Caching**: Frequently checked permissions are cached
2. **Batch Operations**: Support for bulk user and role operations
3. **Efficient Queries**: Optimized database queries with proper indexing
4. **Lazy Loading**: UI components load data on demand

## Monitoring & Maintenance

### Health Checks
- Permission system operational status
- Role assignment integrity
- Audit log functionality

### Regular Tasks
- Review and clean up unused roles
- Monitor permission usage patterns
- Audit user access patterns
- Update permissions for new features

## Troubleshooting

### Common Issues

1. **Permission Denied Errors**
   - Check user's role assignments
   - Verify organization association
   - Review permission definitions

2. **Role Assignment Failures**
   - Ensure role exists in organization
   - Check for role conflicts
   - Verify user exists and is active

3. **Audit Log Gaps**
   - Check database connectivity
   - Verify logging middleware is active
   - Review error logs for failures

### Debug Tools

```typescript
// Check user's effective permissions
const userPermissions = await getUserEffectivePermissions(userId, organizationId);

// Audit permission check
const result = await hasPermission('permission:name', context, { debug: true });

// View role hierarchy
const roleHierarchy = await getRoleHierarchy(roleId);
```

## Future Enhancements

1. **Advanced Features**
   - Time-based role assignments
   - Conditional permissions
   - Resource-level permissions
   - API key management

2. **Integration**
   - SSO/SAML integration
   - External identity providers
   - Webhook notifications

3. **Analytics**
   - Permission usage analytics
   - Role effectiveness metrics
   - Security compliance reports

## Support

For issues or questions regarding the RBAC system:
1. Check the audit logs for permission-related errors
2. Review the API documentation for endpoint usage
3. Run the test suite to validate system integrity
4. Consult the troubleshooting guide for common issues

---

*This RBAC system provides enterprise-grade access control with comprehensive audit trails and flexible role management capabilities.*
