# 🔐 CarbonX Platform - Complete Login Credentials

This document contains all the working login credentials for testing different views and roles in the CarbonX platform after running the comprehensive seed script.

## 📋 **Available Views & Credentials**

### 🔧 **System Admin Dashboard**
**URL:** `/admin` or `/login`
```
Email: <EMAIL>
Password: Admin123!
Role: ADMIN
```
**Access:** Full system administration, user management, platform settings, SPV user management

---

### 🏢 **Organization Admin Dashboard**
**URL:** `/dashboard`

#### **GreenTech Solutions Admin:**
```
Email: <EMAIL>
Password: Password123!
Role: ORGANIZATION_ADMIN
```

#### **Carbon Trading Inc Admin:**
```
Email: <EMAIL>
Password: Password123!
Role: ORGANIZATION_ADMIN
```
**Access:** Organization management, SPV user creation, project assignments, team management

---

### 👤 **Organization User Dashboard**
**URL:** `/dashboard`

#### **GreenTech Solutions Users:**
```
1. Email: <EMAIL>
   Password: Password123!

2. Email: <EMAIL>
   Password: Password123!

3. Email: <EMAIL>
   Password: Password123!
```

#### **Carbon Trading Inc Users:**
```
1. Email: <EMAIL>
   Password: Password123!

2. Email: <EMAIL>
   Password: Password123!
```
**Access:** Organization projects, carbon credits, marketplace, limited admin functions

---

### 🤝 **Broker Dashboard**
**URL:** `/broker` or `/broker/dashboard`

#### **Broker 1:**
```
Email: <EMAIL>
Password: Broker123!
Role: BROKER
```

#### **Broker 2:**
```
Email: <EMAIL>
Password: Broker123!
Role: BROKER
```
**Access:** Broker-specific functionality, client management, transaction history, commission tracking

---

### 🏗️ **SPV Portal - Solar Energy SPV**
**URL:** `/spv` or `/spv/dashboard`

#### **SPV Admin (Solar):**
```
Email: <EMAIL>
Password: SPVUser123!
Role: SPV_USER (SPV_ADMIN)
```

#### **Project Manager (Solar):**
```
Email: <EMAIL>
Password: SPVUser123!
Role: SPV_USER (PROJECT_MANAGER)
```

#### **Site Worker (Solar):**
```
Email: <EMAIL>
Password: SPVUser123!
Role: SPV_USER (SITE_WORKER)
```

---

### 🌪️ **SPV Portal - Wind Power SPV**
**URL:** `/spv` or `/spv/dashboard`

#### **SPV Admin (Wind):**
```
Email: <EMAIL>
Password: SPVUser123!
Role: SPV_USER (SPV_ADMIN)
```

#### **Project Manager (Wind):**
```
Email: <EMAIL>
Password: SPVUser123!
Role: SPV_USER (PROJECT_MANAGER)
```

#### **Site Worker (Wind):**
```
Email: <EMAIL>
Password: SPVUser123!
Role: SPV_USER (SITE_WORKER)
```

**Access:** SPV approval workflow, data verification, project management, analytics

---

## 🏢 **Test Organizations & Projects**

### **Organizations Created:**
1. **GreenTech Solutions** - Renewable Energy
2. **Carbon Trading Inc** - Carbon Trading
3. **EcoSustain Partners** - Sustainability
4. **Forest Conservation Alliance** - Conservation
5. **CleanEnergy Ventures** - Clean Energy
6. **Sustainable Agriculture Cooperative** - Agriculture
7. **Ocean Conservation Institute** - Marine Conservation
8. **Carbon Exchange Platform** - Platform Services

### **SPVs Created:**
1. **Solar Energy SPV** - Solar power project development (Delaware, USA)
2. **Wind Power SPV** - Wind energy project development (Texas, USA)

### **Sample Projects:**
1. **Solar Farm Alpha** - Arizona Desert, USA
2. **Wind Farm Beta** - Texas Coast, USA
3. **Solar Farm Gamma** - California Central Valley, USA
4. **Plus 15 additional projects** across various organizations

---

## 🔄 **RBAC Verification Workflow**

The SPV portal implements a comprehensive 5-stage verification workflow:

1. **Site Worker** → Data Entry (`DRAFT` → `SUBMITTED_FOR_VERIFICATION`)
2. **Project Manager** → Verification (`SUBMITTED_FOR_VERIFICATION` → `VERIFIED`/`REJECTED`)
3. **SPV Admin** → Review (`VERIFIED` → `SPV_APPROVED`/`SPV_REJECTED`)
4. **Organization Admin** → Final Approval (`SPV_APPROVED` → `ORG_APPROVED`/`ORG_REJECTED`)
5. **VVB Submission** → External verification (`ORG_APPROVED` → `SUBMITTED_TO_VVB`)

---

## 🧪 **Testing Scenarios**

### **Admin Testing:**
1. Login as System Admin (`<EMAIL>`) → Manage all users and organizations
2. Login as Organization Admin → Create SPV users and assign projects
3. Check SPV user management at `/admin/spv-users`
4. Check project assignments at `/admin/project-assignments`

### **SPV Portal Testing (Complete RBAC Workflow):**
1. **Data Entry:** Login as Site Worker → Enter data and submit for verification
2. **Verification:** Login as Project Manager → Verify submitted data
3. **SPV Approval:** Login as SPV Admin → Approve verified data
4. **Final Approval:** Login as Organization Admin → Final approval and VVB submission
5. **Test both Solar and Wind SPVs** with different users

### **Broker Testing:**
1. Login as Broker → Access broker dashboard and client management
2. Test broker-specific functionality and transaction history
3. Test both broker accounts for different scenarios

### **Organization Testing:**
1. Login as Organization User → Access projects and carbon credits
2. Login as Organization Admin → Manage organization settings and users
3. Test multiple organizations (GreenTech Solutions, Carbon Trading Inc, etc.)

---

## 🚀 **Quick Start Guide**

1. **Start the application:**
   ```bash
   npm run dev
   ```

2. **Access different portals:**
   - Admin: `http://localhost:3000/admin`
   - Organization: `http://localhost:3000/dashboard`
   - Broker: `http://localhost:3000/broker`
   - SPV: `http://localhost:3000/spv`

3. **Test the complete workflow:**
   - Use Site Worker to enter data
   - Use Project Manager to verify
   - Use SPV Admin to approve
   - Use Organization Admin for final approval

---

## 📝 **Database Contents**

The comprehensive seed has created:
- **8 Organizations** with different industries and purposes
- **16 Users** across all roles and organizations
- **18 Projects** including 3 SPV-specific projects
- **16 Carbon Credits** with tokenization records
- **39 Wallets** (organizational, personal, and project wallets)
- **66 Transactions** across different wallet types
- **10 Marketplace Listings** for carbon credit trading
- **2 SPVs** (Solar Energy SPV and Wind Power SPV)
- **6 SPV Users** with full project assignments
- **18 Project Assignments** for comprehensive testing
- **Complete RBAC verification workflow** ready for testing

## 🔐 **Password Patterns**

- **Admin:** `Admin123!`
- **Organization Users:** `Password123!`
- **Brokers:** `Broker123!`
- **SPV Users:** `SPVUser123!`

---

## 🔒 **Security Note**

These are dummy credentials for development and testing only. In production:
- Use strong, unique passwords
- Implement proper password policies
- Enable two-factor authentication
- Use environment-specific configurations
- Regularly rotate credentials
