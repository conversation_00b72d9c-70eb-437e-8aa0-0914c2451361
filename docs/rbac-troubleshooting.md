# RBAC Troubleshooting Guide

## Issue: "Role & Access Management" not visible in sidebar

### Quick Diagnosis

1. **Check your user role**:
   - Navigate to `/rbac/debug` to see your current role
   - RBAC navigation is only visible to users with `ORGANIZATION_ADMIN` role

2. **Verify session data**:
   - Open browser developer tools (F12)
   - Check console for debug logs: "Dashboard Layout - User Role: ..."
   - Ensure the role is exactly `ORGANIZATION_ADMIN` (case-sensitive)

### Common Solutions

#### Solution 1: Update User Role in Database
```sql
-- Check current user role
SELECT id, email, role FROM User WHERE email = '<EMAIL>';

-- Update user role to Organization Admin
UPDATE User SET role = 'ORGANI<PERSON>ATION_ADMIN' WHERE email = '<EMAIL>';
```

#### Solution 2: Clear Session and Re-login
1. Log out completely
2. Clear browser cookies for your domain
3. Log back in
4. Check if role is now visible

#### Solution 3: Verify Database Schema
```sql
-- Check if role enum includes ORGANIZATION_ADMIN
SHOW COLUMNS FROM User LIKE 'role';

-- Check available roles
SELECT DISTINCT role FROM User;
```

### Step-by-Step Verification

1. **Access Debug Page**:
   ```
   Navigate to: http://localhost:3000/rbac/debug
   ```

2. **Check Session Status**:
   - Status should be "authenticated"
   - User Role should be "ORGANIZATION_ADMIN"
   - RBAC Access should show "Organization Admin"

3. **Initialize RBAC System** (if you're an ADMIN):
   ```
   Click "Initialize RBAC" button on debug page
   OR
   Run: npx tsx scripts/init-rbac.ts
   ```

4. **Test Navigation**:
   - Go back to `/dashboard`
   - Look for "Role & Access Management" in sidebar
   - Should appear after "Settings"

### Manual Role Assignment

If you need to manually assign the ORGANIZATION_ADMIN role:

```typescript
// Using Prisma in a script or API endpoint
import { db } from '@/lib/db';

async function makeUserOrgAdmin(email: string) {
  const user = await db.user.update({
    where: { email },
    data: { role: 'ORGANIZATION_ADMIN' },
  });
  console.log('Updated user:', user);
}

// Usage
makeUserOrgAdmin('<EMAIL>');
```

### Environment-Specific Issues

#### Development Environment
- Ensure your `.env.local` has correct database connection
- Run `npx prisma db push` to sync schema changes
- Restart your development server

#### Production Environment
- Check if database migrations have been applied
- Verify environment variables are set correctly
- Ensure user roles are properly seeded

### Advanced Debugging

#### Check Database Directly
```sql
-- Verify user exists and has correct role
SELECT 
  id, 
  email, 
  name, 
  role, 
  organizationId,
  createdAt 
FROM User 
WHERE email = '<EMAIL>';

-- Check if RBAC tables exist
SHOW TABLES LIKE '%Permission%';
SHOW TABLES LIKE '%Role%';

-- Verify permissions are loaded
SELECT COUNT(*) as permission_count FROM Permission;
SELECT COUNT(*) as role_count FROM CustomRole;
```

#### Check API Endpoints
```bash
# Test RBAC status (requires authentication)
curl -H "Cookie: your-session-cookie" http://localhost:3000/api/rbac/init

# Test user endpoint (requires ORGANIZATION_ADMIN)
curl -H "Cookie: your-session-cookie" http://localhost:3000/api/rbac/users
```

#### Browser Console Debugging
```javascript
// Check session data
console.log('Session:', await fetch('/api/auth/session').then(r => r.json()));

// Check if RBAC APIs are accessible
console.log('RBAC Status:', await fetch('/api/rbac/init').then(r => r.json()));
```

### Still Not Working?

If the RBAC navigation is still not visible after trying the above solutions:

1. **Check the exact file being used**:
   - Verify that `/src/app/dashboard/layout.tsx` contains the RBAC navigation code
   - Look for the line: `{ name: "Role & Access Management", href: "/rbac", icon: UserCog }`

2. **Verify the condition**:
   - The navigation should appear when `session?.user?.role === "ORGANIZATION_ADMIN"`
   - Check browser console for the debug log showing your role

3. **Test with a fresh user**:
   - Create a new user account
   - Manually set their role to `ORGANIZATION_ADMIN` in the database
   - Log in with that account

4. **Check for JavaScript errors**:
   - Open browser developer tools
   - Look for any JavaScript errors in the console
   - Ensure all imports are working correctly

### Contact Support

If you're still experiencing issues, please provide:

1. **Debug page output**: Screenshot of `/rbac/debug` page
2. **Browser console logs**: Any errors or debug messages
3. **Database role check**: Result of `SELECT role FROM User WHERE email = 'your-email'`
4. **Environment details**: Development vs Production, database type, etc.

### Quick Test Commands

```bash
# Initialize RBAC system
npx tsx scripts/init-rbac.ts

# Run RBAC tests
npx tsx scripts/init-rbac.ts --test

# Check database directly (if using SQLite)
sqlite3 prisma/dev.db "SELECT email, role FROM User;"

# Check database directly (if using PostgreSQL)
psql $DATABASE_URL -c "SELECT email, role FROM \"User\";"
```

---

**Remember**: The RBAC navigation is only visible to users with the `ORGANIZATION_ADMIN` role. Make sure your user account has this role assigned in the database.
