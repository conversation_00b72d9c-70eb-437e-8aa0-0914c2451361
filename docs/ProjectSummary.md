# CarbonX Project Summary

## 🌍 **Project Overview**
**CarbonX** (also referred to as Carbonix) is a comprehensive **B2B enterprise SaaS platform** for carbon credit trading. It enables enterprises to self-onboard, manage carbon credit projects, and trade carbon credits with other enterprises through a secure, compliant, and user-friendly marketplace.

## 🎯 **Core Purpose**
The platform supports the complete lifecycle of carbon credits:
- **Verification** → **Tokenization** → **Trading** → **Retirement**
- Focus on security, compliance, and enterprise-grade user experience
- Blockchain-based tokenization with multi-chain support

## 🏗️ **Technical Architecture**

### **Frontend Stack**
- **Next.js 15** with App Router
- **React 19** with TypeScript
- **Tailwind CSS 4** for styling
- **shadcn/ui** component library
- **Framer Motion** for animations
- **TanStack Query/Table** for data management

### **Backend Stack**
- **Node.js** with Next.js API routes
- **PostgreSQL 16** database
- **Prisma ORM 6** for database management
- **NextAuth.js 5** for authentication with 2FA support

### **Blockchain Integration**
- **Alchemy SDK** for wallet and transaction management
- **Account Abstraction** for smart accounts
- **ERC-1155** standard for carbon credit tokens
- **Multi-chain support**: Ethereum, Polygon, Arbitrum, Optimism, Base
- **Gas optimization** and estimation features

### **Infrastructure**
- **Docker & Docker Compose** for containerization
- **Nginx** for reverse proxy
- **Prometheus & Grafana** for monitoring
- **Jest** for testing

## 🚀 **Key Features**

### **Enterprise Onboarding**
- Self-service registration with KYC/AML verification
- Organization management with team structures
- Role-based access control (RBAC)

### **Carbon Credit Management**
- Project creation and management
- Carbon credit listing and verification
- Batch operations for efficiency
- Comprehensive tracking and analytics

### **Trading Platform**
- Marketplace for buying/selling carbon credits
- Automated order matching engine
- Real-time market data and analytics
- Watchlist and preferences management

### **Blockchain Operations**
- Tokenization of carbon credits
- Cross-chain transfers
- Retirement tracking
- Transaction history with detailed analytics
- Gas estimation and optimization

### **Compliance & Security**
- KYC/AML verification workflows
- Document management system
- Audit logging and reporting
- Regulatory compliance dashboard
- Tax reporting capabilities

### **Analytics & Reporting**
- Platform-wide analytics
- Organization-specific insights
- Financial reporting
- Market analysis
- Transaction audit trails

## 📁 **Project Structure**

```
carbon-exchange/
├── docs/                    # Documentation
│   ├── developers/          # Developer documentation
│   ├── users/               # User documentation
│   └── consolidation-summary.md # Summary of module consolidation
├── prisma/                  # Database schema and migrations
├── public/                  # Static assets
├── scripts/                 # Utility scripts
├── src/                     # Source code
│   ├── app/                 # Next.js App Router
│   │   ├── (auth)/          # Authentication routes
│   │   ├── admin/           # Admin dashboard
│   │   ├── api/             # API routes
│   │   │   ├── auth/        # Authentication endpoints
│   │   │   ├── carbon-credits/ # Carbon credit management
│   │   │   ├── marketplace/ # Marketplace operations
│   │   │   ├── organizations/ # Organization management
│   │   │   ├── wallet/      # Wallet operations
│   │   │   └── ...          # Other API endpoints
│   │   ├── dashboard/       # User dashboard
│   │   ├── marketplace/     # Carbon credit marketplace
│   │   ├── settings/        # User and organization settings
│   │   └── wallet/          # Wallet management
│   ├── components/          # Reusable components
│   │   ├── carbon-credits/  # Carbon credit components
│   │   ├── marketplace/     # Marketplace components
│   │   ├── providers/       # Context providers
│   │   ├── ui/              # UI components
│   │   ├── wallet/          # Wallet components
│   │   └── forms/           # Form components
│   ├── hooks/               # Custom React hooks
│   ├── lib/                 # Utility functions and services
│   │   ├── analytics/       # Analytics modules
│   │   ├── audit/           # Audit logging
│   │   ├── auth/            # Authentication utilities
│   │   ├── blockchain/      # Blockchain integration
│   │   ├── carbon-credits/  # Carbon credit management
│   │   ├── compliance/      # Compliance and regulatory features
│   │   ├── marketplace/     # Marketplace functionality
│   │   ├── notifications/   # Notification system
│   │   ├── orders/          # Order management
│   │   ├── payments/        # Payment processing
│   │   ├── rbac/            # Role-based access control
│   │   └── storage/         # File storage system
│   ├── middleware/          # Next.js middleware
│   └── templates/           # Email and document templates
├── monitoring/              # Grafana & Prometheus configs
├── nginx/                   # Nginx configuration
├── .env                     # Environment variables
├── docker-compose.yml       # Docker Compose configuration
├── Dockerfile               # Docker configuration
└── package.json             # Project dependencies and scripts
```

## 🔧 **Recent Improvements**
The project recently underwent a **module consolidation** process:
- Restructured the `lib` directory into domain-specific modules
- Added backward compatibility layer for smooth transitions
- Improved code organization and maintainability
- Enhanced testing framework with Jest

## 🌐 **Deployment Options**
- **Development**: Docker Compose with hot reloading
- **Production**: Multi-stage Docker builds with SSL support
- **Environment-specific**: Separate configs for dev/preprod/prod
- **Monitoring**: Integrated Prometheus/Grafana stack

## 📊 **Database Model**
The system uses a comprehensive PostgreSQL schema with models for:
- Users, Organizations, Teams
- Carbon Credits, Projects, Tokenizations
- Orders, Marketplace Listings
- Wallets, Transactions, Retirements
- Compliance, Audit Logs, Notifications
- RBAC permissions and roles

## 🎯 **Target Users**
- **Enterprises** looking to trade carbon credits
- **Project developers** creating carbon offset projects
- **Compliance officers** managing regulatory requirements
- **Platform administrators** overseeing operations

## 📋 **API Endpoints**
Key API routes include:
- `/api/auth/*` - Authentication endpoints
- `/api/organizations` - Organization management
- `/api/carbon-credits` - Carbon credit management
- `/api/orders` - Order management and matching
- `/api/wallet` - Wallet operations
- `/api/marketplace` - Marketplace functionality
- `/api/compliance` - Compliance and verification
- `/api/analytics` - Analytics and reporting

## 🚀 **Getting Started**
1. Clone the repository
2. Set up environment variables (`.env`)
3. Start with Docker: `pnpm docker:dev:build`
4. Access at `http://localhost:3000`

## 🔗 **Key Technologies**
- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, PostgreSQL, Prisma
- **Blockchain**: Alchemy SDK, Ethers.js, ERC-1155
- **Infrastructure**: Docker, Nginx, Prometheus, Grafana

---

This is a sophisticated, enterprise-grade platform that combines traditional web technologies with cutting-edge blockchain integration to create a comprehensive carbon trading ecosystem.
