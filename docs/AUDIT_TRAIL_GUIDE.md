# 🔍 Audit Trail System Guide

## Overview
The Audit Trail system provides comprehensive logging, monitoring, and analysis of all activities within the CarbonX platform.

## 📊 Analytics Dashboard

### What Analytics Shows:

#### **Key Metrics Cards:**
1. **Total Events** - Total number of audit logs in the selected time period
2. **Error Rate** - Percentage of failed operations (shows system health)
3. **Security Events** - Count of authentication/authorization related events
4. **Average Response Time** - Performance metrics for system operations

#### **Visual Charts:**
1. **Events by Type** - Bar chart showing distribution of different audit log types
   - USER_LOGIN, PROJECT_CREATED, CARBON_CREDIT_LISTED, etc.
2. **Events by Severity** - Pie chart showing severity distribution
   - INFO (blue), WARN (yellow), ERROR (orange), CRITICAL (red)
3. **Events by Category** - Donut chart showing category breakdown
   - AUTHENTICATION, AUTHORIZATION, DATA, SYSTEM, SPV, DOCUMENT
4. **Top Active Users** - List of users with the most activities
5. **Timeline Trends** - Activity patterns over time

#### **Time Range Options:**
- Last Hour
- Last 6 Hours  
- Last 24 Hours
- Last 7 Days
- Last 30 Days
- All Time

### **No Data Display:**
When no data is available for the selected time range, the system shows:
- A helpful message suggesting to try "All Time" range
- Fallback charts with placeholder data
- Guidance on when data might be available

---

## 🔍 Advanced Search

### **Search Capabilities:**

#### **1. Full-Text Search**
- Search across audit log descriptions
- Search user names and email addresses
- Search within JSON metadata fields
- Case-insensitive by default

#### **2. Filter Options:**

**Date & Time Filters:**
- Custom date range picker
- Predefined ranges (Today, Yesterday, Last Week, etc.)
- Time-specific filtering (hour, minute precision)

**Severity Filters:**
- INFO - Informational events
- WARN - Warning events that need attention
- ERROR - Error events that failed
- CRITICAL - Critical system failures

**Category Filters:**
- AUTHENTICATION - Login/logout events
- AUTHORIZATION - Permission and access control
- DATA - Data creation, modification, deletion
- SYSTEM - System operations and maintenance
- SPV - Special Purpose Vehicle operations
- DOCUMENT - Document upload, verification, management

**User Filters:**
- Filter by specific users
- Filter by user roles
- Filter by organization membership

**Technical Filters:**
- Success/Failure status
- IP Address ranges
- Session IDs
- Request IDs
- Response time ranges

#### **3. Advanced Search Features:**

**Regex Search:**
- Pattern matching in descriptions
- Complex text pattern searches
- Field-specific regex queries

**Metadata Search:**
- Search within JSON metadata
- Key-value pair filtering
- Nested object searches

**Saved Searches:**
- Save frequently used search criteria
- Quick access to common searches
- Share searches with team members

**Export Options:**
- Export search results to CSV
- Export to JSON format
- PDF reports with charts

### **Search Interface:**

#### **Basic Search Bar:**
- Quick text search across all fields
- Auto-suggestions based on recent searches
- Real-time search as you type

#### **Advanced Filters Panel:**
- Collapsible filter sections
- Multiple filter combinations
- Clear all filters option
- Apply/Reset buttons

#### **Results Display:**
- Sortable columns
- Expandable rows for detailed view
- Pagination controls
- Results count display

#### **Search Tips:**
1. Use quotes for exact phrase matching: `"user login"`
2. Use wildcards for partial matches: `project_*`
3. Combine filters for precise results
4. Save complex searches for reuse
5. Export results for offline analysis

---

## 🚀 Usage Examples

### **Common Search Scenarios:**

1. **Security Audit:**
   - Category: AUTHENTICATION + AUTHORIZATION
   - Severity: ERROR + CRITICAL
   - Time: Last 24 hours

2. **User Activity Review:**
   - User: Specific user email
   - Category: DATA
   - Time: Custom date range

3. **System Performance:**
   - Search: "timeout" OR "slow"
   - Response time: > 1000ms
   - Time: Last week

4. **Failed Operations:**
   - Success: False
   - Severity: ERROR + CRITICAL
   - Export: CSV for analysis

### **Analytics Use Cases:**

1. **Security Monitoring:**
   - Track failed login attempts
   - Monitor permission denials
   - Identify suspicious IP addresses

2. **Performance Analysis:**
   - Monitor response times
   - Identify slow operations
   - Track error rates over time

3. **User Behavior:**
   - Most active users
   - Peak usage times
   - Feature adoption rates

4. **Compliance Reporting:**
   - Generate audit reports
   - Track data access
   - Document security events

---

## 🔧 Technical Details

### **Data Retention:**
- Audit logs are retained for 2 years
- Older logs are archived automatically
- Critical security events are retained indefinitely

### **Real-time Updates:**
- Optional real-time refresh (disabled by default)
- Manual refresh button available
- Auto-refresh intervals: 30s, 1m, 5m

### **Performance:**
- Indexed searches for fast results
- Pagination for large result sets
- Optimized queries for analytics

### **Security:**
- Role-based access to audit logs
- Encrypted storage of sensitive data
- Audit trail access is itself audited
