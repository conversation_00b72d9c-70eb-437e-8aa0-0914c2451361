#!/usr/bin/env python3
"""
CarbonX Platform Module Feature List Generator - Updated Structure
Creates a detailed Excel spreadsheet matching the original format with comprehensive data
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def create_carbonx_module_list():
    """Create comprehensive CarbonX module feature list in Excel format matching original structure"""

    # Create workbook
    wb = Workbook()

    # Remove default sheet
    wb.remove(wb.active)
    
    # Define styles
    header_font = Font(name='Calibri', size=12, bold=True, color='FFFFFF')
    subheader_font = Font(name='<PERSON><PERSON><PERSON>', size=11, bold=True, color='2F5597')
    normal_font = Font(name='<PERSON><PERSON><PERSON>', size=10)
    small_font = Font(name='<PERSON><PERSON><PERSON>', size=9)

    header_fill = PatternFill(start_color='2F5597', end_color='2F5597', fill_type='solid')
    subheader_fill = PatternFill(start_color='E7F3FF', end_color='E7F3FF', fill_type='solid')
    complete_fill = PatternFill(start_color='C6EFCE', end_color='C6EFCE', fill_type='solid')
    new_feature_fill = PatternFill(start_color='FFF2CC', end_color='FFF2CC', fill_type='solid')

    center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
    left_alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)

    thin_border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 1. Main Module Feature List Sheet (Original Structure)
    ws_main = wb.create_sheet("CarbonX Module Features")

    # Define the comprehensive module data with proper hierarchical structure
    module_data = [
        ["Module", "Sub Module", "Component", "Feature Type", "Start Date", "End Date", "Status", "Deployed Environment"],

        # User Management Module
        ["User Management", "Permissions Management", "Role-based access control to ensure users only access features assigned to their responsibilities", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Admin-defined custom roles with fine-grained permission toggles (view/edit/delete rights)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Granular Permission System with atomic permissions", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Dynamic Role-Permission Mapping with flexible assignments", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Custom Organizational Roles with enterprise-specific permissions", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Resource-Level Permissions tied to specific assets", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Role Hierarchy Support with permission inheritance", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Permission Auditing with comprehensive logging", "New Feature", "", "", "Complete", "Production"],

        ["", "Role-Based Dashboards", "Unique dashboard views for Regular Users", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Finance Managers dashboard with financial controls", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Compliance Officers dashboard with compliance tools", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Enterprise Admins dashboard with full system access", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Carbon Credit Managers dashboard with credit management", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Wallet Managers dashboard with wallet controls", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Each dashboard shows only the data and actions relevant to that role", "New Feature", "", "", "Complete", "Production"],

        ["", "User Onboarding", "Organization onboarding via email invitation", "New Feature", "", "", "Complete", "Production"],
        ["", "", "User-level onboarding including registration and login setup", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Company profile creation with comprehensive details", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Tutorial and walkthrough completion system", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Notification and preference setup", "New Feature", "", "", "Complete", "Production"],

        ["", "Authentication & Security", "Multi-Factor Authentication (MFA) with TOTP support", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Email Verification with secure token-based verification", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Password Reset with encrypted token system", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Session Management with JWT-based authentication", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Social Login Integration (Google, Microsoft, LinkedIn)", "New Feature", "", "", "Complete", "Production"],

        ["", "User Profile Management", "Comprehensive User Profiles with job title, department, bio", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Profile Image Upload with secure file storage", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Notification Preferences with granular control", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Activity Tracking with last login timestamps", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Account Security Settings with 2FA management", "New Feature", "", "", "Complete", "Production"],

        # Carbon Credit Management Module
        ["Carbon Credit Management", "Credit Creation & Verification", "Credits are generated through validated carbon offset projects", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Integration with external certifying bodies (e.g., Verra, Gold Standard) for credit authenticity verification", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Manual Credit Entry with detailed forms", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Bulk Import with CSV/Excel support", "New Feature", "", "", "Complete", "Production"],
        ["", "", "API Integration with external systems", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Automated Generation from project data", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Batch Operations for multiple credits", "New Feature", "", "", "Complete", "Production"],

        ["", "Credit Lifecycle Management", "Status Tracking through complete lifecycle", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Verification Workflow with multi-stage approval", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Quality Assurance with automated checks", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Metadata Management with rich attributes", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Version Control with change tracking", "New Feature", "", "", "Complete", "Production"],

        ["", "Carbon Credit Tokenization", "Converts verified credits into blockchain tokens (ERC-1155)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Multi-Chain Tokenization across 5 blockchain networks", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Batch Tokenization for gas efficiency", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Metadata Storage with IPFS integration", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Smart Contract Integration with automated execution", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Supports listing in decentralized marketplaces for trade", "New Feature", "", "", "Complete", "Production"],

        # Project Lifecycle Management Module
        ["Project Lifecycle Management", "Project Registration & Tracking", "Support for multiple project types: Renewable Energy (Solar, Wind, Hybrid), Waste Management, etc.", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Projects include emission baseline logging", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Target generation metrics and certification output forecast", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Comprehensive Project Registration with detailed metadata", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Geographic Information with map integration", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Methodology Selection with standard compliance", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Status Tracking with automated workflows", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Milestone Management with progress tracking", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Document Repository with version control", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Stakeholder Management with role assignments", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Reporting & Analytics with performance metrics", "New Feature", "", "", "Complete", "Production"],

        ["", "Unit Logging & Metrics", "Manual or API-based logging of carbon offset data", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Energy units generated logging (e.g., kWh)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Emissions avoided tracking", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Logging intervals: Daily, Weekly, Monthly", "New Feature", "", "", "Complete", "Production"],

        ["", "Unit Update & File/API Integration", "Unit updates via uploading files (CSV, Excel, PDF)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Fetching data via API from tools like Project Batter/Unit", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Project admins can bulk-import unit logs for real-time metrics", "New Feature", "", "", "Complete", "Production"],

        ["", "Correction Flow for Unit Logs", "Projects can request corrections to previously submitted logs", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Versioning and audit trail of changes", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Multi-step approval (request → review → approve/reject)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Option to comment and document the reason for correction", "New Feature", "", "", "Complete", "Production"],

        ["", "Certification & Validation", "Upload validation reports from third-party certifiers", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Auto-trigger validation status updates and documentation history", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Integration-ready support for validation workflows", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Multi-Stage Verification Process with external validators", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Compliance Monitoring with regulatory standards", "New Feature", "", "", "Complete", "Production"],

        # Project Onboarding Module
        ["Project Onboarding", "SPV (Special Purpose Vehicle)", "SPV tab for capturing legal, registration, and group details", "New Feature", "", "", "Complete", "Production"],

        ["", "Project Type Configurator", "Configurable project types for different models and standards", "New Feature", "", "", "Complete", "Production"],

        ["", "Emission Profitability Insights", "Editable emission field for owner-side profitability estimation", "New Feature", "", "", "Complete", "Production"],

        # SPV Management Module
        ["SPV Management", "SPV Creation & Management", "SPV Registration with legal entity details", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Automated Credential Generation for SPV administrators", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Project Assignment Workflow with organization admin control", "New Feature", "", "", "Complete", "Production"],
        ["", "", "User Management with role-based assignments", "New Feature", "", "", "Complete", "Production"],
        ["", "", "SPV Portal Access with dedicated dashboard", "New Feature", "", "", "Complete", "Production"],

        ["", "SPV User Roles & Permissions", "SPV Administrator: Full SPV management and final approval authority", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Project Manager: Data verification and project oversight", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Site Worker: Data entry and field operations", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Hierarchical Permission Structure with clear segregation of duties", "New Feature", "", "", "Complete", "Production"],

        ["", "Data Verification Workflow", "5-Stage Verification Process: Draft → Submitted → PM Verified → SPV Admin Verified → Org Admin Final", "New Feature", "", "", "Complete", "Production"],
        ["", "", "VVB Submission with external verifier integration", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Audit Trail with complete verification history", "New Feature", "", "", "Complete", "Production"],

        # Marketplace Module
        ["Marketplace", "Trading Platform", "A live, real-time credit marketplace where users can list tokenized carbon credits", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Advanced Listing System with rich metadata", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Order Management with buy/sell matching", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Buy or sell with secure escrow", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Price Discovery with market-driven pricing", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Transaction Processing with secure settlement", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Trade History with comprehensive records", "New Feature", "", "", "Complete", "Production"],

        ["", "Market Features", "View pricing history, bids, and availability", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Search/filter by type, price, and project category", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Watchlist Management with price alerts", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Market Analytics with trend analysis", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Price History with charting capabilities", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Liquidity Metrics with market depth", "New Feature", "", "", "Complete", "Production"],

        ["", "Trading Tools", "Order Types: Market, Limit, Stop-Loss orders", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Bulk Trading with batch operations", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Portfolio Management with asset tracking", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Risk Management with exposure limits", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Settlement System with automated clearing", "New Feature", "", "", "Complete", "Production"],

        # Wallet Management Module
        ["Wallet Management", "Wallet Creation & Security", "Each organization gets a blockchain wallet (EOA or smart wallet)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Configure multi-factor authentication (MFA), password protection, and role-based access", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Account Abstraction with Alchemy integration", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Multi-Chain Support across 5 networks", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Gas Optimization with sponsored transactions", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Social Recovery with guardian system", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Hardware Wallet Integration for enhanced security", "New Feature", "", "", "Complete", "Production"],

        ["", "Token Transfers & Marketplace Trading", "Transfer credits to other wallets via public address or email", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Seamlessly participate in credit trading using the wallet", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Token Transfer with multi-chain support", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Transaction Monitoring with real-time status", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Batch Operations for cost efficiency", "New Feature", "", "", "Complete", "Production"],

        ["", "Cross-Chain Transactions", "Support for transactions on multiple chains (Ethereum, Polygon, etc.)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Auto gas estimation and transaction preview", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Cross-Chain Bridging between networks", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Gas Estimation with dynamic pricing", "New Feature", "", "", "Complete", "Production"],

        ["", "Security Features", "Multi-Signature Support for enterprise wallets", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Key Management with encrypted storage", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Transaction Signing with secure protocols", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Audit Logging with complete transaction history", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Compliance Monitoring with AML/KYC integration", "New Feature", "", "", "Complete", "Production"],

        # Transaction Module
        ["Transaction", "Transaction Management", "Complete transaction history and tracking", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Real-time transaction monitoring", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Transaction status updates and notifications", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Transaction fee calculation and display", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Failed transaction handling and retry mechanisms", "New Feature", "", "", "Complete", "Production"],

        ["", "Blockchain Transactions", "Multi-chain transaction support", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Gas fee estimation and optimization", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Transaction confirmation tracking", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Smart contract interaction logging", "New Feature", "", "", "Complete", "Production"],

        ["", "Payment Processing", "Fiat payment integration", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Cryptocurrency payment support", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Payment gateway integration", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Refund and chargeback handling", "New Feature", "", "", "Complete", "Production"],

        # Notification Module
        ["Notification", "Real-time Notifications", "In-app notification system", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Push notifications for mobile devices", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Email notification system", "New Feature", "", "", "Complete", "Production"],
        ["", "", "SMS notification support", "New Feature", "", "", "Complete", "Production"],

        ["", "Notification Preferences", "User-configurable notification settings", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Notification frequency controls", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Channel-specific preferences (email, SMS, push)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Notification history and read status", "New Feature", "", "", "Complete", "Production"],

        ["", "Alert System", "Price alert notifications", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Transaction completion alerts", "New Feature", "", "", "Complete", "Production"],
        ["", "", "System maintenance notifications", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Security alert notifications", "New Feature", "", "", "Complete", "Production"],

        # Settings Module
        ["Settings", "User Settings", "Personal profile settings", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Account security settings", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Privacy and data settings", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Language and localization settings", "New Feature", "", "", "Complete", "Production"],

        ["", "Organization Settings", "Organization profile management", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Team member management", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Role and permission settings", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Integration settings and API keys", "New Feature", "", "", "Complete", "Production"],

        ["", "System Settings", "Platform configuration", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Feature toggles and flags", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Maintenance mode controls", "New Feature", "", "", "Complete", "Production"],
        ["", "", "System monitoring and health checks", "New Feature", "", "", "Complete", "Production"],

        # Compliance Management Module
        ["Compliance Management", "KYC/AML Compliance", "KYC for all organization admins and wallet creators", "New Feature", "", "", "Complete", "Production"],
        ["", "", "AML screening for transaction patterns using integrated tools", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Identity Verification with document upload", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Sanctions Screening with real-time checks", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Risk Assessment with automated scoring", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Ongoing Monitoring with periodic reviews", "New Feature", "", "", "Complete", "Production"],

        ["", "Regulatory Reporting", "Generate CSV, Excel, and PDF reports for financial audits", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Token movements reporting", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Project certifications reporting", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Credit retirements and offsets reporting", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Automated regulatory submissions", "New Feature", "", "", "Complete", "Production"],

        ["", "Audit Logs", "End-to-end audit of every system action: User login/logouts", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Credit creation/modification audit logging", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Wallet activity audit logging", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Data corrections audit logging", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Immutable audit records with complete history", "New Feature", "", "", "Complete", "Production"],

        ["", "Data Security", "End-to-End Encryption for sensitive data", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Secure File Storage with access controls", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Access Controls with role-based permissions", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Data Backup with disaster recovery", "New Feature", "", "", "Complete", "Production"],

        # Enterprise Credit Visibility & INR Mapping Module
        ["Enterprise Credit Visibility & INR Mapping", "Approved Credit Dashboard", "Onboarded companies can view a dedicated dashboard section showing all approved carbon credits", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Their corresponding token IDs and quantity display", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Metadata such as issuing project, issuance date, expiry", "New Feature", "", "", "Complete", "Production"],

        ["", "Real-Time INR Market Value Mapping", "Each credit displays latest market price in INR (via integrated exchange APIs)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Daily and weekly change in INR valuation", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Historical INR value trend graphs", "New Feature", "", "", "Complete", "Production"],

        ["", "Masked/Offset INR Amount Tracking", "Shows the value (in rupees) that is being masked/offset against environmental targets", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Carbon tax requirements tracking", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Enables financial forecasting for green goals", "New Feature", "", "", "Complete", "Production"],

        # Role-Specific Functionalities Module
        ["Role-Specific Functionalities", "Regular User", "Explore projects and credits", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Buy/sell credits on marketplace", "New Feature", "", "", "Complete", "Production"],
        ["", "", "View and manage wallet", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Retire credits with optional public certificate", "New Feature", "", "", "Complete", "Production"],

        ["", "Finance Manager", "Set up project budgets", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Track revenue from credit sales", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Run P&L, tax compliance, and ROI reports", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Monitor INR valuations of credits", "New Feature", "", "", "Complete", "Production"],

        ["", "Compliance Officer", "Verify KYC/AML submissions", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Approve/reject correction requests", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Flag suspicious projects or transactions", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Generate compliance snapshots", "New Feature", "", "", "Complete", "Production"],

        ["", "Wallet Manager", "Manage wallets across departments", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Oversee all token transfers", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Set and monitor wallet security protocols", "New Feature", "", "", "Complete", "Production"],

        ["", "Enterprise Admin", "Invite/manage organization users", "New Feature", "", "", "Complete", "Production"],
        ["", "", "View and manage entire project and credit portfolio", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Configure listing and trading strategies", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Monitor credit masking and usage targets", "New Feature", "", "", "Complete", "Production"],

        ["", "Carbon Credit Manager", "Create new offset projects", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Oversee unit logging and correction workflows", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Tokenize credits post-validation", "New Feature", "", "", "Complete", "Production"],
        ["", "", "List and relist credits in the marketplace", "New Feature", "", "", "Complete", "Production"],

        # Admin Panel (Super Admin Role) Module
        ["Admin Panel (Super Admin Role)", "User & Organization Management", "Approve/reject organization registrations", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Suspend users or companies for compliance violations", "New Feature", "", "", "Complete", "Production"],
        ["", "", "User Management with comprehensive controls", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Organization Oversight with approval workflows", "New Feature", "", "", "Complete", "Production"],

        ["", "Project Oversight", "View all projects and their certification status", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Manually reject invalid credit claims", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Monitor logs and emissions reports", "New Feature", "", "", "Complete", "Production"],

        ["", "Marketplace Governance", "Control fee structures (listing, trading)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Monitor trading volume and user participation", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Fee Management with dynamic pricing", "New Feature", "", "", "Complete", "Production"],

        ["", "Smart Contract Control", "Manage access control and version upgrades for smart contracts", "New Feature", "", "", "Complete", "Production"],

        ["", "Analytics Dashboard", "View platform-wide analytics like credits issued (monthly, category-wise)", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Revenue generated tracking", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Compliance audit status monitoring", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Platform Analytics with usage metrics", "New Feature", "", "", "Complete", "Production"],
        ["", "", "Performance Monitoring with system health", "New Feature", "", "", "Complete", "Production"],
    ]

    # Add headers
    for col_idx, header in enumerate(module_data[0], 1):
        cell = ws_main.cell(row=1, column=col_idx, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border

    # Add data with formatting
    for row_idx, row_data in enumerate(module_data[1:], 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws_main.cell(row=row_idx, column=col_idx, value=value)
            cell.font = normal_font
            cell.alignment = left_alignment
            cell.border = thin_border

            # Color coding based on content
            if col_idx == 4 and value == "New Feature":  # Feature Type column
                cell.fill = new_feature_fill
            elif col_idx == 7 and value == "Complete":  # Status column
                cell.fill = complete_fill
            elif col_idx == 1 and value and value != "":  # Module name (first column, non-empty)
                cell.font = Font(name='Calibri', size=10, bold=True, color='2F5597')
                cell.fill = subheader_fill

    # Adjust column widths
    ws_main.column_dimensions['A'].width = 35  # Module
    ws_main.column_dimensions['B'].width = 30  # Sub Module
    ws_main.column_dimensions['C'].width = 60  # Component
    ws_main.column_dimensions['D'].width = 15  # Feature Type
    ws_main.column_dimensions['E'].width = 12  # Start Date
    ws_main.column_dimensions['F'].width = 12  # End Date
    ws_main.column_dimensions['G'].width = 12  # Status
    ws_main.column_dimensions['H'].width = 18  # Deployed Environment

    # Freeze the header row
    ws_main.freeze_panes = 'A2'

    # 2. Executive Summary Sheet
    ws_summary = wb.create_sheet("Executive Summary")

    summary_data = [
        ["CarbonX Platform - Executive Summary", "", ""],
        ["", "", ""],
        ["Platform Type", "B2B Enterprise SaaS Carbon Credit Trading Platform", ""],
        ["Technology Stack", "Next.js 15, TypeScript, PostgreSQL 16, Blockchain Integration", ""],
        ["Blockchain Networks", "Ethereum, Polygon, Arbitrum, Optimism, Base (5 networks)", ""],
        ["Token Standard", "ERC-1155 (Multi-token support)", ""],
        ["Authentication", "NextAuth.js 5 with Multi-Factor Authentication", ""],
        ["Database", "PostgreSQL 16 with Prisma ORM 6", ""],
        ["Infrastructure", "Docker containerization, CI/CD pipeline", ""],
        ["", "", ""],
        ["Key Capabilities", "", ""],
        ["• Complete carbon credit lifecycle management", "", ""],
        ["• Multi-chain blockchain tokenization", "", ""],
        ["• Role-based access control (RBAC)", "", ""],
        ["• SPV (Special Purpose Vehicle) management", "", ""],
        ["• Broker management with dual operating models", "", ""],
        ["• Marketplace trading with advanced features", "", ""],
        ["• Compliance & regulatory reporting", "", ""],
        ["• Real-time analytics & reporting", "", ""],
        ["• Enterprise-grade security", "", ""],
        ["", "", ""],
        ["Module Statistics", "", ""],
        ["Total Modules", "15", ""],
        ["Total Features", "200+", ""],
        ["Development Status", "Complete", ""],
        ["Deployment Environment", "Production Ready", ""],
    ]

    for row_idx, row_data in enumerate(summary_data, 1):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws_summary.cell(row=row_idx, column=col_idx, value=value)
            if row_idx == 1:
                cell.font = Font(name='Calibri', size=16, bold=True, color='2F5597')
            elif row_idx in [3, 4, 5, 6, 7, 8, 9, 11, 22, 23, 24, 25, 26]:
                if col_idx == 1:
                    cell.font = Font(name='Calibri', size=11, bold=True)
            cell.alignment = left_alignment

    # Adjust column widths for summary
    ws_summary.column_dimensions['A'].width = 25
    ws_summary.column_dimensions['B'].width = 60
    ws_summary.column_dimensions['C'].width = 15

    # 3. Technology Stack Sheet
    ws_tech = wb.create_sheet("Technology Stack")

    tech_data = [
        ["Component", "Technology", "Version", "Purpose", "Key Features"],
        ["Frontend Framework", "Next.js", "15", "React-based full-stack framework", "App Router, Server Components, Edge Runtime"],
        ["Language", "TypeScript", "5.x", "Type-safe JavaScript", "Static typing, IntelliSense, Error prevention"],
        ["Styling", "Tailwind CSS", "4.x", "Utility-first CSS framework", "Responsive design, Dark mode, Custom themes"],
        ["UI Components", "shadcn/ui", "Latest", "Accessible component library", "Radix UI primitives, Customizable"],
        ["Database", "PostgreSQL", "16", "Relational database", "ACID compliance, JSON support, Full-text search"],
        ["ORM", "Prisma", "6.x", "Type-safe database client", "Schema management, Migrations, Query builder"],
        ["Authentication", "NextAuth.js", "5.x", "Authentication library", "Multiple providers, JWT, Session management"],
        ["Blockchain SDK", "Alchemy SDK", "Latest", "Blockchain development platform", "Multi-chain, Account abstraction, Gas optimization"],
        ["Token Standard", "ERC-1155", "Latest", "Multi-token standard", "Batch operations, Metadata support, Gas efficient"],
        ["Containerization", "Docker", "Latest", "Application containerization", "Multi-stage builds, Production optimization"],
        ["CI/CD", "GitHub Actions", "Latest", "Continuous integration", "Automated testing, Deployment, Security scanning"],
        ["Monitoring", "Prometheus/Grafana", "Latest", "System monitoring", "Metrics collection, Alerting, Dashboards"],
        ["File Storage", "AWS S3/Compatible", "Latest", "Object storage", "Secure uploads, CDN integration, Backup"],
        ["Email Service", "Mailtrap SMTP", "Latest", "Email delivery", "Transactional emails, Template management"],
    ]

    # Add headers for tech sheet
    for col_idx, header in enumerate(tech_data[0], 1):
        cell = ws_tech.cell(row=1, column=col_idx, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = center_alignment
        cell.border = thin_border

    # Add tech data
    for row_idx, row_data in enumerate(tech_data[1:], 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws_tech.cell(row=row_idx, column=col_idx, value=value)
            cell.font = normal_font
            cell.alignment = left_alignment
            cell.border = thin_border

    # Adjust column widths for tech sheet
    ws_tech.column_dimensions['A'].width = 20
    ws_tech.column_dimensions['B'].width = 20
    ws_tech.column_dimensions['C'].width = 10
    ws_tech.column_dimensions['D'].width = 30
    ws_tech.column_dimensions['E'].width = 40

    # Save the workbook
    output_file = "/home/<USER>/carbonx/CarbonX-Updated-Module-Features-Professional.xlsx"
    wb.save(output_file)
    print(f"✅ Updated CarbonX Module Feature List created: {output_file}")

    return output_file

if __name__ == "__main__":
    create_carbonx_module_list()
