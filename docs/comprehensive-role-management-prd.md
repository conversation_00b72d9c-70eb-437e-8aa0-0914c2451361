# Comprehensive Role Management & User Access Control PRD

## 1. Executive Summary

This Product Requirements Document (PRD) defines a comprehensive role-based access control (RBAC) system for the CarbonX platform, incorporating dynamic role management, hierarchical permissions, and specialized roles for SPV management, organization administration, and broker operations.

## 2. Current System Analysis

### 2.1 Existing Role Structure

Based on codebase analysis, the current system includes:

**System Roles (Built-in):**
- `ADMIN` - Platform Super Administrator
- `ORGANIZATION_ADMIN` - Organization Administrator  
- `ORGANI<PERSON>ATION_USER` - Regular Organization User
- `USER` - Legacy alias for ORGANIZATION_USER
- `BROKER` - Carbon Credit Broker
- `SPV_USER` - Special Purpose Vehicle User
- `INDIVIDUAL` - Individual User
- `VERIFIER` - External Verifier
- `AUDITOR` - External Auditor
- `MARKETPLACE_ADMIN` - Marketplace Administrator
- `MARKETPLACE_USER` - Marketplace User
- `SYSTEM` - System/Service Account

**SPV-Specific Roles:**
- `SPV_ADMIN` - SPV Administrator
- `PROJECT_MANAGER` - Project Manager within SPV
- `SITE_WORKER` - Site Worker within SPV

**Custom Roles (Predefined):**
- `CARBON_CREDIT_MANAGER` - Carbon Credit Management
- `WALLET_MANAGER` - Wallet Management
- `COMPLIANCE_OFFICER` - Compliance Management
- `FINANCE_MANAGER` - Financial Management
- `READONLY_USER` - Read-only Access
- `DEPARTMENT_ADMIN` - Department Administration
- `DIVISION_ADMIN` - Division Administration

### 2.2 Permission Categories

The system uses 11 permission categories:
- Organization Management
- User Management  
- Carbon Credit Management
- Wallet Management
- Team Management
- Admin Functions
- Compliance Management
- Billing Management
- Marketplace Operations
- Reporting & Analytics
- Settings Management

## 3. Comprehensive Role Definitions

### 3.1 Platform-Level Roles

#### 3.1.1 Super Administrator (ADMIN)
**Purpose:** Complete platform control and management
**Scope:** Global platform access
**Key Capabilities:**
- Full platform administration
- Organization management (create, approve, suspend, delete)
- User management across all organizations
- Role management and custom role creation
- System configuration and settings
- Platform analytics and monitoring
- Marketplace governance
- Fee structure management
- Smart contract control

**User Management Powers:**
- Create/modify/delete any user account
- Assign any role to any user
- Create custom roles with specific permissions
- Manage role hierarchies and inheritance
- Override organization-level restrictions
- Access audit logs for all users
- Manage platform-wide user policies

**Limitations:**
- None (full access)

#### 3.1.2 Marketplace Administrator (MARKETPLACE_ADMIN)
**Purpose:** Marketplace operations and governance
**Scope:** Platform marketplace
**Key Capabilities:**
- Marketplace listing management
- Trading oversight and monitoring
- Fee management for marketplace transactions
- Dispute resolution
- Market analytics and reporting
- Compliance monitoring for trades

**User Management Powers:**
- Manage marketplace user accounts
- Suspend/activate marketplace participants
- Review and approve marketplace applications

**Limitations:**
- Cannot access organization internal data
- Cannot modify platform-level settings
- Cannot create custom roles

### 3.2 Organization-Level Roles

#### 3.2.1 Organization Administrator (ORGANIZATION_ADMIN)
**Purpose:** Complete organization management
**Scope:** Single organization
**Key Capabilities:**
- Organization profile management
- User invitation and management within organization
- SPV creation and management
- Project portfolio oversight
- Carbon credit management
- Wallet management
- Team and department creation
- Compliance oversight
- Financial reporting and analytics

**User Management Powers:**
- Invite users to organization
- Assign organization-level roles
- Create custom roles for organization
- Manage user permissions within organization
- Remove users from organization
- View user activity and audit logs
- Manage user access to projects and SPVs

**Limitations:**
- Cannot access other organizations' data
- Cannot modify platform-level settings
- Cannot assign platform-level roles (ADMIN, MARKETPLACE_ADMIN)
- Cannot create SPV_USER role assignments (handled by SPV workflow)

#### 3.2.2 Organization User (ORGANIZATION_USER/USER)
**Purpose:** Standard organization member
**Scope:** Organization with limited access
**Key Capabilities:**
- View organization projects and credits
- Participate in assigned projects
- Basic wallet operations
- View marketplace listings
- Submit compliance documents

**User Management Powers:**
- None (cannot manage other users)

**Limitations:**
- Cannot invite or manage users
- Cannot create projects or SPVs
- Cannot access financial data
- Cannot modify organization settings
- Read-only access to most features

### 3.3 SPV-Specific Roles

#### 3.3.1 SPV User (SPV_USER)
**Purpose:** Access to SPV portal and functionality
**Scope:** Cross-organization SPV access
**Key Capabilities:**
- Access SPV portal dashboard
- Role-specific functionality based on SPV role assignment
- Project assignment workflow participation
- Data verification workflow participation

**User Management Powers:**
- None at base level (depends on SPV role)

**Sub-Roles within SPV:**

##### SPV Administrator (SPV_ADMIN)
**Purpose:** SPV management and administration
**Scope:** Specific SPV
**Key Capabilities:**
- SPV profile management
- User creation within SPV
- Project assignment to project managers
- Data verification and approval
- SPV document management
- SPV analytics and reporting

**User Management Powers:**
- Create SPV users (PROJECT_MANAGER, SITE_WORKER)
- Assign projects to project managers
- Manage SPV user permissions
- Remove users from SPV
- View SPV user activity

**Limitations:**
- Cannot modify organization-level settings
- Cannot access other SPVs
- Cannot assign organization-level roles

##### Project Manager (PROJECT_MANAGER)
**Purpose:** Project management within SPV
**Scope:** Assigned projects within SPV
**Key Capabilities:**
- Project data entry and management
- Site worker assignment to projects
- Data verification from site workers
- Project reporting and analytics
- Project document management

**User Management Powers:**
- Assign site workers to projects
- Manage site worker access to assigned projects
- Review site worker submissions

**Limitations:**
- Cannot create SPV users
- Cannot access unassigned projects
- Cannot modify SPV settings

##### Site Worker (SITE_WORKER)
**Purpose:** Field data collection and entry
**Scope:** Assigned projects
**Key Capabilities:**
- Data entry for assigned projects


**User Management Powers:**
- None

**Limitations:**
- Cannot assign other users
- Cannot access unassigned projects
- Cannot verify data from other workers
- Read-only access to project settings
- one time data entry (cannot edit own entries)

### 3.4 Broker-Specific Roles

#### 3.4.1 Broker (BROKER)
**Purpose:** Carbon credit brokerage operations
**Scope:** Broker profile and client management
**Key Capabilities:**
- Broker profile management
- Client portfolio management
- Transaction facilitation
- Commission tracking
- Market analysis and reporting
- Client onboarding

**User Management Powers:**
- Manage broker client accounts
- Invite clients to platform
- Assign client access levels
- View client transaction history

**Limitations:**
- Cannot access organization internal data
- Cannot create organization users
- Cannot modify platform settings
- Limited to broker-client relationships

### 3.5 Specialized Functional Roles

#### 3.5.1 Carbon Credit Manager
**Purpose:** Specialized carbon credit operations
**Scope:** Organization carbon credit portfolio
**Key Capabilities:**
- Carbon credit creation and management
- Credit verification workflows
- Tokenization processes
- Marketplace listing management
- Credit retirement processes

**User Management Powers:**
- None (functional role only)

**Limitations:**
- Cannot manage users
- Cannot access financial data beyond credits
- Cannot modify organization settings

#### 3.5.2 Wallet Manager
**Purpose:** Digital wallet and token management
**Scope:** Organization wallet operations
**Key Capabilities:**
- Wallet creation and management
- Token transfer operations
- Transaction monitoring
- Security protocol management
- Multi-signature setup

**User Management Powers:**
- None (functional role only)

**Limitations:**
- Cannot manage users
- Cannot access non-wallet financial data
- Cannot modify organization settings

#### 3.5.3 Compliance Officer
**Purpose:** Regulatory compliance and verification
**Scope:** Organization compliance activities
**Key Capabilities:**
- KYC/AML verification
- Document review and approval
- Compliance reporting
- Audit trail management
- Regulatory submission

**User Management Powers:**
- Review and approve user verification documents
- Flag suspicious user activities
- Generate compliance reports on users

**Limitations:**
- Cannot create or delete users
- Cannot assign roles
- Cannot access financial transactions
- Cannot modify user permissions

#### 3.5.4 Finance Manager
**Purpose:** Financial oversight and reporting
**Scope:** Organization financial operations
**Key Capabilities:**
- Financial reporting and analytics
- Budget management
- Revenue tracking
- P&L analysis
- Tax compliance reporting
- ROI analysis

**User Management Powers:**
- View user financial activities
- Generate user-specific financial reports

**Limitations:**
- Cannot manage users
- Cannot modify user permissions
- Cannot access operational data
- Read-only access to financial data

## 4. Role Hierarchy and Relationships

### 4.1 Hierarchical Structure

```
ADMIN (Super Admin)
├── MARKETPLACE_ADMIN
├── ORGANIZATION_ADMIN
│   ├── DEPARTMENT_ADMIN
│   │   └── DIVISION_ADMIN
│   ├── CARBON_CREDIT_MANAGER
│   ├── WALLET_MANAGER
│   ├── COMPLIANCE_OFFICER
│   ├── FINANCE_MANAGER
│   └── ORGANIZATION_USER
├── SPV_USER
│   ├── SPV_ADMIN
│   ├── PROJECT_MANAGER
│   └── SITE_WORKER
├── BROKER
├── VERIFIER
├── AUDITOR
└── INDIVIDUAL
```

### 4.2 Role Inheritance

- Child roles inherit base permissions from parent roles
- Additional permissions can be granted to child roles
- Permissions cannot be revoked from inherited base set
- Custom roles can inherit from any system role

### 4.3 Cross-Role Relationships

**SPV-Organization Relationship:**
- SPV users belong to organizations
- Organization admins can create SPVs
- SPV admins can create SPV users within their organization
- Data flows from SPV to organization level

**Broker-Client Relationship:**
- Brokers can have organization clients
- Brokers can have individual clients
- Broker access is limited to client data only
- Commission tracking spans broker-client transactions

## 5. Dynamic Role Management System

### 5.1 Super Admin Role Creation

**Custom Role Builder:**
- Permission-based role construction
- Granular permission assignment
- Role naming and description
- Role hierarchy positioning
- Inheritance configuration

**Role Templates:**
- Industry-specific role templates
- Common permission combinations
- Quick role deployment
- Template customization

**Role Versioning:**
- Role change tracking
- Version history maintenance
- Rollback capabilities
- Impact analysis

### 5.2 Permission Management

**Granular Permissions:**
- Action-based permissions (create, read, update, delete)
- Resource-specific permissions
- Conditional permissions
- Time-based permissions

**Permission Categories:**
- Functional permissions (carbon credits, wallets, etc.)
- Administrative permissions (user management, settings)
- Data access permissions (read, write, admin)
- System permissions (audit, monitoring, configuration)

**Permission Inheritance:**
- Parent-child permission flow
- Override capabilities
- Conflict resolution
- Permission validation

### 5.3 Role Assignment Workflow

**Assignment Process:**
1. Role selection from available roles
2. Permission validation
3. Conflict checking
4. Approval workflow (if required)
5. Role activation
6. Audit logging

**Bulk Operations:**
- Mass role assignment
- Role migration
- Permission updates
- Deactivation processes

## 6. User Management by Role

### 6.1 Super Admin User Management

**Global User Operations:**
- Create users in any organization
- Assign any role to any user
- Cross-organization user transfers
- Global user search and filtering
- Platform-wide user analytics

**Role Management:**
- Create custom roles
- Modify existing roles
- Delete unused roles
- Role impact analysis
- Permission auditing

**Organization Management:**
- Create organizations
- Assign organization admins
- Transfer organizations
- Merge organizations
- Organization analytics

### 6.2 Organization Admin User Management

**Organization User Operations:**
- Invite users to organization
- Assign organization-level roles
- Create custom roles for organization
- Manage user permissions
- Remove users from organization

**SPV User Management:**
- Create SPVs within organization
- Assign SPV admins
- Monitor SPV user activities
- SPV performance analytics

**Team Management:**
- Create departments and divisions
- Assign team leads
- Manage team permissions
- Team performance tracking

### 6.3 SPV Admin User Management

**SPV User Operations:**
- Create SPV users (PROJECT_MANAGER, SITE_WORKER)
- Assign projects to project managers
- Manage SPV user permissions
- Monitor SPV user activities

**Project Assignment:**
- Assign project managers to projects
- Enable project manager to assign site workers
- Monitor project assignments
- Project performance tracking

**Data Verification:**
- Verify project manager submissions
- Approve/reject data entries
- Manage verification workflows
- Quality control processes

### 6.4 Broker User Management

**Client Management:**
- Onboard new clients
- Manage client profiles
- Assign client access levels
- Monitor client activities

**Transaction Management:**
- Facilitate client transactions
- Track commission earnings
- Generate client reports
- Manage client relationships

## 7. Technical Implementation

### 7.1 Database Schema Extensions

**Custom Roles Table:**
```sql
CREATE TABLE custom_roles (
  id VARCHAR PRIMARY KEY,
  name VARCHAR UNIQUE NOT NULL,
  display_name VARCHAR NOT NULL,
  description TEXT,
  is_system_role BOOLEAN DEFAULT FALSE,
  parent_role_id VARCHAR REFERENCES custom_roles(id),
  organization_id VARCHAR REFERENCES organizations(id),
  permissions JSONB NOT NULL,
  metadata JSONB,
  created_by VARCHAR REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

**User Custom Roles Table:**
```sql
CREATE TABLE user_custom_roles (
  id VARCHAR PRIMARY KEY,
  user_id VARCHAR REFERENCES users(id),
  role_id VARCHAR REFERENCES custom_roles(id),
  assigned_by VARCHAR REFERENCES users(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  context JSONB,
  UNIQUE(user_id, role_id)
);
```

### 7.2 Permission System

**Permission Structure:**
```typescript
interface Permission {
  name: string;
  displayName: string;
  description: string;
  category: PermissionCategory;
  resourceType?: string;
  conditions?: PermissionCondition[];
}
```

**Permission Checking:**
```typescript
async function hasPermission(
  userId: string,
  permission: string,
  context?: PermissionContext
): Promise<boolean>
```

### 7.3 Role Management API

**Role Creation:**
```typescript
POST /api/admin/roles
{
  name: string;
  displayName: string;
  description: string;
  parentRole?: string;
  permissions: string[];
  organizationId?: string;
}
```

**Role Assignment:**
```typescript
POST /api/admin/users/{userId}/roles
{
  roleId: string;
  expiresAt?: Date;
  context?: Record<string, any>;
}
```

## 8. Security Considerations

### 8.1 Access Control

- Role-based access control (RBAC)
- Attribute-based access control (ABAC) for complex scenarios
- Multi-factor authentication for sensitive roles
- Session management and timeout
- IP-based access restrictions

### 8.2 Audit and Compliance

- Comprehensive audit logging
- Role change tracking
- Permission usage monitoring
- Compliance reporting
- Data retention policies

### 8.3 Data Protection

- Role-based data encryption
- Data masking for unauthorized access
- Secure data transmission
- Privacy controls
- GDPR compliance

## 9. Implementation Roadmap

### Phase 1: Core Role System Enhancement
- Implement dynamic role creation
- Enhance permission granularity
- Develop role management UI

### Phase 2: SPV Role Integration
- Complete SPV role implementation
- Implement SPV user workflows
- Develop SPV admin dashboard

### Phase 3: Broker Role System
- Implement broker role functionality
- Develop broker dashboard
- Implement client management

### Phase 4: Advanced Features
- Implement role analytics
- Develop compliance reporting
- Implement advanced security features

## 10. Success Metrics

- Role assignment accuracy: >99%
- Permission resolution time: <100ms
- User onboarding time reduction: 50%
- Security incident reduction: 80%
- Compliance audit success rate: 100%

## 11. Detailed Role Specifications

### 11.1 Role Permission Matrix

| Role | Organization | User Mgmt | Carbon Credits | Wallet | Marketplace | Compliance | Reporting | Admin |
|------|-------------|-----------|----------------|--------|-------------|------------|-----------|-------|
| ADMIN | Full | Full | Full | Full | Full | Full | Full | Full |
| ORGANIZATION_ADMIN | Full | Org-level | Full | Full | Limited | Full | Full | None |
| SPV_ADMIN | SPV-only | SPV-level | SPV-projects | SPV-wallet | None | SPV-only | SPV-only | None |
| PROJECT_MANAGER | Project-only | Project-team | Project-credits | None | None | Project-only | Project-only | None |
| SITE_WORKER | None | None | Data-entry | None | None | Document-upload | None | None |
| BROKER | None | Client-only | Broker-credits | Client-wallet | Full | Client-kyc | Client-reports | None |
| CARBON_CREDIT_MANAGER | None | None | Full | None | Limited | Credit-compliance | Credit-reports | None |
| WALLET_MANAGER | None | None | None | Full | None | Wallet-compliance | Wallet-reports | None |
| COMPLIANCE_OFFICER | None | User-verification | Compliance-only | None | None | Full | Compliance-reports | None |
| FINANCE_MANAGER | None | None | Financial-view | Financial-view | Financial-view | None | Full | None |

### 11.2 Role Limitations and Restrictions

#### Super Administrator (ADMIN)
**Restrictions:**
- Must have 2FA enabled
- Cannot be assigned to organization users
- Requires approval for certain destructive actions
- Activity is fully audited
- Cannot delegate admin privileges without approval

#### Organization Administrator (ORGANIZATION_ADMIN)
**Restrictions:**
- Cannot access other organizations
- Cannot create platform-level roles
- Cannot modify platform settings
- Cannot assign ADMIN or MARKETPLACE_ADMIN roles
- Limited to organization's subscription features

#### SPV Administrator (SPV_ADMIN)
**Restrictions:**
- Cannot access organization-level data outside SPV scope
- Cannot create organization users
- Cannot modify organization settings
- Cannot assign organization-level roles
- Limited to assigned SPV only

#### Project Manager (PROJECT_MANAGER)
**Restrictions:**
- Cannot access unassigned projects
- Cannot create SPV users
- Cannot modify SPV settings
- Cannot verify own data entries
- Limited to project-specific operations

#### Site Worker (SITE_WORKER)
**Restrictions:**
- Cannot access other projects
- Cannot assign other users
- Cannot verify data from other workers
- Cannot modify project settings
- Limited to data entry and document upload

#### Broker (BROKER)
**Restrictions:**
- Cannot access organization internal data
- Cannot create organization users
- Cannot modify platform settings
- Limited to broker-client relationships only
- Cannot access non-client marketplace data

### 11.3 Role Transition Workflows

#### User Role Upgrade Process
1. **Request Initiation:** User or admin initiates role change
2. **Validation:** System validates role compatibility and requirements
3. **Approval:** Required approvals based on role hierarchy
4. **Transition:** Gradual permission transition with overlap period
5. **Verification:** Post-transition access verification
6. **Audit:** Complete audit trail of role change

#### Role Downgrade Process
1. **Notification:** Advance notice to affected user
2. **Data Transfer:** Transfer of responsibilities to other users
3. **Access Revocation:** Gradual removal of elevated permissions
4. **Verification:** Confirmation of access removal
5. **Documentation:** Complete documentation of changes

### 11.4 Emergency Access Procedures

#### Emergency Admin Access
- Break-glass procedures for critical situations
- Temporary elevated access with automatic expiration
- Full audit trail of emergency access usage
- Post-incident review and documentation

#### Role Recovery Procedures
- Account recovery for locked admin accounts
- Role restoration after system failures
- Backup admin designation procedures
- Emergency contact protocols

## 12. Integration Specifications

### 12.1 SPV Portal Integration

**Role-Based Dashboard Views:**
- SPV_ADMIN: Full SPV management dashboard
- PROJECT_MANAGER: Project-focused dashboard with assigned projects
- SITE_WORKER: Simple data entry interface

**Data Flow Integration:**
- Site Worker → Project Manager → SPV Admin → Organization Admin
- Verification workflow with role-based approval stages
- Automated notifications based on role assignments

**Permission Inheritance:**
- SPV roles inherit base organization permissions
- Additional SPV-specific permissions overlay
- Project-specific permission scoping

### 12.2 Broker System Integration

**Broker Dashboard Features:**
- Client portfolio management
- Commission tracking and reporting
- Market analysis tools
- Transaction facilitation interface

**Client Relationship Management:**
- Broker-client permission mapping
- Client data access controls
- Transaction authorization workflows
- Commission calculation automation

### 12.3 Marketplace Integration

**Role-Based Marketplace Access:**
- ADMIN: Full marketplace administration
- MARKETPLACE_ADMIN: Marketplace operations and governance
- BROKER: Client transaction facilitation
- ORGANIZATION_ADMIN: Organization credit trading
- ORGANIZATION_USER: Limited marketplace browsing

**Trading Permissions:**
- Role-based trading limits
- Approval workflows for large transactions
- Automated compliance checking
- Real-time permission validation

## 13. User Experience Considerations

### 13.1 Role-Based UI/UX

**Adaptive Interface:**
- Dynamic menu generation based on role permissions
- Context-sensitive feature availability
- Role-appropriate dashboard layouts
- Progressive disclosure of advanced features

**Navigation Patterns:**
- Role-specific navigation menus
- Breadcrumb trails with permission context
- Quick access to frequently used features
- Role-based search and filtering

### 13.2 Onboarding Workflows

**Role-Specific Onboarding:**
- Customized onboarding flows per role
- Role-appropriate training materials
- Progressive feature introduction
- Mentorship assignment for complex roles

**Permission Education:**
- Clear explanation of role capabilities
- Interactive permission demonstrations
- Help documentation tailored to roles
- Video tutorials for complex workflows

### 13.3 Notification Systems

**Role-Based Notifications:**
- Permission-relevant notifications only
- Escalation paths based on role hierarchy
- Customizable notification preferences
- Real-time updates for role changes

## 14. Performance and Scalability

### 14.1 Permission Caching

**Multi-Level Caching:**
- User permission cache with TTL
- Role definition caching
- Permission inheritance caching
- Database query optimization

**Cache Invalidation:**
- Real-time cache updates on role changes
- Distributed cache synchronization
- Graceful degradation on cache failures
- Performance monitoring and alerting

### 14.2 Database Optimization

**Indexing Strategy:**
- Composite indexes on user-role relationships
- Permission lookup optimization
- Role hierarchy traversal optimization
- Query performance monitoring

**Scalability Considerations:**
- Horizontal scaling for permission checks
- Read replica optimization
- Connection pooling for role queries
- Performance benchmarking

## 15. Monitoring and Analytics

### 15.1 Role Usage Analytics

**Usage Metrics:**
- Role assignment frequency
- Permission utilization rates
- Feature adoption by role
- User activity patterns

**Performance Metrics:**
- Permission check latency
- Role assignment success rates
- System availability by role
- Error rates and resolution times

### 15.2 Security Monitoring

**Access Monitoring:**
- Unusual access pattern detection
- Failed permission attempts
- Role escalation monitoring
- Suspicious activity alerting

**Compliance Monitoring:**
- Role compliance with regulations
- Audit trail completeness
- Data access logging
- Privacy compliance tracking

## 16. Testing Strategy

### 16.1 Role Testing Framework

**Automated Testing:**
- Permission matrix validation
- Role inheritance testing
- Access control verification
- Performance testing under load

**Manual Testing:**
- User experience testing per role
- Workflow validation
- Edge case testing
- Security penetration testing

### 16.2 Regression Testing

**Role Change Impact:**
- Permission regression testing
- UI/UX regression validation
- Performance impact assessment
- Security vulnerability scanning

---

*This comprehensive PRD provides the complete specification for implementing a robust, scalable, and secure role management system for the CarbonX platform, covering all aspects from basic role definitions to advanced security and performance considerations.*
