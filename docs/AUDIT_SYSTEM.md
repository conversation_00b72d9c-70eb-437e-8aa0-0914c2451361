# Comprehensive Audit Trail System

## Overview

The CarbonX platform features a comprehensive, role-based audit trail system that provides full transparency and accountability for all user actions and system operations. The system is designed to meet enterprise-grade compliance requirements while maintaining high performance and scalability.

## Features

### Core Functionality
- **Complete API Logging**: Tracks ALL API calls and user actions within organization scope
- **Role-Based Access Control**: Different audit views based on user roles
- **Real-Time Updates**: Live audit trail updates without page refresh
- **Advanced Filtering**: Comprehensive filtering by date, type, severity, category, and more
- **Export Capabilities**: CSV, JSON, and PDF export with date range selection
- **Search Functionality**: Full-text search across audit logs with advanced filters

### Tracked Events
- **Authentication Events**: Login, logout, failed attempts, password changes, 2FA
- **Data Entry Operations**: Create, read, update, delete operations
- **SPV Management**: Creation, verification, document uploads, user management
- **Project Management**: Assignments, status changes, project operations
- **Carbon Credit Transactions**: Marketplace activities and wallet operations
- **Role Management**: Permission changes and role assignments
- **Document Operations**: Uploads, verification status changes, access grants
- **System Events**: Backups, maintenance, configuration changes

### Role-Based Access
- **Organization Admin**: All logs for entire organization
- **SPV Admin**: Logs for specific SPV operations
- **Project Manager**: Logs for assigned projects only
- **Site Workers**: Logs for own data entries only
- **Super Admin**: System-wide access (with proper restrictions)

## Architecture

### Database Schema

```sql
-- Enhanced AuditLog model with comprehensive metadata
model AuditLog {
  id             String        @id @default(cuid())
  type           AuditLogType
  description    String
  metadata       Json?
  ipAddress      String?
  userAgent      String?
  createdAt      DateTime      @default(now())
  
  -- Core relationships
  userId         String?
  organizationId String?
  spvId          String?
  projectId      String?
  resourceType   String?
  resourceId     String?
  oldValue       Json?
  newValue       Json?
  
  -- Enhanced audit fields
  sessionId      String?       -- Session tracking
  requestId      String?       -- Request correlation
  severity       String?       -- INFO, WARN, ERROR, CRITICAL
  category       String?       -- AUTHENTICATION, DATA, SYSTEM, etc.
  source         String?       -- WEB, API, MOBILE, SYSTEM
  duration       Int?          -- Operation duration in ms
  success        Boolean?      -- Operation success status
  errorCode      String?       -- Error code if failed
  tags           String[]      -- Categorization tags
  
  -- Performance indexes for fast queries
  @@index([organizationId, type, createdAt])
  @@index([userId, createdAt])
  @@index([severity, createdAt])
  @@index([category, createdAt])
}
```

### API Endpoints

#### Core Audit Logs API
- `GET /api/audit-logs` - Retrieve audit logs with filtering and pagination
- `GET /api/audit-logs/analytics` - Get audit analytics and insights
- `GET /api/audit-logs/search` - Advanced search functionality
- `GET /api/audit-logs/security` - Security-specific audit logs
- `GET /api/audit-logs/session/[sessionId]` - Session-specific logs

#### Query Parameters
```typescript
interface AuditLogQuery {
  // Pagination
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'type' | 'severity';
  sortOrder?: 'asc' | 'desc';
  
  // Filtering
  type?: AuditLogType;
  types?: AuditLogType[];
  severity?: 'INFO' | 'WARN' | 'ERROR' | 'CRITICAL';
  category?: 'AUTHENTICATION' | 'DATA' | 'SYSTEM' | 'SPV' | 'DOCUMENT';
  source?: 'WEB' | 'API' | 'MOBILE' | 'SYSTEM';
  success?: boolean;
  
  // Date filtering
  startDate?: string;
  endDate?: string;
  
  // Context filtering
  userId?: string;
  organizationId?: string;
  spvId?: string;
  projectId?: string;
  sessionId?: string;
  
  // Search and export
  search?: string;
  export?: 'csv' | 'json' | 'pdf';
  
  // Real-time
  realtime?: boolean;
  lastTimestamp?: string;
}
```

## Usage

### Frontend Components

#### Enhanced Audit Trail
```tsx
import { EnhancedAuditTrail } from '@/components/audit/enhanced-audit-trail';

<EnhancedAuditTrail
  title="Organization Audit Trail"
  description="Real-time audit trail for all activities"
  organizationId={organizationId}
  showFilters={true}
  showExport={true}
  showRealtime={true}
  autoRefresh={false}
  refreshInterval={30000}
/>
```

#### Audit Analytics
```tsx
import { AuditAnalytics } from '@/components/audit/audit-analytics';

<AuditAnalytics
  organizationId={organizationId}
  timeRange="24h"
  autoRefresh={true}
/>
```

#### Advanced Search
```tsx
import { AuditSearch } from '@/components/audit/audit-search';

<AuditSearch
  organizationId={organizationId}
  placeholder="Search audit logs..."
  onResultSelect={(result) => console.log(result)}
/>
```

### Backend Integration

#### Authentication Logging
```typescript
import { AuthAuditLogger } from '@/lib/audit/auth-logger';

// Log successful login
await AuthAuditLogger.logLoginSuccess({
  userId: user.id,
  organizationId: user.organizationId,
  email: user.email,
  ipAddress: req.ip,
  userAgent: req.headers['user-agent']
});

// Log failed login
await AuthAuditLogger.logLoginFailure({
  email: credentials.email,
  ipAddress: req.ip
}, 'Invalid password');
```

#### SPV Management Logging
```typescript
import { SPVAuditLogger } from '@/lib/audit/spv-logger';

// Log SPV creation
await SPVAuditLogger.logSPVCreated(context, spvData);

// Log document upload
await SPVAuditLogger.logSPVDocumentUploaded(
  context, 
  spvId, 
  'INCORPORATION_CERTIFICATE', 
  fileName
);
```

#### Data Entry Logging
```typescript
import { DataAuditLogger } from '@/lib/audit/data-logger';

// Log data entry creation
await DataAuditLogger.logDataEntryCreated(
  context, 
  'CARBON_CREDIT', 
  entryId, 
  entryData
);

// Log verification
await DataAuditLogger.logDataEntryVerified(
  context, 
  'CARBON_CREDIT', 
  entryId, 
  'SPV_ADMIN'
);
```

### Middleware Integration

#### API Route Wrapper
```typescript
import { withAuditLogging } from '@/lib/audit/integration';

export const GET = withAuditLogging(async (req: NextRequest) => {
  // Your API logic here
  return NextResponse.json({ success: true });
}, {
  logRequest: true,
  logResponse: true,
  logErrors: true
});
```

#### Audit Context Helper
```typescript
import { getAuditContextFromRequest } from '@/lib/audit/integration';

export async function POST(req: NextRequest) {
  const context = await getAuditContextFromRequest(req);
  
  // Use context for audit logging
  await AuditHooks.data.onEntryCreated(context, entryType, entryId, data);
}
```

## Performance Optimization

### Database Indexes
The system includes comprehensive database indexes for optimal query performance:
- Composite indexes for common query patterns
- Partial indexes for filtered queries
- GIN indexes for array operations (tags)

### Batch Operations
```typescript
import { BatchAuditLogger } from '@/lib/audit/integration';

const batchLogger = new BatchAuditLogger();

// Add multiple operations
batchLogger.add(() => AuthAuditLogger.logLoginSuccess(context1));
batchLogger.add(() => DataAuditLogger.logDataEntryCreated(context2, ...));

// Flush all at once
await batchLogger.flush();
```

### Maintenance Jobs
```typescript
import { AuditPerformanceOptimizer } from '@/lib/audit/performance';

// Run maintenance job (should be scheduled)
await AuditPerformanceOptimizer.runMaintenanceJob();

// Get performance statistics
const stats = await AuditPerformanceOptimizer.getStatistics();

// Monitor system health
const health = await AuditPerformanceOptimizer.monitorPerformance();
```

## Security Considerations

### Data Protection
- Sensitive data is automatically filtered from logs
- IP addresses and user agents are logged for security analysis
- Role-based access ensures data isolation
- Audit logs themselves are immutable once created

### Compliance Features
- Comprehensive audit trail for regulatory compliance
- Tamper-evident logging with metadata integrity
- Long-term retention policies with archiving
- Export capabilities for compliance reporting

### Access Controls
- Strict role-based access to audit data
- Users cannot view audit logs for data they didn't create
- Organization-level data isolation
- Admin-only access to system-wide logs

## Testing

### Unit Tests
```bash
npm test src/__tests__/audit/audit-service.test.ts
npm test src/__tests__/audit/audit-api.test.ts
```

### Integration Tests
The system includes comprehensive integration tests covering:
- API endpoint functionality
- Role-based access controls
- Export functionality
- Real-time updates
- Error handling

### Performance Tests
- Load testing for high-volume logging
- Query performance optimization
- Database index effectiveness
- Memory usage monitoring

## Monitoring and Alerts

### Health Monitoring
The system continuously monitors:
- Error rates and response times
- Database performance and size
- Log volume and growth patterns
- System resource usage

### Alerting
Automatic alerts for:
- High error rates (>10%)
- Slow response times (>1000ms)
- Excessive log volume
- Database size issues
- Security events

## Best Practices

### Implementation Guidelines
1. Always use the provided audit loggers for consistency
2. Include relevant context information in all logs
3. Use appropriate severity levels for different events
4. Implement batch logging for high-volume operations
5. Regular maintenance and cleanup of old logs

### Security Best Practices
1. Never log sensitive information (passwords, tokens)
2. Implement proper access controls for audit data
3. Use HTTPS for all audit-related API calls
4. Regular security reviews of audit configurations
5. Monitor for suspicious audit patterns

### Performance Best Practices
1. Use indexes effectively for common queries
2. Implement pagination for large result sets
3. Use batch operations for multiple logs
4. Regular database maintenance and optimization
5. Monitor and alert on performance metrics

## Troubleshooting

### Common Issues
1. **Slow Query Performance**: Check database indexes and query patterns
2. **High Memory Usage**: Implement pagination and limit result sets
3. **Missing Logs**: Verify audit logger integration and error handling
4. **Access Denied**: Check role-based access controls and permissions
5. **Export Failures**: Verify data volume and timeout settings

### Debug Mode
Enable debug logging for troubleshooting:
```typescript
// Set environment variable
DEBUG_AUDIT=true

// Or use logger directly
logger.debug('Audit operation details', { context, operation });
```

## Future Enhancements

### Planned Features
- Real-time notifications for critical events
- Machine learning for anomaly detection
- Advanced visualization and dashboards
- Integration with external SIEM systems
- Blockchain-based audit trail integrity

### Scalability Improvements
- Distributed logging architecture
- Event streaming for real-time processing
- Horizontal scaling for high-volume environments
- Advanced caching strategies
- Microservices architecture support
