# Enhanced Functional Requirements Document (FRD)
## Carbonix Carbon Credit Trading Platform with Broker System

**Document Version:** 2.0
**Date:** June 25, 2025
**Project Start Date:** December 24, 2024
**Last Updated:** June 25, 2025
**Technical Lead:** [To be filled]
**Architecture Lead:** [To be filled]
**Security Lead:** [To be filled]
**Broker System Lead:** [To be filled]

---

## Table of Contents

1. [Enhanced System Architecture](#enhanced-system-architecture)
2. [Database Design with Broker Schema](#database-design-with-broker-schema)
3. [API Specifications](#api-specifications)
4. [Security Requirements](#security-requirements)
5. [Integration Specifications](#integration-specifications)
6. [Performance Requirements](#performance-requirements)
7. [Deployment Architecture](#deployment-architecture)
8. [Monitoring and Observability](#monitoring-and-observability)
9. [Broker System Technical Specifications](#broker-system-technical-specifications)
10. [Enhanced Features Technical Implementation](#enhanced-features-technical-implementation)

---

## 1. Enhanced System Architecture

### 1.1 High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Application]
        MOBILE[Mobile App]
        API_CLIENT[API Clients]
    end
    
    subgraph "API Gateway"
        GATEWAY[API Gateway/Load Balancer]
        AUTH[Authentication Service]
        RATE_LIMIT[Rate Limiting]
    end
    
    subgraph "Application Layer"
        NEXT[Next.js Application]
        API[API Routes]
        MIDDLEWARE[Middleware Layer]
    end
    
    subgraph "Business Logic Layer"
        USER_SVC[User Service]
        PROJECT_SVC[Project Service]
        CREDIT_SVC[Carbon Credit Service]
        MARKETPLACE_SVC[Marketplace Service]
        WALLET_SVC[Wallet Service]
        COMPLIANCE_SVC[Compliance Service]
        NOTIFICATION_SVC[Notification Service]
        BROKER_SVC[Broker Service]
        CALCULATION_SVC[Carbon Credit Calculation Service]
        COMMISSION_SVC[Commission Service]
    end
    
    subgraph "Data Layer"
        POSTGRES[(PostgreSQL)]
        REDIS[(Redis Cache)]
        S3[(Object Storage)]
        BLOCKCHAIN[Blockchain Networks]
    end
    
    subgraph "External Services"
        REGISTRIES[Carbon Registries]
        KYC[KYC/AML Services]
        EMAIL[Email Service]
        ANALYTICS[Analytics Service]
        INR_API[INR Exchange APIs]
        PROJECT_BATTER[Project Batter/Unit APIs]
        SMART_CONTRACTS[Smart Contract Oracles]
    end
    
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    API_CLIENT --> GATEWAY
    
    GATEWAY --> AUTH
    GATEWAY --> RATE_LIMIT
    GATEWAY --> NEXT
    
    NEXT --> API
    API --> MIDDLEWARE
    
    MIDDLEWARE --> USER_SVC
    MIDDLEWARE --> PROJECT_SVC
    MIDDLEWARE --> CREDIT_SVC
    MIDDLEWARE --> MARKETPLACE_SVC
    MIDDLEWARE --> WALLET_SVC
    MIDDLEWARE --> COMPLIANCE_SVC
    MIDDLEWARE --> NOTIFICATION_SVC
    MIDDLEWARE --> BROKER_SVC
    MIDDLEWARE --> CALCULATION_SVC
    MIDDLEWARE --> COMMISSION_SVC
    
    USER_SVC --> POSTGRES
    PROJECT_SVC --> POSTGRES
    CREDIT_SVC --> POSTGRES
    MARKETPLACE_SVC --> POSTGRES
    BROKER_SVC --> POSTGRES
    COMMISSION_SVC --> POSTGRES
    CALCULATION_SVC --> POSTGRES
    WALLET_SVC --> BLOCKCHAIN

    USER_SVC --> REDIS
    MARKETPLACE_SVC --> REDIS
    BROKER_SVC --> REDIS

    PROJECT_SVC --> S3
    CREDIT_SVC --> S3
    COMPLIANCE_SVC --> S3
    BROKER_SVC --> S3

    COMPLIANCE_SVC --> KYC
    BROKER_SVC --> KYC
    PROJECT_SVC --> REGISTRIES
    NOTIFICATION_SVC --> EMAIL
    USER_SVC --> ANALYTICS
    CALCULATION_SVC --> SMART_CONTRACTS
    MARKETPLACE_SVC --> INR_API
    PROJECT_SVC --> PROJECT_BATTER
    COMMISSION_SVC --> BLOCKCHAIN
```

### 1.2 Technology Stack

#### 1.2.1 Frontend Technologies
- **Framework**: Next.js 15 with React 19
- **Language**: TypeScript 5.x
- **Styling**: Tailwind CSS 4.x with shadcn/ui components
- **State Management**: Zustand for global state, React Query for server state
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for data visualization
- **Animation**: Framer Motion for micro-interactions

#### 1.2.2 Backend Technologies
- **Runtime**: Node.js 20+ with TypeScript
- **Framework**: Next.js API routes
- **Database ORM**: Prisma 6.x with TypeScript support (updated from Sequelize)
- **Authentication**: NextAuth.js 5.x with Prisma adapter
- **Validation**: Zod for schema validation with Prisma model integration
- **Blockchain**: Alchemy SDK, ethers.js, viem
- **Email**: Nodemailer with SMTP
- **Smart Contracts**: Solidity for carbon credit calculation contracts
- **Commission Processing**: Automated payment systems integration
- **File Processing**: Multer for file uploads, CSV/Excel parsers

#### 1.2.3 Database and Storage
- **Primary Database**: PostgreSQL 15+
- **Cache**: Redis 7.x for session and application caching
- **Object Storage**: AWS S3 or compatible for file storage
- **Search**: PostgreSQL full-text search with potential Elasticsearch upgrade

#### 1.2.4 Infrastructure
- **Containerization**: Docker with multi-stage builds
- **Orchestration**: Docker Compose for development, Kubernetes for production
- **Cloud Provider**: AWS, GCP, or Azure (cloud-agnostic design)
- **CDN**: CloudFront or equivalent for static asset delivery
- **Load Balancer**: Application Load Balancer with SSL termination

### 1.3 Microservices Architecture

#### 1.3.1 Service Boundaries
While initially built as a monolith, the system is designed with clear service boundaries for future microservices migration:

- **User Management Service**: Authentication, authorization, user profiles
- **Organization Service**: Multi-tenant organization management
- **Project Service**: Project lifecycle management and documentation
- **Carbon Credit Service**: Credit creation, verification, and lifecycle
- **Marketplace Service**: Trading, order matching, and settlement
- **Wallet Service**: Blockchain operations and asset management
- **Compliance Service**: KYC/AML, regulatory reporting, audit trails
- **Notification Service**: Real-time notifications and communications
- **Analytics Service**: Reporting, metrics, and business intelligence

#### 1.3.2 Inter-Service Communication
- **Synchronous**: HTTP/REST APIs for real-time operations
- **Asynchronous**: Event-driven architecture using message queues
- **Data Consistency**: Eventual consistency with compensation patterns
- **Service Discovery**: DNS-based service discovery in Kubernetes

---

## 2. Database Design with Broker Schema

### 2.1 Entity Relationship Diagram

```mermaid
erDiagram
    User ||--o{ Organization : "belongs to"
    User ||--o{ UserCustomRole : "has roles"
    User ||--o{ Wallet : "owns"
    User ||--o{ CarbonCredit : "creates"
    User ||--o{ Order : "places"
    User ||--o{ Notification : "receives"
    User ||--o{ Broker : "can be"

    Organization ||--o{ Project : "owns"
    Organization ||--o{ CarbonCredit : "lists"
    Organization ||--o{ Wallet : "manages"
    Organization ||--o{ Team : "has"
    Organization ||--o{ Subscription : "subscribes"
    Organization ||--o{ Broker : "can be"

    Broker ||--o{ BrokerClient : "manages"
    Broker ||--o{ Commission : "earns"
    Broker ||--o{ BrokerKYC : "completes"
    Broker ||--o{ BrokerOperatingModel : "operates under"

    BrokerClient ||--o{ Project : "owns via broker"
    BrokerClient ||--o{ CarbonCredit : "generates via broker"
    BrokerClient ||--o{ ClientOnboarding : "completes"

    Project ||--o{ CarbonCredit : "generates"
    Project ||--o{ ProjectDocument : "contains"
    Project ||--o{ ProjectVerification : "undergoes"
    Project ||--o{ UnitLog : "tracks"
    Project ||--o{ UnitCorrection : "corrects"
    Project ||--o{ CarbonCreditCalculation : "calculates"

    UnitLog ||--o{ UnitCorrection : "corrected by"
    UnitCorrection ||--o{ CorrectionApproval : "approved via"

    CarbonCredit ||--o{ Order : "traded in"
    CarbonCredit ||--o{ Tokenization : "tokenized as"
    CarbonCredit ||--o{ Retirement : "retired as"
    CarbonCredit ||--o{ MarketplaceListing : "listed as"
    CarbonCredit ||--o{ INRValuation : "valued in"

    CarbonCreditCalculation ||--o{ CalculationMethodology : "uses"
    CarbonCreditCalculation ||--o{ SmartContractExecution : "executed via"

    Wallet ||--o{ Transaction : "executes"
    Wallet ||--o{ Token : "holds"
    Wallet ||--o{ NFT : "contains"
    Wallet ||--o{ WalletAccessControl : "controlled by"
    Wallet ||--o{ Commission : "receives"

    Order ||--o{ Transaction : "settles with"
    Order ||--o{ OrderHistory : "tracked in"
    Order ||--o{ Commission : "generates"

    Team ||--o{ TeamMember : "includes"
    Team ||--o{ TeamRole : "defines"

    CustomRole ||--o{ RolePermission : "grants"
    Permission ||--o{ RolePermission : "assigned to"

    EnterpriseCredit ||--o{ INRValuation : "tracks value"
    EnterpriseCredit ||--o{ CreditVisibility : "displays"
```

### 2.2 Core Entity Specifications

#### 2.2.1 Enhanced Prisma Schema Definitions

```prisma
// Enhanced Prisma Schema with Broker System
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Model with Broker Support
model User {
  id              String    @id @default(cuid())
  email           String    @unique
  name            String?
  password        String?
  role            UserRole  @default(USER)
  emailVerified   DateTime?
  organizationId  String?
  brokerId        String?   // New: Link to broker if user is a broker
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  organization    Organization? @relation(fields: [organizationId], references: [id])
  broker          Broker?       @relation(fields: [brokerId], references: [id])
  carbonCredits   CarbonCredit[]
  wallets         Wallet[]
  orders          Order[]
  notifications   Notification[]
  userRoles       UserCustomRole[]

  @@map("users")
}

// Enhanced User Roles including Broker roles
enum UserRole {
  USER
  ADMIN
  CARBON_CREDIT_MANAGER
  WALLET_MANAGER
  COMPLIANCE_OFFICER
  FINANCE_MANAGER
  ENTERPRISE_ADMIN
  INDEPENDENT_BROKER
  ONIX_MANAGED_BROKER
  BROKER_CLIENT
}

// Broker Model
model Broker {
  id                    String              @id @default(cuid())
  userId                String              @unique
  organizationId        String?
  operatingModel        BrokerOperatingModel @default(INDEPENDENT)
  kycStatus             KYCStatus           @default(PENDING)
  approvalStatus        ApprovalStatus      @default(PENDING)
  twoFactorEnabled      Boolean             @default(false)
  professionalLicense   String?
  specializations       String[]
  commissionRate        Decimal             @default(0.05)
  totalEarnings         Decimal             @default(0)
  activeClients         Int                 @default(0)
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  // Relations
  user                  User                @relation(fields: [userId], references: [id])
  organization          Organization?       @relation(fields: [organizationId], references: [id])
  clients               BrokerClient[]
  commissions           Commission[]
  kycDocuments          BrokerKYC[]
  operatingModelConfig  BrokerOperatingModelConfig?

  @@map("brokers")
}

// Broker Operating Model
enum BrokerOperatingModel {
  INDEPENDENT
  ONIX_MANAGED
}

// Broker KYC Documents
model BrokerKYC {
  id                String        @id @default(cuid())
  brokerId          String
  documentType      DocumentType
  documentUrl       String
  verificationStatus VerificationStatus @default(PENDING)
  verifiedAt        DateTime?
  verifiedBy        String?
  rejectionReason   String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  broker            Broker        @relation(fields: [brokerId], references: [id])

  @@map("broker_kyc")
}

// Broker Client Relationship
model BrokerClient {
  id                String              @id @default(cuid())
  brokerId          String
  clientId          String              // Organization ID
  relationshipType  ClientRelationshipType @default(MANAGED)
  onboardingStatus  OnboardingStatus    @default(INVITED)
  contractStartDate DateTime?
  contractEndDate   DateTime?
  commissionRate    Decimal?
  isActive          Boolean             @default(true)
  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  // Relations
  broker            Broker              @relation(fields: [brokerId], references: [id])
  client            Organization        @relation(fields: [clientId], references: [id])
  onboardingSteps   ClientOnboarding[]

  @@unique([brokerId, clientId])
  @@map("broker_clients")
}

// Commission Tracking
model Commission {
  id              String          @id @default(cuid())
  brokerId        String
  orderId         String?
  transactionId   String?
  amount          Decimal
  currency        String          @default("USD")
  commissionRate  Decimal
  status          CommissionStatus @default(PENDING)
  paidAt          DateTime?
  description     String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  broker          Broker          @relation(fields: [brokerId], references: [id])
  order           Order?          @relation(fields: [orderId], references: [id])
  transaction     Transaction?    @relation(fields: [transactionId], references: [id])

  @@map("commissions")
}

// Carbon Credit Calculation
model CarbonCreditCalculation {
  id                    String                    @id @default(cuid())
  projectId             String
  inputData             Json                      // Raw calculation input data
  methodology           CalculationMethodology
  calculationResult     Json                      // Calculated output
  smartContractAddress  String?                   // Smart contract used
  transactionHash       String?                   // Blockchain transaction
  verificationStatus    VerificationStatus        @default(PENDING)
  calculatedBy          String                    // User ID
  verifiedBy            String?
  calculatedAt          DateTime                  @default(now())
  verifiedAt            DateTime?

  // Relations
  project               Project                   @relation(fields: [projectId], references: [id])
  carbonCredits         CarbonCredit[]

  @@map("carbon_credit_calculations")
}

// Enhanced Unit Logging
model UnitLog {
  id              String          @id @default(cuid())
  projectId       String
  logDate         DateTime
  frequency       LoggingFrequency @default(MONTHLY)
  unitType        String          // kWh, tCO2e, etc.
  quantity        Decimal
  dataSource      DataSource      @default(MANUAL)
  sourceFile      String?         // File path if uploaded
  apiSource       String?         // API endpoint if automated
  verificationStatus VerificationStatus @default(PENDING)
  loggedBy        String          // User ID
  verifiedBy      String?
  notes           String?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  project         Project         @relation(fields: [projectId], references: [id])
  corrections     UnitCorrection[]

  @@map("unit_logs")
}

// Unit Correction System
model UnitCorrection {
  id                String              @id @default(cuid())
  unitLogId         String
  originalValue     Decimal
  correctedValue    Decimal
  reason            String
  requestedBy       String              // User ID
  status            CorrectionStatus    @default(PENDING)
  approvedBy        String?
  rejectedBy        String?
  approvalComments  String?
  requestedAt       DateTime            @default(now())
  processedAt       DateTime?

  // Relations
  unitLog           UnitLog             @relation(fields: [unitLogId], references: [id])
  approvals         CorrectionApproval[]

  @@map("unit_corrections")
}

// INR Valuation Tracking
model INRValuation {
  id              String          @id @default(cuid())
  carbonCreditId  String
  inrValue        Decimal
  exchangeRate    Decimal
  marketPrice     Decimal
  valuationDate   DateTime        @default(now())
  source          String          // Exchange API source

  // Relations
  carbonCredit    CarbonCredit    @relation(fields: [carbonCreditId], references: [id])

  @@map("inr_valuations")
}
```
    defaultValue: 'USER'
  },
  emailVerified: {
    type: DataTypes.DATE,
    allowNull: true
  },
  jobTitle: {
    type: DataTypes.STRING,
    allowNull: true
  },
  departmentName: {
    type: DataTypes.STRING,
    allowNull: true
  },
  phoneNumber: {
    type: DataTypes.STRING,
    allowNull: true
  },
  profileImage: {
    type: DataTypes.STRING,
    allowNull: true
  },
  bio: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  lastLoginAt: {
    type: DataTypes.DATE,
    allowNull: true
  },
  twoFactorEnabled: {
    type: DataTypes.BOOLEAN,
    defaultValue: false
  },
  preferences: {
    type: DataTypes.JSONB,
    allowNull: true
  },
  organizationId: {
    type: DataTypes.UUID,
    allowNull: true,
    references: {
      model: 'Organization',
      key: 'id'
    }
  }
}, {
  sequelize,
  modelName: 'User',
  tableName: 'User',
  timestamps: true,
  indexes: [
    { fields: ['email'] },
    { fields: ['organizationId'] },
    { fields: ['role'] }
  ]
});
```

#### 2.2.2 User Entity SQL Schema
```sql
CREATE TABLE "User" (
    id                     TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    email                  TEXT UNIQUE NOT NULL,
    name                   TEXT,
    password               TEXT,
    role                   "UserRole" DEFAULT 'USER',
    email_verified         TIMESTAMP,
    job_title              TEXT,
    department_name        TEXT,
    phone_number           TEXT,
    profile_image          TEXT,
    bio                    TEXT,
    last_login_at          TIMESTAMP,
    two_factor_enabled     BOOLEAN DEFAULT false,
    preferences            JSONB,
    organization_id        TEXT REFERENCES "Organization"(id),
    department_id          TEXT REFERENCES "Department"(id),
    division_id            TEXT REFERENCES "Division"(id),
    created_at             TIMESTAMP DEFAULT NOW(),
    updated_at             TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_user_email ON "User"(email);
CREATE INDEX idx_user_organization ON "User"(organization_id);
CREATE INDEX idx_user_role ON "User"(role);
```

#### 2.2.2 Organization Entity
```sql
CREATE TABLE "Organization" (
    id                     TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    name                   TEXT NOT NULL,
    description            TEXT,
    website                TEXT,
    logo                   TEXT,
    status                 "OrganizationStatus" DEFAULT 'PENDING',
    verification_status    "VerificationStatus" DEFAULT 'PENDING',
    industry               TEXT,
    size                   "OrganizationSize",
    founded_year           INTEGER,
    primary_contact        TEXT,
    primary_contact_email  TEXT,
    primary_contact_phone  TEXT,
    address_line1          TEXT,
    address_line2          TEXT,
    city                   TEXT,
    state                  TEXT,
    postal_code            TEXT,
    country                TEXT,
    tax_id                 TEXT,
    registration_number    TEXT,
    created_at             TIMESTAMP DEFAULT NOW(),
    updated_at             TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_organization_status ON "Organization"(status);
CREATE INDEX idx_organization_verification ON "Organization"(verification_status);
CREATE INDEX idx_organization_industry ON "Organization"(industry);
```

#### 2.2.3 Project Entity
```sql
CREATE TABLE "Project" (
    id                     TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    name                   TEXT NOT NULL,
    description            TEXT,
    type                   "ProjectType" NOT NULL,
    status                 "ProjectStatus" DEFAULT 'PENDING',
    verification_status    "VerificationStatus" DEFAULT 'PENDING',
    start_date             TIMESTAMP,
    end_date               TIMESTAMP,
    location               TEXT,
    country                TEXT,
    coordinates            TEXT,
    area                   DECIMAL,
    external_project_id    TEXT,
    registry_id            TEXT,
    standard               TEXT,
    methodology            TEXT,
    baseline_emissions     DECIMAL,
    projected_reductions   DECIMAL,
    monitoring_frequency   "MonitoringFrequency",
    organization_id        TEXT NOT NULL REFERENCES "Organization"(id),
    created_at             TIMESTAMP DEFAULT NOW(),
    updated_at             TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_project_organization ON "Project"(organization_id);
CREATE INDEX idx_project_type ON "Project"(type);
CREATE INDEX idx_project_status ON "Project"(status);
CREATE INDEX idx_project_country ON "Project"(country);
```

#### 2.2.4 Carbon Credit Entity
```sql
CREATE TABLE "CarbonCredit" (
    id                     TEXT PRIMARY KEY DEFAULT gen_random_uuid(),
    name                   TEXT NOT NULL,
    description            TEXT,
    quantity               DECIMAL NOT NULL,
    available_quantity     DECIMAL NOT NULL,
    price                  DECIMAL,
    vintage                INTEGER NOT NULL,
    standard               TEXT NOT NULL,
    methodology            TEXT,
    location               TEXT,
    status                 "CarbonCreditStatus" DEFAULT 'PENDING',
    verification_status    "VerificationStatus" DEFAULT 'PENDING',
    serial_number_start    TEXT,
    serial_number_end      TEXT,
    issuance_date          TIMESTAMP,
    expiry_date            TIMESTAMP,
    token_id               TEXT,
    contract_address       TEXT,
    chain_id               INTEGER,
    metadata               JSONB,
    user_id                TEXT NOT NULL REFERENCES "User"(id),
    organization_id        TEXT NOT NULL REFERENCES "Organization"(id),
    project_id             TEXT NOT NULL REFERENCES "Project"(id),
    created_at             TIMESTAMP DEFAULT NOW(),
    updated_at             TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_carbon_credit_organization ON "CarbonCredit"(organization_id);
CREATE INDEX idx_carbon_credit_project ON "CarbonCredit"(project_id);
CREATE INDEX idx_carbon_credit_status ON "CarbonCredit"(status);
CREATE INDEX idx_carbon_credit_vintage ON "CarbonCredit"(vintage);
CREATE INDEX idx_carbon_credit_standard ON "CarbonCredit"(standard);
```

### 2.3 Database Performance Optimization

#### 2.3.1 Indexing Strategy
- **Primary Keys**: All tables use UUID primary keys for global uniqueness
- **Foreign Keys**: Indexed for efficient joins and referential integrity
- **Query Patterns**: Indexes based on common query patterns and filters
- **Composite Indexes**: Multi-column indexes for complex queries
- **Partial Indexes**: For filtered queries on status fields

#### 2.3.2 Partitioning Strategy
- **Time-based Partitioning**: Transaction and audit tables partitioned by month
- **Organization Partitioning**: Large tables partitioned by organization_id
- **Archive Strategy**: Old data archived to separate tables/databases

#### 2.3.3 Connection Management
- **Connection Pooling**: Sequelize connection pooling with optimized pool sizes
- **Read Replicas**: Read-only replicas for analytics and reporting queries
- **Connection Limits**: Proper connection limits to prevent database overload

---

## 3. API Specifications

### 3.1 API Architecture

#### 3.1.1 RESTful Design Principles
- **Resource-based URLs**: Clear, hierarchical resource naming
- **HTTP Methods**: Proper use of GET, POST, PUT, PATCH, DELETE
- **Status Codes**: Appropriate HTTP status codes for all responses
- **Content Negotiation**: Support for JSON (primary) and other formats
- **Versioning**: URL-based versioning (/api/v1/, /api/v2/)

#### 3.1.2 API Standards
- **Request/Response Format**: JSON with consistent structure
- **Error Handling**: Standardized error response format
- **Pagination**: Cursor-based pagination for large datasets
- **Filtering**: Query parameter-based filtering and sorting
- **Field Selection**: Sparse fieldsets for optimized responses

### 3.2 Authentication and Authorization

#### 3.2.1 Authentication Flow
```typescript
// JWT Token Structure
interface JWTPayload {
  sub: string;           // User ID
  email: string;         // User email
  org: string;          // Organization ID
  roles: string[];      // User roles
  permissions: string[]; // User permissions
  iat: number;          // Issued at
  exp: number;          // Expires at
}

// API Authentication Middleware
async function authenticateRequest(req: Request): Promise<AuthContext> {
  const token = extractBearerToken(req.headers.authorization);
  const payload = await verifyJWT(token);
  const user = await getUserById(payload.sub);
  const permissions = await getUserPermissions(user.id, user.organizationId);
  
  return {
    user,
    organization: user.organization,
    permissions,
    isAuthenticated: true
  };
}
```

#### 3.2.2 Authorization Patterns
```typescript
// Permission-based Authorization
function requirePermission(permission: string) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    const hasPermission = await checkPermission(
      req.auth.user.id,
      req.auth.organization.id,
      permission,
      req.params
    );
    
    if (!hasPermission) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    next();
  };
}

// Usage in API routes
app.get('/api/v1/projects', 
  authenticate,
  requirePermission('read:projects'),
  getProjects
);
```

### 3.3 Core API Endpoints

#### 3.3.1 User Management APIs
```typescript
// User Registration
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "John Doe",
  "organizationName": "Acme Corp"
}

Response: 201 Created
{
  "user": {
    "id": "usr_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "emailVerified": false
  },
  "organization": {
    "id": "org_456",
    "name": "Acme Corp",
    "status": "PENDING"
  }
}

// User Authentication
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securePassword123"
}

Response: 200 OK
{
  "accessToken": "eyJhbGciOiJIUzI1NiIs...",
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "user": {
    "id": "usr_123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "role": "USER",
    "organizationId": "org_456"
  }
}
```

#### 3.3.2 Project Management APIs
```typescript
// Create Project
POST /api/v1/projects
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Solar Farm Project",
  "description": "100MW solar installation",
  "type": "RENEWABLE_ENERGY",
  "location": "California, USA",
  "coordinates": "34.0522,-118.2437",
  "area": 500.0,
  "standard": "Verra",
  "methodology": "VM0015",
  "startDate": "2024-01-01T00:00:00Z",
  "endDate": "2034-01-01T00:00:00Z",
  "projectedReductions": 50000.0
}

Response: 201 Created
{
  "id": "prj_789",
  "name": "Solar Farm Project",
  "type": "RENEWABLE_ENERGY",
  "status": "PENDING",
  "verificationStatus": "PENDING",
  "organizationId": "org_456",
  "createdAt": "2024-01-01T00:00:00Z"
}

// List Projects
GET /api/v1/projects?page=1&limit=20&type=RENEWABLE_ENERGY&status=ACTIVE
Authorization: Bearer {token}

Response: 200 OK
{
  "data": [
    {
      "id": "prj_789",
      "name": "Solar Farm Project",
      "type": "RENEWABLE_ENERGY",
      "status": "ACTIVE",
      "location": "California, USA",
      "projectedReductions": 50000.0,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 1,
    "totalPages": 1,
    "hasNext": false,
    "hasPrev": false
  }
}
```

#### 3.3.3 Carbon Credit APIs
```typescript
// Create Carbon Credit
POST /api/v1/carbon-credits
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "Solar Farm Credits 2024",
  "projectId": "prj_789",
  "quantity": 1000.0,
  "vintage": 2024,
  "standard": "Verra",
  "methodology": "VM0015",
  "serialNumberStart": "VCS-001-2024-001",
  "serialNumberEnd": "VCS-001-2024-1000",
  "issuanceDate": "2024-06-01T00:00:00Z"
}

Response: 201 Created
{
  "id": "cc_101",
  "name": "Solar Farm Credits 2024",
  "projectId": "prj_789",
  "quantity": 1000.0,
  "availableQuantity": 1000.0,
  "vintage": 2024,
  "status": "PENDING",
  "verificationStatus": "PENDING",
  "createdAt": "2024-06-01T00:00:00Z"
}

// Tokenize Carbon Credit
POST /api/v1/carbon-credits/{id}/tokenize
Authorization: Bearer {token}
Content-Type: application/json

{
  "network": "polygon",
  "walletId": "wal_202",
  "quantity": 500.0
}

Response: 202 Accepted
{
  "tokenizationId": "tkn_303",
  "status": "PENDING",
  "transactionHash": null,
  "estimatedCompletion": "2024-06-01T00:05:00Z"
}
```

### 3.4 WebSocket API Specifications

#### 3.4.1 Real-time Market Data
```typescript
// WebSocket Connection
const ws = new WebSocket('wss://api.carbonix.com/v1/market-data');

// Subscribe to market updates
ws.send(JSON.stringify({
  type: 'subscribe',
  channels: ['prices', 'orderbook', 'trades'],
  symbols: ['CC_101', 'CC_102']
}));

// Market data message format
{
  "type": "price_update",
  "symbol": "CC_101",
  "price": 25.50,
  "change": 0.50,
  "changePercent": 2.0,
  "volume24h": 1500.0,
  "timestamp": "2024-06-01T12:00:00Z"
}
```

#### 3.4.2 Order Book Updates
```typescript
// Order book subscription
ws.send(JSON.stringify({
  type: 'subscribe',
  channel: 'orderbook',
  symbol: 'CC_101',
  depth: 20
}));

// Order book update message
{
  "type": "orderbook_update",
  "symbol": "CC_101",
  "bids": [
    {"price": 25.00, "quantity": 100.0},
    {"price": 24.95, "quantity": 250.0}
  ],
  "asks": [
    {"price": 25.05, "quantity": 150.0},
    {"price": 25.10, "quantity": 200.0}
  ],
  "timestamp": "2024-06-01T12:00:00Z"
}
```

---

## 4. Security Requirements

### 4.1 Authentication Security

#### 4.1.1 Password Security
```typescript
// Password Requirements
interface PasswordPolicy {
  minLength: 12;
  requireUppercase: true;
  requireLowercase: true;
  requireNumbers: true;
  requireSpecialChars: true;
  preventCommonPasswords: true;
  preventUserInfoInPassword: true;
  maxAge: 90; // days
  historyCount: 12; // prevent reuse of last 12 passwords
}

// Password Hashing
import bcrypt from 'bcryptjs';

async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}

async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return bcrypt.compare(password, hash);
}
```

#### 4.1.2 Multi-Factor Authentication
```typescript
// TOTP Implementation
import { authenticator } from 'otplib';

interface MFASetup {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

async function setupMFA(userId: string): Promise<MFASetup> {
  const secret = authenticator.generateSecret();
  const qrCode = await generateQRCode(secret, userId);
  const backupCodes = generateBackupCodes();

  await saveMFASecret(userId, secret, backupCodes);

  return { secret, qrCode, backupCodes };
}

function verifyMFAToken(secret: string, token: string): boolean {
  return authenticator.verify({ token, secret });
}
```

#### 4.1.3 Session Management
```typescript
// Session Configuration
interface SessionConfig {
  maxAge: 24 * 60 * 60 * 1000; // 24 hours
  rolling: true; // extend session on activity
  secure: true; // HTTPS only
  httpOnly: true; // prevent XSS
  sameSite: 'strict'; // CSRF protection
}

// Session Store (Redis)
class SessionStore {
  async set(sessionId: string, data: SessionData): Promise<void> {
    await redis.setex(
      `session:${sessionId}`,
      SESSION_TTL,
      JSON.stringify(data)
    );
  }

  async get(sessionId: string): Promise<SessionData | null> {
    const data = await redis.get(`session:${sessionId}`);
    return data ? JSON.parse(data) : null;
  }

  async destroy(sessionId: string): Promise<void> {
    await redis.del(`session:${sessionId}`);
  }
}
```

### 4.2 Data Protection

#### 4.2.1 Encryption at Rest
```typescript
// Database Encryption
// Sensitive fields encrypted using AES-256-GCM
interface EncryptedField {
  encrypt(value: string): string;
  decrypt(encryptedValue: string): string;
}

class FieldEncryption implements EncryptedField {
  private readonly algorithm = 'aes-256-gcm';
  private readonly key: Buffer;

  constructor(key: string) {
    this.key = Buffer.from(key, 'hex');
  }

  encrypt(value: string): string {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.algorithm, this.key, iv);

    let encrypted = cipher.update(value, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  }

  decrypt(encryptedValue: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedValue.split(':');

    const iv = Buffer.from(ivHex, 'hex');
    const authTag = Buffer.from(authTagHex, 'hex');

    const decipher = crypto.createDecipher(this.algorithm, this.key, iv);
    decipher.setAuthTag(authTag);

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
```

#### 4.2.2 Encryption in Transit
```typescript
// TLS Configuration
const tlsConfig = {
  minVersion: 'TLSv1.2',
  ciphers: [
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-SHA256',
    'ECDHE-RSA-AES256-SHA384'
  ].join(':'),
  honorCipherOrder: true,
  secureProtocol: 'TLSv1_2_method'
};

// HSTS Headers
const securityHeaders = {
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'",
  'Referrer-Policy': 'strict-origin-when-cross-origin'
};
```

### 4.3 Blockchain Security

#### 4.3.1 Wallet Security
```typescript
// Hierarchical Deterministic Wallet
import { HDNode } from 'ethers';

class SecureWallet {
  private readonly hdNode: HDNode;
  private readonly encryptionKey: string;

  constructor(mnemonic: string, encryptionKey: string) {
    this.hdNode = HDNode.fromMnemonic(mnemonic);
    this.encryptionKey = encryptionKey;
  }

  async createWallet(index: number): Promise<WalletInfo> {
    const wallet = this.hdNode.derivePath(`m/44'/60'/0'/0/${index}`);
    const encryptedPrivateKey = await this.encryptPrivateKey(wallet.privateKey);

    return {
      address: wallet.address,
      encryptedPrivateKey,
      derivationPath: `m/44'/60'/0'/0/${index}`
    };
  }

  private async encryptPrivateKey(privateKey: string): Promise<string> {
    const fieldEncryption = new FieldEncryption(this.encryptionKey);
    return fieldEncryption.encrypt(privateKey);
  }
}
```

#### 4.3.2 Smart Contract Security
```solidity
// Carbon Credit Token Contract (ERC-1155)
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/Pausable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";

contract CarbonCreditToken is ERC1155, AccessControl, Pausable, ReentrancyGuard {
    bytes32 public constant MINTER_ROLE = keccak256("MINTER_ROLE");
    bytes32 public constant BURNER_ROLE = keccak256("BURNER_ROLE");

    mapping(uint256 => CreditInfo) public creditInfo;
    mapping(uint256 => bool) public retired;

    struct CreditInfo {
        string projectId;
        string standard;
        uint256 vintage;
        string serialNumber;
        uint256 totalSupply;
        string metadataURI;
    }

    event CreditMinted(uint256 indexed tokenId, address indexed to, uint256 amount);
    event CreditRetired(uint256 indexed tokenId, address indexed from, uint256 amount);

    modifier onlyMinter() {
        require(hasRole(MINTER_ROLE, msg.sender), "Caller is not a minter");
        _;
    }

    modifier onlyBurner() {
        require(hasRole(BURNER_ROLE, msg.sender), "Caller is not a burner");
        _;
    }

    modifier notRetired(uint256 tokenId) {
        require(!retired[tokenId], "Credit has been retired");
        _;
    }

    function mintCredit(
        address to,
        uint256 tokenId,
        uint256 amount,
        CreditInfo memory info
    ) external onlyMinter nonReentrant {
        require(creditInfo[tokenId].totalSupply == 0, "Token already exists");

        creditInfo[tokenId] = info;
        creditInfo[tokenId].totalSupply = amount;

        _mint(to, tokenId, amount, "");

        emit CreditMinted(tokenId, to, amount);
    }

    function retireCredit(uint256 tokenId, uint256 amount)
        external
        nonReentrant
        notRetired(tokenId)
    {
        require(balanceOf(msg.sender, tokenId) >= amount, "Insufficient balance");

        _burn(msg.sender, tokenId, amount);

        if (totalSupply(tokenId) == 0) {
            retired[tokenId] = true;
        }

        emit CreditRetired(tokenId, msg.sender, amount);
    }
}
```

### 4.4 API Security

#### 4.4.1 Rate Limiting
```typescript
// Rate Limiting Implementation
import rateLimit from 'express-rate-limit';

const createRateLimit = (windowMs: number, max: number) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      error: 'Too many requests',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    store: new RedisStore({
      client: redis,
      prefix: 'rl:'
    })
  });
};

// Different limits for different endpoints
const authLimiter = createRateLimit(15 * 60 * 1000, 5); // 5 attempts per 15 minutes
const apiLimiter = createRateLimit(15 * 60 * 1000, 1000); // 1000 requests per 15 minutes
const publicLimiter = createRateLimit(15 * 60 * 1000, 100); // 100 requests per 15 minutes
```

#### 4.4.2 Input Validation
```typescript
// Zod Schema Validation
import { z } from 'zod';

const CreateProjectSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().max(2000).optional(),
  type: z.enum(['RENEWABLE_ENERGY', 'FORESTRY', 'METHANE_REDUCTION']),
  location: z.string().min(1).max(255),
  coordinates: z.string().regex(/^-?\d+\.?\d*,-?\d+\.?\d*$/),
  area: z.number().positive(),
  standard: z.string().min(1).max(100),
  methodology: z.string().min(1).max(100),
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  projectedReductions: z.number().positive()
});

// Validation Middleware
function validateSchema<T>(schema: z.ZodSchema<T>) {
  return (req: Request, res: Response, next: NextFunction) => {
    try {
      const validated = schema.parse(req.body);
      req.body = validated;
      next();
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          error: 'Validation failed',
          details: error.errors
        });
      }
      next(error);
    }
  };
}
```

---

## 5. Integration Specifications

### 5.1 Carbon Registry Integrations

#### 5.1.1 Verra Registry Integration
```typescript
// Verra API Client
class VerraRegistryClient {
  private readonly baseURL = 'https://registry.verra.org/api/v1';
  private readonly apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  async verifyProject(projectId: string): Promise<VerraProject> {
    const response = await fetch(`${this.baseURL}/projects/${projectId}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Verra API error: ${response.statusText}`);
    }

    return response.json();
  }

  async verifyCreditSeries(serialNumber: string): Promise<VerraCredit> {
    const response = await fetch(`${this.baseURL}/credits/${serialNumber}`, {
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`Verra API error: ${response.statusText}`);
    }

    return response.json();
  }

  async submitForVerification(projectData: ProjectSubmission): Promise<SubmissionResult> {
    const response = await fetch(`${this.baseURL}/submissions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(projectData)
    });

    if (!response.ok) {
      throw new Error(`Verra API error: ${response.statusText}`);
    }

    return response.json();
  }
}
```

#### 5.1.2 Gold Standard Integration
```typescript
// Gold Standard API Client
class GoldStandardClient {
  private readonly baseURL = 'https://api.goldstandard.org/v2';
  private readonly credentials: GoldStandardCredentials;

  constructor(credentials: GoldStandardCredentials) {
    this.credentials = credentials;
  }

  async authenticate(): Promise<string> {
    const response = await fetch(`${this.baseURL}/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: this.credentials.username,
        password: this.credentials.password,
        client_id: this.credentials.clientId
      })
    });

    const data = await response.json();
    return data.access_token;
  }

  async getProjectDetails(projectId: string): Promise<GoldStandardProject> {
    const token = await this.authenticate();

    const response = await fetch(`${this.baseURL}/projects/${projectId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response.json();
  }
}
```

### 5.2 KYC/AML Service Integrations

#### 5.2.1 Jumio Integration
```typescript
// Jumio KYC Client
class JumioKYCClient {
  private readonly baseURL: string;
  private readonly credentials: JumioCredentials;

  constructor(credentials: JumioCredentials, sandbox: boolean = false) {
    this.baseURL = sandbox
      ? 'https://netverify.com/api/netverify/v2'
      : 'https://lon.netverify.com/api/netverify/v2';
    this.credentials = credentials;
  }

  async initiateVerification(userInfo: UserKYCInfo): Promise<JumioSession> {
    const auth = Buffer.from(
      `${this.credentials.apiToken}:${this.credentials.apiSecret}`
    ).toString('base64');

    const response = await fetch(`${this.baseURL}/initiateNetverify`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Carbonix/1.0'
      },
      body: JSON.stringify({
        customerInternalReference: userInfo.userId,
        successUrl: `${process.env.APP_URL}/kyc/success`,
        errorUrl: `${process.env.APP_URL}/kyc/error`,
        callbackUrl: `${process.env.API_URL}/webhooks/jumio`,
        enabledFields: 'idNumber,idFirstName,idLastName,idDob,idExpiry,idUsState,idPersonalNumber',
        authorizationTokenLifetime: 5184000 // 60 days
      })
    });

    return response.json();
  }

  async getVerificationStatus(scanReference: string): Promise<JumioResult> {
    const auth = Buffer.from(
      `${this.credentials.apiToken}:${this.credentials.apiSecret}`
    ).toString('base64');

    const response = await fetch(`${this.baseURL}/scans/${scanReference}`, {
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/json'
      }
    });

    return response.json();
  }
}
```

### 5.3 Blockchain Network Integrations

#### 5.3.1 Multi-chain Wallet Management
```typescript
// Multi-chain Wallet Service
class MultiChainWalletService {
  private readonly providers: Map<string, ethers.Provider>;
  private readonly configs: Map<string, NetworkConfig>;

  constructor() {
    this.providers = new Map();
    this.configs = new Map();
    this.initializeNetworks();
  }

  private initializeNetworks() {
    const networks = [
      { name: 'ethereum', chainId: 1, rpc: process.env.ETHEREUM_RPC },
      { name: 'polygon', chainId: 137, rpc: process.env.POLYGON_RPC },
      { name: 'arbitrum', chainId: 42161, rpc: process.env.ARBITRUM_RPC },
      { name: 'optimism', chainId: 10, rpc: process.env.OPTIMISM_RPC },
      { name: 'base', chainId: 8453, rpc: process.env.BASE_RPC }
    ];

    networks.forEach(network => {
      const provider = new ethers.JsonRpcProvider(network.rpc);
      this.providers.set(network.name, provider);
      this.configs.set(network.name, {
        chainId: network.chainId,
        name: network.name,
        rpcUrl: network.rpc
      });
    });
  }

  async createWallet(network: string): Promise<WalletInfo> {
    const provider = this.providers.get(network);
    if (!provider) {
      throw new Error(`Unsupported network: ${network}`);
    }

    const wallet = ethers.Wallet.createRandom();
    const encryptedPrivateKey = await this.encryptPrivateKey(wallet.privateKey);

    return {
      address: wallet.address,
      network,
      chainId: this.configs.get(network)!.chainId,
      encryptedPrivateKey,
      publicKey: wallet.publicKey
    };
  }

  async sendTransaction(
    walletId: string,
    to: string,
    amount: string,
    network: string
  ): Promise<TransactionResult> {
    const provider = this.providers.get(network);
    const walletInfo = await this.getWalletInfo(walletId);

    const privateKey = await this.decryptPrivateKey(walletInfo.encryptedPrivateKey);
    const wallet = new ethers.Wallet(privateKey, provider);

    const transaction = {
      to,
      value: ethers.parseEther(amount),
      gasLimit: 21000,
      gasPrice: await provider.getFeeData().then(fee => fee.gasPrice)
    };

    const tx = await wallet.sendTransaction(transaction);

    return {
      hash: tx.hash,
      from: wallet.address,
      to,
      amount,
      network,
      status: 'PENDING'
    };
  }
}
```

---

## 6. Performance Requirements

### 6.1 Response Time Requirements

#### 6.1.1 API Performance Targets
```typescript
// Performance SLA Definitions
interface PerformanceSLA {
  endpoint: string;
  p50: number; // 50th percentile response time (ms)
  p95: number; // 95th percentile response time (ms)
  p99: number; // 99th percentile response time (ms)
  availability: number; // Uptime percentage
}

const performanceTargets: PerformanceSLA[] = [
  {
    endpoint: '/api/v1/auth/*',
    p50: 100,
    p95: 300,
    p99: 500,
    availability: 99.9
  },
  {
    endpoint: '/api/v1/projects',
    p50: 150,
    p95: 400,
    p99: 800,
    availability: 99.9
  },
  {
    endpoint: '/api/v1/carbon-credits',
    p50: 200,
    p95: 500,
    p99: 1000,
    availability: 99.9
  },
  {
    endpoint: '/api/v1/marketplace/*',
    p50: 100,
    p95: 250,
    p99: 500,
    availability: 99.95
  },
  {
    endpoint: '/api/v1/wallet/*',
    p50: 300,
    p95: 1000,
    p99: 2000,
    availability: 99.9
  }
];
```

#### 6.1.2 Database Performance Optimization
```typescript
// Database Query Optimization
class DatabaseOptimizer {
  // Connection pooling configuration
  static readonly POOL_CONFIG = {
    min: 5,
    max: 20,
    acquireTimeoutMillis: 30000,
    createTimeoutMillis: 30000,
    destroyTimeoutMillis: 5000,
    idleTimeoutMillis: 30000,
    reapIntervalMillis: 1000,
    createRetryIntervalMillis: 100
  };

  // Query performance monitoring
  static async executeWithMetrics<T>(
    query: () => Promise<T>,
    queryName: string
  ): Promise<T> {
    const startTime = Date.now();

    try {
      const result = await query();
      const duration = Date.now() - startTime;

      // Log slow queries
      if (duration > 1000) {
        logger.warn(`Slow query detected: ${queryName} took ${duration}ms`);
      }

      // Record metrics
      metrics.histogram('database.query.duration', duration, {
        query: queryName,
        status: 'success'
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;

      metrics.histogram('database.query.duration', duration, {
        query: queryName,
        status: 'error'
      });

      throw error;
    }
  }

  // Optimized pagination
  static createCursorPagination<T>(
    query: any,
    cursor?: string,
    limit: number = 20
  ) {
    if (cursor) {
      query = query.where({
        id: {
          gt: cursor
        }
      });
    }

    return query
      .take(limit + 1)
      .orderBy({ id: 'asc' });
  }
}
```

### 6.2 Scalability Architecture

#### 6.2.1 Horizontal Scaling Strategy
```typescript
// Load Balancer Configuration
interface LoadBalancerConfig {
  algorithm: 'round-robin' | 'least-connections' | 'ip-hash';
  healthCheck: {
    path: string;
    interval: number;
    timeout: number;
    retries: number;
  };
  instances: ServerInstance[];
}

const loadBalancerConfig: LoadBalancerConfig = {
  algorithm: 'least-connections',
  healthCheck: {
    path: '/health',
    interval: 30000, // 30 seconds
    timeout: 5000,   // 5 seconds
    retries: 3
  },
  instances: [
    { host: 'app-1.carbonix.com', port: 3000, weight: 1 },
    { host: 'app-2.carbonix.com', port: 3000, weight: 1 },
    { host: 'app-3.carbonix.com', port: 3000, weight: 1 }
  ]
};

// Auto-scaling configuration
interface AutoScalingConfig {
  minInstances: number;
  maxInstances: number;
  targetCPUUtilization: number;
  targetMemoryUtilization: number;
  scaleUpCooldown: number;
  scaleDownCooldown: number;
}

const autoScalingConfig: AutoScalingConfig = {
  minInstances: 2,
  maxInstances: 10,
  targetCPUUtilization: 70,
  targetMemoryUtilization: 80,
  scaleUpCooldown: 300,   // 5 minutes
  scaleDownCooldown: 600  // 10 minutes
};
```

#### 6.2.2 Caching Strategy
```typescript
// Multi-level Caching Implementation
class CacheManager {
  private readonly redis: Redis;
  private readonly memoryCache: Map<string, CacheEntry>;

  constructor(redisClient: Redis) {
    this.redis = redisClient;
    this.memoryCache = new Map();
  }

  async get<T>(key: string): Promise<T | null> {
    // Level 1: Memory cache
    const memoryEntry = this.memoryCache.get(key);
    if (memoryEntry && !this.isExpired(memoryEntry)) {
      return memoryEntry.value;
    }

    // Level 2: Redis cache
    const redisValue = await this.redis.get(key);
    if (redisValue) {
      const value = JSON.parse(redisValue);
      this.setMemoryCache(key, value, 300); // 5 minutes
      return value;
    }

    return null;
  }

  async set<T>(key: string, value: T, ttl: number): Promise<void> {
    // Set in both caches
    await this.redis.setex(key, ttl, JSON.stringify(value));
    this.setMemoryCache(key, value, Math.min(ttl, 300));
  }

  private setMemoryCache<T>(key: string, value: T, ttl: number): void {
    this.memoryCache.set(key, {
      value,
      expiresAt: Date.now() + (ttl * 1000)
    });
  }

  private isExpired(entry: CacheEntry): boolean {
    return Date.now() > entry.expiresAt;
  }
}

// Cache key strategies
class CacheKeys {
  static user(userId: string): string {
    return `user:${userId}`;
  }

  static userPermissions(userId: string, orgId: string): string {
    return `permissions:${userId}:${orgId}`;
  }

  static project(projectId: string): string {
    return `project:${projectId}`;
  }

  static carbonCredit(creditId: string): string {
    return `credit:${creditId}`;
  }

  static marketData(symbol: string): string {
    return `market:${symbol}`;
  }

  static organizationProjects(orgId: string, page: number): string {
    return `org:${orgId}:projects:${page}`;
  }
}
```

### 6.3 Performance Monitoring

#### 6.3.1 Application Performance Monitoring
```typescript
// APM Integration
import { NodeSDK } from '@opentelemetry/sdk-node';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';

const sdk = new NodeSDK({
  instrumentations: [getNodeAutoInstrumentations()],
  serviceName: 'carbonix-api',
  serviceVersion: process.env.APP_VERSION
});

sdk.start();

// Custom metrics collection
class MetricsCollector {
  private readonly prometheus = require('prom-client');

  constructor() {
    // Create custom metrics
    this.createMetrics();
  }

  private createMetrics() {
    // HTTP request duration
    this.httpDuration = new this.prometheus.Histogram({
      name: 'http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10]
    });

    // Database query duration
    this.dbQueryDuration = new this.prometheus.Histogram({
      name: 'database_query_duration_seconds',
      help: 'Duration of database queries in seconds',
      labelNames: ['query_type', 'table'],
      buckets: [0.01, 0.05, 0.1, 0.3, 0.5, 1, 3, 5]
    });

    // Active connections
    this.activeConnections = new this.prometheus.Gauge({
      name: 'active_connections_total',
      help: 'Number of active connections',
      labelNames: ['type']
    });

    // Business metrics
    this.transactionVolume = new this.prometheus.Counter({
      name: 'transaction_volume_total',
      help: 'Total transaction volume',
      labelNames: ['currency', 'type']
    });
  }

  recordHttpRequest(method: string, route: string, statusCode: number, duration: number) {
    this.httpDuration
      .labels(method, route, statusCode.toString())
      .observe(duration / 1000);
  }

  recordDatabaseQuery(queryType: string, table: string, duration: number) {
    this.dbQueryDuration
      .labels(queryType, table)
      .observe(duration / 1000);
  }
}
```

---

## 7. Deployment Architecture

### 7.1 Container Architecture

#### 7.1.1 Docker Configuration
```dockerfile
# Multi-stage Dockerfile for production
FROM node:20-alpine AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

FROM node:20-alpine AS build
WORKDIR /app
COPY package*.json ./
RUN npm ci
COPY . .
RUN npm run build

FROM node:20-alpine AS runtime
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

# Copy built application
COPY --from=build --chown=nextjs:nodejs /app/.next ./.next
COPY --from=build --chown=nextjs:nodejs /app/public ./public
COPY --from=base --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --chown=nextjs:nodejs package.json ./

USER nextjs

EXPOSE 3000
ENV PORT 3000
ENV NODE_ENV production

CMD ["npm", "start"]
```

#### 7.1.2 Docker Compose Configuration
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: runtime
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - REDIS_URL=${REDIS_URL}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER}"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 7.2 Kubernetes Deployment

#### 7.2.1 Kubernetes Manifests
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: carbonix-app
  labels:
    app: carbonix-app
spec:
  replicas: 3
  selector:
    matchLabels:
      app: carbonix-app
  template:
    metadata:
      labels:
        app: carbonix-app
    spec:
      containers:
      - name: app
        image: carbonix/app:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: carbonix-secrets
              key: database-url
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: carbonix-secrets
              key: nextauth-secret
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /ready
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: carbonix-app-service
spec:
  selector:
    app: carbonix-app
  ports:
  - protocol: TCP
    port: 80
    targetPort: 3000
  type: ClusterIP

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: carbonix-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.carbonix.com
    secretName: carbonix-tls
  rules:
  - host: api.carbonix.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: carbonix-app-service
            port:
              number: 80
```

### 7.3 CI/CD Pipeline

#### 7.3.1 GitHub Actions Workflow
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run linting
      run: npm run lint

    - name: Run type checking
      run: npm run type-check

    - name: Run tests
      run: npm run test:coverage

    - name: Upload coverage
      uses: codecov/codecov-action@v3

  build:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v3

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    - name: Login to Container Registry
      uses: docker/login-action@v2
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push Docker image
      uses: docker/build-push-action@v4
      with:
        context: .
        push: true
        tags: |
          ghcr.io/carbonix/app:latest
          ghcr.io/carbonix/app:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - uses: actions/checkout@v3

    - name: Deploy to Kubernetes
      uses: azure/k8s-deploy@v1
      with:
        manifests: |
          k8s/deployment.yaml
          k8s/service.yaml
          k8s/ingress.yaml
        images: |
          ghcr.io/carbonix/app:${{ github.sha }}
        kubectl-version: 'latest'
```

---

## 8. Monitoring and Observability

### 8.1 Logging Strategy

#### 8.1.1 Structured Logging
```typescript
// Logging Configuration
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'carbonix-api',
    version: process.env.APP_VERSION
  },
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});

// Audit Logging
class AuditLogger {
  static logUserAction(
    userId: string,
    action: string,
    resource: string,
    details?: any
  ) {
    logger.info('User action', {
      type: 'audit',
      userId,
      action,
      resource,
      details,
      timestamp: new Date().toISOString(),
      ip: this.getClientIP(),
      userAgent: this.getUserAgent()
    });
  }

  static logSecurityEvent(
    event: string,
    severity: 'low' | 'medium' | 'high' | 'critical',
    details: any
  ) {
    logger.warn('Security event', {
      type: 'security',
      event,
      severity,
      details,
      timestamp: new Date().toISOString()
    });
  }

  static logBusinessEvent(
    event: string,
    data: any
  ) {
    logger.info('Business event', {
      type: 'business',
      event,
      data,
      timestamp: new Date().toISOString()
    });
  }
}
```

### 8.2 Health Checks

#### 8.2.1 Health Check Implementation
```typescript
// Health Check Service
class HealthCheckService {
  async checkHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkBlockchain(),
      this.checkExternalServices()
    ]);

    const results = checks.map((check, index) => ({
      name: ['database', 'redis', 'blockchain', 'external'][index],
      status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
      details: check.status === 'fulfilled' ? check.value : check.reason
    }));

    const overallStatus = results.every(r => r.status === 'healthy')
      ? 'healthy'
      : 'unhealthy';

    return {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.APP_VERSION,
      checks: results
    };
  }

  private async checkDatabase(): Promise<any> {
    const start = Date.now();
    await sequelize.query('SELECT 1');
    const duration = Date.now() - start;

    return {
      status: 'connected',
      responseTime: `${duration}ms`
    };
  }

  private async checkRedis(): Promise<any> {
    const start = Date.now();
    await redis.ping();
    const duration = Date.now() - start;

    return {
      status: 'connected',
      responseTime: `${duration}ms`
    };
  }

  private async checkBlockchain(): Promise<any> {
    const checks = await Promise.allSettled([
      this.checkEthereumNetwork(),
      this.checkPolygonNetwork()
    ]);

    return {
      ethereum: checks[0].status === 'fulfilled' ? 'connected' : 'disconnected',
      polygon: checks[1].status === 'fulfilled' ? 'connected' : 'disconnected'
    };
  }
}

// Health check endpoints
app.get('/health', async (req, res) => {
  try {
    const health = await healthCheckService.checkHealth();
    const statusCode = health.status === 'healthy' ? 200 : 503;
    res.status(statusCode).json(health);
  } catch (error) {
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

app.get('/ready', async (req, res) => {
  // Readiness check - can the service handle requests?
  try {
    await sequelize.query('SELECT 1');
    res.status(200).json({ status: 'ready' });
  } catch (error) {
    res.status(503).json({ status: 'not ready', error: error.message });
  }
});
```

### 8.3 Alerting and Monitoring

#### 8.3.1 Alert Configuration
```typescript
// Alert Rules Configuration
interface AlertRule {
  name: string;
  condition: string;
  threshold: number;
  duration: string;
  severity: 'warning' | 'critical';
  channels: string[];
}

const alertRules: AlertRule[] = [
  {
    name: 'High Error Rate',
    condition: 'error_rate > threshold',
    threshold: 5, // 5% error rate
    duration: '5m',
    severity: 'critical',
    channels: ['slack', 'email', 'pagerduty']
  },
  {
    name: 'High Response Time',
    condition: 'response_time_p95 > threshold',
    threshold: 1000, // 1 second
    duration: '10m',
    severity: 'warning',
    channels: ['slack']
  },
  {
    name: 'Database Connection Issues',
    condition: 'database_connections_failed > threshold',
    threshold: 10,
    duration: '2m',
    severity: 'critical',
    channels: ['slack', 'email', 'pagerduty']
  },
  {
    name: 'Low Disk Space',
    condition: 'disk_usage_percent > threshold',
    threshold: 85,
    duration: '5m',
    severity: 'warning',
    channels: ['slack', 'email']
  }
];

// Notification Service
class NotificationService {
  async sendAlert(alert: Alert) {
    const promises = alert.channels.map(channel => {
      switch (channel) {
        case 'slack':
          return this.sendSlackAlert(alert);
        case 'email':
          return this.sendEmailAlert(alert);
        case 'pagerduty':
          return this.sendPagerDutyAlert(alert);
        default:
          logger.warn(`Unknown alert channel: ${channel}`);
      }
    });

    await Promise.allSettled(promises);
  }

  private async sendSlackAlert(alert: Alert) {
    const webhook = process.env.SLACK_WEBHOOK_URL;
    const message = {
      text: `🚨 ${alert.severity.toUpperCase()}: ${alert.name}`,
      attachments: [{
        color: alert.severity === 'critical' ? 'danger' : 'warning',
        fields: [
          { title: 'Service', value: 'Carbonix API', short: true },
          { title: 'Environment', value: process.env.NODE_ENV, short: true },
          { title: 'Threshold', value: alert.threshold.toString(), short: true },
          { title: 'Current Value', value: alert.currentValue.toString(), short: true }
        ],
        ts: Math.floor(Date.now() / 1000)
      }]
    };

    await fetch(webhook, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(message)
    });
  }
}
```

---

## 9. Broker System Technical Specifications

### 9.1 Broker Registration and KYC Service

#### 9.1.1 Broker Registration API
```typescript
// Broker Registration Service
class BrokerRegistrationService {
  async registerBroker(data: BrokerRegistrationData): Promise<BrokerRegistrationResult> {
    // Validate input data
    const validatedData = await this.validateRegistrationData(data);

    // Create user account
    const user = await this.createBrokerUser(validatedData);

    // Create broker profile
    const broker = await this.createBrokerProfile(user.id, validatedData);

    // Initialize KYC process
    await this.initializeKYCProcess(broker.id);

    // Send verification email
    await this.sendVerificationEmail(user.email);

    return {
      brokerId: broker.id,
      userId: user.id,
      status: 'PENDING_VERIFICATION',
      nextSteps: ['EMAIL_VERIFICATION', 'KYC_DOCUMENTS', '2FA_SETUP']
    };
  }

  async submitKYCDocuments(
    brokerId: string,
    documents: KYCDocument[]
  ): Promise<KYCSubmissionResult> {
    // Validate document types and formats
    await this.validateDocuments(documents);

    // Upload documents to secure storage
    const uploadedDocs = await this.uploadDocuments(documents);

    // Create KYC records
    const kycRecords = await Promise.all(
      uploadedDocs.map(doc => this.createKYCRecord(brokerId, doc))
    );

    // Trigger automated verification
    await this.triggerAutomatedVerification(kycRecords);

    return {
      submissionId: generateId(),
      documentsUploaded: kycRecords.length,
      status: 'UNDER_REVIEW',
      estimatedReviewTime: '24-48 hours'
    };
  }

  async setup2FA(brokerId: string, method: '2FA_METHOD'): Promise<TwoFactorSetupResult> {
    const broker = await this.getBroker(brokerId);

    switch (method) {
      case 'TOTP':
        return await this.setupTOTP(broker);
      case 'SMS':
        return await this.setupSMS(broker);
      case 'EMAIL':
        return await this.setupEmail2FA(broker);
      default:
        throw new Error('Unsupported 2FA method');
    }
  }
}
```

#### 9.1.2 Operating Model Configuration
```typescript
// Operating Model Service
class BrokerOperatingModelService {
  async setOperatingModel(
    brokerId: string,
    model: BrokerOperatingModel,
    configuration: OperatingModelConfig
  ): Promise<void> {
    const broker = await this.getBroker(brokerId);

    // Validate model configuration
    await this.validateModelConfiguration(model, configuration);

    // Update broker operating model
    await this.updateBrokerModel(brokerId, model);

    // Configure model-specific settings
    await this.configureModelSettings(brokerId, configuration);

    // Set up appropriate permissions
    await this.configurePermissions(brokerId, model);

    // Initialize model-specific features
    await this.initializeModelFeatures(brokerId, model);
  }

  async getModelCapabilities(model: BrokerOperatingModel): Promise<ModelCapabilities> {
    const capabilities = {
      [BrokerOperatingModel.INDEPENDENT]: {
        clientManagement: true,
        projectCreation: true,
        marketplaceAccess: true,
        directTrading: true,
        commissionTracking: true,
        fullDashboard: true
      },
      [BrokerOperatingModel.ONIX_MANAGED]: {
        clientManagement: false,
        projectCreation: false,
        marketplaceAccess: false,
        directTrading: false,
        commissionTracking: true,
        fullDashboard: false,
        dataSubmission: true,
        statusTracking: true
      }
    };

    return capabilities[model];
  }
}
```

### 9.2 Commission Management System

#### 9.2.1 Commission Calculation Engine
```typescript
// Commission Calculation Service
class CommissionCalculationService {
  async calculateCommission(
    brokerId: string,
    transaction: Transaction,
    commissionStructure: CommissionStructure
  ): Promise<CommissionCalculation> {
    const broker = await this.getBroker(brokerId);
    const baseAmount = transaction.amount;

    let commissionAmount: Decimal;

    switch (commissionStructure.type) {
      case 'PERCENTAGE':
        commissionAmount = baseAmount.mul(commissionStructure.rate);
        break;
      case 'FIXED':
        commissionAmount = new Decimal(commissionStructure.fixedAmount);
        break;
      case 'TIERED':
        commissionAmount = this.calculateTieredCommission(baseAmount, commissionStructure.tiers);
        break;
      default:
        throw new Error('Unknown commission structure type');
    }

    // Apply any caps or minimums
    commissionAmount = this.applyCommissionLimits(commissionAmount, commissionStructure);

    return {
      brokerId,
      transactionId: transaction.id,
      baseAmount,
      commissionAmount,
      commissionRate: commissionStructure.rate,
      calculatedAt: new Date(),
      status: 'CALCULATED'
    };
  }

  async processCommissionPayment(commissionId: string): Promise<PaymentResult> {
    const commission = await this.getCommission(commissionId);
    const broker = await this.getBroker(commission.brokerId);

    // Validate payment eligibility
    await this.validatePaymentEligibility(commission);

    // Process payment to broker wallet
    const paymentResult = await this.processPayment(
      commission.commissionAmount,
      broker.walletAddress
    );

    // Update commission status
    await this.updateCommissionStatus(commissionId, 'PAID', paymentResult.transactionHash);

    // Send payment notification
    await this.sendPaymentNotification(broker, commission, paymentResult);

    return paymentResult;
  }
}
```

### 9.3 Client Management System

#### 9.3.1 Client Onboarding Service
```typescript
// Client Onboarding Service
class BrokerClientOnboardingService {
  async inviteClient(
    brokerId: string,
    clientData: ClientInvitationData
  ): Promise<ClientInvitationResult> {
    const broker = await this.getBroker(brokerId);

    // Validate broker permissions
    await this.validateBrokerPermissions(broker, 'CLIENT_INVITATION');

    // Create client invitation
    const invitation = await this.createClientInvitation(brokerId, clientData);

    // Generate secure onboarding link
    const onboardingLink = await this.generateOnboardingLink(invitation.id);

    // Send invitation email
    await this.sendInvitationEmail(clientData.email, onboardingLink, broker);

    return {
      invitationId: invitation.id,
      clientEmail: clientData.email,
      onboardingLink,
      expiresAt: invitation.expiresAt,
      status: 'SENT'
    };
  }

  async trackOnboardingProgress(
    brokerId: string,
    clientId: string
  ): Promise<OnboardingProgress> {
    const onboardingSteps = await this.getOnboardingSteps(clientId);

    const progress = {
      totalSteps: onboardingSteps.length,
      completedSteps: onboardingSteps.filter(step => step.completed).length,
      currentStep: onboardingSteps.find(step => !step.completed),
      overallProgress: 0,
      estimatedCompletion: null as Date | null
    };

    progress.overallProgress = (progress.completedSteps / progress.totalSteps) * 100;

    if (progress.currentStep) {
      progress.estimatedCompletion = this.estimateCompletionTime(progress.currentStep);
    }

    return progress;
  }
}
```

---

## 10. Enhanced Features Technical Implementation

### 10.1 Carbon Credit Calculation Smart Contracts

#### 10.1.1 Smart Contract Architecture
```solidity
// Carbon Credit Calculation Smart Contract
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/access/AccessControl.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/math/SafeMath.sol";

contract CarbonCreditCalculator is AccessControl, ReentrancyGuard {
    using SafeMath for uint256;

    bytes32 public constant CALCULATOR_ROLE = keccak256("CALCULATOR_ROLE");
    bytes32 public constant VERIFIER_ROLE = keccak256("VERIFIER_ROLE");

    enum Methodology { GOLD_STANDARD, VCS, CAR, ACR }
    enum DataUnit { KWH, TCO2E, MWH, CUBIC_METERS }

    struct CalculationInput {
        uint256 quantity;
        DataUnit unit;
        Methodology methodology;
        uint256 emissionFactor;
        uint256 baselineEmissions;
        bytes32 projectId;
    }

    struct CalculationResult {
        uint256 carbonCredits;
        uint256 inputQuantity;
        DataUnit inputUnit;
        Methodology methodology;
        uint256 calculationTimestamp;
        address calculatedBy;
        bool verified;
        address verifiedBy;
    }

    mapping(bytes32 => CalculationResult) public calculations;
    mapping(Methodology => mapping(DataUnit => uint256)) public conversionFactors;

    event CalculationPerformed(
        bytes32 indexed calculationId,
        bytes32 indexed projectId,
        uint256 carbonCredits,
        Methodology methodology
    );

    event CalculationVerified(
        bytes32 indexed calculationId,
        address indexed verifier
    );

    constructor() {
        _grantRole(DEFAULT_ADMIN_ROLE, msg.sender);
        _initializeConversionFactors();
    }

    function calculateCarbonCredits(
        CalculationInput memory input
    ) external onlyRole(CALCULATOR_ROLE) returns (bytes32 calculationId) {
        calculationId = keccak256(
            abi.encodePacked(
                input.projectId,
                input.quantity,
                input.unit,
                block.timestamp,
                msg.sender
            )
        );

        uint256 standardizedQuantity = _convertToStandardUnit(input.quantity, input.unit);
        uint256 carbonCredits = _applyMethodology(standardizedQuantity, input);

        calculations[calculationId] = CalculationResult({
            carbonCredits: carbonCredits,
            inputQuantity: input.quantity,
            inputUnit: input.unit,
            methodology: input.methodology,
            calculationTimestamp: block.timestamp,
            calculatedBy: msg.sender,
            verified: false,
            verifiedBy: address(0)
        });

        emit CalculationPerformed(calculationId, input.projectId, carbonCredits, input.methodology);
        return calculationId;
    }

    function verifyCalculation(
        bytes32 calculationId
    ) external onlyRole(VERIFIER_ROLE) {
        require(calculations[calculationId].calculationTimestamp > 0, "Calculation not found");
        require(!calculations[calculationId].verified, "Already verified");

        calculations[calculationId].verified = true;
        calculations[calculationId].verifiedBy = msg.sender;

        emit CalculationVerified(calculationId, msg.sender);
    }

    function _convertToStandardUnit(
        uint256 quantity,
        DataUnit unit
    ) internal pure returns (uint256) {
        // Convert all units to tCO2e equivalent
        if (unit == DataUnit.TCO2E) {
            return quantity;
        } else if (unit == DataUnit.KWH) {
            return quantity.mul(1e15).div(1e18); // Example conversion factor
        } else if (unit == DataUnit.MWH) {
            return quantity.mul(1e18).div(1e18); // Example conversion factor
        }
        // Add more conversions as needed
        return quantity;
    }

    function _applyMethodology(
        uint256 standardizedQuantity,
        CalculationInput memory input
    ) internal pure returns (uint256) {
        // Apply methodology-specific calculations
        if (input.methodology == Methodology.GOLD_STANDARD) {
            return _goldStandardCalculation(standardizedQuantity, input);
        } else if (input.methodology == Methodology.VCS) {
            return _vcsCalculation(standardizedQuantity, input);
        }
        // Add more methodologies
        return standardizedQuantity;
    }

    function _goldStandardCalculation(
        uint256 quantity,
        CalculationInput memory input
    ) internal pure returns (uint256) {
        // Gold Standard specific calculation logic
        uint256 emissionReduction = input.baselineEmissions.sub(
            quantity.mul(input.emissionFactor).div(1e18)
        );
        return emissionReduction;
    }

    function _vcsCalculation(
        uint256 quantity,
        CalculationInput memory input
    ) internal pure returns (uint256) {
        // VCS specific calculation logic
        return quantity.mul(input.emissionFactor).div(1e18);
    }
}
```

#### 10.1.2 Smart Contract Integration Service
```typescript
// Smart Contract Integration Service
class CarbonCreditCalculationService {
  private contract: Contract;
  private provider: Provider;

  constructor() {
    this.provider = new ethers.providers.AlchemyProvider(
      process.env.ETHEREUM_NETWORK,
      process.env.ALCHEMY_API_KEY
    );

    this.contract = new ethers.Contract(
      process.env.CALCULATION_CONTRACT_ADDRESS,
      CALCULATION_CONTRACT_ABI,
      this.provider
    );
  }

  async calculateCarbonCredits(
    projectId: string,
    inputData: CalculationInputData
  ): Promise<CalculationResult> {
    // Prepare input for smart contract
    const contractInput = this.prepareContractInput(inputData);

    // Get signer for transaction
    const signer = await this.getSigner();
    const contractWithSigner = this.contract.connect(signer);

    // Execute calculation on blockchain
    const tx = await contractWithSigner.calculateCarbonCredits(contractInput);
    const receipt = await tx.wait();

    // Extract calculation ID from events
    const calculationId = this.extractCalculationId(receipt);

    // Store calculation record in database
    await this.storeCalculationRecord(projectId, calculationId, inputData, receipt);

    return {
      calculationId,
      transactionHash: receipt.transactionHash,
      blockNumber: receipt.blockNumber,
      carbonCredits: await this.getCalculationResult(calculationId),
      status: 'CALCULATED'
    };
  }

  async verifyCalculation(
    calculationId: string,
    verifierId: string
  ): Promise<VerificationResult> {
    const signer = await this.getVerifierSigner(verifierId);
    const contractWithSigner = this.contract.connect(signer);

    const tx = await contractWithSigner.verifyCalculation(calculationId);
    const receipt = await tx.wait();

    // Update database record
    await this.updateCalculationVerification(calculationId, verifierId, receipt);

    return {
      calculationId,
      verifiedBy: verifierId,
      transactionHash: receipt.transactionHash,
      verifiedAt: new Date(),
      status: 'VERIFIED'
    };
  }
}
```

### 10.2 INR Valuation Service

#### 10.2.1 Real-time INR Integration
```typescript
// INR Valuation Service
class INRValuationService {
  private exchangeAPIs: ExchangeAPI[];
  private redis: Redis;

  constructor() {
    this.exchangeAPIs = [
      new CoinGeckoAPI(),
      new CoinMarketCapAPI(),
      new ExchangeRateAPI()
    ];
    this.redis = new Redis(process.env.REDIS_URL);
  }

  async updateINRValuations(): Promise<void> {
    // Get current carbon credit market prices
    const marketPrices = await this.getMarketPrices();

    // Get current USD to INR exchange rate
    const usdToInr = await this.getUSDToINRRate();

    // Calculate INR valuations for all active credits
    const credits = await this.getActiveCarbonCredits();

    for (const credit of credits) {
      const inrValue = this.calculateINRValue(credit, marketPrices, usdToInr);
      await this.storeINRValuation(credit.id, inrValue);
    }

    // Cache results for fast access
    await this.cacheValuations(credits);
  }

  async getINRValuation(carbonCreditId: string): Promise<INRValuation> {
    // Try cache first
    const cached = await this.redis.get(`inr_valuation:${carbonCreditId}`);
    if (cached) {
      return JSON.parse(cached);
    }

    // Calculate fresh valuation
    const credit = await this.getCarbonCredit(carbonCreditId);
    const marketPrice = await this.getCurrentMarketPrice(credit.type);
    const usdToInr = await this.getUSDToINRRate();

    const valuation = {
      carbonCreditId,
      inrValue: marketPrice * usdToInr * credit.quantity,
      usdValue: marketPrice * credit.quantity,
      exchangeRate: usdToInr,
      marketPrice,
      lastUpdated: new Date(),
      source: 'REAL_TIME'
    };

    // Cache for 5 minutes
    await this.redis.setex(
      `inr_valuation:${carbonCreditId}`,
      300,
      JSON.stringify(valuation)
    );

    return valuation;
  }

  async getHistoricalTrends(
    carbonCreditId: string,
    period: 'DAILY' | 'WEEKLY' | 'MONTHLY'
  ): Promise<ValuationTrend[]> {
    const endDate = new Date();
    const startDate = this.getStartDate(endDate, period);

    const historicalData = await this.getHistoricalValuations(
      carbonCreditId,
      startDate,
      endDate
    );

    return this.calculateTrends(historicalData, period);
  }

  private async getUSDToINRRate(): Promise<number> {
    const cacheKey = 'usd_to_inr_rate';
    const cached = await this.redis.get(cacheKey);

    if (cached) {
      return parseFloat(cached);
    }

    // Fetch from multiple sources for reliability
    const rates = await Promise.allSettled([
      this.exchangeAPIs[0].getUSDToINR(),
      this.exchangeAPIs[1].getUSDToINR(),
      this.exchangeAPIs[2].getUSDToINR()
    ]);

    const validRates = rates
      .filter(result => result.status === 'fulfilled')
      .map(result => (result as PromiseFulfilledResult<number>).value);

    if (validRates.length === 0) {
      throw new Error('Unable to fetch USD to INR exchange rate');
    }

    // Use median rate for stability
    const medianRate = this.calculateMedian(validRates);

    // Cache for 1 minute
    await this.redis.setex(cacheKey, 60, medianRate.toString());

    return medianRate;
  }
}
```

---

**Document Control**
- **Version**: 2.0
- **Last Updated**: June 25, 2025
- **Previous Version**: 1.0 (December 24, 2024)
- **Next Review**: September 25, 2025
- **Owner**: Engineering Team
- **Contributors**: Broker System Team, Smart Contract Team, Enhanced Features Team
- **Stakeholders**: Architecture, Security, DevOps, Product Management, Broker Network Team
