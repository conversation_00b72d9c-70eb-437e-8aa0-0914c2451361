"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Pagination, PaginationContent, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "@/components/ui/pagination";
import { RefreshCw, Filter, Download, Calendar, Edit, Trash2, MoreHorizontal } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { format } from "date-fns";
import { toast } from "@/components/ui/use-toast";
import { EditUnitLogModal } from "./edit-unit-log-modal";
import { DeleteUnitLogDialog } from "./delete-unit-log-dialog";
import { PROJECT_ENDPOINTS, SPV_ENDPOINTS } from "@/lib/api-endpoints";
import { getVerificationStatusInfo } from "@/lib/utils/verification-status";

// Helper function to get project type descriptions
const getProjectTypeDescription = (projectType?: string) => {
  const descriptions = {
    "RENEWABLE_ENERGY": "Renewable energy generation and monitoring data",
    "ENERGY_EFFICIENCY": "Energy efficiency and consumption monitoring data",
    "FORESTRY": "Forest conservation and carbon sequestration data",
    "METHANE_REDUCTION": "Methane capture and reduction monitoring data",
    "WASTE_MANAGEMENT": "Waste processing and emission reduction data",
    "AGRICULTURE": "Agricultural practices and emission monitoring data",
    "TRANSPORTATION": "Transportation efficiency and emission data",
    "INDUSTRIAL": "Industrial process and emission monitoring data",
    "OTHER": "Project monitoring and measurement data"
  };
  return descriptions[projectType as keyof typeof descriptions] || "Project monitoring and measurement data";
};

// Helper function to get quantity labels for different project types
const getQuantityLabel = (projectType?: string) => {
  const labels = {
    "RENEWABLE_ENERGY": "Generation (MWh)",
    "ENERGY_EFFICIENCY": "Savings (MWh)",
    "FORESTRY": "Carbon Sequestered (tCO2)",
    "METHANE_REDUCTION": "Methane Reduced (tCH4)",
    "WASTE_MANAGEMENT": "Waste Processed (tonnes)",
    "AGRICULTURE": "Area/Volume",
    "TRANSPORTATION": "Distance/Fuel Saved",
    "INDUSTRIAL": "Production/Efficiency",
    "OTHER": "Quantity"
  };
  return labels[projectType as keyof typeof labels] || "Quantity";
};

// Helper function to get unit labels
const getUnitLabel = (projectType?: string, unitType?: string) => {
  if (unitType) return unitType;

  const defaultUnits = {
    "RENEWABLE_ENERGY": "MWh",
    "ENERGY_EFFICIENCY": "MWh",
    "FORESTRY": "tCO2",
    "METHANE_REDUCTION": "tCH4",
    "WASTE_MANAGEMENT": "tonnes",
    "AGRICULTURE": "hectares",
    "TRANSPORTATION": "km",
    "INDUSTRIAL": "units",
    "OTHER": "units"
  };
  return defaultUnits[projectType as keyof typeof defaultUnits] || "units";
};

interface UnitLog {
  id: string;
  logDate: string;
  frequency: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  verificationStatus: string;

  metadata?: {
    generationType?: string;
    fileName?: string;
    [key: string]: any;
  };
  logger?: {
    id: string;
    name: string;
    email: string;
  };
}

interface MonitoringDataTableProps {
  projectId: string;
  projectType?: string;
  projectSubType?: string;
  refreshTrigger?: number;
  onDataChange?: () => void;
  apiContext?: 'spv' | 'dashboard'; // Add context to determine which API to use
}

export function MonitoringDataTable({
  projectId,
  projectType,
  projectSubType,
  refreshTrigger = 0,
  onDataChange,
  apiContext = 'dashboard'
}: MonitoringDataTableProps) {
  const [unitLogs, setUnitLogs] = useState<UnitLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState("all");
  const [sourceFilter, setSourceFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [apiTotalGeneration, setApiTotalGeneration] = useState(0);
  const [apiVerifiedCount, setApiVerifiedCount] = useState(0);
  const [apiPendingCount, setApiPendingCount] = useState(0);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedUnitLog, setSelectedUnitLog] = useState<UnitLog | null>(null);
  const itemsPerPage = 5;

  // Check if this is a hybrid renewable project
  const isHybridProject = projectType === "RENEWABLE_ENERGY" && projectSubType === "HYBRID_RENEWABLE";

  // Filter handlers that reset pagination
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  const handleSourceFilterChange = (value: string) => {
    setSourceFilter(value);
    setCurrentPage(1);
  };

  // Action handlers
  const handleEdit = (logId: string) => {
    console.log("Edit clicked for logId:", logId);
    const log = unitLogs.find(l => l.id === logId);
    console.log("Found log:", log);
    if (log) {
      setSelectedUnitLog(log);
      setEditModalOpen(true);
    }
  };

  const handleDelete = (logId: string) => {
    console.log("Delete clicked for logId:", logId);
    const log = unitLogs.find(l => l.id === logId);
    console.log("Found log:", log);
    if (log) {
      setSelectedUnitLog(log);
      setDeleteDialogOpen(true);
    }
  };



  useEffect(() => {
    fetchUnitLogs();
  }, [projectId, refreshTrigger, currentPage, statusFilter, sourceFilter]);

  const fetchUnitLogs = async () => {
    try {
      setIsLoading(true);
      // Build query parameters for pagination and filtering
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
      });

      if (statusFilter !== "all") {
        params.append("verificationStatus", statusFilter);
      }
      if (sourceFilter !== "all") {
        params.append("dataSource", sourceFilter);
      }

      // Use appropriate API endpoint based on context
      const apiEndpoint = apiContext === 'spv'
        ? SPV_ENDPOINTS.SPV_PROJECT_UNIT_LOGS({ id: projectId })
        : PROJECT_ENDPOINTS.PROJECT_UNIT_LOGS({ id: projectId });

      const response = await fetch(`${apiEndpoint}?${params.toString()}`);

      if (response.ok) {
        const data = await response.json();
        setUnitLogs(data.unitLogs || []);
        setTotalItems(data.pagination?.totalCount || 0);
        setTotalPages(data.pagination?.totalPages || 1);

        // Set summary data from API
        if (data.summary) {
          setApiTotalGeneration(data.summary.totalGeneration || 0);
          setApiVerifiedCount(data.summary.verifiedCount || 0);
          setApiPendingCount(data.summary.pendingCount || 0);
        }
      } else {
        const errorData = await response.json().catch(() => ({ error: "Unknown error" }));
        console.error("Failed to fetch unit logs:", {
          status: response.status,
          statusText: response.statusText,
          error: errorData
        });
        toast({
          title: "Error",
          description: errorData.error || "Failed to load monitoring data",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching unit logs:", error);
      toast({
        title: "Error",
        description: "Failed to load monitoring data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Use server-filtered data directly
  const filteredLogs = unitLogs;

  // Calculate number of rows (not individual entries)
  // For hybrid projects, group by date since each CSV row creates 3 entries with same date
  // For standard projects, each entry represents one row
  const calculateRowCount = (logs: UnitLog[]) => {
    const isHybridProject = projectType === "RENEWABLE_ENERGY" && projectSubType === "HYBRID_RENEWABLE";

    if (isHybridProject) {
      // Group by date to count unique CSV rows
      const uniqueDates = new Set(logs.map(log =>
        new Date(log.logDate).toISOString().split('T')[0]
      ));
      return uniqueDates.size;
    } else {
      // For standard projects, each entry is one row
      return logs.length;
    }
  };

  // Note: The API now returns the correct counts directly, no need for frontend calculation

  // Note: Status counts and total generation are now fetched from the API

  // Group hybrid data by date for better display
  const groupedData = isHybridProject ? 
    filteredLogs.reduce((acc, log) => {
      const date = format(new Date(log.logDate), "yyyy-MM-dd");
      if (!acc[date]) {
        acc[date] = {
          date,
          solar: null,
          wind: null,
          outgoing: null,
          status: log.verificationStatus,
          source: log.dataSource,
          logIds: [],
        };
      }
      
      // Add log ID to the group
      acc[date].logIds.push(log.id);

      const generationType = log.metadata?.generationType?.toLowerCase();
      if (generationType === 'solar') {
        acc[date].solar = log.quantity;
      } else if (generationType === 'wind') {
        acc[date].wind = log.quantity;
      } else if (generationType === 'outgoing') {
        acc[date].outgoing = log.quantity;
      }

      return acc;
    }, {} as Record<string, any>) : {};

  const getStatusBadge = (status: string) => {
    const statusInfo = getVerificationStatusInfo(status);

    const colorClasses = {
      "gray": "bg-gray-100 text-gray-800",
      "yellow": "bg-yellow-100 text-yellow-800",
      "green": "bg-green-100 text-green-800",
      "blue": "bg-blue-100 text-blue-800",
      "red": "bg-red-100 text-red-800"
    };

    const colorClass = colorClasses[statusInfo.color as keyof typeof colorClasses] || "bg-gray-100 text-gray-800";

    return (
      <Badge
        variant="outline"
        className={`${colorClass} border-0`}
        title={statusInfo.description}
      >
        <span className="mr-1">{statusInfo.icon}</span>
        {statusInfo.label}
      </Badge>
    );
  };

  const getSourceBadge = (source: string) => {
    switch (source) {
      case "MANUAL":
        return <Badge variant="outline" className="bg-green-50 text-green-700">Manual</Badge>;
      case "CSV_UPLOAD":
        return <Badge variant="outline" className="bg-purple-50 text-purple-700">CSV</Badge>;
      case "API_INTEGRATION":
        return <Badge variant="outline" className="bg-green-50 text-green-700">API</Badge>;
      case "IOT_DEVICE":
        return <Badge variant="outline" className="bg-orange-50 text-orange-700">IoT</Badge>;
      default:
        return <Badge variant="outline">{source}</Badge>;
    }
  };

  return (
    <>
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Data Entry</CardTitle>
            <CardDescription>
              {isHybridProject
                ? "Hybrid renewable energy generation data (Solar, Wind, Outgoing)"
                : getProjectTypeDescription(projectType)
              }
            </CardDescription>
          </div>
          <div className="flex items-center gap-3">
            <Select value={statusFilter} onValueChange={handleStatusFilterChange}>
              <SelectTrigger className="w-fit min-w-[120px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="DRAFT">📝 Draft</SelectItem>
                <SelectItem value="SUBMITTED_FOR_VERIFICATION">📤 Submitted for Verification</SelectItem>
                <SelectItem value="PM_VERIFIED">👤 PM Verified</SelectItem>
                <SelectItem value="SPV_ADMIN_VERIFIED">🏢 SPV Admin Verified</SelectItem>
                <SelectItem value="SUBMITTED_TO_ORG_ADMIN">📋 Submitted to Org Admin</SelectItem>
                <SelectItem value="VERIFIED">✅ Verified</SelectItem>
                <SelectItem value="ORG_APPROVED">🏛️ Org Approved</SelectItem>
                <SelectItem value="SPV_APPROVED">🔵 SPV Approved</SelectItem>
                <SelectItem value="SUBMITTED_TO_VVB">📊 Submitted to VVB</SelectItem>
                <SelectItem value="VVB_VERIFIED">🔬 VVB Verified</SelectItem>
                <SelectItem value="REJECTED">❌ Rejected</SelectItem>
                <SelectItem value="ORG_REJECTED">🚫 Org Rejected</SelectItem>
                <SelectItem value="VVB_REJECTED">⚠️ VVB Rejected</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sourceFilter} onValueChange={handleSourceFilterChange}>
              <SelectTrigger className="w-fit min-w-[120px]">
                <SelectValue placeholder="Source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="MANUAL">Manual</SelectItem>
                <SelectItem value="CSV_UPLOAD">CSV Upload</SelectItem>
                <SelectItem value="API_INTEGRATION">API</SelectItem>
                <SelectItem value="IOT_DEVICE">IoT Device</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" onClick={fetchUnitLogs} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin mr-2" />
            <span>Loading monitoring data...</span>
          </div>
        ) : filteredLogs.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-2 text-[16px] font-medium">No monitoring data found</h3>
            <p className="text-sm font-normal text-muted-foreground">
              {statusFilter !== "all" || sourceFilter !== "all"
                ? "Try adjusting your filter criteria"
                : "Start by adding monitoring data through manual entry or CSV upload"}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl text-green-700">{totalItems}</div>
                <div className="text-sm text-green-600">Total Entries</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl text-green-700">
                  {Math.ceil(apiVerifiedCount / (isHybridProject ? 3 : 1))}
                </div>
                <div className="text-sm text-green-600">Verified</div>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-2xl text-yellow-700">
                  {Math.ceil(apiPendingCount / (isHybridProject ? 3 : 1))}
                </div>
                <div className="text-sm text-yellow-600">Pending</div>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <div className="text-2xl text-purple-700">
                  {apiTotalGeneration.toLocaleString()}
                </div>
                <div className="text-sm text-purple-600">Total {getUnitLabel(projectType, filteredLogs[0]?.unitType)}</div>
              </div>
            </div>

            {/* Data Table */}
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="text-sm font-medium text-gray-700">Date</TableHead>
                  {isHybridProject ? (
                    <>
                      <TableHead className="text-sm font-medium text-gray-700">Solar (MW)</TableHead>
                      <TableHead className="text-sm font-medium text-gray-700">Wind (MW)</TableHead>
                      <TableHead className="text-sm font-medium text-gray-700">Outgoing (MW)</TableHead>
                    </>
                  ) : (
                    <>
                      <TableHead className="text-sm font-medium text-gray-700">Unit Type</TableHead>
                      <TableHead className="text-sm font-medium text-gray-700">{getQuantityLabel(projectType)}</TableHead>
                    </>
                  )}
                  <TableHead className="text-sm font-medium text-gray-700">Status</TableHead>
                  <TableHead className="text-sm font-medium text-gray-700">Source</TableHead>
                  <TableHead className="text-sm font-medium text-gray-700 w-24">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isHybridProject ? (
                  // Hybrid project display
                  Object.values(groupedData).map((row: any, index) => (
                    <TableRow key={index}>
                      <TableCell className="text-sm text-gray-900">{row.date}</TableCell>
                      <TableCell className="text-sm text-gray-700">{row.solar ? row.solar.toLocaleString() : '-'}</TableCell>
                      <TableCell className="text-sm text-gray-700">{row.wind ? row.wind.toLocaleString() : '-'}</TableCell>
                      <TableCell className="text-sm text-gray-700">{row.outgoing ? row.outgoing.toLocaleString() : '-'}</TableCell>
                      <TableCell className="text-sm">{getStatusBadge(row.status)}</TableCell>
                      <TableCell className="text-sm">{getSourceBadge(row.source)}</TableCell>

                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            {row.logIds && row.logIds.length > 0 ? (
                              <>
                                <DropdownMenuItem onClick={() => handleEdit(row.logIds[0])}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => handleDelete(row.logIds[0])}
                                  className="text-red-600"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete
                                </DropdownMenuItem>
                              </>
                            ) : (
                              <DropdownMenuItem disabled>
                                No actions available
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  // Standard project display
                  filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell className="text-sm text-gray-900">
                        {format(new Date(log.logDate), "yyyy-MM-dd")}
                      </TableCell>
                      <TableCell className="text-sm text-gray-700">{log.unitType}</TableCell>
                      <TableCell className="text-sm text-gray-700">{log.quantity.toLocaleString()}</TableCell>
                      <TableCell className="text-sm">{getStatusBadge(log.verificationStatus)}</TableCell>
                      <TableCell className="text-sm">{getSourceBadge(log.dataSource)}</TableCell>

                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(log.id)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDelete(log.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="mt-6 space-y-4">
                <div className="text-center text-sm text-muted-foreground">
                  Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} entries
                </div>
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage > 1) {
                            setCurrentPage(currentPage - 1);
                          }
                        }}
                        className={currentPage <= 1 ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>

                    {(() => {
                      const maxVisiblePages = 5;
                      const pages = [];

                      if (totalPages <= maxVisiblePages) {
                        // Show all pages if total is small
                        for (let i = 1; i <= totalPages; i++) {
                          pages.push(
                            <PaginationItem key={i}>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  setCurrentPage(i);
                                }}
                                isActive={i === currentPage}
                              >
                                {i}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        }
                      } else {
                        // Always show first page
                        pages.push(
                          <PaginationItem key={1}>
                            <PaginationLink
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                setCurrentPage(1);
                              }}
                              isActive={1 === currentPage}
                            >
                              1
                            </PaginationLink>
                          </PaginationItem>
                        );

                        // Show ellipsis if needed
                        if (currentPage > 3) {
                          pages.push(
                            <PaginationItem key="ellipsis-start">
                              <span className="px-3 py-2">...</span>
                            </PaginationItem>
                          );
                        }

                        // Show pages around current page
                        const start = Math.max(2, currentPage - 1);
                        const end = Math.min(totalPages - 1, currentPage + 1);

                        for (let i = start; i <= end; i++) {
                          if (i !== 1 && i !== totalPages) {
                            pages.push(
                              <PaginationItem key={i}>
                                <PaginationLink
                                  href="#"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    setCurrentPage(i);
                                  }}
                                  isActive={i === currentPage}
                                >
                                  {i}
                                </PaginationLink>
                              </PaginationItem>
                            );
                          }
                        }

                        // Show ellipsis if needed
                        if (currentPage < totalPages - 2) {
                          pages.push(
                            <PaginationItem key="ellipsis-end">
                              <span className="px-3 py-2">...</span>
                            </PaginationItem>
                          );
                        }

                        // Always show last page
                        if (totalPages > 1) {
                          pages.push(
                            <PaginationItem key={totalPages}>
                              <PaginationLink
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  setCurrentPage(totalPages);
                                }}
                                isActive={totalPages === currentPage}
                              >
                                {totalPages}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        }
                      }

                      return pages;
                    })()}

                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage < totalPages) {
                            setCurrentPage(currentPage + 1);
                          }
                        }}
                        className={currentPage >= totalPages ? "pointer-events-none opacity-50" : ""}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>

    {/* Edit Modal */}
    <EditUnitLogModal
      isOpen={editModalOpen}
      onClose={() => {
        setEditModalOpen(false);
        setSelectedUnitLog(null);
      }}
      unitLog={selectedUnitLog}
      projectId={projectId}
      projectType={projectType}
      projectSubType={projectSubType}
      onSuccess={() => {
        fetchUnitLogs();
        setEditModalOpen(false);
        setSelectedUnitLog(null);
        // Trigger parent dashboard refresh
        if (onDataChange) {
          onDataChange();
        }
      }}
    />

    {/* Delete Dialog */}
    <DeleteUnitLogDialog
      isOpen={deleteDialogOpen}
      onClose={() => {
        setDeleteDialogOpen(false);
        setSelectedUnitLog(null);
      }}
      unitLog={selectedUnitLog}
      projectId={projectId}
      onSuccess={() => {
        fetchUnitLogs();
        setDeleteDialogOpen(false);
        setSelectedUnitLog(null);
        // Trigger parent dashboard refresh
        if (onDataChange) {
          onDataChange();
        }
      }}
    />


    </>
  );
}
