"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import {
  Leaf,
  DollarSign,
  Zap,
  RefreshCw,
  Info,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { PROJECT_ENDPOINTS } from "@/lib/api-endpoints";

// Helper functions for unit display
const getDisplayUnit = (projectType: string) => {
  const units = {
    "RENEWABLE_ENERGY": "MWh",
    "ENERGY_EFFICIENCY": "MWh",
    "FORESTRY": "tCO2",
    "METHANE_REDUCTION": "tCH4",
    "WASTE_MANAGEMENT": "tonnes",
    "AGRICULTURE": "hectares",
    "TRANSPORTATION": "km",
    "INDUSTRIAL": "units",
    "OTHER": "units"
  };
  return units[projectType as keyof typeof units] || "units";
};

const getRawUnit = (projectType: string) => {
  const units = {
    "RENEWABLE_ENERGY": "kWh",
    "ENERGY_EFFICIENCY": "kWh",
    "FORESTRY": "kg CO2",
    "METHANE_REDUCTION": "kg CH4",
    "WASTE_MANAGEMENT": "kg",
    "AGRICULTURE": "m²",
    "TRANSPORTATION": "m",
    "INDUSTRIAL": "units",
    "OTHER": "units"
  };
  return units[projectType as keyof typeof units] || "units";
};

interface CarbonCreditEstimate {
  projectId: string;
  projectName: string;
  projectType: string;
  methodology?: string;
  standard?: string;
  calculation: {
    totalGeneration: number;
    totalGenerationInBaseUnits: number;
    calculationFactor: number;
    factorDescription: string;
    estimatedCredits: number;
    marketRate: number;
    estimatedValue: number;
    verificationRate: number;
    dataPoints: number;
    lastUpdated: string;
  };
}

interface EstimatedCarbonCreditsProps {
  projectId: string;
  refreshTrigger?: number;
  className?: string;
}

export function EstimatedCarbonCredits({ 
  projectId, 
  refreshTrigger = 0, 
  className 
}: EstimatedCarbonCreditsProps) {
  const [estimate, setEstimate] = useState<CarbonCreditEstimate | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchEstimate();
  }, [projectId]);

  // Refresh when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger && refreshTrigger > 0) {
      refreshEstimate();
    }
  }, [refreshTrigger]);

  const fetchEstimate = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      const response = await fetch(PROJECT_ENDPOINTS.PROJECT_CARBON_CREDITS_ESTIMATE({ id: projectId }));

      if (!response.ok) {
        // Check if it's a 404 or other specific error
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || "Failed to fetch carbon credit estimate");
      }

      const data = await response.json();
      setEstimate(data);

      if (isRefresh) {
        toast({
          title: "Estimate Updated",
          description: "Carbon credit estimate has been refreshed",
        });
      }
    } catch (error) {
      console.error("Error fetching carbon credit estimate:", error);

      // For existing projects with no data, create a default zero estimate instead of showing error
      const defaultEstimate: CarbonCreditEstimate = {
        projectId,
        projectName: "Project",
        projectType: "RENEWABLE_ENERGY",
        calculation: {
          totalGeneration: 0,
          totalGenerationInBaseUnits: 0,
          calculationFactor: 0,
          factorDescription: "No monitoring data available",
          estimatedCredits: 0,
          marketRate: 0,
          estimatedValue: 0,
          verificationRate: 0,
          dataPoints: 0,
          lastUpdated: new Date().toISOString(),
        },
      };

      setEstimate(defaultEstimate);
      setError(null); // Clear error to show the zero values instead

      // Only show error toast for refresh attempts, not initial loads
      if (isRefresh) {
        toast({
          title: "Error",
          description: "Failed to load carbon credit estimate",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const refreshEstimate = () => {
    fetchEstimate(true);
  };

  const getVerificationBadge = (rate: number) => {
    if (rate >= 90) {
      return (
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <CheckCircle className="h-3 w-3 mr-1" />
          High Accuracy
        </Badge>
      );
    } else if (rate >= 70) {
      return (
        <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Medium Accuracy
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Low Accuracy
        </Badge>
      );
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <Skeleton className="h-6 w-48 mb-2" />
              <Skeleton className="h-4 w-64" />
            </div>
            <Skeleton className="h-8 w-20" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="text-center p-4 border rounded-lg">
                <Skeleton className="h-4 w-24 mx-auto mb-2" />
                <Skeleton className="h-8 w-20 mx-auto mb-1" />
                <Skeleton className="h-3 w-32 mx-auto" />
              </div>
            ))}
          </div>
          <div className="mt-4 flex items-center justify-between">
            <Skeleton className="h-4 w-40" />
            <Skeleton className="h-6 w-24" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error && !estimate) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-8 w-8 mx-auto mb-2 text-red-500" />
          <p className="text-muted-foreground mb-4">{error || "Failed to load estimate"}</p>
          <Button variant="outline" onClick={() => fetchEstimate()}>
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  // If no estimate data, don't render anything (let loading state handle it)
  if (!estimate) {
    return null;
  }

  // Check if this is a project with no monitoring data
  const hasNoData = estimate.calculation.dataPoints === 0;

  return (
    <TooltipProvider>
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Leaf className="h-5 w-5 text-green-600" />
                Estimated Carbon Credits
              </CardTitle>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshEstimate}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Updating...' : 'Refresh'}
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-2">
                <Zap className="h-4 w-4 text-blue-600" />
                <p className="text-sm text-muted-foreground">Total Generation</p>
              </div>
              <p className="text-2xl font-bold text-blue-600">
                {hasNoData ? "0" : estimate.calculation.totalGenerationInBaseUnits.toLocaleString()} {getDisplayUnit(estimate.projectType)}
              </p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-2">
                <Leaf className="h-4 w-4 text-green-600" />
                <p className="text-sm text-muted-foreground">Estimated Credits</p>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Calculated using {estimate.calculation.calculationFactor} tCO2e/MWh factor</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <p className="text-2xl font-bold text-green-600">
                {hasNoData ? "0" : estimate.calculation.estimatedCredits.toLocaleString()} tCO2e
              </p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-2">
                <DollarSign className="h-4 w-4 text-purple-600" />
                <p className="text-sm text-muted-foreground">Estimated Value</p>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Info className="h-3 w-3 text-muted-foreground cursor-help" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">
                      {hasNoData
                        ? "Potential market value"
                        : `At $${estimate.calculation.marketRate}/tCO2e market rate`
                      }
                    </p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <p className="text-2xl font-bold text-purple-600">
                ${hasNoData ? "0" : estimate.calculation.estimatedValue.toLocaleString()}
              </p>
            </div>
          </div>

          
          {hasNoData && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Info className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">Get Started</span>
              </div>
              <p className="text-sm text-blue-800 mb-3">
                Start adding monitoring data to see real-time carbon credit estimates and track your project's environmental impact.
              </p>
              <Button
                size="sm"
                onClick={() => window.location.href = `/dashboard/projects/${estimate.projectId}/monitoring/data-entry`}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Add Monitoring Data
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  );
}
