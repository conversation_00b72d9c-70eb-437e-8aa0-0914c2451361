"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  C<PERSON>, 
  HelpCircle,
  Hash,
  Wind,
  Zap
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function WindEquipmentStep() {
  const { control } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5 text-green-500" />
              Equipment Specifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* WTG Details */}
              <motion.div variants={itemVariants} className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Wind className="h-4 w-4" />
                  Wind Turbine Generator (WTG)
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={control}
                    name="windEnergyData.windWTGMake"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>WTG Make <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Manufacturer/brand of the wind turbine generator</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., Vestas, Siemens Gamesa"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windWTGCapacity"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>WTG Capacity <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Capacity of each wind turbine generator in MW</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 2.5"
                            type="number"
                            min="0"
                            step="0.01"
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            icon={<Zap className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windNoOfWTG"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>No of WTG <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total number of wind turbine generators</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 40"
                            type="number"
                            min="1"
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            icon={<Hash className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>

              {/* Module Details */}
              <motion.div variants={itemVariants} className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Cpu className="h-4 w-4" />
                  Modules
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={control}
                    name="windEnergyData.windModuleType"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Type of Module <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Type/model of modules being used</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., Power Module PM-2500"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windModuleSupplier"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Module Supplier <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Supplier/manufacturer of the modules</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., ABB, Schneider Electric"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windNoOfModules"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>No Of Modules <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total number of modules to be installed</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 40"
                            type="number"
                            min="1"
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            icon={<Hash className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>

              {/* Inverter Details */}
              <motion.div variants={itemVariants} className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Inverters
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={control}
                    name="windEnergyData.windInverter"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Inverter <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Type/model of inverter being used</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., ABB PVS980-CS"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windNoOfInverters"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>No of Inverters (Nos) <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total number of inverters</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 40"
                            type="number"
                            min="1"
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            icon={<Hash className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windInverterCapacityKW"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Inverter Capacity (KW) <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Capacity of each inverter in KW</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 2500"
                            type="number"
                            min="0"
                            step="0.01"
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            icon={<Zap className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>
            </motion.div>

            {/* Dynamic Custom Fields */}
            <DynamicCustomFields
              step="WIND_EQUIPMENT"
              projectType="RENEWABLE_ENERGY"
              projectSubtype="WIND_POWER"
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
