"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  MapPin, 
  HelpCircle,
  Hash,
  Wind
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function WindLandResourcesStep() {
  const { control } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-orange-500" />
              Land & Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Available Land for WTG */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="windEnergyData.windAvailableLandWTGNos"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Available Land (WTG) Nos <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Number of wind turbine generator positions available on the land</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          placeholder="e.g., 40"
                          type="number"
                          min="1"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          icon={<Hash className="h-4 w-4" />}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Summary Card */}
              <motion.div variants={itemVariants}>
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-100 rounded-full">
                        <Wind className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-blue-900 mb-2">Wind Farm Land Requirements</h4>
                        <p className="text-sm text-blue-700 mb-3">
                          Wind farms typically require 2-5 acres per MW of installed capacity, but the actual 
                          footprint of wind turbines is much smaller (less than 1% of the total area). 
                          The remaining land can often be used for other purposes like agriculture.
                        </p>
                        <div className="space-y-2 text-sm text-blue-700">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span>Minimum spacing between turbines: 3-5 rotor diameters</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span>Access roads and maintenance areas required</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <span>Substation and transmission line corridors needed</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>

              {/* Additional Information Card */}
              <motion.div variants={itemVariants}>
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-green-100 rounded-full">
                        <MapPin className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-green-900 mb-2">Land Use Considerations</h4>
                        <div className="space-y-2 text-sm text-green-700">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span><strong>Wind Resource:</strong> Ensure consistent wind speeds of 6+ m/s</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span><strong>Terrain:</strong> Relatively flat or gently sloping land preferred</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span><strong>Access:</strong> Good road connectivity for equipment transportation</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                            <span><strong>Grid:</strong> Proximity to transmission infrastructure</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>

            {/* Dynamic Custom Fields */}
            <DynamicCustomFields
              step="WIND_LAND_RESOURCES"
              projectType="RENEWABLE_ENERGY"
              projectSubtype="WIND_POWER"
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
