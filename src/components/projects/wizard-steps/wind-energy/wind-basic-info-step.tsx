"use client";

import { useState } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import {
  Wind,
  Building,
  User,
  HelpCircle,
  Zap,
  Phone,
  Mail,
  MapPin,
  Globe,
  Calendar,
  FileText,
  Upload,
  X,
  CheckCircle
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function WindBasicInfoStep() {
  const { control, setValue, watch } = useFormContext();
  const [uploadingFile, setUploadingFile] = useState(false);
  const commissioningCertificate = watch("windEnergyData.windCommissioningCertificate");

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploadingFile(true);
    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'wind-commissioning-certificates');

      // Upload file to server
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload file');
      }

      const result = await response.json();

      // Store the uploaded file information
      setValue("windEnergyData.windCommissioningCertificate", {
        name: file.name,
        size: file.size,
        url: result.url,
        uploadedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("File upload error:", error);
      // For now, still store the file object as fallback
      setValue("windEnergyData.windCommissioningCertificate", file);
    } finally {
      setUploadingFile(false);
    }
  };

  const removeFile = () => {
    setValue("windEnergyData.windCommissioningCertificate", null);
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wind className="h-5 w-5 text-blue-500" />
              Project Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Basic Project Information */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Building className="h-4 w-4" />
                    Basic Project Information
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Project Name */}
                    <FormField
                      control={control}
                      name="windEnergyData.windProjectName"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Name of Project <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the official name of your wind energy project</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., Gujarat Wind Farm Phase 1"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Client Name */}
                    <FormField
                      control={control}
                      name="windEnergyData.windClientName"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Name of Client <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the name of the client or organization commissioning this project</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., State Electricity Board"
                              icon={<User className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Project Type */}
                    <FormField
                      control={control}
                      name="windEnergyData.windProjectType"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Project Type <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="max-w-xs">
                                  <p><strong>EPC:</strong> Engineering, Procurement, and Construction - You handle the complete project development</p>
                                  <p><strong>IPP:</strong> Independent Power Producer - You generate and sell electricity</p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select project type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="EPC">
                                  <div className="flex items-center gap-2">
                                    <Building className="h-4 w-4" />
                                    EPC (Engineering, Procurement, Construction)
                                  </div>
                                </SelectItem>
                                <SelectItem value="IPP">
                                  <div className="flex items-center gap-2">
                                    <Zap className="h-4 w-4" />
                                    IPP (Independent Power Producer)
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </motion.div>

              {/* Project Team Information */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Project Team Information
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* VP Name */}
                    <FormField
                      control={control}
                      name="windEnergyData.windVPName"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>VP Name <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Name of the Vice President overseeing this project</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., John Smith"
                              icon={<User className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VP Contact No */}
                    <FormField
                      control={control}
                      name="windEnergyData.windVPContactNo"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>VP Contact No <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Contact number of the Vice President</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., +91 9876543210"
                              icon={<Phone className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* VP Mail Id */}
                    <FormField
                      control={control}
                      name="windEnergyData.windVPMailId"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>VP Mail Id <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Email address of the Vice President</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., <EMAIL>"
                              type="email"
                              icon={<Mail className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Site Engineer Name */}
                    <FormField
                      control={control}
                      name="windEnergyData.windSiteEngineerName"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Site Engineer Name <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Name of the Site Engineer responsible for on-site operations</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., Jane Doe"
                              icon={<User className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Site Engineer Contact NO */}
                    <FormField
                      control={control}
                      name="windEnergyData.windSiteEngineerContactNo"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Site Engineer Contact NO <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Contact number of the Site Engineer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., +91 9876543210"
                              icon={<Phone className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Site Engineer Mail ID */}
                    <FormField
                      control={control}
                      name="windEnergyData.windSiteEngineerMailId"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Site Engineer Mail ID <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Email address of the Site Engineer</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., <EMAIL>"
                              type="email"
                              icon={<Mail className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </motion.div>

              {/* COD and Commissioning Certificate */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Project Timeline & Documentation
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* COD */}
                    <FormField
                      control={control}
                      name="windEnergyData.windCOD"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>COD (Commercial Operation Date) <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>The date when the wind farm will start commercial operations</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              type="date"
                              icon={<Calendar className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Commissioning Certificate */}
                  <FormField
                    control={control}
                    name="windEnergyData.windCommissioningCertificate"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Commissioning Certificate</FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Upload the official commissioning certificate document (PDF, DOC, DOCX)</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <div className="space-y-4">
                            {!commissioningCertificate ? (
                              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                                <FileText className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                                <p className="text-sm text-muted-foreground mb-4">
                                  Upload commissioning certificate
                                </p>
                                <Button
                                  type="button"
                                  variant="outline"
                                  disabled={uploadingFile}
                                  onClick={() => document.getElementById('wind-commissioning-file-input')?.click()}
                                >
                                  <Upload className="h-4 w-4 mr-2" />
                                  {uploadingFile ? "Uploading..." : "Choose File"}
                                </Button>
                                <input
                                  id="wind-commissioning-file-input"
                                  type="file"
                                  accept=".pdf,.doc,.docx"
                                  onChange={handleFileUpload}
                                  className="hidden"
                                />
                              </div>
                            ) : (
                              <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                                <div className="flex items-center gap-2">
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                  <span className="text-sm font-medium text-green-800">
                                    {commissioningCertificate.name}
                                  </span>
                                  <span className="text-xs text-green-600">
                                    ({((commissioningCertificate.size || 0) / 1024 / 1024).toFixed(2)} MB)
                                  </span>
                                  {commissioningCertificate.url && (
                                    <span className="text-xs text-green-600">• Uploaded</span>
                                  )}
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={removeFile}
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>
            </motion.div>

            {/* Dynamic Custom Fields */}
            <DynamicCustomFields
              step="WIND_BASIC_INFO"
              projectType="RENEWABLE_ENERGY"
              projectSubtype="WIND_POWER"
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
