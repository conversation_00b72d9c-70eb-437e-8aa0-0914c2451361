"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  Building2, 
  HelpCircle,
  Calendar,
  Upload,
  Hash,
  Zap,
  Users,
  BarChart3,
  Building,
  Network
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function WindInfrastructureStep() {
  const { control } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-purple-500" />
              Infrastructure & Commissioning
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* COD */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="windEnergyData.windCOD"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>COD (Commercial Operation Date) <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Date when the wind farm becomes commercially operational</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          type="date"
                          icon={<Calendar className="h-4 w-4" />}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Commissioning Certificate */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="windEnergyData.windCommissioningCertificate"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Commissioning Certificate <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Upload the commissioning certificate document</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <div className="flex items-center gap-2 p-3 border border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors">
                          <Upload className="h-4 w-4 text-gray-500" />
                          <Input
                            type="file"
                            accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                            onChange={(e) => field.onChange(e.target.files?.[0] || null)}
                            className="border-0 p-0 h-auto file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Transformer Details */}
              <motion.div variants={itemVariants} className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Transformer Details
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name="windEnergyData.windTransformerCapacity"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Transformer Capacity <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Capacity of transformers in MVA</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 100"
                            type="number"
                            min="0"
                            step="0.01"
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            icon={<Zap className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windNoOfTransformers"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>No Of Transformer <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total number of transformers</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 2"
                            type="number"
                            min="1"
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            icon={<Hash className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>

              {/* Subcontractors */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="windEnergyData.windNoOfSubcontractors"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>No of Subcontractor <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Total number of subcontractors involved in the project</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          placeholder="e.g., 5"
                          type="number"
                          min="0"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          icon={<Users className="h-4 w-4" />}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Generation Details */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="windEnergyData.windGenerationDetails"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Generation Details <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Details about power generation capacity and expected output</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="e.g., Expected annual generation: 250 GWh, Capacity factor: 28%, Peak generation: 100 MW"
                          className="min-h-[100px]"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Connectivity Details */}
              <motion.div variants={itemVariants} className="space-y-4">
                <h3 className="text-lg font-medium flex items-center gap-2">
                  <Network className="h-4 w-4" />
                  Grid Connectivity
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={control}
                    name="windEnergyData.windSubstationName"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Name of Substation <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Name of the electrical substation for grid connection</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., Kutch 220kV Substation"
                            icon={<Building className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windEvacuationClass"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Evacuation Class <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Voltage class for power evacuation (e.g., 220kV, 400kV)</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 220kV"
                            icon={<Zap className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windConnectivityData"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Connectivity Data <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Grid connectivity specifications and transmission details</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., ISTS connectivity approved"
                            icon={<Network className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>
            </motion.div>

            {/* Dynamic Custom Fields */}
            <DynamicCustomFields
              step="WIND_INFRASTRUCTURE"
              projectType="RENEWABLE_ENERGY"
              projectSubtype="WIND_POWER"
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
