"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import {
  MapPin,
  HelpCircle,
  User,
  Phone,
  Mail,
  Zap,
  Hash
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function WindLocationTechnicalStep() {
  const { control } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              Technical Specifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >


              {/* Project Capacity */}
              <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={control}
                  name="windEnergyData.windProjectCapacity"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Project Capacity <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Total project capacity in MW</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          placeholder="e.g., 100"
                          type="number"
                          min="0"
                          step="0.01"
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          icon={<Zap className="h-4 w-4" />}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={control}
                  name="windEnergyData.windCapacityMW"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Wind Capacity (MW) <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Wind generation capacity in MW</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          placeholder="e.g., 100"
                          type="number"
                          min="0"
                          step="0.01"
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          icon={<Zap className="h-4 w-4" />}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* WTG Specifications */}
              <motion.div variants={itemVariants}>
                <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Wind Turbine Generator (WTG) Specifications
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <FormField
                    control={control}
                    name="windEnergyData.windWTGMake"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>WTG Make <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Manufacturer/brand of the Wind Turbine Generator</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., Vestas, Siemens Gamesa"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windWTGCapacity"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>WTG Capacity <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Capacity of each Wind Turbine Generator in MW</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 2.5"
                            type="number"
                            step="0.01"
                            min="0"
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            icon={<Zap className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name="windEnergyData.windNoOfWTG"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>No of WTG <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total number of Wind Turbine Generators to be installed</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 40"
                            type="number"
                            min="1"
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            icon={<Hash className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>
            </motion.div>

            {/* Dynamic Custom Fields */}
            <DynamicCustomFields
              step="WIND_LOCATION_TECHNICAL"
              projectType="RENEWABLE_ENERGY"
              projectSubtype="WIND_POWER"
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
