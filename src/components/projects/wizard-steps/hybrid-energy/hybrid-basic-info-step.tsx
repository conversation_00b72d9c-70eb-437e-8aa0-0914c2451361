"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  Zap, 
  Building, 
  User, 
  HelpCircle,
  Phone,
  Mail
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function HybridBasicInfoStep() {
  const { control, watch } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              Hybrid (Solar + Wind) Energy Project - Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Project Name */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="hybridEnergyData.hybridProjectName"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Name of Project <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Enter the official name of your hybrid renewable energy project</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          placeholder="e.g., Gujarat Hybrid Renewable Energy Park"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>



              {/* VP Information Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* VP Name */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridVPName"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>VP Name <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Name of the Vice President from Onix</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="Enter VP name"
                            icon={<User className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                {/* VP Contact No */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridVPContactNo"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>VP Contact No <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Contact number of the Vice President from Onix</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="Enter VP contact number"
                            icon={<Phone className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                {/* VP Mail Id */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridVPMailId"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>VP Mail Id <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Email address of the Vice President from Onix</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            type="email"
                            placeholder="Enter VP email address"
                            icon={<Mail className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              </div>

              {/* Site Engineer Information Section */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Site Engineer Name */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridSiteEngineerName"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Site Engineer Name <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Name of the Site Engineer from Onix</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="Enter site engineer name"
                            icon={<User className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                {/* Site Engineer Contact No */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridSiteEngineerContactNo"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Site Engineer Contact No <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Contact number of the Site Engineer from Onix</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="Enter site engineer contact"
                            icon={<Phone className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                {/* Site Engineer Mail ID */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridSiteEngineerMailId"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Site Engineer Mail ID <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Email address of the Site Engineer from Onix</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            type="email"
                            placeholder="Enter site engineer email"
                            icon={<Mail className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              </div>

              {/* Client Name */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="hybridEnergyData.hybridClientName"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Name of Client <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Enter the name of the client for this hybrid energy project</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          placeholder="e.g., Gujarat State Electricity Corporation"
                          icon={<Building className="h-4 w-4" />}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Project Type */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="hybridEnergyData.hybridProjectType"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Project Type <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Select whether this is an EPC or IPP project</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="EPC">
                              <div className="flex items-center gap-2">
                                <Building className="h-4 w-4" />
                                EPC (Engineering, Procurement, Construction)
                              </div>
                            </SelectItem>
                            <SelectItem value="IPP">
                              <div className="flex items-center gap-2">
                                <Zap className="h-4 w-4" />
                                IPP (Independent Power Producer)
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>
            </motion.div>
          </CardContent>
        </Card>

        {/* Dynamic Custom Fields */}
        <DynamicCustomFields stepName="hybridBasicInfo" />
      </div>
    </TooltipProvider>
  );
}
