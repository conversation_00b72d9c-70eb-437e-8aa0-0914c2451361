"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  MapPin, 
  Zap, 
  HelpCircle,
  Sun,
  Wind,
  Globe
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function HybridLocationTechnicalStep() {
  const { control, watch } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-green-500" />
              Hybrid Energy Project - Technical Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >


              {/* Project Capacity Section */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="hybridEnergyData.hybridProjectCapacity"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Project Capacity (MW) <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Enter the total project capacity in megawatts</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          type="number"
                          placeholder="e.g., 500"
                          icon={<Zap className="h-4 w-4" />}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Solar Capacity Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Solar Capacity AC */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridSolarCapacityAC"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Solar Capacity AC (MW) <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Enter the solar AC capacity in megawatts</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            type="number"
                            placeholder="e.g., 200"
                            icon={<Sun className="h-4 w-4" />}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                {/* Solar Capacity DC */}
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridSolarCapacityDC"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Solar Capacity DC (MW) <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Enter the solar DC capacity in megawatts</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            type="number"
                            placeholder="e.g., 250"
                            icon={<Sun className="h-4 w-4" />}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>
              </div>

              {/* Wind Capacity */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="hybridEnergyData.hybridWindCapacity"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>Wind Capacity (MW) <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Enter the wind capacity in megawatts</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          type="number"
                          placeholder="e.g., 300"
                          icon={<Wind className="h-4 w-4" />}
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>
            </motion.div>
          </CardContent>
        </Card>

        {/* Dynamic Custom Fields */}
        <DynamicCustomFields stepName="hybridLocationTechnical" />
      </div>
    </TooltipProvider>
  );
}
