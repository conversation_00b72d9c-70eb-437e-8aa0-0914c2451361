"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Too<PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  Settings, 
  Wind, 
  Sun, 
  HelpCircle,
  Zap,
  Package
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function HybridEquipmentStep() {
  const { control, watch } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-purple-500" />
              Hybrid Energy Project - Equipment Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Wind Turbine Generator Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Wind className="h-5 w-5 text-blue-500" />
                  Wind Turbine Generator (WTG) Details
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* WTG Make */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridWTGMake"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>WTG Make <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the manufacturer/make of the wind turbine generator</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., Vestas, Siemens Gamesa"
                              icon={<Wind className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>

                  {/* WTG Capacity */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridWTGCapacity"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>WTG Capacity (MW) <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the capacity of each wind turbine generator in MW</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              type="number"
                              placeholder="e.g., 2.5"
                              icon={<Zap className="h-4 w-4" />}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>

                  {/* No of WTG */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridNoOfWTG"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>No of WTG <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the total number of wind turbine generators</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              type="number"
                              placeholder="e.g., 120"
                              icon={<Package className="h-4 w-4" />}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>
                </div>
              </div>

              {/* Solar Module Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Sun className="h-5 w-5 text-yellow-500" />
                  Solar Module Details
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Type of Module */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridModuleType"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Type of Module <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the type/model of solar modules</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., Monocrystalline, Polycrystalline"
                              icon={<Sun className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>

                  {/* Module Supplier */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridModuleSupplier"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Module Supplier <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the supplier/manufacturer of solar modules</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., Trina Solar, JinkoSolar"
                              icon={<Package className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>

                  {/* No Of Modules */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridNoOfModules"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>No Of Modules <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the total number of solar modules</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              type="number"
                              placeholder="e.g., 500000"
                              icon={<Package className="h-4 w-4" />}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>
                </div>
              </div>

              {/* Inverter Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Zap className="h-5 w-5 text-orange-500" />
                  Inverter Details
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Inverter */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridInverter"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Inverter <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the inverter make/model</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., ABB, SMA, Huawei"
                              icon={<Zap className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>

                  {/* No of Inverters */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridNoOfInverters"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>No of Inverters <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the total number of inverters</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              type="number"
                              placeholder="e.g., 100"
                              icon={<Package className="h-4 w-4" />}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>

                  {/* Inverter Capacity */}
                  <motion.div variants={itemVariants}>
                    <FormField
                      control={control}
                      name="hybridEnergyData.hybridInverterCapacity"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Inverter Capacity (KW) <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Enter the capacity of each inverter in kilowatts</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              type="number"
                              placeholder="e.g., 2500"
                              icon={<Zap className="h-4 w-4" />}
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </CardContent>
        </Card>

        {/* Dynamic Custom Fields */}
        <DynamicCustomFields stepName="hybridEquipment" />
      </div>
    </TooltipProvider>
  );
}
