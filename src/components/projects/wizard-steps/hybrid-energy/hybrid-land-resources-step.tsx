"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  MapPin, 
  Wind, 
  Sun, 
  HelpCircle,
  Ruler,
  TreePine
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function HybridLandResourcesStep() {
  const { control, watch } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-600" />
              Hybrid Energy Project - Land & Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Wind Land Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Wind className="h-5 w-5 text-blue-500" />
                  Wind Turbine Land Requirements
                </h3>
                
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridAvailableLandWTGNos"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Available Land (WTG) Nos <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Enter the number of available land parcels for wind turbine generators</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            type="number"
                            placeholder="e.g., 120"
                            icon={<Wind className="h-4 w-4" />}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                        <p className="text-sm text-muted-foreground mt-1">
                          This represents the number of individual land plots or positions available for installing wind turbines
                        </p>
                      </FormItem>
                    )}
                  />
                </motion.div>
              </div>

              {/* Solar Land Section */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center gap-2">
                  <Sun className="h-5 w-5 text-yellow-500" />
                  Solar Panel Land Requirements
                </h3>
                
                <motion.div variants={itemVariants}>
                  <FormField
                    control={control}
                    name="hybridEnergyData.hybridAvailableLandSolarAcre"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Available Land Solar (Acre) <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Enter the total available land area in acres for solar panel installation</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            type="number"
                            step="0.01"
                            placeholder="e.g., 1000.50"
                            icon={<Sun className="h-4 w-4" />}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                        <p className="text-sm text-muted-foreground mt-1">
                          This represents the total contiguous land area available for solar panel arrays
                        </p>
                      </FormItem>
                    )}
                  />
                </motion.div>
              </div>

              {/* Land Summary Information */}
              <div className="bg-muted/50 p-4 rounded-lg">
                <h4 className="font-medium mb-3 flex items-center gap-2">
                  <TreePine className="h-4 w-4 text-green-600" />
                  Land Resource Summary
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Wind className="h-4 w-4 text-blue-500" />
                      <span className="font-medium">Wind Turbine Land:</span>
                    </div>
                    <ul className="list-disc list-inside text-muted-foreground space-y-1 ml-6">
                      <li>Individual plots for each turbine</li>
                      <li>Typically 1-2 acres per turbine</li>
                      <li>Allows for agricultural use between turbines</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Sun className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium">Solar Panel Land:</span>
                    </div>
                    <ul className="list-disc list-inside text-muted-foreground space-y-1 ml-6">
                      <li>Contiguous area for panel arrays</li>
                      <li>Typically 4-5 acres per MW</li>
                      <li>Requires clear, unobstructed land</li>
                    </ul>
                  </div>
                </div>
              </div>

              {/* Land Utilization Efficiency */}
              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-medium mb-2 flex items-center gap-2 text-blue-700 dark:text-blue-300">
                  <Ruler className="h-4 w-4" />
                  Hybrid Land Utilization Benefits
                </h4>
                <p className="text-sm text-blue-600 dark:text-blue-400">
                  Hybrid renewable energy projects optimize land use by combining wind and solar technologies. 
                  Wind turbines can be installed with adequate spacing to allow solar panels in between, 
                  maximizing energy generation per acre while maintaining agricultural or other land uses.
                </p>
              </div>
            </motion.div>
          </CardContent>
        </Card>

        {/* Dynamic Custom Fields */}
        <DynamicCustomFields stepName="hybridLandResources" />
      </div>
    </TooltipProvider>
  );
}
