"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  Building2, 
  HelpCircle,
  Hash,
  MapPin,
  Home,
  CreditCard,
  Users
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function SolarInfrastructureResourcesStep() {
  const { control } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-purple-500" />
              Infrastructure & Resources
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Transformer Details */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Transformer Details
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={control}
                      name="solarEnergyData.solarTransformerCapacity"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Transformer capacity <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Total capacity of transformers in MVA</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., 100"
                              type="number"
                              step="0.1"
                              min="0"
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="solarEnergyData.solarNoOfTransformers"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>No Of Transformer <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Total number of transformers to be installed</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., 4"
                              type="number"
                              min="1"
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              icon={<Hash className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </motion.div>

              {/* Subcontractors */}
              <motion.div variants={itemVariants}>
                <FormField
                  control={control}
                  name="solarEnergyData.solarNoOfSubcontractors"
                  render={({ field }) => (
                    <FormItem>
                      <div className="flex items-center gap-2">
                        <FormLabel>No of subcontractor <span className="text-red-500">*</span></FormLabel>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Total number of subcontractors involved in the project</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>
                      <FormControl>
                        <AnimatedInput
                          {...field}
                          placeholder="e.g., 5"
                          type="number"
                          min="0"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          icon={<Users className="h-4 w-4" />}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>

              {/* Land Resources */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    Land Resources
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={control}
                      name="solarEnergyData.solarAvailableLand"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Available Land <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <div className="max-w-xs">
                                  <p><strong>Lease:</strong> Long-term land lease agreement</p>
                                  <p><strong>Rent:</strong> Short-term rental arrangement</p>
                                </div>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select land availability type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Lease">
                                  <div className="flex items-center gap-2">
                                    <Home className="h-4 w-4" />
                                    Lease
                                  </div>
                                </SelectItem>
                                <SelectItem value="Rent">
                                  <div className="flex items-center gap-2">
                                    <CreditCard className="h-4 w-4" />
                                    Rent
                                  </div>
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="solarEnergyData.solarTotalLandAvailability"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Total Land Avaiblity <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Total area of land available for the solar project in acres</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <div className="relative">
                              <AnimatedInput
                                {...field}
                                placeholder="e.g., 500"
                                type="number"
                                step="0.1"
                                min="0"
                                onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                icon={<MapPin className="h-4 w-4" />}
                              />
                              <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                acres
                              </div>
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </motion.div>

              {/* Summary Card */}
              <motion.div variants={itemVariants}>
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-green-100 rounded-full">
                        <Building2 className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-green-900 mb-2">Infrastructure Summary</h4>
                        <p className="text-sm text-green-700">
                          This section covers the essential infrastructure components and resources required 
                          for your solar project. Transformers handle power conversion, subcontractors 
                          provide specialized services, and adequate land ensures proper installation and 
                          maintenance access. Solar projects typically require 4-5 acres per MW of capacity.
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </motion.div>

            {/* Dynamic Custom Fields */}
            <DynamicCustomFields
              step="SOLAR_INFRASTRUCTURE_RESOURCES"
              projectType="RENEWABLE_ENERGY"
              projectSubtype="SOLAR_PV"
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
