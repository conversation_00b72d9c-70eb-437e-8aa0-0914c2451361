"use client";

import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  Cpu, 
  Factory, 
  HelpCircle,
  Hash,
  Zap,
  Building2
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";
import { DynamicCustomFields } from "../dynamic-custom-fields";

export function SolarTechnicalEquipmentStep() {
  const { control } = useFormContext();
  
  return (
    <TooltipProvider>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5 text-green-500" />
              Technical Specifications & Equipment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <motion.div
              className="space-y-6"
              variants={staggeredListVariants}
              initial="hidden"
              animate="visible"
            >
              {/* Power Capacity & Grid Connection */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Power Capacity & Grid Connection
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={control}
                      name="solarEnergyData.solarMwCapacityAC"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>MW Capacity AC <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>AC power output capacity in Megawatts</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., 100"
                              type="number"
                              step="0.01"
                              min="0"
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              icon={<Zap className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="solarEnergyData.solarMwCapacityDC"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>MW Capacity DC <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>DC power generation capacity in Megawatts</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., 120"
                              type="number"
                              step="0.01"
                              min="0"
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              icon={<Zap className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={control}
                    name="solarEnergyData.solarSubstationName"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Name Of Substation <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Name of the electrical substation where the solar plant will connect to the grid</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., Jodhpur 220kV Substation"
                            icon={<Building2 className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>

              {/* Solar Modules */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Cpu className="h-4 w-4" />
                    Solar Modules
                  </h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={control}
                      name="solarEnergyData.solarModuleType"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Type of Module <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Specify the type/model of solar modules being used</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., Monocrystalline 540W"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="solarEnergyData.solarModuleSupplier"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Module Supplier <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Name of the company supplying the solar modules</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., Trina Solar"
                              icon={<Factory className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={control}
                    name="solarEnergyData.solarNoOfModules"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>No Of Modules <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Total number of solar modules to be installed</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., 185000"
                            type="number"
                            min="1"
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            icon={<Hash className="h-4 w-4" />}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </motion.div>

              {/* Inverters */}
              <motion.div variants={itemVariants}>
                <div className="space-y-4">
                  <h3 className="text-lg font-medium flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    Inverters
                  </h3>
                  
                  <FormField
                    control={control}
                    name="solarEnergyData.solarInverter"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2">
                          <FormLabel>Inverter <span className="text-red-500">*</span></FormLabel>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Type/model of inverters being used</p>
                            </TooltipContent>
                          </Tooltip>
                        </div>
                        <FormControl>
                          <AnimatedInput
                            {...field}
                            placeholder="e.g., SMA Sunny Central 2500-EV"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={control}
                      name="solarEnergyData.solarNoOfInverters"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>No of Inverters <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Total number of inverters to be installed</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., 40"
                              type="number"
                              min="1"
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                              icon={<Hash className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name="solarEnergyData.solarInverterCapacity"
                      render={({ field }) => (
                        <FormItem>
                          <div className="flex items-center gap-2">
                            <FormLabel>Inverter Capacity <span className="text-red-500">*</span></FormLabel>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p>Power capacity of each inverter in Kilowatts</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>
                          <FormControl>
                            <AnimatedInput
                              {...field}
                              placeholder="e.g., 2500"
                              type="number"
                              step="0.1"
                              min="0"
                              onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                              icon={<Zap className="h-4 w-4" />}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </motion.div>
            </motion.div>

            {/* Dynamic Custom Fields */}
            <DynamicCustomFields
              step="SOLAR_TECHNICAL_EQUIPMENT"
              projectType="RENEWABLE_ENERGY"
              projectSubtype="SOLAR_PV"
            />
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
