"use client";

import { useState, useEffect } from "react";
import { useFormContext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { AnimatedInput } from "@/components/ui/animated";
import { 
  HelpCircle,
  Upload,
  X,
  CheckCircle
} from "lucide-react";
import { motion } from "framer-motion";
import { staggeredListVariants, itemVariants } from "@/lib/animations";

interface CustomField {
  id?: string;
  name: string;
  label: string;
  type: "text" | "number" | "date" | "select" | "textarea" | "file" | "checkbox";
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: string[];
  step?: string;
}

interface DynamicCustomFieldsProps {
  step: string;
  projectType: string;
  projectSubtype?: string;
}

export function DynamicCustomFields({ step, projectType, projectSubtype }: DynamicCustomFieldsProps) {
  const { control, setValue, watch } = useFormContext();
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load custom fields for this step
  useEffect(() => {
    const loadCustomFields = async () => {
      try {
        const params = new URLSearchParams({
          projectType,
          ...(projectSubtype && { projectSubtype }),
        });

        const response = await fetch(`/api/organizations/custom-fields?${params}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data.fields) {
            // Filter fields for this specific step
            const stepFields = data.data.fields.filter((field: CustomField) => field.step === step);
            setCustomFields(stepFields);
          }
        }
      } catch (error) {
        console.error("Error loading custom fields:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadCustomFields();
  }, [step, projectType, projectSubtype]);

  const handleFileUpload = async (fieldName: string, event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'custom-field-uploads');
      
      // Upload file to server
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload file');
      }
      
      const result = await response.json();
      
      // Store the uploaded file information
      setValue(`customFields.${fieldName}`, {
        name: file.name,
        size: file.size,
        url: result.url,
        uploadedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error("File upload error:", error);
      // For now, still store the file object as fallback
      setValue(`customFields.${fieldName}`, file);
    }
  };

  const removeFile = (fieldName: string) => {
    setValue(`customFields.${fieldName}`, null);
  };

  const renderField = (field: CustomField) => {
    const fieldName = `customFields.${field.name}`;
    
    switch (field.type) {
      case "text":
        return (
          <FormField
            control={control}
            name={fieldName}
            render={({ field: formField }) => (
              <FormItem>
                <div className="flex items-center gap-2">
                  <FormLabel>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </FormLabel>
                  {field.helpText && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{field.helpText}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <FormControl>
                  <AnimatedInput
                    {...formField}
                    placeholder={field.placeholder}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "number":
        return (
          <FormField
            control={control}
            name={fieldName}
            render={({ field: formField }) => (
              <FormItem>
                <div className="flex items-center gap-2">
                  <FormLabel>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </FormLabel>
                  {field.helpText && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{field.helpText}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <FormControl>
                  <AnimatedInput
                    {...formField}
                    type="number"
                    placeholder={field.placeholder}
                    onChange={(e) => formField.onChange(parseFloat(e.target.value) || 0)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "date":
        return (
          <FormField
            control={control}
            name={fieldName}
            render={({ field: formField }) => (
              <FormItem>
                <div className="flex items-center gap-2">
                  <FormLabel>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </FormLabel>
                  {field.helpText && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{field.helpText}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <FormControl>
                  <AnimatedInput
                    {...formField}
                    type="date"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "select":
        return (
          <FormField
            control={control}
            name={fieldName}
            render={({ field: formField }) => (
              <FormItem>
                <div className="flex items-center gap-2">
                  <FormLabel>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </FormLabel>
                  {field.helpText && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{field.helpText}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <FormControl>
                  <Select onValueChange={formField.onChange} defaultValue={formField.value}>
                    <SelectTrigger>
                      <SelectValue placeholder={field.placeholder || "Select an option"} />
                    </SelectTrigger>
                    <SelectContent>
                      {field.options?.map((option) => (
                        <SelectItem key={option} value={option}>
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "textarea":
        return (
          <FormField
            control={control}
            name={fieldName}
            render={({ field: formField }) => (
              <FormItem>
                <div className="flex items-center gap-2">
                  <FormLabel>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </FormLabel>
                  {field.helpText && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{field.helpText}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <FormControl>
                  <Textarea
                    {...formField}
                    placeholder={field.placeholder}
                    className="min-h-20"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      case "checkbox":
        return (
          <FormField
            control={control}
            name={fieldName}
            render={({ field: formField }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={formField.value}
                    onCheckedChange={formField.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </FormLabel>
                  {field.helpText && (
                    <p className="text-sm text-muted-foreground">{field.helpText}</p>
                  )}
                </div>
              </FormItem>
            )}
          />
        );

      case "file":
        const uploadedFile = watch(fieldName);
        return (
          <FormField
            control={control}
            name={fieldName}
            render={({ field: formField }) => (
              <FormItem>
                <div className="flex items-center gap-2">
                  <FormLabel>
                    {field.label} {field.required && <span className="text-red-500">*</span>}
                  </FormLabel>
                  {field.helpText && (
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{field.helpText}</p>
                      </TooltipContent>
                    </Tooltip>
                  )}
                </div>
                <FormControl>
                  <div className="space-y-4">
                    {!uploadedFile ? (
                      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                        <p className="text-sm text-muted-foreground mb-4">
                          {field.placeholder || "Upload file"}
                        </p>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById(`${field.name}-file-input`)?.click()}
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Choose File
                        </Button>
                        <input
                          id={`${field.name}-file-input`}
                          type="file"
                          onChange={(e) => handleFileUpload(field.name, e)}
                          className="hidden"
                        />
                      </div>
                    ) : (
                      <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <span className="text-sm font-medium text-green-800">
                            {uploadedFile.name}
                          </span>
                          {uploadedFile.size && (
                            <span className="text-xs text-green-600">
                              ({((uploadedFile.size || 0) / 1024 / 1024).toFixed(2)} MB)
                            </span>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(field.name)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        );

      default:
        return null;
    }
  };

  if (isLoading) {
    return <div className="text-sm text-muted-foreground">Loading custom fields...</div>;
  }

  if (customFields.length === 0) {
    return null;
  }

  return (
    <TooltipProvider>
      <motion.div
        className="space-y-6 mt-6"
        variants={staggeredListVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium mb-4">Additional Information</h3>
          <div className="space-y-4">
            {customFields.map((field, index) => (
              <motion.div key={field.name} variants={itemVariants}>
                {renderField(field)}
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </TooltipProvider>
  );
}
