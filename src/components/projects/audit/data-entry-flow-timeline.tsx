"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  FileText, 
  User, 
  CheckCircle, 
  XCircle, 
  Clock, 
  ArrowRight,
  RefreshCw,
  Filter,
  Search,
  Calendar
} from "lucide-react";
import { format } from "date-fns";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface DataEntryFlowEvent {
  id: string;
  type: string;
  description: string;
  userId: string;
  userName?: string; // Optional for backward compatibility
  userEmail?: string; // Optional for backward compatibility
  userRole?: string; // Optional for backward compatibility
  createdAt: string;
  user?: { // Nested user object from audit logs
    id: string;
    name: string;
    email: string;
  };
  metadata: {
    unitLogId?: string;
    action?: string;
    fromStatus?: string;
    toStatus?: string;
    quantity?: number;
    unitType?: string;
    notes?: string;
    rejectionReason?: string;
    userRole?: string; // Role might be in metadata
  };
}

interface DataEntryFlowTimelineProps {
  projectId: string;
  unitLogId?: string; // Optional: filter by specific unit log
}

const FLOW_STAGES = [
  { key: "CREATED", label: "Data Entry", icon: FileText, color: "bg-blue-500" },
  { key: "SUBMITTED", label: "Submitted for Verification", icon: ArrowRight, color: "bg-yellow-500" },
  { key: "PM_VERIFIED", label: "PM Verified", icon: CheckCircle, color: "bg-green-500" },
  { key: "SPV_ADMIN_VERIFIED", label: "SPV Admin Verified", icon: CheckCircle, color: "bg-green-600" },
  { key: "ORG_APPROVED", label: "Org Admin Approved", icon: CheckCircle, color: "bg-green-700" },
  { key: "VVB_VERIFIED", label: "VVB Verified", icon: CheckCircle, color: "bg-green-800" },
  { key: "REJECTED", label: "Rejected", icon: XCircle, color: "bg-red-500" },
];

const getEventIcon = (type: string) => {
  if (type.includes("CREATED") || type.includes("ENTRY")) return <FileText className="h-4 w-4" />;
  if (type.includes("VERIFIED") || type.includes("APPROVED")) return <CheckCircle className="h-4 w-4" />;
  if (type.includes("REJECTED")) return <XCircle className="h-4 w-4" />;
  if (type.includes("SUBMITTED")) return <ArrowRight className="h-4 w-4" />;
  return <Clock className="h-4 w-4" />;
};

const getEventColor = (type: string) => {
  if (type.includes("CREATED") || type.includes("ENTRY")) return "bg-blue-500";
  if (type.includes("VERIFIED") || type.includes("APPROVED")) return "bg-green-500";
  if (type.includes("REJECTED")) return "bg-red-500";
  if (type.includes("SUBMITTED")) return "bg-yellow-500";
  return "bg-gray-500";
};

const getStatusBadgeVariant = (status: string) => {
  if (status.includes("VERIFIED") || status.includes("APPROVED")) return "default";
  if (status.includes("REJECTED")) return "destructive";
  if (status.includes("SUBMITTED") || status.includes("PENDING")) return "secondary";
  return "outline";
};

export function DataEntryFlowTimeline({ projectId, unitLogId }: DataEntryFlowTimelineProps) {
  const [events, setEvents] = useState<DataEntryFlowEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState("all");
  const [filterUser, setFilterUser] = useState("all");

  const fetchFlowEvents = async () => {
    setIsLoading(true);
    try {
      const params = new URLSearchParams({
        projectId,
        ...(unitLogId && { unitLogId }),
        includeDataEntryFlow: "true"
      });

      const response = await fetch(`/api/spv/projects/${projectId}/audit?${params}`);
      if (response.ok) {
        const data = await response.json();
        // Filter for data entry related events
        const dataEntryEvents = data.auditLogs.filter((log: any) => 
          log.type.includes("DATA_") || 
          log.type.includes("VERIFICATION") ||
          log.type.includes("MONITORING_DATA")
        );
        setEvents(dataEntryEvents);
      }
    } catch (error) {
      console.error("Error fetching data entry flow events:", error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchFlowEvents();
  }, [projectId, unitLogId]);

  // Group events by unit log ID
  const groupedEvents = events.reduce((acc, event) => {
    const unitLogId = event.metadata?.unitLogId || "unknown";
    if (!acc[unitLogId]) {
      acc[unitLogId] = [];
    }
    acc[unitLogId].push(event);
    return acc;
  }, {} as Record<string, DataEntryFlowEvent[]>);

  // Filter events
  const filteredGroups = Object.entries(groupedEvents).filter(([unitLogId, events]) => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return events.some(event =>
        event.description.toLowerCase().includes(query) ||
        (event.userName || event.user?.name || '').toLowerCase().includes(query) ||
        (event.userEmail || event.user?.email || '').toLowerCase().includes(query) ||
        (event.metadata?.unitType && event.metadata.unitType.toLowerCase().includes(query))
      );
    }
    return true;
  }).map(([unitLogId, events]) => {
    let filteredEvents = events;
    
    if (filterType !== "all") {
      filteredEvents = events.filter(event => event.type.includes(filterType.toUpperCase()));
    }
    
    if (filterUser !== "all") {
      filteredEvents = events.filter(event => event.userId === filterUser);
    }
    
    return [unitLogId, filteredEvents.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())];
  });

  // Get unique users for filter
  const uniqueUsers = Array.from(new Set(events.map(event => ({ id: event.userId, name: event.userName || event.user?.name || 'Unknown User' }))))
    .filter((user, index, self) => self.findIndex(u => u.id === user.id) === index);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
            Loading Data Entry Flow...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Data Entry Flow Timeline
            </span>
            <Button variant="outline" size="sm" onClick={fetchFlowEvents}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by description, user, or unit type..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="entry">Data Entry</SelectItem>
                <SelectItem value="verified">Verified</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterUser} onValueChange={setFilterUser}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by user" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Users</SelectItem>
                {uniqueUsers.map(user => (
                  <SelectItem key={user.id} value={user.id}>{user.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Timeline */}
      {filteredGroups.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium">No data entry flow events found</h3>
            <p className="text-sm text-muted-foreground mt-2">
              Try adjusting your search or filter criteria
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {filteredGroups.map(([unitLogId, events]) => (
            <Card key={unitLogId}>
              <CardHeader>
                <CardTitle className="text-lg">
                  Unit Log: {unitLogId.slice(0, 8)}...
                  {events[0]?.metadata?.unitType && events[0]?.metadata?.quantity && (
                    <Badge variant="outline" className="ml-2">
                      {events[0].metadata.quantity} {events[0].metadata.unitType}
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {events.map((event, index) => (
                    <div key={event.id} className="flex items-start gap-4">
                      <div className="flex flex-col items-center">
                        <div className={`flex items-center justify-center w-8 h-8 rounded-full text-white ${getEventColor(event.type)}`}>
                          {getEventIcon(event.type)}
                        </div>
                        {index < events.length - 1 && (
                          <div className="w-px h-8 bg-border mt-2" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">{event.description}</p>
                          <span className="text-sm text-muted-foreground">
                            {format(new Date(event.createdAt), "MMM d, h:mm a")}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Avatar className="h-6 w-6">
                            <AvatarFallback className="text-xs">
                              {(event.userName || event.user?.name || 'Unknown').split(' ').map(n => n[0]).join('')}
                            </AvatarFallback>
                          </Avatar>
                          <span className="text-sm text-muted-foreground">
                            {event.userName || event.user?.name || 'Unknown User'} ({(event.userRole || event.metadata?.userRole || 'unknown').replace('_', ' ').toLowerCase()})
                          </span>
                          {event.metadata?.fromStatus && event.metadata?.toStatus && (
                            <Badge variant={getStatusBadgeVariant(event.metadata.toStatus)} className="text-xs">
                              {event.metadata.fromStatus} → {event.metadata.toStatus}
                            </Badge>
                          )}
                        </div>
                        {(event.metadata?.notes || event.metadata?.rejectionReason) && (
                          <p className="text-sm text-muted-foreground mt-2 italic">
                            "{event.metadata.notes || event.metadata.rejectionReason}"
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
