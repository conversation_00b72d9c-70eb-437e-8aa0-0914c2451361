"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Users, 
  UserCog, 
  Key, 
  Activity,
  ArrowRight,
  Shield,
  TrendingUp,
  Clock,
} from "lucide-react";
import { toast } from "sonner";

interface DashboardStats {
  totalUsers: number;
  totalRoles: number;
  totalPermissions: number;
  recentActivity: number;
  systemRoles: number;
  customRoles: number;
  activeUsers: number;
}

export function RBACDashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalRoles: 0,
    totalPermissions: 0,
    recentActivity: 0,
    systemRoles: 0,
    customRoles: 0,
    activeUsers: 0,
  });
  const [loading, setLoading] = useState(true);

  const fetchStats = async () => {
    try {
      setLoading(true);
      
      // Fetch users count
      const usersResponse = await fetch("/api/rbac/users?limit=1");
      const usersData = await usersResponse.json();
      
      // Fetch roles count
      const rolesResponse = await fetch("/api/rbac/roles");
      const rolesData = await rolesResponse.json();
      
      // Fetch permissions count
      const permissionsResponse = await fetch("/api/rbac/permissions");
      const permissionsData = await permissionsResponse.json();
      
      // Fetch audit stats
      const auditResponse = await fetch("/api/rbac/audit?limit=1");
      const auditData = await auditResponse.json();

      if (usersData.success && rolesData.success && permissionsData.success && auditData.success) {
        const roles = rolesData.data || [];
        const systemRoles = roles.filter((role: any) => role.isSystemRole).length;
        const customRoles = roles.filter((role: any) => !role.isSystemRole).length;

        setStats({
          totalUsers: usersData.data?.pagination?.total || 0,
          totalRoles: roles.length,
          totalPermissions: permissionsData.data?.totalPermissions || 0,
          recentActivity: auditData.data?.pagination?.total || 0,
          systemRoles,
          customRoles,
          activeUsers: auditData.data?.stats?.activeUsers || 0,
        });
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      toast.error("Failed to load dashboard statistics");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  const quickActions = [
    {
      title: "Manage Users",
      description: "Add, edit, and manage organization users",
      href: "/dashboard/rbac/users",
      icon: Users,
      color: "bg-blue-500",
    },
    {
      title: "Manage Roles",
      description: "Create and configure custom roles",
      href: "/dashboard/rbac/roles",
      icon: UserCog,
      color: "bg-green-500",
    },
    {
      title: "View Permissions",
      description: "Browse all available permissions",
      href: "/dashboard/rbac/permissions",
      icon: Key,
      color: "bg-purple-500",
    },
    {
      title: "Audit Log",
      description: "Track access and permission changes",
      href: "/dashboard/rbac/audit",
      icon: Activity,
      color: "bg-orange-500",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.totalUsers}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.activeUsers} active in last 30 days
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
            <UserCog className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.totalRoles}
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.systemRoles} system, {stats.customRoles} custom
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Permissions</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.totalPermissions}
            </div>
            <p className="text-xs text-muted-foreground">
              Available permissions
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Recent Activity</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {loading ? "..." : stats.recentActivity}
            </div>
            <p className="text-xs text-muted-foreground">
              Audit log entries
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Quick Actions
          </CardTitle>
          <CardDescription>
            Common RBAC management tasks
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {quickActions.map((action) => (
              <Link key={action.href} href={action.href}>
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      <div className={`p-2 rounded-lg ${action.color}`}>
                        <action.icon className="h-5 w-5 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium">{action.title}</h3>
                        <p className="text-sm text-muted-foreground">
                          {action.description}
                        </p>
                      </div>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* System Status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              System Health
            </CardTitle>
            <CardDescription>
              RBAC system status and health metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Permission System</span>
              <Badge variant="default" className="bg-green-500">
                Operational
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Role Management</span>
              <Badge variant="default" className="bg-green-500">
                Operational
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Audit Logging</span>
              <Badge variant="default" className="bg-green-500">
                Operational
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Recent Updates
            </CardTitle>
            <CardDescription>
              Latest changes to the RBAC system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-sm">
              <div className="font-medium">RBAC System Initialized</div>
              <div className="text-muted-foreground">
                All permissions and system roles have been set up
              </div>
            </div>
            <div className="text-sm">
              <div className="font-medium">Permission Sync Available</div>
              <div className="text-muted-foreground">
                New permissions can be synced from code to database
              </div>
            </div>
            <div className="text-sm">
              <div className="font-medium">Audit Logging Active</div>
              <div className="text-muted-foreground">
                All permission checks and role changes are being logged
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Getting Started */}
      <Card>
        <CardHeader>
          <CardTitle>Getting Started with RBAC</CardTitle>
          <CardDescription>
            Follow these steps to set up role-based access control for your organization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center font-medium">
                  1
                </div>
                <span className="font-medium">Create Roles</span>
              </div>
              <p className="text-sm text-muted-foreground ml-8">
                Define custom roles with specific permissions for your organization's needs
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center font-medium">
                  2
                </div>
                <span className="font-medium">Add Users</span>
              </div>
              <p className="text-sm text-muted-foreground ml-8">
                Invite team members and assign appropriate roles based on their responsibilities
              </p>
            </div>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 rounded-full bg-purple-500 text-white text-xs flex items-center justify-center font-medium">
                  3
                </div>
                <span className="font-medium">Monitor Access</span>
              </div>
              <p className="text-sm text-muted-foreground ml-8">
                Use the audit log to track access patterns and ensure security compliance
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
