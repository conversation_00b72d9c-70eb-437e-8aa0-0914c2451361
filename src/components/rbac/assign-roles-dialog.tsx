"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Shield, Users, Key, Trash2, Eye } from "lucide-react";
import { toast } from "sonner";

interface Role {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  permissions: Array<{
    name: string;
    displayName: string;
    category: string;
  }>;
  userCount: number;
}

interface UserRole {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  isSystemRole: boolean;
  assignedAt: string;
  expiresAt?: string;
}

interface User {
  id: string;
  name: string;
  email: string;
  customRoles: UserRole[];
}

interface AssignRolesDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: User;
  onSuccess: () => void;
}

export function AssignRolesDialog({ open, onOpenChange, user, onSuccess }: AssignRolesDialogProps) {
  const [availableRoles, setAvailableRoles] = useState<Role[]>([]);
  const [selectedRoleIds, setSelectedRoleIds] = useState<string[]>([]);
  const [rolesToRevoke, setRolesToRevoke] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedRoleForPermissions, setSelectedRoleForPermissions] = useState<Role | null>(null);
  const [showPermissionsDialog, setShowPermissionsDialog] = useState(false);

  const fetchRoles = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/rbac/roles");
      if (!response.ok) {
        throw new Error("Failed to fetch roles");
      }
      const data = await response.json();
      setAvailableRoles(data.data);
    } catch (error) {
      console.error("Error fetching roles:", error);
      toast.error("Failed to fetch roles");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchRoles();
      setSelectedRoleIds([]);
      setRolesToRevoke([]);
    }
  }, [open]);

  const handleAssignRoles = async () => {
    if (selectedRoleIds.length === 0) {
      toast.error("Please select at least one role to assign");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/rbac/users/${user.id}/roles`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roleIds: selectedRoleIds,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to assign roles");
      }

      toast.success("Roles assigned successfully");
      setSelectedRoleIds([]);
      onSuccess();
    } catch (error) {
      console.error("Error assigning roles:", error);
      toast.error(error instanceof Error ? error.message : "Failed to assign roles");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRevokeRoles = async () => {
    if (rolesToRevoke.length === 0) {
      toast.error("Please select at least one role to revoke");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch(`/api/rbac/users/${user.id}/roles`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          roleIds: rolesToRevoke,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to revoke roles");
      }

      toast.success("Roles revoked successfully");
      setRolesToRevoke([]);
      onSuccess();
    } catch (error) {
      console.error("Error revoking roles:", error);
      toast.error(error instanceof Error ? error.message : "Failed to revoke roles");
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentRoleIds = user.customRoles.map(role => role.id);
  const availableToAssign = availableRoles.filter(role => !currentRoleIds.includes(role.id));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Manage Roles for {user.name}
          </DialogTitle>
          <DialogDescription>
            Assign or revoke roles for this user
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Roles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Current Roles
              </CardTitle>
              <CardDescription>
                Roles currently assigned to this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              {user.customRoles.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  No custom roles assigned
                </p>
              ) : (
                <div className="space-y-3">
                  {user.customRoles.map((role) => (
                    <div key={role.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={rolesToRevoke.includes(role.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setRolesToRevoke([...rolesToRevoke, role.id]);
                            } else {
                              setRolesToRevoke(rolesToRevoke.filter(id => id !== role.id));
                            }
                          }}
                        />
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{role.displayName}</span>
                            {role.isSystemRole && (
                              <Badge variant="secondary" className="text-xs">
                                System
                              </Badge>
                            )}
                          </div>
                          {role.description && (
                            <p className="text-sm text-muted-foreground">{role.description}</p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            Assigned on {new Date(role.assignedAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                  
                  {rolesToRevoke.length > 0 && (
                    <div className="flex justify-end pt-2">
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={handleRevokeRoles}
                        disabled={isSubmitting}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Revoke Selected ({rolesToRevoke.length})
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          <Separator />

          {/* Available Roles */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                Available Roles
              </CardTitle>
              <CardDescription>
                Select roles to assign to this user
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="text-center py-4">Loading available roles...</div>
              ) : availableToAssign.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">
                  All available roles are already assigned to this user
                </p>
              ) : (
                <div className="space-y-3">
                  {availableToAssign.map((role) => (
                    <div key={role.id} className="flex items-center space-x-3 p-3 border rounded-lg">
                      <Checkbox
                        checked={selectedRoleIds.includes(role.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedRoleIds([...selectedRoleIds, role.id]);
                          } else {
                            setSelectedRoleIds(selectedRoleIds.filter(id => id !== role.id));
                          }
                        }}
                      />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{role.displayName}</span>
                          {role.isSystemRole && (
                            <Badge variant="secondary" className="text-xs">
                              System
                            </Badge>
                          )}
                        </div>
                        {role.description && (
                          <p className="text-sm text-muted-foreground">{role.description}</p>
                        )}
                        <div className="flex items-center justify-between mt-1">
                          <div className="flex items-center gap-4 text-xs text-muted-foreground">
                            <span>{role.permissions.length} permissions</span>
                            <span>{role.userCount} users</span>
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-6 px-2 text-xs"
                            onClick={() => {
                              setSelectedRoleForPermissions(role);
                              setShowPermissionsDialog(true);
                            }}
                          >
                            <Eye className="h-3 w-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          {selectedRoleIds.length > 0 && (
            <Button
              onClick={handleAssignRoles}
              disabled={isSubmitting}
            >
              {isSubmitting ? "Assigning..." : `Assign Selected (${selectedRoleIds.length})`}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>

      {/* Permissions View Dialog */}
      <Dialog open={showPermissionsDialog} onOpenChange={setShowPermissionsDialog}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              {selectedRoleForPermissions?.displayName} Permissions
            </DialogTitle>
            <DialogDescription>
              View all permissions included in this role
            </DialogDescription>
          </DialogHeader>

          {selectedRoleForPermissions && (
            <div className="space-y-4">
              {/* Role Info */}
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <h3 className="font-medium">{selectedRoleForPermissions.displayName}</h3>
                  {selectedRoleForPermissions.isSystemRole && (
                    <Badge variant="secondary" className="text-xs">
                      System Role
                    </Badge>
                  )}
                </div>
                {selectedRoleForPermissions.description && (
                  <p className="text-sm text-muted-foreground mb-2">
                    {selectedRoleForPermissions.description}
                  </p>
                )}
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>{selectedRoleForPermissions.permissions.length} permissions total</span>
                  <span>{selectedRoleForPermissions.userCount} users assigned</span>
                </div>
              </div>

              {/* Permissions List */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm">Permissions:</h4>
                {selectedRoleForPermissions.permissions.length > 0 ? (
                  <div className="grid gap-2">
                    {selectedRoleForPermissions.permissions.map((permission, index) => (
                      <div key={index} className="flex items-start gap-3 p-2 border rounded">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{permission.displayName}</div>
                          {permission.description && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {permission.description}
                            </div>
                          )}
                          <div className="text-xs text-muted-foreground mt-1">
                            Category: {permission.category}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground">No permissions assigned to this role.</p>
                )}
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowPermissionsDialog(false)}
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
