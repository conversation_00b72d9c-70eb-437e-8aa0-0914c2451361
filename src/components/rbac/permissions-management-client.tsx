"use client";

import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { 
  Key, 
  Search, 
  RefreshCw,
  Shield,
  Users,
  Filter,
} from "lucide-react";
import { toast } from "sonner";

interface Permission {
  name: string;
  displayName: string;
  description?: string;
  category: string;
  usageCount: number;
}

interface PermissionsByCategory {
  [category: string]: Permission[];
}

interface PermissionsData {
  permissions: PermissionsByCategory;
  totalPermissions: number;
  categories: string[];
}

export function PermissionsManagementClient() {
  const [permissionsData, setPermissionsData] = useState<PermissionsData>({
    permissions: {},
    totalPermissions: 0,
    categories: [],
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [syncing, setSyncing] = useState(false);

  const fetchPermissions = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/rbac/permissions");
      if (!response.ok) {
        throw new Error("Failed to fetch permissions");
      }
      const data = await response.json();
      setPermissionsData(data.data);
    } catch (error) {
      console.error("Error fetching permissions:", error);
      toast.error("Failed to fetch permissions");
    } finally {
      setLoading(false);
    }
  };

  const syncPermissions = async () => {
    try {
      setSyncing(true);
      const response = await fetch("/api/rbac/permissions", {
        method: "POST",
      });
      if (!response.ok) {
        throw new Error("Failed to sync permissions");
      }
      const data = await response.json();
      toast.success(`Permissions synced: ${data.data.created} created, ${data.data.updated} updated`);
      fetchPermissions();
    } catch (error) {
      console.error("Error syncing permissions:", error);
      toast.error("Failed to sync permissions");
    } finally {
      setSyncing(false);
    }
  };

  useEffect(() => {
    fetchPermissions();
  }, []);

  // Filter permissions based on search term and category
  const filteredPermissions = Object.entries(permissionsData.permissions).reduce((acc, [category, permissions]) => {
    if (selectedCategory !== "all" && category !== selectedCategory) {
      return acc;
    }

    const filtered = permissions.filter(permission =>
      permission.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (permission.description && permission.description.toLowerCase().includes(searchTerm.toLowerCase()))
    );

    if (filtered.length > 0) {
      acc[category] = filtered;
    }

    return acc;
  }, {} as PermissionsByCategory);

  const totalFilteredPermissions = Object.values(filteredPermissions).reduce(
    (sum, permissions) => sum + permissions.length,
    0
  );

  const getCategoryStats = (category: string) => {
    const permissions = permissionsData.permissions[category] || [];
    const totalUsage = permissions.reduce((sum, p) => sum + p.usageCount, 0);
    return {
      count: permissions.length,
      usage: totalUsage,
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Permissions</h2>
          <p className="text-muted-foreground">
            View and manage all available permissions in the system
          </p>
        </div>
        <Button onClick={syncPermissions} disabled={syncing}>
          <RefreshCw className={`h-4 w-4 mr-2 ${syncing ? 'animate-spin' : ''}`} />
          {syncing ? "Syncing..." : "Sync Permissions"}
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
            <Key className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{permissionsData.totalPermissions}</div>
            <p className="text-xs text-muted-foreground">
              Across {permissionsData.categories.length} categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Categories</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{permissionsData.categories.length}</div>
            <p className="text-xs text-muted-foreground">
              Permission categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Most Used</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.max(...Object.values(permissionsData.permissions).flat().map(p => p.usageCount), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Highest usage count
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filter
          </CardTitle>
          <CardDescription>
            Find specific permissions or browse by category
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search permissions by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 border rounded-md text-sm"
              >
                <option value="all">All Categories</option>
                {permissionsData.categories.map((category) => (
                  <option key={category} value={category}>
                    {category} ({getCategoryStats(category).count})
                  </option>
                ))}
              </select>
            </div>
          </div>
          {searchTerm || selectedCategory !== "all" ? (
            <div className="mt-4 text-sm text-muted-foreground">
              Showing {totalFilteredPermissions} of {permissionsData.totalPermissions} permissions
            </div>
          ) : null}
        </CardContent>
      </Card>

      {/* Permissions List */}
      {loading ? (
        <Card>
          <CardContent className="text-center py-8">
            Loading permissions...
          </CardContent>
        </Card>
      ) : Object.keys(filteredPermissions).length === 0 ? (
        <Card>
          <CardContent className="text-center py-8">
            <Key className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No permissions found</h3>
            <p className="text-muted-foreground">
              {searchTerm || selectedCategory !== "all"
                ? "Try adjusting your search or filter criteria"
                : "No permissions are available"}
            </p>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Permissions by Category</CardTitle>
            <CardDescription>
              Expand categories to view individual permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Accordion type="multiple" className="w-full">
              {Object.entries(filteredPermissions).map(([category, permissions]) => {
                const stats = getCategoryStats(category);
                return (
                  <AccordionItem key={category} value={category}>
                    <AccordionTrigger className="hover:no-underline">
                      <div className="flex items-center justify-between w-full mr-4">
                        <div className="flex items-center gap-3">
                          <span className="font-medium">{category}</span>
                          <Badge variant="outline" className="text-xs">
                            {permissions.length} permissions
                          </Badge>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {stats.usage} total usage
                        </div>
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <div className="space-y-3">
                        {permissions.map((permission) => (
                          <div
                            key={permission.name}
                            className="flex items-start justify-between p-4 border rounded-lg"
                          >
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <span className="font-medium">{permission.displayName}</span>
                                <Badge variant="secondary" className="text-xs font-mono">
                                  {permission.name}
                                </Badge>
                              </div>
                              {permission.description && (
                                <p className="text-sm text-muted-foreground">
                                  {permission.description}
                                </p>
                              )}
                            </div>
                            <div className="text-right">
                              <div className="text-sm font-medium">{permission.usageCount}</div>
                              <div className="text-xs text-muted-foreground">roles using</div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                );
              })}
            </Accordion>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
