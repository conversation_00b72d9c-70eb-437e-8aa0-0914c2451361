/**
 * Organization Verification Dashboard Component
 * 
 * Reusable component for organization admin verification functionality
 * Can be used in both standalone page and as part of SPV Management tabs
 */

"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  CheckCircle,
  XCircle,
  Clock,
  Send,
  AlertCircle,
  Award,
  FileText,
  Users,
  TrendingUp
} from "lucide-react";
import { toast } from "sonner";
// import { VerificationAuditTrail } from "@/components/verification/verification-audit-trail";

interface UnitLogEntry {
  id: string;
  date: string;
  unitType: string;
  quantity: number;
  status: string;
  project: {
    id: string;
    name: string;
  };
  logger: {
    id: string;
    name: string;
  };
  verifiedBy?: {
    id: string;
    name: string;
  };
  notes?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

interface VerificationStats {
  SUBMITTED_TO_ORG_ADMIN: number;
  ORG_APPROVED: number;
  ORG_REJECTED: number;
  SUBMITTED_TO_VVB: number;
  VVB_VERIFIED: number;
  VVB_REJECTED: number;
}

export function OrganizationVerificationDashboard() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("pending");
  const [unitLogs, setUnitLogs] = useState<UnitLogEntry[]>([]);
  const [stats, setStats] = useState<VerificationStats>({
    SUBMITTED_TO_ORG_ADMIN: 0,
    ORG_APPROVED: 0,
    ORG_REJECTED: 0,
    SUBMITTED_TO_VVB: 0,
    VVB_VERIFIED: 0,
    VVB_REJECTED: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [projectFilter, setProjectFilter] = useState<string>("all");
  const [actionDialog, setActionDialog] = useState<{
    isOpen: boolean;
    action: string | null;
  }>({ isOpen: false, action: null });
  const [actionNotes, setActionNotes] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if user is organization admin - moved after all hooks
  const isOrgAdmin = session?.user?.role === "ORGANIZATION_ADMIN";

  // Early return after all hooks are declared
  if (!isOrgAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">
            Only organization administrators can access the verification dashboard.
          </p>
        </div>
      </div>
    );
  }

  const getStatusForTab = (tab: string) => {
    switch (tab) {
      case "pending":
        return "SUBMITTED_TO_ORG_ADMIN";
      case "approved":
        return "ORG_APPROVED";
      case "submitted":
        return "SUBMITTED_TO_VVB";
      case "vvb-verified":
        return "VVB_VERIFIED";
      case "vvb-rejected":
        return "VVB_REJECTED";
      default:
        return "SUBMITTED_TO_ORG_ADMIN";
    }
  };

  const fetchVerificationData = async () => {
    try {
      setLoading(true);
      const status = getStatusForTab(activeTab);

      const params = new URLSearchParams({
        status,
        ...(projectFilter !== "all" && { projectId: projectFilter }),
      });

      const response = await fetch(`/api/organizations/verification/data?${params}`);

      if (!response.ok) {
        console.error("Verification API failed:", response.status, response.statusText);
        setUnitLogs([]);
        setStats({
          SUBMITTED_TO_ORG_ADMIN: 0,
          ORG_APPROVED: 0,
          ORG_REJECTED: 0,
          SUBMITTED_TO_VVB: 0,
          VVB_VERIFIED: 0,
          VVB_REJECTED: 0,
        });
        return;
      }

      const result = await response.json();

      if (result.success && result.data) {
        setUnitLogs(result.data.unitLogs || []);
        setStats(result.data.statistics || {
          SUBMITTED_TO_ORG_ADMIN: 0,
          ORG_APPROVED: 0,
          ORG_REJECTED: 0,
          SUBMITTED_TO_VVB: 0,
          VVB_VERIFIED: 0,
          VVB_REJECTED: 0,
        });
      } else {
        console.error("API returned unsuccessful response:", result);
        setUnitLogs([]);
        setStats({
          SUBMITTED_TO_ORG_ADMIN: 0,
          ORG_APPROVED: 0,
          ORG_REJECTED: 0,
          SUBMITTED_TO_VVB: 0,
          VVB_VERIFIED: 0,
          VVB_REJECTED: 0,
        });
      }
    } catch (error) {
      console.error("Error fetching verification data:", error);
      setUnitLogs([]);
      setStats({
        SUBMITTED_TO_ORG_ADMIN: 0,
        ORG_APPROVED: 0,
        ORG_REJECTED: 0,
        SUBMITTED_TO_VVB: 0,
        VVB_VERIFIED: 0,
        VVB_REJECTED: 0,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isOrgAdmin && session?.user?.organizationId) {
      fetchVerificationData();
    }
  }, [activeTab, projectFilter, session?.user?.organizationId, isOrgAdmin]);

  const handleAction = (action: string) => {
    if (selectedEntries.length === 0) {
      toast.error("Please select at least one entry");
      return;
    }
    setActionDialog({ isOpen: true, action });
    setActionNotes("");
    setRejectionReason("");
  };

  const performAction = async () => {
    if (!actionDialog.action) return;

    // Validate rejection reason for rejection actions
    if (actionDialog.action === "ORG_ADMIN_REJECT" && !rejectionReason?.trim()) {
      toast.error("Rejection reason is required for rejection actions");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch("/api/organizations/verification/data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          unitLogIds: selectedEntries,
          action: actionDialog.action,
          notes: actionNotes,
          rejectionReason: rejectionReason,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to perform verification action");
      }

      const result = await response.json();
      toast.success(result.message);

      setActionDialog({ isOpen: false, action: null });
      setSelectedEntries([]);
      await fetchVerificationData();
    } catch (error) {
      console.error("Error performing action:", error);
      toast.error("Failed to perform verification action");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleSelectEntry = (entryId: string, checked: boolean) => {
    if (checked) {
      setSelectedEntries([...selectedEntries, entryId]);
    } else {
      setSelectedEntries(selectedEntries.filter(id => id !== entryId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEntries(unitLogs.map(log => log.id));
    } else {
      setSelectedEntries([]);
    }
  };

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.SUBMITTED_TO_ORG_ADMIN || 0}</div>
            <p className="text-xs text-muted-foreground">Awaiting Review</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.ORG_APPROVED || 0}</div>
            <p className="text-xs text-muted-foreground">Ready for VVB</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submitted to VVB</CardTitle>
            <Send className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.SUBMITTED_TO_VVB || 0}</div>
            <p className="text-xs text-muted-foreground">Under VVB Review</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VVB Verified</CardTitle>
            <Award className="h-4 w-4 text-green-700" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.VVB_VERIFIED || 0}</div>
            <p className="text-xs text-muted-foreground">Fully Certified</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VVB Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.VVB_REJECTED || 0}</div>
            <p className="text-xs text-muted-foreground">Rejected by VVB</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="pending">Pending Review</TabsTrigger>
          <TabsTrigger value="approved">Org Approved</TabsTrigger>
          <TabsTrigger value="submitted">Submitted to VVB</TabsTrigger>
          <TabsTrigger value="vvb-verified">VVB Verified</TabsTrigger>
          <TabsTrigger value="vvb-rejected">VVB Rejected</TabsTrigger>
          <TabsTrigger value="audit">Audit Trail</TabsTrigger>
        </TabsList>

        <TabsContent value="audit">
          <Card>
            <CardHeader>
              <CardTitle>Audit Trail</CardTitle>
              <CardDescription>
                Complete audit trail of all verification actions and status changes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">Audit Trail</h3>
                <p className="text-muted-foreground">
                  Audit trail functionality will be available here
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {["pending", "approved", "submitted", "vvb-verified", "vvb-rejected"].map((tab) => (
          <TabsContent key={tab} value={tab}>
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Data Entries</CardTitle>
                    <CardDescription>
                      {tab === "pending" && "Review and approve data entries verified by SPV admins"}
                      {tab === "approved" && "Data entries approved by organization admin"}
                      {tab === "submitted" && "Data entries submitted to VVB for verification"}
                      {tab === "vvb-verified" && "Data entries verified and certified by VVB"}
                      {tab === "vvb-rejected" && "Data entries rejected by VVB"}
                    </CardDescription>
                  </div>
                  <Badge variant="outline">
                    {selectedEntries.length} selected
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                {/* Filters and Actions */}
                <div className="flex justify-between items-center mb-4">
                  <div className="flex space-x-2">
                    <Select value={projectFilter} onValueChange={setProjectFilter}>
                      <SelectTrigger className="w-48">
                        <SelectValue placeholder="Filter by project" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Projects</SelectItem>
                        {/* Add project options here */}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex space-x-2">
                    {tab === "pending" && (
                      <>
                        <Button
                          onClick={() => handleAction("ORG_ADMIN_VERIFY")}
                          disabled={selectedEntries.length === 0}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </Button>
                        <Button
                          onClick={() => handleAction("ORG_ADMIN_REJECT")}
                          disabled={selectedEntries.length === 0}
                          variant="destructive"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Reject
                        </Button>
                      </>
                    )}
                    {tab === "approved" && (
                      <Button
                        onClick={() => handleAction("SUBMIT_TO_VVB")}
                        disabled={selectedEntries.length === 0}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        Submit to VVB
                      </Button>
                    )}
                  </div>
                </div>

                {/* Data Table */}
                {loading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
                    <p className="mt-2 text-muted-foreground">Loading verification data...</p>
                  </div>
                ) : unitLogs.length > 0 ? (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        checked={selectedEntries.length === unitLogs.length}
                        onCheckedChange={handleSelectAll}
                      />
                      <span className="text-sm text-muted-foreground">Select all</span>
                    </div>
                    
                    <div className="space-y-2">
                      {unitLogs.map((log) => (
                        <div key={log.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                          <Checkbox
                            checked={selectedEntries.includes(log.id)}
                            onCheckedChange={(checked) => handleSelectEntry(log.id, checked as boolean)}
                          />
                          <div className="flex-1">
                            <div className="flex justify-between items-start">
                              <div>
                                <h4 className="font-medium">{log.project.name}</h4>
                                <p className="text-sm text-muted-foreground">
                                  {log.unitType} - {log.quantity} units
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  Logged by {log.logger.name} on {new Date(log.date).toLocaleDateString()}
                                </p>
                              </div>
                              <Badge variant="outline">{log.status}</Badge>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No entries found</h3>
                    <p className="text-muted-foreground">
                      There are no data entries in this category at this time.
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Action Dialog */}
      <Dialog open={actionDialog.isOpen} onOpenChange={(open) => setActionDialog({ isOpen: open, action: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{getActionTitle(actionDialog.action || "")}</DialogTitle>
            <DialogDescription>
              You are about to {actionDialog.action?.toLowerCase().replace("_", " ")} {selectedEntries.length} data entries.
              {actionDialog.action === "ORG_ADMIN_REJECT" && " Please provide a reason for rejection."}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Notes (Optional)</label>
              <Textarea
                value={actionNotes}
                onChange={(e) => setActionNotes(e.target.value)}
                placeholder="Add any additional notes..."
                className="mt-1"
              />
            </div>
            {actionDialog.action === "ORG_ADMIN_REJECT" && (
              <div>
                <label className="text-sm font-medium">Rejection Reason *</label>
                <Textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Please provide a reason for rejection..."
                  className="mt-1"
                  required
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setActionDialog({ isOpen: false, action: null })}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={performAction}
              disabled={isSubmitting || (actionDialog.action === "ORG_ADMIN_REJECT" && !rejectionReason?.trim())}
            >
              {isSubmitting ? "Processing..." : "Confirm"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

const getActionTitle = (action: string) => {
  switch (action) {
    case "ORG_ADMIN_VERIFY":
      return "Approve Selected Entries";
    case "ORG_ADMIN_REJECT":
      return "Reject Selected Entries";
    case "SUBMIT_TO_VVB":
      return "Submit to VVB";
    default:
      return "Confirm Action";
  }
};
