"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Filter,
  Download,
  Clock,
  User,
  FileText,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
  Calendar,
} from "lucide-react";
import { toast } from "sonner";

interface AuditLogEntry {
  id: string;
  type: string;
  description: string;
  userId?: string;
  organizationId?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  organization?: {
    id: string;
    name: string;
  };
}

interface VerificationAuditTrailProps {
  unitLogId?: string;
  projectId?: string;
  organizationId?: string;
  showFilters?: boolean;
}

export function VerificationAuditTrail({
  unitLogId,
  projectId,
  organizationId,
  showFilters = true,
}: VerificationAuditTrailProps) {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [typeFilter, setTypeFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchAuditLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "20",
      });

      if (unitLogId) params.append("unitLogId", unitLogId);
      if (projectId) params.append("projectId", projectId);
      if (organizationId) params.append("organizationId", organizationId);
      if (typeFilter !== "all") params.append("type", typeFilter);
      if (dateFilter !== "all") params.append("dateFilter", dateFilter);

      const response = await fetch(`/api/audit/verification?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch audit logs");
      }

      const data = await response.json();
      setAuditLogs(data.auditLogs || []);
      setTotalPages(data.pagination?.pages || 1);
    } catch (error) {
      console.error("Error fetching audit logs:", error);
      toast.error("Failed to load audit logs");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAuditLogs();
  }, [currentPage, unitLogId, projectId, organizationId]);

  const handleSearch = () => {
    setCurrentPage(1);
    fetchAuditLogs();
  };

  const handleExport = async () => {
    try {
      const params = new URLSearchParams();
      if (unitLogId) params.append("unitLogId", unitLogId);
      if (projectId) params.append("projectId", projectId);
      if (organizationId) params.append("organizationId", organizationId);
      if (searchTerm) params.append("search", searchTerm);
      if (typeFilter !== "all") params.append("type", typeFilter);
      params.append("export", "true");

      const response = await fetch(`/api/audit/verification?${params}`);
      if (!response.ok) {
        throw new Error("Failed to export audit logs");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.style.display = "none";
      a.href = url;
      a.download = `verification-audit-logs-${new Date().toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success("Audit logs exported successfully");
    } catch (error) {
      console.error("Error exporting audit logs:", error);
      toast.error("Failed to export audit logs");
    }
  };

  const getActionIcon = (type: string) => {
    switch (type) {
      case "DATA_VERIFIED":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "DATA_REJECTED":
        return <XCircle className="h-4 w-4 text-red-600" />;
      case "VERIFICATION_REQUESTED":
        return <AlertCircle className="h-4 w-4 text-blue-600" />;
      case "VVB_VERIFICATION_APPROVED":
        return <CheckCircle className="h-4 w-4 text-green-700" />;
      case "VVB_VERIFICATION_REJECTED":
        return <XCircle className="h-4 w-4 text-red-700" />;
      case "ORG_ADMIN_VERIFICATION_APPROVED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "ORG_ADMIN_VERIFICATION_REJECTED":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionBadge = (type: string) => {
    switch (type) {
      case "DATA_VERIFIED":
        return <Badge variant="default" className="bg-green-100 text-green-800">Verified</Badge>;
      case "DATA_REJECTED":
        return <Badge variant="destructive">Rejected</Badge>;
      case "VERIFICATION_REQUESTED":
        return <Badge variant="secondary">Requested</Badge>;
      case "VVB_VERIFICATION_APPROVED":
        return <Badge variant="default" className="bg-green-200 text-green-900">VVB Approved</Badge>;
      case "VVB_VERIFICATION_REJECTED":
        return <Badge variant="destructive" className="bg-red-200 text-red-900">VVB Rejected</Badge>;
      case "ORG_ADMIN_VERIFICATION_APPROVED":
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Org Approved</Badge>;
      case "ORG_ADMIN_VERIFICATION_REJECTED":
        return <Badge variant="destructive" className="bg-red-100 text-red-800">Org Rejected</Badge>;
      default:
        return <Badge variant="outline">{type.replace(/_/g, " ")}</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Verification Audit Trail</h2>
          <p className="text-muted-foreground">
            Complete audit trail of all verification actions and status changes
          </p>
        </div>
        <Button onClick={handleExport} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export
        </Button>
      </div>

      {showFilters && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
              <div>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="DATA_VERIFIED">Verified</SelectItem>
                    <SelectItem value="DATA_REJECTED">Rejected</SelectItem>
                    <SelectItem value="VERIFICATION_REQUESTED">Requested</SelectItem>
                    <SelectItem value="VVB_VERIFICATION_APPROVED">VVB Approved</SelectItem>
                    <SelectItem value="VVB_VERIFICATION_REJECTED">VVB Rejected</SelectItem>
                    <SelectItem value="ORG_ADMIN_VERIFICATION_APPROVED">Org Approved</SelectItem>
                    <SelectItem value="ORG_ADMIN_VERIFICATION_REJECTED">Org Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Time</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">This Week</SelectItem>
                    <SelectItem value="month">This Month</SelectItem>
                    <SelectItem value="quarter">This Quarter</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Input
                  type="date"
                  placeholder="Start date"
                  value=""
                  onChange={() => {}}
                />
              </div>
              <div>
                <Button onClick={handleSearch} className="w-full">
                  <Filter className="h-4 w-4 mr-2" />
                  Apply Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Audit Log Entries</CardTitle>
            <Badge variant="outline" className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {auditLogs.length} entries
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : auditLogs.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Audit Logs Found</h3>
              <p className="text-muted-foreground">
                No audit log entries match your current filters.
              </p>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Action</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Date & Time</TableHead>
                  <TableHead>Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {auditLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        {getActionIcon(log.type)}
                        {getActionBadge(log.type)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-md">
                        <p className="text-sm">{log.description}</p>
                        {log.metadata?.rejectionReason && (
                          <p className="text-xs text-red-600 mt-1">
                            Reason: {log.metadata.rejectionReason}
                          </p>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">
                            {log.user?.name || "Unknown User"}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {log.user?.role?.replace("_", " ")}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm">
                            {new Date(log.createdAt).toLocaleDateString()}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(log.createdAt).toLocaleTimeString()}
                          </p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {log.metadata && (
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Page {currentPage} of {totalPages}
          </p>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
