"use client";

import { useState, useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/components/ui/use-toast";
import { 
  Plus, 
  Trash2, 
  Save, 
  Settings,
  HelpCircle
} from "lucide-react";
import { motion } from "framer-motion";

const customFieldSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Field name is required"),
  label: z.string().min(1, "Field label is required"),
  type: z.enum(["text", "number", "date", "select", "textarea", "file", "checkbox"]),
  required: z.boolean().default(false),
  placeholder: z.string().optional(),
  helpText: z.string().optional(),
  options: z.array(z.string()).optional(),
  step: z.string().optional(),
});

const formSchema = z.object({
  projectType: z.string().min(1, "Project type is required"),
  projectSubtype: z.string().optional(),
  fields: z.array(customFieldSchema),
});

type FormValues = z.infer<typeof formSchema>;

interface CustomFieldsManagerProps {
  projectType?: string;
  projectSubtype?: string;
}

export function CustomFieldsManager({ 
  projectType = "RENEWABLE_ENERGY", 
  projectSubtype = "SOLAR_PV" 
}: CustomFieldsManagerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      projectType,
      projectSubtype,
      fields: [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "fields",
  });

  // Load existing custom fields
  useEffect(() => {
    const loadCustomFields = async () => {
      setIsLoading(true);
      try {
        const params = new URLSearchParams({
          projectType,
          ...(projectSubtype && { projectSubtype }),
        });

        const response = await fetch(`/api/organizations/custom-fields?${params}`);
        if (response.ok) {
          const data = await response.json();
          if (data.success && data.data.fields) {
            form.setValue("fields", data.data.fields);
          }
        }
      } catch (error) {
        console.error("Error loading custom fields:", error);
        toast({
          title: "Error",
          description: "Failed to load custom fields",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadCustomFields();
  }, [projectType, projectSubtype, form]);

  const addField = () => {
    append({
      name: "",
      label: "",
      type: "text",
      required: false,
      placeholder: "",
      helpText: "",
      step: "SOLAR_BASIC_INFO",
    });
  };

  const onSubmit = async (data: FormValues) => {
    setIsSaving(true);
    try {
      const response = await fetch("/api/organizations/custom-fields", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Custom fields saved successfully",
        });
      } else {
        throw new Error("Failed to save custom fields");
      }
    } catch (error) {
      console.error("Error saving custom fields:", error);
      toast({
        title: "Error",
        description: "Failed to save custom fields",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const fieldTypes = [
    { value: "text", label: "Text" },
    { value: "number", label: "Number" },
    { value: "date", label: "Date" },
    { value: "select", label: "Select" },
    { value: "textarea", label: "Textarea" },
    { value: "file", label: "File" },
    { value: "checkbox", label: "Checkbox" },
  ];

  const stepOptions = [
    { value: "SOLAR_BASIC_INFO", label: "Solar Basic Info" },
    { value: "SOLAR_LOCATION_TECHNICAL", label: "Location & Technical" },
    { value: "SOLAR_EQUIPMENT", label: "Equipment" },
    { value: "SOLAR_INFRASTRUCTURE", label: "Infrastructure" },
    { value: "SOLAR_LAND_RESOURCES", label: "Land & Resources" },
  ];

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">Loading custom fields...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Custom Fields Manager
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Add custom fields to the {projectSubtype} project creation form
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Custom Fields</h3>
            <Button type="button" onClick={addField} variant="outline">
              <Plus className="h-4 w-4 mr-2" />
              Add Field
            </Button>
          </div>

          <div className="space-y-4">
            {fields.map((field, index) => (
              <motion.div
                key={field.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="p-4 border rounded-lg space-y-4"
              >
                <div className="flex justify-between items-center">
                  <h4 className="font-medium">Field {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => remove(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`fields.${index}.name`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Field Name</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="e.g., customField1" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`fields.${index}.label`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Field Label</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="e.g., Custom Field" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`fields.${index}.type`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Field Type</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {fieldTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`fields.${index}.step`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Show in Step</FormLabel>
                        <FormControl>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {stepOptions.map((step) => (
                                <SelectItem key={step.value} value={step.value}>
                                  {step.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name={`fields.${index}.placeholder`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Placeholder</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter placeholder text" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name={`fields.${index}.required`}
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Required Field</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name={`fields.${index}.helpText`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Help Text</FormLabel>
                      <FormControl>
                        <Textarea {...field} placeholder="Help text for users" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </motion.div>
            ))}
          </div>

          {fields.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              No custom fields added yet. Click "Add Field" to get started.
            </div>
          )}

          <div className="flex justify-end">
            <Button type="submit" disabled={isSaving}>
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? "Saving..." : "Save Custom Fields"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
