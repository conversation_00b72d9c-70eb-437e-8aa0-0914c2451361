"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, FolderOpen, Users, CheckCircle } from "lucide-react";
import { toast } from "sonner";

const projectAssignmentSchema = z.object({
  spvUserId: z.string().min(1, "Please select a user"),
  projectIds: z.array(z.string()).min(1, "Please select at least one project"),
});

type ProjectAssignmentFormValues = z.infer<typeof projectAssignmentSchema>;

interface Project {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  location?: string;
  country?: string;
}

interface SPVUser {
  id: string;
  role: string;
  user: {
    id: string;
    name: string;
    email: string;
    jobTitle?: string;
  };
  spv: {
    id: string;
    name: string;
  };
  projectAssignments: {
    id: string;
    project: {
      id: string;
      name: string;
    };
  }[];
}

interface OrgProjectAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function OrgProjectAssignmentModal({
  isOpen,
  onClose,
  onSuccess,
}: OrgProjectAssignmentModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [spvUsers, setSPVUsers] = useState<SPVUser[]>([]);
  const [loading, setLoading] = useState(false);

  const form = useForm<ProjectAssignmentFormValues>({
    resolver: zodResolver(projectAssignmentSchema),
    defaultValues: {
      spvUserId: "",
      projectIds: [],
    },
  });

  const selectedUserId = form.watch("spvUserId");
  const selectedUser = spvUsers.find(user => user.id === selectedUserId);

  useEffect(() => {
    if (isOpen) {
      fetchData();
      form.reset();
    }
  }, [isOpen, form]);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch SPV users and available projects in parallel
      const [spvUsersResponse, projectsResponse] = await Promise.all([
        fetch("/api/organizations/spv-users"),
        fetch("/api/organizations/projects/available"),
      ]);

      if (spvUsersResponse.ok) {
        const spvUsersData = await spvUsersResponse.json();
        setSPVUsers(spvUsersData.data.spvUsers);
      }

      if (projectsResponse.ok) {
        const projectsData = await projectsResponse.json();
        setProjects(projectsData.data.projects);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProjectAssignmentFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch("/api/organizations/project-assignments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          spvUserId: data.spvUserId,
          projectIds: data.projectIds,
        }),
      });

      if (response.ok) {
        toast.success("Projects assigned successfully");
        onSuccess();
        onClose();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to assign projects");
      }
    } catch (error) {
      console.error("Error assigning projects:", error);
      toast.error("Failed to assign projects");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAssignedProjectIds = (user: SPVUser) => {
    return user.projectAssignments.map(assignment => assignment.project.id);
  };

  const getAvailableProjects = () => {
    if (!selectedUser) return projects;
    const assignedIds = getAssignedProjectIds(selectedUser);
    return projects.filter(project => !assignedIds.includes(project.id));
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "SPV_ADMIN": return "bg-purple-100 text-purple-800";
      case "PROJECT_MANAGER": return "bg-green-100 text-green-800";
      case "SITE_WORKER": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Assign Projects to SPV User
          </DialogTitle>
          <DialogDescription>
            Select an SPV user and assign projects to them for data entry and verification.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* User Selection */}
              <FormField
                control={form.control}
                name="spvUserId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Select SPV User</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Choose an SPV user" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {spvUsers.map((user) => (
                          <SelectItem key={user.id} value={user.id}>
                            <div className="flex items-center gap-2">
                              <div>
                                <p className="font-medium">{user.user.name}</p>
                                <p className="text-sm text-muted-foreground">
                                  {user.user.email} • {user.spv.name}
                                </p>
                              </div>
                              <Badge className={getRoleBadgeColor(user.role)}>
                                {user.role.replace("_", " ")}
                              </Badge>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Current Assignments */}
              {selectedUser && selectedUser.projectAssignments.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Current Assignments</CardTitle>
                    <CardDescription>
                      Projects already assigned to {selectedUser.user.name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {selectedUser.projectAssignments.map((assignment) => (
                        <Badge key={assignment.id} variant="secondary" className="flex items-center gap-1">
                          <CheckCircle className="h-3 w-3" />
                          {assignment.project.name}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Project Selection */}
              <FormField
                control={form.control}
                name="projectIds"
                render={() => (
                  <FormItem>
                    <FormLabel>Select Projects to Assign</FormLabel>
                    <div className="grid gap-3 max-h-60 overflow-y-auto">
                      {getAvailableProjects().map((project) => (
                        <FormField
                          key={project.id}
                          control={form.control}
                          name="projectIds"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={project.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(project.id)}
                                    onCheckedChange={(checked) => {
                                      return checked
                                        ? field.onChange([...field.value, project.id])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== project.id
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <div className="flex-1">
                                  <FormLabel className="text-sm font-medium">
                                    {project.name}
                                  </FormLabel>
                                  <p className="text-xs text-muted-foreground">
                                    {project.type}
                                    {project.location && ` • ${project.location}`}
                                    {project.country && ` • ${project.country}`}
                                  </p>
                                </div>
                                <Badge variant="outline">{project.status}</Badge>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Actions */}
              <div className="flex justify-end gap-3">
                <Button type="button" variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  Assign Projects
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
