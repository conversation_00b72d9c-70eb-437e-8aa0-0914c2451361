"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MoreHorizontal,
  Trash2,
  Eye,
  FolderOpen,
  Calendar,
  Search,
  Loader2,
  AlertCircle,
  BarChart3
} from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";
import { useRouter } from "next/navigation";

interface ProjectAssignment {
  id: string;
  assignedAt: string;
  project: {
    id: string;
    name: string;
    type: string;
    status: string;
    location?: string;
  };
  spvUser: {
    id: string;
    role: string;
    user: {
      id: string;
      name: string;
      email: string;
      jobTitle?: string;
    };
    spv: {
      id: string;
      name: string;
    };
  };
  assignedByUser: {
    id: string;
    name: string;
    email: string;
  };
}

interface OrgProjectAssignmentsTableProps {
  onRefresh?: () => void;
}

export function OrgProjectAssignmentsTable({ onRefresh }: OrgProjectAssignmentsTableProps) {
  const router = useRouter();
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAssignment, setSelectedAssignment] = useState<ProjectAssignment | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    fetchAssignments();
  }, []);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/organizations/project-assignments");
      
      if (response.ok) {
        const data = await response.json();
        setAssignments(data.data.assignments);
      } else {
        toast.error("Failed to fetch project assignments");
      }
    } catch (error) {
      console.error("Error fetching assignments:", error);
      toast.error("Error fetching project assignments");
    } finally {
      setLoading(false);
    }
  };

  const handleUnassignProject = async () => {
    if (!selectedAssignment) return;

    try {
      setIsDeleting(true);
      const response = await fetch(
        `/api/organizations/project-assignments?assignmentId=${selectedAssignment.id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Project unassigned successfully");
        fetchAssignments();
        onRefresh?.();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to unassign project");
      }
    } catch (error) {
      console.error("Error unassigning project:", error);
      toast.error("Failed to unassign project");
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setSelectedAssignment(null);
    }
  };

  const filteredAssignments = assignments.filter(assignment => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      assignment.project.name.toLowerCase().includes(searchLower) ||
      assignment.spvUser.user.name.toLowerCase().includes(searchLower) ||
      assignment.spvUser.user.email.toLowerCase().includes(searchLower) ||
      assignment.spvUser.spv.name.toLowerCase().includes(searchLower) ||
      assignment.project.type.toLowerCase().includes(searchLower) ||
      assignment.project.status.toLowerCase().includes(searchLower)
    );
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case "SPV_ADMIN": return "bg-purple-100 text-purple-800";
      case "PROJECT_MANAGER": return "bg-green-100 text-green-800";
      case "SITE_WORKER": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active": return "bg-green-100 text-green-800";
      case "completed": return "bg-blue-100 text-blue-800";
      case "on_hold": return "bg-yellow-100 text-yellow-800";
      case "cancelled": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Project Assignments
          </CardTitle>
          <CardDescription>
            Manage project assignments for your organization's SPV users
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search assignments by project, user, SPV, or status..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : filteredAssignments.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">
                {searchTerm ? "No assignments match your search." : "No project assignments found."}
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    <TableHead>Assigned User</TableHead>
                    <TableHead>SPV</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned Date</TableHead>
                    <TableHead>Assigned By</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment.project.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {assignment.project.type}
                            {assignment.project.location && ` • ${assignment.project.location}`}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment.spvUser.user.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {assignment.spvUser.user.email}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{assignment.spvUser.spv.name}</p>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRoleColor(assignment.spvUser.role)}>
                          {assignment.spvUser.role.replace("_", " ")}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(assignment.project.status)}>
                          {assignment.project.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          {format(new Date(assignment.assignedAt), "MMM dd, yyyy")}
                        </div>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm">{assignment.assignedByUser.name}</p>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => router.push(`/dashboard/projects/${assignment.project.id}`)}
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              View Project
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => router.push(`/dashboard/projects/${assignment.project.id}/audit`)}
                            >
                              <BarChart3 className="mr-2 h-4 w-4" />
                              View Audit Trail
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedAssignment(assignment);
                                setShowDeleteDialog(true);
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Unassign Project
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Unassign Project</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to unassign "{selectedAssignment?.project.name}" from{" "}
              {selectedAssignment?.spvUser.user.name}? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleUnassignProject}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Unassign
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
