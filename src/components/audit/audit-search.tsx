"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import {
  Search,
  Filter,
  X,
  Clock,
  User,
  Building,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Calendar
} from "lucide-react";
import { format } from "date-fns";

interface SearchResult {
  id: string;
  type: string;
  description: string;
  createdAt: string;
  severity?: string;
  category?: string;
  source?: string;
  success?: boolean;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  organization?: {
    id: string;
    name: string;
  };
  spv?: {
    id: string;
    name: string;
  };
  project?: {
    id: string;
    name: string;
  };
}

interface SearchFilters {
  severity: string;
  category: string;
  source: string;
  startDate: string;
  endDate: string;
}

interface AuditSearchProps {
  organizationId?: string;
  placeholder?: string;
  onResultSelect?: (result: SearchResult) => void;
}

export function AuditSearch({
  organizationId,
  placeholder = "Search audit logs...",
  onResultSelect
}: AuditSearchProps) {
  const { data: session } = useSession();
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
  });

  const [filters, setFilters] = useState<SearchFilters>({
    severity: "",
    category: "",
    source: "",
    startDate: "",
    endDate: ""
  });

  const [searchHistory, setSearchHistory] = useState<string[]>([]);

  // Load search history from localStorage
  useEffect(() => {
    const history = localStorage.getItem('audit-search-history');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }
  }, []);

  // Save search to history
  const saveToHistory = (searchQuery: string) => {
    if (!searchQuery.trim()) return;
    
    const newHistory = [searchQuery, ...searchHistory.filter(h => h !== searchQuery)].slice(0, 10);
    setSearchHistory(newHistory);
    localStorage.setItem('audit-search-history', JSON.stringify(newHistory));
  };

  // Perform search
  const performSearch = async (searchQuery: string = query, page = 1) => {
    if (!searchQuery.trim() || !session?.user) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        q: searchQuery,
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.severity && { severity: filters.severity }),
        ...(filters.category && { category: filters.category }),
        ...(filters.source && { source: filters.source }),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
        ...(organizationId && { organizationId })
      });

      const response = await fetch(`/api/audit-logs/search?${params}`);
      if (!response.ok) {
        throw new Error('Failed to search audit logs');
      }

      const data = await response.json();
      setResults(data.auditLogs);
      setPagination(data.pagination);
      
      // Save successful search to history
      saveToHistory(searchQuery);
    } catch (error) {
      console.error('Error searching audit logs:', error);
      toast({
        title: "Error",
        description: "Failed to search audit logs",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch();
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof SearchFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Clear filters
  const clearFilters = () => {
    setFilters({
      severity: "",
      category: "",
      source: "",
      startDate: "",
      endDate: ""
    });
  };

  // Clear search
  const clearSearch = () => {
    setQuery("");
    setResults([]);
    setPagination({ total: 0, limit: 20, offset: 0, hasMore: false });
  };

  // Get severity color
  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'ERROR': return 'bg-red-50 text-red-700 border-red-100';
      case 'WARN': return 'bg-yellow-50 text-yellow-700 border-yellow-100';
      case 'INFO': return 'bg-blue-50 text-blue-700 border-blue-100';
      default: return 'bg-gray-50 text-gray-700 border-gray-100';
    }
  };

  // Get category icon
  const getCategoryIcon = (category?: string) => {
    switch (category) {
      case 'AUTHENTICATION': return <Shield className="h-4 w-4" />;
      case 'AUTHORIZATION': return <Shield className="h-4 w-4" />;
      case 'DATA': return <Info className="h-4 w-4" />;
      case 'SYSTEM': return <AlertTriangle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  // Get success icon
  const getSuccessIcon = (success?: boolean) => {
    if (success === true) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (success === false) return <XCircle className="h-4 w-4 text-red-600" />;
    return <Info className="h-4 w-4 text-gray-400" />;
  };

  return (
    <div className="space-y-6">
      {/* Search Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Advanced Audit Search
          </CardTitle>
          <CardDescription>
            Search through audit logs with advanced filtering capabilities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="flex gap-2">
              <div className="flex-1 relative">
                <Input
                  type="text"
                  placeholder={placeholder}
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  className="pr-10"
                />
                {query && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={clearSearch}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 p-1"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <Button type="submit" disabled={loading || !query.trim()}>
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>

            {/* Search History */}
            {searchHistory.length > 0 && !query && (
              <div className="space-y-2">
                <div className="text-sm font-medium">Recent Searches</div>
                <div className="flex flex-wrap gap-2">
                  {searchHistory.slice(0, 5).map((historyQuery, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setQuery(historyQuery);
                        performSearch(historyQuery);
                      }}
                      className="text-xs"
                    >
                      {historyQuery}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Advanced Filters */}
            {showFilters && (
              <div className="border-t pt-4">
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  <Select value={filters.severity} onValueChange={(value) => handleFilterChange('severity', value === 'ALL' ? '' : value)}>
                    <SelectTrigger className="w-fit min-w-[120px]">
                      <SelectValue placeholder="Severity" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Severities</SelectItem>
                      <SelectItem value="INFO">Info</SelectItem>
                      <SelectItem value="WARN">Warning</SelectItem>
                      <SelectItem value="ERROR">Error</SelectItem>
                      <SelectItem value="CRITICAL">Critical</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filters.category} onValueChange={(value) => handleFilterChange('category', value === 'ALL' ? '' : value)}>
                    <SelectTrigger className="w-fit min-w-[120px]">
                      <SelectValue placeholder="Category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Categories</SelectItem>
                      <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
                      <SelectItem value="AUTHORIZATION">Authorization</SelectItem>
                      <SelectItem value="DATA">Data</SelectItem>
                      <SelectItem value="SYSTEM">System</SelectItem>
                      <SelectItem value="SPV">SPV</SelectItem>
                      <SelectItem value="DOCUMENT">Document</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={filters.source} onValueChange={(value) => handleFilterChange('source', value === 'ALL' ? '' : value)}>
                    <SelectTrigger className="w-fit min-w-[120px]">
                      <SelectValue placeholder="Source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ALL">All Sources</SelectItem>
                      <SelectItem value="WEB">Web</SelectItem>
                      <SelectItem value="API">API</SelectItem>
                      <SelectItem value="MOBILE">Mobile</SelectItem>
                      <SelectItem value="SYSTEM">System</SelectItem>
                    </SelectContent>
                  </Select>

                  <Input
                    type="datetime-local"
                    placeholder="Start Date"
                    value={filters.startDate}
                    onChange={(e) => handleFilterChange('startDate', e.target.value)}
                  />

                  <Input
                    type="datetime-local"
                    placeholder="End Date"
                    value={filters.endDate}
                    onChange={(e) => handleFilterChange('endDate', e.target.value)}
                  />
                </div>

                <div className="flex gap-2 mt-4">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={clearFilters}
                  >
                    Clear Filters
                  </Button>
                </div>
              </div>
            )}
          </form>
        </CardContent>
      </Card>

      {/* Search Results */}
      {results.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Search Results</CardTitle>
              <Badge variant="outline" className="flex items-center">
                <Clock className="h-3 w-3 mr-1" />
                {pagination.total} {pagination.total === 1 ? 'result' : 'results'}
              </Badge>
            </div>
            <CardDescription>
              Found {pagination.total} audit log entries matching your search
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type & Status</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Context</TableHead>
                    <TableHead>Date</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {results.map((result) => (
                    <TableRow 
                      key={result.id} 
                      className="hover:bg-muted/50 cursor-pointer"
                      onClick={() => onResultSelect?.(result)}
                    >
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(result.category)}
                            <Badge variant="outline" className="text-xs">
                              {result.type.replace(/_/g, ' ')}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            {getSuccessIcon(result.success)}
                            {result.severity && (
                              <Badge className={`text-xs ${getSeverityColor(result.severity)}`}>
                                {result.severity}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="max-w-md">
                        <div className="truncate" title={result.description}>
                          {result.description}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium text-sm">
                              {result.user?.name || 'Unknown User'}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {result.user?.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {result.organization && (
                            <div className="flex items-center space-x-1 text-xs">
                              <Building className="h-3 w-3" />
                              <span>{result.organization.name}</span>
                            </div>
                          )}
                          {result.spv && (
                            <div className="flex items-center space-x-1 text-xs">
                              <Shield className="h-3 w-3" />
                              <span>{result.spv.name}</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(new Date(result.createdAt), 'MMM dd, HH:mm')}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(new Date(result.createdAt), 'yyyy')}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Results */}
      {query && !loading && results.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No results found</h3>
            <p className="text-muted-foreground">
              Try adjusting your search query or filters to find what you're looking for.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Pagination */}
      {pagination.total > pagination.limit && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {pagination.offset + 1} to {Math.min(pagination.offset + pagination.limit, pagination.total)} of {pagination.total} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => performSearch(query, Math.max(1, Math.floor(pagination.offset / pagination.limit)))}
              disabled={pagination.offset === 0 || loading}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => performSearch(query, Math.floor(pagination.offset / pagination.limit) + 2)}
              disabled={!pagination.hasMore || loading}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
