"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { toast } from "@/components/ui/use-toast";
import {
  RefreshCw,
  Download,
  ChevronDown,
  ChevronRight,
  Clock,
  User,
  Building,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Info,
  Calendar,
  Eye
} from "lucide-react";
import { format } from "date-fns";

interface AuditLog {
  id: string;
  type: string;
  description: string;
  createdAt: string;
  severity?: string;
  category?: string;
  source?: string;
  success?: boolean;
  duration?: number;
  sessionId?: string;
  requestId?: string;
  tags?: string[];
  ipAddress?: string;
  userAgent?: string;
  metadata?: any;
  user?: {
    id: string;
    name: string;
    email: string;
  };
  organization?: {
    id: string;
    name: string;
  };
  spv?: {
    id: string;
    name: string;
  };
  project?: {
    id: string;
    name: string;
  };
}

interface AuditTrailFilters {
  severity: string;
  category: string;
}

interface EnhancedAuditTrailProps {
  title?: string;
  description?: string;
  organizationId?: string;
  spvId?: string;
  projectId?: string;
  showFilters?: boolean;
  showExport?: boolean;
  showRealtime?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export function EnhancedAuditTrail({
  title = "Audit Trail",
  description = "Comprehensive audit trail with advanced filtering and real-time updates",
  organizationId,
  spvId,
  projectId,
  showFilters = true,
  showExport = true,
  showRealtime = false,
  autoRefresh = false,
  refreshInterval = 30000
}: EnhancedAuditTrailProps) {
  const { data: session } = useSession();
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
  });

  const [filters, setFilters] = useState<AuditTrailFilters>({
    severity: "",
    category: ""
  });

  const [lastTimestamp, setLastTimestamp] = useState<string>("");
  const [realtimeEnabled, setRealtimeEnabled] = useState(false);

  // Fetch audit logs
  const fetchAuditLogs = useCallback(async (page = 1, isRealtime = false, customFilters?: AuditTrailFilters) => {
    if (!session?.user) return;

    setLoading(true);
    try {
      const activeFilters = customFilters || filters;
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        ...(activeFilters.severity && { severity: activeFilters.severity }),
        ...(activeFilters.category && { category: activeFilters.category }),
        ...(organizationId && { organizationId }),
        ...(spvId && { spvId }),
        ...(projectId && { projectId }),
        ...(isRealtime && lastTimestamp && { realtime: 'true', lastTimestamp }),
        includeMetadata: 'true'
      });

      const response = await fetch(`/api/audit-logs?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch audit logs');
      }

      const data = await response.json();
      
      if (isRealtime && data.auditLogs.length > 0) {
        // Prepend new logs for real-time updates
        setAuditLogs(prev => [...data.auditLogs, ...prev]);
        setLastTimestamp(data.auditLogs[0].createdAt);
      } else {
        setAuditLogs(data.auditLogs);
        setPagination(data.pagination);
        if (data.auditLogs.length > 0) {
          setLastTimestamp(data.auditLogs[0].createdAt);
        }
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      toast({
        title: "Error",
        description: "Failed to fetch audit logs",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [session, filters, pagination.limit, organizationId, spvId, projectId, lastTimestamp]);

  // Real-time updates
  useEffect(() => {
    if (!realtimeEnabled || !showRealtime) return;

    const interval = setInterval(() => {
      fetchAuditLogs(1, true);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [realtimeEnabled, showRealtime, refreshInterval]); // Removed fetchAuditLogs dependency to prevent infinite loop

  // Auto refresh
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      fetchAuditLogs(1);
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [autoRefresh, refreshInterval]); // Removed fetchAuditLogs dependency to prevent infinite loop

  // Initial load
  useEffect(() => {
    fetchAuditLogs(1);
  }, []); // Empty dependency array for initial load only

  // Handle filter changes with auto-apply
  const handleFilterChange = (key: keyof AuditTrailFilters, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    // Auto-apply filters with new filter values
    fetchAuditLogs(1, false, newFilters);
  };





  // Export functionality
  const handleExport = async (format: 'csv' | 'json' | 'pdf') => {
    try {
      const params = new URLSearchParams({
        export: format,
        ...(filters.severity && { severity: filters.severity }),
        ...(filters.category && { category: filters.category }),
        ...(organizationId && { organizationId }),
        ...(spvId && { spvId }),
        ...(projectId && { projectId })
      });

      const response = await fetch(`/api/audit-logs?${params}`);
      if (!response.ok) {
        throw new Error('Failed to export audit logs');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Success",
        description: `Audit logs exported as ${format.toUpperCase()}`,
      });
    } catch (error) {
      console.error('Error exporting audit logs:', error);
      toast({
        title: "Error",
        description: "Failed to export audit logs",
        variant: "destructive",
      });
    }
  };

  // Toggle row expansion
  const toggleRowExpansion = (logId: string) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(logId)) {
        newSet.delete(logId);
      } else {
        newSet.add(logId);
      }
      return newSet;
    });
  };

  // Get severity color
  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 border-red-200';
      case 'ERROR': return 'bg-red-50 text-red-700 border-red-100';
      case 'WARN': return 'bg-yellow-50 text-yellow-700 border-yellow-100';
      case 'INFO': return 'bg-blue-50 text-blue-700 border-blue-100';
      default: return 'bg-gray-50 text-gray-700 border-gray-100';
    }
  };

  // Get category icon
  const getCategoryIcon = (category?: string) => {
    switch (category) {
      case 'AUTHENTICATION': return <Shield className="h-4 w-4" />;
      case 'AUTHORIZATION': return <Shield className="h-4 w-4" />;
      case 'DATA': return <Info className="h-4 w-4" />;
      case 'SYSTEM': return <AlertTriangle className="h-4 w-4" />;
      default: return <Info className="h-4 w-4" />;
    }
  };

  // Get success icon
  const getSuccessIcon = (success?: boolean) => {
    if (success === true) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (success === false) return <XCircle className="h-4 w-4 text-red-600" />;
    return <Info className="h-4 w-4 text-gray-400" />;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
     

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Select value={filters.severity || 'ALL'} onValueChange={(value) => handleFilterChange('severity', value === 'ALL' ? '' : value)}>
                  <SelectTrigger className="w-fit min-w-[120px]">
                    <SelectValue placeholder="Severity" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Severities</SelectItem>
                    <SelectItem value="INFO">Info</SelectItem>
                    <SelectItem value="WARN">Warning</SelectItem>
                    <SelectItem value="ERROR">Error</SelectItem>
                    <SelectItem value="CRITICAL">Critical</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.category || 'ALL'} onValueChange={(value) => handleFilterChange('category', value === 'ALL' ? '' : value)}>
                  <SelectTrigger className="w-fit min-w-[120px]">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Categories</SelectItem>
                    <SelectItem value="AUTHENTICATION">Authentication</SelectItem>
                    <SelectItem value="AUTHORIZATION">Authorization</SelectItem>
                    <SelectItem value="DATA">Data</SelectItem>
                    <SelectItem value="DATA_ENTRY">Data Entry</SelectItem>
                    <SelectItem value="SYSTEM">System</SelectItem>
                    <SelectItem value="SPV">SPV</SelectItem>
                    <SelectItem value="DOCUMENT">Document</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={() => fetchAuditLogs(1)}
                  disabled={loading}
                  variant="outline"
                  size="sm"
                >
                  {loading ? (
                    <RefreshCw className="h-4 w-4 animate-spin" />
                  ) : (
                    <RefreshCw className="h-4 w-4" />
                  )}
                  Refresh
                </Button>
                {showExport && (
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuItem onClick={() => handleExport('csv')}>
                        Export as CSV
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleExport('json')}>
                        Export as JSON
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleExport('pdf')}>
                        Export as PDF
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Audit Logs Table */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Audit Logs</CardTitle>
            <Badge variant="outline" className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {pagination.total} {pagination.total === 1 ? 'entry' : 'entries'}
            </Badge>
          </div>
          <CardDescription>
            Showing {auditLogs.length} of {pagination.total} audit log entries
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[50px]"></TableHead>
                  <TableHead>Type & Status</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Context</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {auditLogs.map((log) => (
                  <React.Fragment key={log.id}>
                    <TableRow className="hover:bg-muted/50">
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRowExpansion(log.id)}
                          className="p-1"
                        >
                          {expandedRows.has(log.id) ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                        </Button>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {getCategoryIcon(log.category)}
                            <Badge variant="outline" className="text-xs">
                              {log.type.replace(/_/g, ' ')}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-2">
                            {getSuccessIcon(log.success)}
                            {log.severity && (
                              <Badge className={`text-xs ${getSeverityColor(log.severity)}`}>
                                {log.severity}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="max-w-md">
                        <div className="truncate" title={log.description}>
                          {log.description}
                        </div>
                        {log.tags && log.tags.length > 0 && (
                          <div className="flex gap-1 mt-1">
                            {log.tags.slice(0, 3).map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {log.tags.length > 3 && (
                              <Badge variant="secondary" className="text-xs">
                                +{log.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <div className="font-medium text-sm">
                              {log.user?.name || 'Unknown User'}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {log.user?.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {log.organization && (
                            <div className="flex items-center space-x-1 text-xs">
                              <Building className="h-3 w-3" />
                              <span>{log.organization.name}</span>
                            </div>
                          )}
                          {log.spv && (
                            <div className="flex items-center space-x-1 text-xs">
                              <Shield className="h-3 w-3" />
                              <span>{log.spv.name}</span>
                            </div>
                          )}
                          {log.project && (
                            <div className="text-xs text-muted-foreground">
                              Project: {log.project.name}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {format(new Date(log.createdAt), 'MMM dd, HH:mm')}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {format(new Date(log.createdAt), 'yyyy')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleRowExpansion(log.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                    
                    {/* Expanded Row */}
                    {expandedRows.has(log.id) && (
                      <TableRow>
                        <TableCell colSpan={7} className="bg-muted/20">
                          <div className="p-4 space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <div>
                                <h4 className="font-medium text-sm mb-2">Technical Details</h4>
                                <div className="space-y-1 text-xs">
                                  <div><strong>ID:</strong> {log.id}</div>
                                  {log.sessionId && <div><strong>Session:</strong> {log.sessionId}</div>}
                                  {log.requestId && <div><strong>Request:</strong> {log.requestId}</div>}
                                  {log.duration && <div><strong>Duration:</strong> {log.duration}ms</div>}
                                  {log.ipAddress && <div><strong>IP:</strong> {log.ipAddress}</div>}
                                </div>
                              </div>
                              
                              <div>
                                <h4 className="font-medium text-sm mb-2">Source Information</h4>
                                <div className="space-y-1 text-xs">
                                  {log.source && <div><strong>Source:</strong> {log.source}</div>}
                                  {log.category && <div><strong>Category:</strong> {log.category}</div>}
                                  {log.userAgent && (
                                    <div><strong>User Agent:</strong> 
                                      <span className="truncate block max-w-[200px]" title={log.userAgent}>
                                        {log.userAgent}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                              
                              <div>
                                <h4 className="font-medium text-sm mb-2">Metadata</h4>
                                {log.metadata ? (
                                  <pre className="text-xs bg-gray-50 p-2 rounded overflow-auto max-h-32">
                                    {JSON.stringify(log.metadata, null, 2)}
                                  </pre>
                                ) : (
                                  <div className="text-xs text-muted-foreground">No metadata available</div>
                                )}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </React.Fragment>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.total > pagination.limit && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Showing {pagination.offset + 1} to {Math.min(pagination.offset + pagination.limit, pagination.total)} of {pagination.total} entries
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchAuditLogs(Math.max(1, Math.floor(pagination.offset / pagination.limit)))}
              disabled={pagination.offset === 0 || loading}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => fetchAuditLogs(Math.floor(pagination.offset / pagination.limit) + 2)}
              disabled={!pagination.hasMore || loading}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
