"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Download,
  RefreshCw,
  Calendar,
  Filter,
  Eye,
  ChevronLeft,
  ChevronRight,
  Clock,
  User,
  Building,
  FolderOpen,
} from 'lucide-react';
import { format } from 'date-fns';
import { AuditLogType } from '@prisma/client';

interface AuditLog {
  id: string;
  type: AuditLogType;
  description: string;
  createdAt: string;
  userId?: string;
  organizationId?: string;
  spvId?: string;
  projectId?: string;
  resourceType?: string;
  resourceId?: string;
  oldValue?: any;
  newValue?: any;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  user?: {
    id: string;
    name: string | null;
    email: string;
  };
  organization?: {
    id: string;
    name: string;
  };
  spv?: {
    id: string;
    name: string;
  };
  project?: {
    id: string;
    name: string;
  };
}

interface AuditLogResponse {
  auditLogs: AuditLog[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
}

interface AuditTrailProps {
  title?: string;
  description?: string;
  className?: string;
  showFilters?: boolean;
  showHeaderButtons?: boolean;
  defaultFilters?: {
    type?: AuditLogType;
    resourceType?: string;
    startDate?: string;
    endDate?: string;
  };
}

const AUDIT_LOG_TYPE_LABELS: Record<AuditLogType, string> = {
  USER_CREATED: 'User Created',
  USER_UPDATED: 'User Updated',
  USER_DELETED: 'User Deleted',
  ORGANIZATION_CREATED: 'Organization Created',
  ORGANIZATION_UPDATED: 'Organization Updated',
  ORGANIZATION_DELETED: 'Organization Deleted',
  PROJECT_CREATED: 'Project Created',
  PROJECT_UPDATED: 'Project Updated',
  PROJECT_DELETED: 'Project Deleted',
  DATA_ENTRY_MANUAL: 'Manual Data Entry',
  DATA_ENTRY_CSV: 'CSV Data Entry',
  DATA_ENTRY_API: 'API Data Entry',
  DATA_VERIFIED: 'Data Verified',
  DATA_REJECTED: 'Data Rejected',
  DATA_APPROVED: 'Data Approved',
  VERIFICATION_STATUS_CHANGED: 'Verification Status Changed',
  AUDIT_LOG_ACCESSED: 'Audit Log Accessed',
  AUDIT_LOG_EXPORTED: 'Audit Log Exported',
  LOGIN_SUCCESS: 'Login Success',
  LOGIN_FAILED: 'Login Failed',
  PASSWORD_RESET: 'Password Reset',
  EMAIL_VERIFIED: 'Email Verified',
  PERMISSION_GRANTED: 'Permission Granted',
  PERMISSION_REVOKED: 'Permission Revoked',
  ROLE_CREATED: 'Role Created',
  ROLE_UPDATED: 'Role Updated',
  ROLE_DELETED: 'Role Deleted',
  SETTINGS_UPDATED: 'Settings Updated',
  DOCUMENT_UPLOADED: 'Document Uploaded',
  DOCUMENT_VERIFIED: 'Document Verified',
  // Add other types as needed
} as any;

const AUDIT_LOG_TYPE_COLORS: Record<string, string> = {
  USER_CREATED: 'bg-green-100 text-green-800',
  USER_UPDATED: 'bg-blue-100 text-blue-800',
  USER_DELETED: 'bg-red-100 text-red-800',
  PROJECT_CREATED: 'bg-green-100 text-green-800',
  PROJECT_UPDATED: 'bg-blue-100 text-blue-800',
  PROJECT_DELETED: 'bg-red-100 text-red-800',
  DATA_VERIFIED: 'bg-green-100 text-green-800',
  DATA_REJECTED: 'bg-red-100 text-red-800',
  DATA_APPROVED: 'bg-green-100 text-green-800',
  LOGIN_SUCCESS: 'bg-green-100 text-green-800',
  LOGIN_FAILED: 'bg-red-100 text-red-800',
  PERMISSION_GRANTED: 'bg-green-100 text-green-800',
  PERMISSION_REVOKED: 'bg-red-100 text-red-800',
  default: 'bg-gray-100 text-gray-800',
};

export function AuditTrail({
  title = "Audit Trail",
  description = "Comprehensive record of all system activities and changes",
  className = "",
  showFilters = true,
  showHeaderButtons = false,
  defaultFilters = {}
}: AuditTrailProps) {
  const { data: session } = useSession();
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false,
  });
  const [loading, setLoading] = useState(false);
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  
  // Filter states
  const [typeFilter, setTypeFilter] = useState<string>(defaultFilters.type || 'all');
  const [resourceTypeFilter, setResourceTypeFilter] = useState<string>(defaultFilters.resourceType || 'all');
  const [startDate, setStartDate] = useState(defaultFilters.startDate || '');
  const [endDate, setEndDate] = useState(defaultFilters.endDate || '');
  const [currentPage, setCurrentPage] = useState(1);

  const fetchAuditLogs = useCallback(async (page = 1) => {
    if (!session?.user) return;

    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pagination.limit.toString(),
        sortBy: 'createdAt',
        sortOrder: 'desc',
        ...(typeFilter && typeFilter !== 'all' && { type: typeFilter }),
        ...(resourceTypeFilter && resourceTypeFilter !== 'all' && { resourceType: resourceTypeFilter }),
        ...(startDate && { startDate: new Date(startDate).toISOString() }),
        ...(endDate && { endDate: new Date(endDate).toISOString() }),
      });

      const response = await fetch(`/api/audit-logs?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch audit logs');
      }

      const data: AuditLogResponse = await response.json();
      setAuditLogs(data.auditLogs);
      setPagination(data.pagination);
      setCurrentPage(page);
    } catch (error) {
      console.error('Error fetching audit logs:', error);
    } finally {
      setLoading(false);
    }
  }, [session, typeFilter, resourceTypeFilter, startDate, endDate, pagination.limit]);

  useEffect(() => {
    fetchAuditLogs(1);
  }, [fetchAuditLogs]);

  const handleExport = async () => {
    if (!session?.user) return;

    try {
      const params = new URLSearchParams({
        export: 'csv',
        ...(typeFilter && typeFilter !== 'all' && { type: typeFilter }),
        ...(resourceTypeFilter && resourceTypeFilter !== 'all' && { resourceType: resourceTypeFilter }),
        ...(startDate && { startDate: new Date(startDate).toISOString() }),
        ...(endDate && { endDate: new Date(endDate).toISOString() }),
      });

      const response = await fetch(`/api/audit-logs?${params}`);
      if (!response.ok) {
        throw new Error('Failed to export audit logs');
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${format(new Date(), 'yyyy-MM-dd')}.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting audit logs:', error);
    }
  };

  const handleViewDetails = async (log: AuditLog) => {
    try {
      const response = await fetch(`/api/audit-logs/${log.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch audit log details');
      }
      
      const detailedLog = await response.json();
      setSelectedLog(detailedLog);
      setShowDetails(true);
    } catch (error) {
      console.error('Error fetching audit log details:', error);
    }
  };

  const getTypeColor = (type: string) => {
    return AUDIT_LOG_TYPE_COLORS[type] || AUDIT_LOG_TYPE_COLORS.default;
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm:ss');
  };

  const totalPages = Math.ceil(pagination.total / pagination.limit);

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">{title}</h2>
          <p className="text-muted-foreground">{description}</p>
        </div>

        {showHeaderButtons && (
          <div className="flex items-center gap-2">
            <Button
              onClick={() => fetchAuditLogs(currentPage)}
              disabled={loading}
              variant="outline"
              size="sm"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
              Refresh
            </Button>

            <Button onClick={handleExport} variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export CSV
            </Button>
          </div>
        )}
      </div>

      {showFilters && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 items-end">
              <div>
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    {Object.entries(AUDIT_LOG_TYPE_LABELS).map(([value, label]) => (
                      <SelectItem key={value} value={value}>
                        {label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select value={resourceTypeFilter} onValueChange={setResourceTypeFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All resources" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All resources</SelectItem>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="organization">Organization</SelectItem>
                    <SelectItem value="project">Project</SelectItem>
                    <SelectItem value="spv">SPV</SelectItem>
                    <SelectItem value="carbon_credit">Carbon Credit</SelectItem>
                    <SelectItem value="transaction">Transaction</SelectItem>
                    <SelectItem value="wallet">Wallet</SelectItem>
                    <SelectItem value="data_entry">Data Entry</SelectItem>
                    <SelectItem value="document">Document</SelectItem>
                    <SelectItem value="api_endpoint">API Endpoint</SelectItem>
                    <SelectItem value="audit_log">Audit Log</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Input
                  type="date"
                  placeholder="Start date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                />
              </div>

              <div>
                <Input
                  type="date"
                  placeholder="End date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                />
              </div>

              <div>
                <Button onClick={() => fetchAuditLogs(1)} disabled={loading} className="w-full">
                  <Filter className="h-4 w-4 mr-2" />
                  Apply Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle>Audit Logs</CardTitle>
            <Badge variant="outline" className="flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              {pagination.total} {pagination.total === 1 ? 'entry' : 'entries'}
            </Badge>
          </div>
          <CardDescription>
            Showing {auditLogs.length} of {pagination.total} audit log entries
          </CardDescription>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>User</TableHead>
                  <TableHead>Context</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {auditLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <Badge className={getTypeColor(log.type)}>
                        {AUDIT_LOG_TYPE_LABELS[log.type] || log.type}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-md">
                      <div className="truncate" title={log.description}>
                        {log.description}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium text-sm">
                            {log.user?.name || 'Unknown User'}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {log.user?.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        {log.organization && (
                          <div className="flex items-center space-x-1 text-xs">
                            <Building className="h-3 w-3" />
                            <span>{log.organization.name}</span>
                          </div>
                        )}
                        {log.spv && (
                          <div className="flex items-center space-x-1 text-xs">
                            <Building className="h-3 w-3" />
                            <span>SPV: {log.spv.name}</span>
                          </div>
                        )}
                        {log.project && (
                          <div className="flex items-center space-x-1 text-xs">
                            <FolderOpen className="h-3 w-3" />
                            <span>{log.project.name}</span>
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {formatDate(log.createdAt)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewDetails(log)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {auditLogs.length === 0 && !loading && (
            <div className="text-center py-8 text-muted-foreground">
              No audit logs found matching your criteria.
            </div>
          )}

          {loading && (
            <div className="text-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
              <p className="text-muted-foreground">Loading audit logs...</p>
            </div>
          )}
        </CardContent>

        {totalPages > 1 && (
          <div className="flex items-center justify-between px-6 py-4 border-t">
            <div className="text-sm text-muted-foreground">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchAuditLogs(currentPage - 1)}
                disabled={currentPage === 1 || loading}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => fetchAuditLogs(currentPage + 1)}
                disabled={currentPage === totalPages || loading}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </Card>

      {/* Audit Log Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Audit Log Details</DialogTitle>
            <DialogDescription>
              Detailed information about this audit log entry
            </DialogDescription>
          </DialogHeader>
          
          {selectedLog && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">Type</label>
                  <Badge className={getTypeColor(selectedLog.type)}>
                    {AUDIT_LOG_TYPE_LABELS[selectedLog.type] || selectedLog.type}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium">Date</label>
                  <p className="text-sm">{formatDate(selectedLog.createdAt)}</p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium">Description</label>
                <p className="text-sm mt-1">{selectedLog.description}</p>
              </div>

              {selectedLog.user && (
                <div>
                  <label className="text-sm font-medium">User</label>
                  <div className="text-sm mt-1">
                    <p>{selectedLog.user.name} ({selectedLog.user.email})</p>
                  </div>
                </div>
              )}

              {(selectedLog.oldValue || selectedLog.newValue) && (
                <div className="grid grid-cols-2 gap-4">
                  {selectedLog.oldValue && (
                    <div>
                      <label className="text-sm font-medium">Old Value</label>
                      <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                        {JSON.stringify(selectedLog.oldValue, null, 2)}
                      </pre>
                    </div>
                  )}
                  {selectedLog.newValue && (
                    <div>
                      <label className="text-sm font-medium">New Value</label>
                      <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                        {JSON.stringify(selectedLog.newValue, null, 2)}
                      </pre>
                    </div>
                  )}
                </div>
              )}

              {selectedLog.metadata && (
                <div>
                  <label className="text-sm font-medium">Metadata</label>
                  <pre className="text-xs bg-muted p-2 rounded mt-1 overflow-auto">
                    {JSON.stringify(selectedLog.metadata, null, 2)}
                  </pre>
                </div>
              )}

              <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                {selectedLog.ipAddress && (
                  <div>
                    <label className="font-medium">IP Address</label>
                    <p>{selectedLog.ipAddress}</p>
                  </div>
                )}
                {selectedLog.userAgent && (
                  <div>
                    <label className="font-medium">User Agent</label>
                    <p className="truncate" title={selectedLog.userAgent}>
                      {selectedLog.userAgent}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
