"use client";

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { TestTube, Loader2 } from 'lucide-react';

interface TestSPVAuditButtonProps {
  onSuccess?: () => void;
}

export function TestSPVAuditButton({ onSuccess }: TestSPVAuditButtonProps) {
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const createTestAuditLogs = async () => {
    try {
      setLoading(true);
      
      const response = await fetch('/api/test/create-audit-logs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create test audit logs');
      }

      const data = await response.json();
      
      toast({
        title: "Success",
        description: `Created ${data.logs?.length || 0} test audit logs`,
        variant: "default",
      });

      // Call onSuccess callback to refresh the audit trail
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('Error creating test audit logs:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create test audit logs",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Button
      onClick={createTestAuditLogs}
      disabled={loading}
      variant="outline"
      size="sm"
      className="gap-2"
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <TestTube className="h-4 w-4" />
      )}
      {loading ? 'Creating...' : 'Create Test Logs'}
    </Button>
  );
}
