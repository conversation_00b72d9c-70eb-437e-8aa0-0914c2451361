"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Loader2, Building2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { spvCreationSchema } from "@/lib/validation/schemas";
import { AdminSPVCreateData, SPVUpdateData } from "@/types/spv";
import { toast } from "sonner";

// Export the schemas for reuse
export { adminSPVCreationSchema, spvUpdateSchema };

// Extended schema for admin SPV creation
const adminSPVCreationSchema = spvCreationSchema.extend({
  organizationId: z.string().min(1, "Organization is required"),
});

// Schema for SPV updates
const spvUpdateSchema = spvCreationSchema.partial().extend({
  status: z.enum(["ACTIVE", "INACTIVE", "DISSOLVED"]).optional(),
});

type AdminSPVFormValues = z.infer<typeof adminSPVCreationSchema>;
type SPVUpdateFormValues = z.infer<typeof spvUpdateSchema>;

const legalStructures = [
  { value: "LLC", label: "Limited Liability Company (LLC)" },
  { value: "Corporation", label: "Corporation" },
  { value: "Partnership", label: "Partnership" },
  { value: "Limited Partnership", label: "Limited Partnership" },
  { value: "Trust", label: "Trust" },
  { value: "Other", label: "Other" },
];

const statusOptions = [
  { value: "ACTIVE", label: "Active", description: "SPV is operational" },
  { value: "INACTIVE", label: "Inactive", description: "SPV is not currently operational" },
  { value: "DISSOLVED", label: "Dissolved", description: "SPV has been dissolved" },
];

interface AdminSPVFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AdminSPVCreateData | SPVUpdateData) => Promise<void>;
  organizations: { id: string; name: string; legalName: string | null }[];
  initialData?: Partial<AdminSPVFormValues>;
  mode: "create" | "edit";
  isSubmitting?: boolean;
}

export function AdminSPVForm({
  isOpen,
  onClose,
  onSubmit,
  organizations,
  initialData,
  mode = "create",
  isSubmitting = false,
}: AdminSPVFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<AdminSPVFormValues>({
    resolver: zodResolver(mode === "create" ? adminSPVCreationSchema : spvUpdateSchema),
    defaultValues: {
      name: initialData?.name || "",
      purpose: initialData?.purpose || "",
      jurisdiction: initialData?.jurisdiction || "",
      legalStructure: initialData?.legalStructure || "",
      registrationNumber: initialData?.registrationNumber || "",
      taxId: initialData?.taxId || "",
      address: initialData?.address || "",
      description: initialData?.description || "",
      establishedDate: initialData?.establishedDate || "",
      organizationId: initialData?.organizationId || "",
      // New required fields from PRD
      country: (initialData as any)?.country || "",
      legalEntityId: (initialData as any)?.legalEntityId || "",
      contact: (initialData as any)?.contact || "",
      projectCategories: (initialData as any)?.projectCategories || "",
      ...(mode === "edit" && { status: (initialData as any)?.status || "ACTIVE" }),
    },
  });

  const handleSubmit = async (values: AdminSPVFormValues) => {
    try {
      setSubmitError(null);
      
      const submitData = {
        ...values,
        establishedDate: values.establishedDate || undefined,
      };

      await onSubmit(submitData);
      handleClose();
    } catch (error) {
      console.error("Error submitting SPV form:", error);
      setSubmitError(error instanceof Error ? error.message : "Failed to save SPV");
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      form.reset();
      setSubmitError(null);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {mode === "create" ? "Create New SPV" : "Edit SPV"}
          </DialogTitle>
          <DialogDescription>
            {mode === "create" 
              ? "Create a new Special Purpose Vehicle for an organization"
              : "Update the SPV information"
            }
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Organization Selection (only for create mode) */}
            {mode === "create" && (
              <FormField
                control={form.control}
                name="organizationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an organization" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {organizations.map((org) => (
                          <SelectItem key={org.id} value={org.id}>
                            {org.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the organization that will own this SPV
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
                <CardDescription>
                  Enter the basic details for the SPV
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SPV Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter SPV name"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., United States, Singapore"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="legalEntityId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Legal Entity ID *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter legal entity identifier"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="contact"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Information *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Contact person or phone number"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="projectCategories"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Category *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="Solar">Solar</SelectItem>
                          <SelectItem value="Wind">Wind</SelectItem>
                          <SelectItem value="Hybrid">Hybrid</SelectItem>
                          <SelectItem value="Forestry">Forestry</SelectItem>
                          <SelectItem value="Energy Efficiency">Energy Efficiency</SelectItem>
                          <SelectItem value="Waste Management">Waste Management</SelectItem>
                          <SelectItem value="Transportation">Transportation</SelectItem>
                          <SelectItem value="Other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the primary project category this SPV will handle
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="purpose"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purpose</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the purpose of this SPV (optional)"
                          className="min-h-[80px]"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="legalStructure"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Legal Structure *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select structure" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {legalStructures.map((structure) => (
                              <SelectItem key={structure.value} value={structure.value}>
                                {structure.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="jurisdiction"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Jurisdiction *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Delaware, USA"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {mode === "edit" && (
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {statusOptions.map((status) => (
                              <SelectItem key={status.value} value={status.value}>
                                <div>
                                  <div>{status.label}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {status.description}
                                  </div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </Card>

            {/* Additional Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Additional Details</CardTitle>
                <CardDescription>
                  Optional information for the SPV
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="registrationNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Registration Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter registration number"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter tax ID"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="establishedDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Established Date</FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter physical address"
                          className="min-h-[60px]"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Additional description or notes"
                          className="min-h-[80px]"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Error Display */}
            {submitError && (
              <div className="p-3 rounded-md bg-destructive/10 border border-destructive/20">
                <p className="text-sm text-destructive">{submitError}</p>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {mode === "create" ? "Creating..." : "Updating..."}
                  </>
                ) : (
                  mode === "create" ? "Create SPV" : "Update SPV"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
