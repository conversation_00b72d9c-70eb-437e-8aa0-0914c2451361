"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, Eye, ArrowUpDown, Search, Filter } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SPVWithOrganization, SPVQueryParams } from "@/types/spv";
import { spvUtils } from "@/lib/api/spv";

interface SPVDataTableProps {
  spvs: SPVWithOrganization[];
  pagination?: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters?: {
    search?: string;
    organizationId?: string;
    status?: string;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
  onSort?: (sortBy: string) => void;
  onPageChange?: (page: number) => void;
  onEdit?: (spv: SPVWithOrganization) => void;
  onDelete?: (spv: SPVWithOrganization) => void;
  onView?: (spv: SPVWithOrganization) => void;
  showOrganization?: boolean;
  showActions?: boolean;
  isLoading?: boolean;
}

export function SPVDataTable({
  spvs,
  pagination,
  filters,
  onSort,
  onPageChange,
  onEdit,
  onDelete,
  onView,
  showOrganization = false,
  showActions = true,
  isLoading = false,
}: SPVDataTableProps) {
  const handleSort = (sortBy: string) => {
    if (onSort) {
      onSort(sortBy);
    }
  };

  const getSortIcon = (column: string) => {
    if (filters?.sortBy === column) {
      return filters.sortOrder === "asc" ? "↑" : "↓";
    }
    return null;
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>SPVs</CardTitle>
          <CardDescription>Loading SPV data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex space-x-4 animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-32"></div>
                <div className="h-4 bg-gray-200 rounded w-48"></div>
                <div className="h-4 bg-gray-200 rounded w-24"></div>
                <div className="h-4 bg-gray-200 rounded w-20"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (spvs.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">No SPVs found</h3>
            <p className="text-muted-foreground">
              {filters?.search ? "Try adjusting your search criteria" : "No SPVs have been created yet"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>SPVs</CardTitle>
        {pagination && (
          <CardDescription>
            Showing {spvs.length} of {pagination.totalCount} SPVs
          </CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className={onSort ? "cursor-pointer hover:bg-muted/50" : ""}
                onClick={() => onSort && handleSort("name")}
              >
                <div className="flex items-center gap-1">
                  Name
                  {onSort && <ArrowUpDown className="h-4 w-4" />}
                  {getSortIcon("name")}
                </div>
              </TableHead>
              
              {showOrganization && (
                <TableHead 
                  className={onSort ? "cursor-pointer hover:bg-muted/50" : ""}
                  onClick={() => onSort && handleSort("organization")}
                >
                  <div className="flex items-center gap-1">
                    Organization
                    {onSort && <ArrowUpDown className="h-4 w-4" />}
                    {getSortIcon("organization")}
                  </div>
                </TableHead>
              )}
              
              <TableHead>Legal Structure</TableHead>
              <TableHead>Jurisdiction</TableHead>
              
              <TableHead 
                className={onSort ? "cursor-pointer hover:bg-muted/50" : ""}
                onClick={() => onSort && handleSort("status")}
              >
                <div className="flex items-center gap-1">
                  Status
                  {onSort && <ArrowUpDown className="h-4 w-4" />}
                  {getSortIcon("status")}
                </div>
              </TableHead>
              
              <TableHead 
                className={onSort ? "cursor-pointer hover:bg-muted/50" : ""}
                onClick={() => onSort && handleSort("createdAt")}
              >
                <div className="flex items-center gap-1">
                  Created
                  {onSort && <ArrowUpDown className="h-4 w-4" />}
                  {getSortIcon("createdAt")}
                </div>
              </TableHead>
              
              {showActions && <TableHead className="w-[50px]"></TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {spvs.map((spv) => (
              <TableRow key={spv.id}>
                <TableCell className="font-medium">{spv.name}</TableCell>
                
                {showOrganization && (
                  <TableCell>{spv.organization.name}</TableCell>
                )}
                
                <TableCell>{spv.legalStructure || "N/A"}</TableCell>
                <TableCell>{spv.jurisdiction || "N/A"}</TableCell>
                
                <TableCell>
                  <Badge
                    variant={spv.status === "ACTIVE" ? "default" : "secondary"}
                    className={
                      spv.status === "ACTIVE" ? "bg-green-100 text-green-800" :
                      // spv.status === "PENDING" ? "bg-yellow-100 text-yellow-800" :
                      spv.status === "INACTIVE" ? "bg-gray-100 text-gray-800" :
                      "bg-red-100 text-red-800"
                    }
                  >
                    {spvUtils.formatStatus(spv.status)}
                  </Badge>
                </TableCell>
                
                <TableCell>{spvUtils.formatDate(spv.createdAt)}</TableCell>
                
                {showActions && (
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onView && (
                          <DropdownMenuItem onClick={() => onView(spv)}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Details
                          </DropdownMenuItem>
                        )}
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(spv)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        {onDelete && (
                          <DropdownMenuItem 
                            className="text-destructive"
                            onClick={() => onDelete(spv)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                )}
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {/* Pagination */}
        {pagination && pagination.totalPages > 1 && onPageChange && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Page {pagination.page} of {pagination.totalPages}
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrev}
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
