"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Loader2, Building2, Info, ChevronLeft, ChevronRight, Shield, CreditCard, FileText, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { spvVerificationSchema } from "@/lib/validation/schemas";
import { SPVVerificationData } from "@/types/spv";
import { toast } from "sonner";

// Enhanced SPV creation schema with complete verification details
const enhancedSPVCreationSchema = spvVerificationSchema.extend({
  organizationId: z.string().optional(), // For admin mode
});

type SPVFormValues = z.infer<typeof enhancedSPVCreationSchema>;

const legalStructures = [
  { value: "LLC", label: "Limited Liability Company (LLC)" },
  { value: "Corporation", label: "Corporation" },
  { value: "Partnership", label: "Partnership" },
  { value: "Limited Partnership", label: "Limited Partnership" },
  { value: "Trust", label: "Trust" },
  { value: "Other", label: "Other" },
];

const projectCategories = [
  { value: "Solar", label: "Solar Energy" },
  { value: "Wind", label: "Wind Energy" },
  { value: "Hybrid", label: "Hybrid Renewable" },
  { value: "Forestry", label: "Forestry & REDD+" },
  { value: "Energy Efficiency", label: "Energy Efficiency" },
  { value: "Waste Management", label: "Waste Management" },
  { value: "Transportation", label: "Transportation" },
  { value: "Other", label: "Other" },
];

// Field descriptions for info tooltips
const fieldDescriptions = {
  name: "The official name of your Special Purpose Vehicle",
  purpose: "Brief description of the SPV's business purpose and objectives",
  legalStructure: "The legal form of your organization (e.g., Private Limited Company, LLP)",
  registrationNumber: "Official company registration number from the registrar",
  jurisdiction: "The state or territory where the SPV is registered",
  country: "Country of incorporation and operation",
  gstNumber: "Goods and Services Tax identification number (15 digits)",
  cinNumber: "Corporate Identity Number assigned by MCA (21 characters)",
  panNumber: "Permanent Account Number for tax purposes (10 characters)",
  incorporationDate: "Date when the company was officially incorporated",
  registeredAddress: "Official registered address as per incorporation documents",
  contactPersonName: "Name of the primary contact person for this SPV",
  contactPersonEmail: "Email address of the primary contact person",
  contactPersonMobile: "Mobile number of the primary contact person",
  bankAccountNumber: "Primary bank account number for the SPV",
  ifscCode: "Indian Financial System Code of the bank branch",
  projectCategories: "Type of carbon credit projects this SPV will manage",
  adminEmail: "Email address for the SPV administrator account"
};

// Info button component
const InfoButton = ({ description }: { description: string }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger asChild>
        <Info className="h-4 w-4 text-muted-foreground cursor-help" />
      </TooltipTrigger>
      <TooltipContent className="max-w-xs">
        <p className="text-sm">{description}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
);

// Form steps configuration
const formSteps = [
  {
    id: "basic",
    title: "Basic Information",
    description: "SPV name, purpose, and basic details",
    icon: Building2,
  },
  {
    id: "legal",
    title: "Legal & Compliance",
    description: "Registration, GST, PAN, and legal details",
    icon: Shield,
  },
  {
    id: "banking",
    title: "Banking & Contact",
    description: "Banking information and contact details",
    icon: CreditCard,
  },
];

interface Organization {
  id: string;
  name: string;
  legalName?: string | null;
}

interface SPVCreationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: SPVVerificationData | (SPVVerificationData & { organizationId: string })) => Promise<void>;
  organizations?: Organization[]; // Optional - for admin use
  mode: "user" | "admin"; // user = org admin creating SPV, admin = platform admin
  isSubmitting?: boolean;
  initialData?: Partial<SPVFormValues>;
}

export function SPVCreationForm({
  isOpen,
  onClose,
  onSubmit,
  organizations = [],
  mode = "user",
  isSubmitting = false,
  initialData,
}: SPVCreationFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);

  const isAdminMode = mode === "admin";
  const schema = isAdminMode ? adminSPVCreationSchema : spvCreationSchema;

  const form = useForm<SPVFormValues | AdminSPVFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: initialData?.name || "",
      purpose: initialData?.purpose || "",
      legalStructure: initialData?.legalStructure || "",
      registrationNumber: initialData?.registrationNumber || "",
      taxId: initialData?.taxId || "",
      address: initialData?.address || "",
      description: initialData?.description || "",
      establishedDate: initialData?.establishedDate || "",
      jurisdiction: initialData?.jurisdiction || "",
      country: initialData?.country || "",
      legalEntityId: initialData?.legalEntityId || "",
      contact: initialData?.contact || "",
      projectCategories: initialData?.projectCategories || "",
      adminEmail: initialData?.adminEmail || "",
      ...(isAdminMode && { organizationId: "" }),
    },
  });

  const handleSubmit = async (values: SPVFormValues | AdminSPVFormValues) => {
    try {
      setSubmitError(null);
      
      const submitData = {
        ...values,
        establishedDate: values.establishedDate || undefined,
      };

      await onSubmit(submitData);
      handleClose();
      toast.success("SPV created successfully!");
    } catch (error) {
      console.error("Error submitting SPV form:", error);
      setSubmitError(error instanceof Error ? error.message : "Failed to create SPV");
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      form.reset();
      setSubmitError(null);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Create New SPV
          </DialogTitle>
          <DialogDescription>
            Create a Special Purpose Vehicle for carbon project management. 
            You can add detailed verification information later.
          </DialogDescription>
        </DialogHeader>

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            This form creates the basic SPV structure. Additional verification details 
            (GST, CIN, PAN, banking information) can be added later during the verification process.
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Organization Selection (only for admin mode) */}
            {isAdminMode && (
              <FormField
                control={form.control}
                name="organizationId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select an organization" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {organizations.map((org) => (
                          <SelectItem key={org.id} value={org.id}>
                            {org.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select the organization that will own this SPV
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Information</CardTitle>
                <CardDescription>
                  Enter the basic details for the SPV
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SPV Name *</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter SPV name"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="adminEmail"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SPV Administrator Email *</FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="<EMAIL>"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Login credentials will be sent to this email address
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., India, United States"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="jurisdiction"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Jurisdiction</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="e.g., Maharashtra, Delaware"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="projectCategories"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Primary Project Category</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {projectCategories.map((category) => (
                            <SelectItem key={category.value} value={category.value}>
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the primary project category this SPV will handle
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="purpose"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Purpose</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe the purpose of this SPV (optional)"
                          className="min-h-[80px]"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Optional Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Optional Details</CardTitle>
                <CardDescription>
                  Additional information that can be provided now or later
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="legalStructure"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Legal Structure</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select structure" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {legalStructures.map((structure) => (
                              <SelectItem key={structure.value} value={structure.value}>
                                {structure.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="establishedDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Established Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="registrationNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Registration Number</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter registration number"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="taxId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tax ID</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter tax ID"
                            autoComplete="off"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="contact"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Contact Information</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Contact person or phone number"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter physical address"
                          className="min-h-[60px]"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Additional description or notes"
                          className="min-h-[80px]"
                          autoComplete="off"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Error Display */}
            {submitError && (
              <div className="p-3 rounded-md bg-destructive/10 border border-destructive/20">
                <p className="text-sm text-destructive">{submitError}</p>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create SPV"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

// Export schemas for reuse
export { spvCreationSchema, adminSPVCreationSchema };
export type { SPVFormValues, AdminSPVFormValues };
