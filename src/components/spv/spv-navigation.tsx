"use client";

import { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useSPVUser, useSPVPermissions } from "@/components/auth/client-spv-auth-guard";
import {
  LayoutDashboard,
  FolderOpen,
  Upload,
  ClipboardCheck,
  BarChart3,
  FileText,
  Users,
  Settings,
  ChevronLeft,
  ChevronRight,
  Shield,
  Target,
  Calendar,
  RefreshCw,
} from "lucide-react";

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
  roles?: string[];
  permissions?: string[];
}

interface SPVNavigationProps {
  className?: string;
}

export function SPVNavigation({ className }: SPVNavigationProps) {
  const pathname = usePathname();
  const { spvUser, loading: userLoading } = useSPVUser();
  const { hasPermission } = useSPVPermissions();
  const [collapsed, setCollapsed] = useState(false);

  const navigationItems: NavigationItem[] = [
    {
      name: "Dashboard",
      href: "/spv/dashboard",
      icon: LayoutDashboard,
    },
    {
      name: "Projects",
      href: "/spv/projects",
      icon: FolderOpen,
    },
    {
      name: "Data Entry",
      href: "/spv/data-entry",
      icon: Upload,
      roles: ["SITE_WORKER", "PROJECT_MANAGER", "SPV_ADMIN"],
    },
    {
      name: "Rejected Entries",
      href: "/spv/rejected-entries",
      icon: RefreshCw,
      roles: ["SITE_WORKER"],
    },
    {
      name: "Verification Queue",
      href: "/spv/verification",
      icon: ClipboardCheck,
      roles: ["PROJECT_MANAGER", "SPV_ADMIN"],
    },
    {
      name: "Analytics",
      href: "/spv/analytics",
      icon: BarChart3,
      roles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      permissions: ["view:spv_analytics"],
    },
    {
      name: "Reports",
      href: "/spv/reports",
      icon: FileText,
      roles: ["PROJECT_MANAGER", "SPV_ADMIN"],
      permissions: ["generate:reports"],
    },
    {
      name: "User Management",
      href: "/spv/users",
      icon: Users,
      roles: ["SPV_ADMIN"],
      permissions: ["manage:spv_users"],
    },
    {
      name: "Settings",
      href: "/spv/settings",
      icon: Settings,
      roles: ["SPV_ADMIN"],
    },
  ];

  const isItemVisible = (item: NavigationItem) => {
    // Don't show items if user data is not loaded yet
    if (!spvUser?.role) {
      return false;
    }

    // Check role-based access
    if (item.roles && !item.roles.includes(spvUser.role)) {
      return false;
    }

    // Check permission-based access
    if (item.permissions && !item.permissions.some(permission => hasPermission(permission))) {
      return false;
    }

    return true;
  };

  const isActive = (href: string) => {
    if (href === "/spv/dashboard") {
      return pathname === "/spv" || pathname === "/spv/dashboard";
    }
    return pathname.startsWith(href);
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "SITE_WORKER":
        return "bg-blue-100 text-blue-800";
      case "PROJECT_MANAGER":
        return "bg-green-100 text-green-800";
      case "SPV_ADMIN":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "SITE_WORKER":
        return <Upload className="h-3 w-3" />;
      case "PROJECT_MANAGER":
        return <ClipboardCheck className="h-3 w-3" />;
      case "SPV_ADMIN":
        return <Shield className="h-3 w-3" />;
      default:
        return <Target className="h-3 w-3" />;
    }
  };

  // Show loading state while user data is being fetched
  if (userLoading) {
    return (
      <div className={cn("flex flex-col h-full bg-white border-r", className)}>
        <div className="p-4 border-b">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <div className="h-6 w-24 bg-gray-200 rounded animate-pulse mb-2" />
              <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
            </div>
          </div>
        </div>
        <div className="p-4">
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-8 bg-gray-200 rounded animate-pulse" />
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("flex flex-col h-full bg-white border-r", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between">
          {!collapsed && (
            <div className="flex-1">
              <h2 className="text-lg font-semibold">SPV Portal</h2>
              {spvUser?.spv?.name && (
                <p className="text-sm text-muted-foreground truncate">
                  {spvUser.spv.name}
                </p>
              )}
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCollapsed(!collapsed)}
            className="h-8 w-8 p-0"
          >
            {collapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronLeft className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>

      {/* User Info */}
      {!collapsed && spvUser?.role && (
        <div className="p-4 border-b">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
              {getRoleIcon(spvUser.role)}
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {spvUser.user?.name || spvUser.user?.email}
              </p>
              <Badge className={cn("text-xs", getRoleBadgeColor(spvUser.role))}>
                {spvUser.role.replace('_', ' ')}
              </Badge>
            </div>
          </div>
        </div>
      )}

      {/* Navigation Items */}
      <nav className="flex-1 p-4">
        <div className="space-y-2">
          {spvUser?.role ? (
            navigationItems.filter(isItemVisible).map((item) => {
              const Icon = item.icon;
              const active = isActive(item.href);

              return (
                <Link key={item.href} href={item.href}>
                  <div
                    className={cn(
                      "flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors",
                      active
                        ? "bg-blue-100 text-blue-900 font-medium"
                        : "text-gray-700 hover:bg-gray-100"
                    )}
                  >
                    <Icon className="h-4 w-4 flex-shrink-0" />
                    {!collapsed && (
                      <>
                        <span className="flex-1">{item.name}</span>
                        {item.badge && (
                          <Badge variant="secondary" className="text-xs">
                            {item.badge}
                          </Badge>
                        )}
                      </>
                    )}
                  </div>
                </Link>
              );
            })
          ) : (
            <div className="text-center py-6">
              <div className="text-sm text-muted-foreground">
                Loading navigation...
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Footer */}
      {!collapsed && (
        <div className="p-4 border-t">
          <div className="text-xs text-muted-foreground">
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="h-3 w-3" />
              <span>Last updated: {new Date().toLocaleDateString()}</span>
            </div>
            <div className="text-center">
              CarbonX SPV Portal v1.0
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
