"use client";

import { useState } from "react";
import { Building2, Calendar, MapPin, FileText, Hash, Users, Folder, Edit, Trash2, Key, Copy } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { SPVWithDetails } from "@/types/spv";
import { spvUtils } from "@/lib/api/spv";
import { toast } from "sonner";

interface SPVDetailsViewProps {
  spv: SPVWithDetails;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: () => void;
  onDelete?: () => void;
  showActions?: boolean;
}

interface ResetCredentials {
  email: string;
  password: string;
  name: string;
  spvName: string;
}

export function SPVDetailsView({
  spv,
  isOpen,
  onClose,
  onEdit,
  onDelete,
  showActions = true,
}: SPVDetailsViewProps) {
  const [isResettingPassword, setIsResettingPassword] = useState(false);
  const [resetCredentials, setResetCredentials] = useState<ResetCredentials | null>(null);

  const handleResetPassword = async () => {
    try {
      setIsResettingPassword(true);

      const response = await fetch(`/api/admin/spv/${spv.id}/reset-password`, {
        method: "POST",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to reset password");
      }

      const result = await response.json();
      setResetCredentials(result.credentials);
      toast.success("SPV admin password reset successfully!");
    } catch (error) {
      console.error("Error resetting password:", error);
      toast.error(error instanceof Error ? error.message : "Failed to reset password");
    } finally {
      setIsResettingPassword(false);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="flex items-center gap-2 text-xl">
                <Building2 className="h-6 w-6" />
                {spv.name}
              </DialogTitle>
              <DialogDescription className="mt-1">
                {spv.legalStructure} • {spv.jurisdiction}
              </DialogDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge 
                variant={spv.status === "ACTIVE" ? "default" : "secondary"}
                className={
                  spv.status === "ACTIVE" ? "bg-green-100 text-green-800" :
                  spv.status === "PENDING" ? "bg-yellow-100 text-yellow-800" :
                  spv.status === "INACTIVE" ? "bg-gray-100 text-gray-800" :
                  "bg-red-100 text-red-800"
                }
              >
                {spvUtils.formatStatus(spv.status)}
              </Badge>
              
              {showActions && (
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetPassword}
                    disabled={isResettingPassword}
                  >
                    <Key className="h-4 w-4 mr-2" />
                    {isResettingPassword ? "Resetting..." : "Reset Admin Password"}
                  </Button>
                  {onEdit && (
                    <Button variant="outline" size="sm" onClick={onEdit}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </Button>
                  )}
                  {onDelete && (
                    <Button variant="outline" size="sm" onClick={onDelete}>
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="grid gap-6">
          {/* Organization Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Organization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Name</p>
                  <p className="text-sm">{spv.organization.name}</p>
                </div>
                {spv.organization.legalName && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Legal Name</p>
                    <p className="text-sm">{spv.organization.legalName}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Status</p>
                  <p className="text-sm">{spv.organization.status}</p>
                </div>
                {/* Verification flow commented out for now */}
                {/* <div>
                  <p className="text-sm font-medium text-muted-foreground">Verification</p>
                  <p className="text-sm">{spv.organization.verificationStatus}</p>
                </div> */}
                {spv.organization.country && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Country</p>
                    <p className="text-sm">{spv.organization.country}</p>
                  </div>
                )}
                {spv.organization.industry && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Industry</p>
                    <p className="text-sm">{spv.organization.industry}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* SPV Details */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                SPV Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Purpose */}
              {spv.purpose && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-2">Purpose</p>
                  <p className="text-sm">{spv.purpose}</p>
                </div>
              )}

              {/* Description */}
              {spv.description && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-2">Description</p>
                  <p className="text-sm">{spv.description}</p>
                </div>
              )}

              <Separator />

              {/* Legal Information */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Legal Structure</p>
                  <p className="text-sm">{spv.legalStructure || "N/A"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Jurisdiction</p>
                  <p className="text-sm">{spv.jurisdiction || "N/A"}</p>
                </div>
                {spv.registrationNumber && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Registration Number</p>
                    <p className="text-sm flex items-center gap-2">
                      <Hash className="h-4 w-4" />
                      {spv.registrationNumber}
                    </p>
                  </div>
                )}
                {spv.taxId && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Tax ID</p>
                    <p className="text-sm flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      {spv.taxId}
                    </p>
                  </div>
                )}
              </div>

              <Separator />

              {/* Dates and Location */}
              <div className="grid grid-cols-2 gap-4">
                {spv.establishedDate && (
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Established Date</p>
                    <p className="text-sm flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      {spvUtils.formatDate(spv.establishedDate)}
                    </p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Created</p>
                  <p className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {spvUtils.formatDateTime(spv.createdAt)}
                  </p>
                </div>
              </div>

              {/* Address */}
              {spv.address && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground mb-2">Address</p>
                  <p className="text-sm flex items-start gap-2">
                    <MapPin className="h-4 w-4 mt-0.5" />
                    {spv.address}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Users */}
          {'spvUsers' in spv && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  SPV Users ({spv._count.spvUsers || 0})
                </CardTitle>
                <CardDescription>
                  Users assigned to this SPV
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!spv.spvUsers || spv.spvUsers.length === 0 ? (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground">No users assigned to this SPV</p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {spv.spvUsers.map((spvUser) => (
                      <div key={spvUser.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{spvUser.user.name}</p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span>Email: {spvUser.user.email}</span>
                            <span>Role: {spvUser.role.replace('_', ' ')}</span>
                            <span className={`px-2 py-1 rounded-full text-xs ${
                              spvUser.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {spvUser.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </div>
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {spvUtils.formatDate(spvUser.createdAt)}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Projects */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Associated Projects ({spv._count.projects})
              </CardTitle>
              <CardDescription>
                Projects managed by this SPV
              </CardDescription>
            </CardHeader>
            <CardContent>
              {spv.projects.length === 0 ? (
                <div className="text-center py-8">
                  <Folder className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-sm text-muted-foreground">No projects associated with this SPV</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {spv.projects.map((project) => (
                    <div key={project.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{project.name}</p>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span>Status: {project.status}</span>
                          {project.type && <span>Type: {project.type}</span>}
                          {project.estimatedReductions && (
                            <span>Est. Reductions: {project.estimatedReductions.toLocaleString()} tCO2e</span>
                          )}
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {spvUtils.formatDate(project.createdAt)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Reset Credentials Display */}
        {resetCredentials && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <Key className="h-5 w-5" />
                New SPV Admin Credentials
              </CardTitle>
              <CardDescription className="text-green-700">
                Password has been reset successfully. Please save these credentials securely.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertDescription className="text-sm">
                  <strong>Important:</strong> These credentials will only be shown once. Please copy them now.
                </AlertDescription>
              </Alert>

              <div className="grid gap-4">
                <div>
                  <Label htmlFor="reset-email" className="text-sm font-medium">
                    Email
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="reset-email"
                      value={resetCredentials.email}
                      readOnly
                      className="bg-white"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(resetCredentials.email, "Email")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div>
                  <Label htmlFor="reset-password" className="text-sm font-medium">
                    Password
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="reset-password"
                      value={resetCredentials.password}
                      readOnly
                      className="bg-white font-mono"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(resetCredentials.password, "Password")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex justify-end">
                <Button
                  variant="outline"
                  onClick={() => setResetCredentials(null)}
                >
                  Dismiss
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Footer */}
        <div className="flex justify-end pt-6 border-t border-border">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
