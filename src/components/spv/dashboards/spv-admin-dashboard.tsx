"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Shield,
  Users,
  BarChart3,
  Settings,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  FileText,
  Clock,
  Target,
  UserPlus,
  Download,
} from "lucide-react";
import Link from "next/link";
import { SPVWelcomeGuide } from "@/components/spv/spv-welcome-guide";

interface SPVAdminStats {
  totalUsers: number;
  activeProjects: number;
  pendingApprovals: number;
  monthlyVerifications: number;
  spvApprovalRate: number;
  systemHealth: number;
  dataQuality: number;
  complianceScore: number;
}

interface PendingApproval {
  id: string;
  type: 'VERIFICATION' | 'USER_REQUEST' | 'PROJECT_CHANGE';
  title: string;
  description: string;
  submittedBy: string;
  submittedAt: string;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  projectName?: string;
}

interface SystemAlert {
  id: string;
  type: 'WARNING' | 'ERROR' | 'INFO';
  message: string;
  timestamp: string;
  resolved: boolean;
}

interface SPVUser {
  id: string;
  name: string;
  email: string;
  role: string;
  lastActive: string;
  entriesCount: number;
  status: 'ACTIVE' | 'INACTIVE';
}

interface SPVAdminDashboardProps {
  spvUser: any;
}

export function SPVAdminDashboard({ spvUser }: SPVAdminDashboardProps) {
  const [stats, setStats] = useState<SPVAdminStats | null>(null);
  const [pendingApprovals, setPendingApprovals] = useState<PendingApproval[]>([]);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [spvUsers, setSPVUsers] = useState<SPVUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showWelcomeGuide, setShowWelcomeGuide] = useState(true);

  useEffect(() => {
    if (spvUser) {
      fetchDashboardData();
    }
  }, [spvUser]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      const [statsResponse, approvalsResponse] = await Promise.all([
        fetch("/api/spv/dashboard/stats"),
        fetch("/api/spv/verification?status=VERIFIED&limit=5"),
      ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      if (approvalsResponse.ok) {
        const approvalsData = await approvalsResponse.json();
        // Transform verification queue into pending approvals
        const approvals = approvalsData.data.unitLogs?.map((unitLog: any) => ({
          id: unitLog.id,
          type: 'VERIFICATION',
          title: `${unitLog.project.name} - ${unitLog.unitType}`,
          description: `${unitLog.quantity} units logged by ${unitLog.logger.name}`,
          submittedBy: unitLog.logger.name,
          submittedAt: unitLog.verifiedAt || unitLog.createdAt,
          priority: 'MEDIUM',
          projectName: unitLog.project.name,
        })) || [];
        setPendingApprovals(approvals);
      }

    } catch (err) {
      setError("Failed to load dashboard data");
      console.error("Dashboard data fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'ERROR':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'WARNING':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Guide */}
      {showWelcomeGuide && (
        <SPVWelcomeGuide
          spvUser={spvUser}
          onDismiss={() => setShowWelcomeGuide(false)}
        />
      )}

      {/* Welcome Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">SPV Administration</h1>
            <p className="text-muted-foreground">
              System oversight and management • {spvUser?.spv?.name}
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" asChild>
              <Link href="/spv/users">
                <UserPlus className="h-4 w-4 mr-2" />
                Manage Users
              </Link>
            </Button>
            <Button asChild>
              <Link href="/spv/analytics">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">SPV Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.totalUsers || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Active team members
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Active Projects</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.activeProjects || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Projects under management
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Pending Approvals</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.pendingApprovals || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Requiring your approval
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">System Health</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.systemHealth || 0}%</div>
            <p className="text-xs font-normal text-muted-foreground">
              Overall system status
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Pending Approvals */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Pending Approvals</CardTitle>
                <CardDescription>
                  Items requiring SPV-level approval
                </CardDescription>
              </div>
              <Button size="sm" variant="outline" asChild>
                <Link href="/spv/verification?status=VERIFIED">
                  View All
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {pendingApprovals.length > 0 ? (
              <div className="space-y-3">
                {pendingApprovals.slice(0, 4).map((approval) => (
                  <div key={approval.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{approval.title}</h4>
                        <Badge className={getPriorityColor(approval.priority)} variant="outline">
                          {approval.priority}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {approval.description}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Submitted by {approval.submittedBy} • {new Date(approval.submittedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <Button size="sm" variant="outline" asChild>
                      <Link href={`/spv/verification?unitLog=${approval.id}`}>
                        Review
                      </Link>
                    </Button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No pending approvals</h3>
                <p className="text-muted-foreground">
                  All items have been processed.
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Administrative Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Administrative Actions</CardTitle>
            <CardDescription>
              SPV management and oversight tools
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" asChild>
              <Link href="/spv/verification?status=VERIFIED">
                <Shield className="h-4 w-4 mr-2" />
                Review SPV Approvals
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/users">
                <Users className="h-4 w-4 mr-2" />
                Manage SPV Users
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/analytics">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/reports">
                <Download className="h-4 w-4 mr-2" />
                Generate Reports
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/settings">
                <Settings className="h-4 w-4 mr-2" />
                SPV Settings
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* System Overview */}
      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>Data Quality</CardTitle>
            <CardDescription>
              Overall data integrity metrics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Verification Rate</span>
                  <span>{stats?.spvApprovalRate || 0}%</span>
                </div>
                <Progress value={stats?.spvApprovalRate || 0} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Data Quality</span>
                  <span>{stats?.dataQuality || 0}%</span>
                </div>
                <Progress value={stats?.dataQuality || 0} className="h-2" />
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Compliance Score</span>
                  <span>{stats?.complianceScore || 0}%</span>
                </div>
                <Progress value={stats?.complianceScore || 0} className="h-2" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Performance</CardTitle>
            <CardDescription>
              Key performance indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">Verifications</span>
                <span className="text-2xl font-bold">{stats?.monthlyVerifications || 0}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Approval Rate</span>
                <span className="text-2xl font-bold">{stats?.spvApprovalRate || 0}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Active Users</span>
                <span className="text-2xl font-bold">{stats?.totalUsers || 0}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
            <CardDescription>
              Current system health
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">All systems operational</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Data sync up to date</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm">Security checks passed</span>
              </div>
              <div className="pt-2">
                <Button variant="outline" size="sm" className="w-full">
                  View System Logs
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
