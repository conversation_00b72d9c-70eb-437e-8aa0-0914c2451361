"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  ClipboardCheck,
  Clock,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Users,
  FileText,
  BarChart3,
  Eye,
  Filter,
} from "lucide-react";
import Link from "next/link";
import { SPVWelcomeGuide } from "@/components/spv/spv-welcome-guide";

interface ProjectManagerStats {
  assignedProjects: number;
  pendingVerification: number;
  verifiedToday: number;
  rejectedToday: number;
  totalVerifications: number;
  averageVerificationTime: number;
  verificationAccuracy: number;
  teamProductivity: number;
}

interface VerificationQueueItem {
  id: string;
  logDate: string;
  unitType: string;
  quantity: number;
  verificationStatus: string;
  project: {
    id: string;
    name: string;
    type: string;
  };
  logger: {
    id: string;
    name: string;
    email: string;
  };
  submittedAt: string;
  priority: string;
}

interface TeamMember {
  id: string;
  name: string;
  email: string;
  entriesCount: number;
  pendingCount: number;
  approvalRate: number;
}

interface ProjectManagerDashboardProps {
  spvUser: any;
}

export function ProjectManagerDashboard({ spvUser }: ProjectManagerDashboardProps) {
  const [stats, setStats] = useState<ProjectManagerStats | null>(null);
  const [verificationQueue, setVerificationQueue] = useState<VerificationQueueItem[]>([]);
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showWelcomeGuide, setShowWelcomeGuide] = useState(true);

  useEffect(() => {
    if (spvUser) {
      fetchDashboardData();
    }
  }, [spvUser]);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      const [statsResponse, queueResponse] = await Promise.all([
        fetch("/api/spv/dashboard/stats"),
        fetch("/api/spv/verification?status=SUBMITTED_FOR_VERIFICATION&limit=5"),
      ]);

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setStats(statsData.data);
      }

      if (queueResponse.ok) {
        const queueData = await queueResponse.json();
        setVerificationQueue(queueData.data.unitLogs || []);
      }

    } catch (err) {
      setError("Failed to load dashboard data");
      console.error("Dashboard data fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUBMITTED_FOR_VERIFICATION':
        return 'bg-yellow-100 text-yellow-800';
      case 'VERIFIED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'HIGH':
        return 'bg-red-100 text-red-800';
      case 'MEDIUM':
        return 'bg-yellow-100 text-yellow-800';
      case 'LOW':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Guide */}
      {showWelcomeGuide && (
        <SPVWelcomeGuide
          spvUser={spvUser}
          onDismiss={() => setShowWelcomeGuide(false)}
        />
      )}

      {/* Welcome Section */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Project Manager Dashboard</h1>
            <p className="text-muted-foreground">
              Verification queue and team oversight • {spvUser?.spv?.name}
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" asChild>
              <Link href="/spv/verification">
                <ClipboardCheck className="h-4 w-4 mr-2" />
                Verification Queue
              </Link>
            </Button>
            <Button asChild>
              <Link href="/spv/analytics">
                <BarChart3 className="h-4 w-4 mr-2" />
                Analytics
              </Link>
            </Button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Pending Verification</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.pendingVerification || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Entries awaiting review
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Verified Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.verifiedToday || 0}</div>
            <p className="text-xs font-normal text-muted-foreground">
              Entries processed today
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Verification Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.verificationAccuracy || 0}%</div>
            <p className="text-xs font-normal text-muted-foreground">
              Accuracy this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-normal">Team Productivity</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{stats?.teamProductivity || 0}%</div>
            <p className="text-xs font-normal text-muted-foreground">
              vs. last month
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Verification Queue */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Verification Queue</CardTitle>
                <CardDescription>
                  Entries requiring your immediate attention
                </CardDescription>
              </div>
              <Button size="sm" variant="outline" asChild>
                <Link href="/spv/verification">
                  <Eye className="h-4 w-4 mr-2" />
                  View All
                </Link>
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {verificationQueue.length > 0 ? (
              <div className="space-y-3">
                {verificationQueue.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium text-sm">{item.project.name}</h4>
                        <Badge className={getPriorityColor(item.priority)} variant="outline">
                          {item.priority}
                        </Badge>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {item.unitType}: {item.quantity} • by {item.logger.name}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Submitted {new Date(item.submittedAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(item.verificationStatus)}>
                        Pending
                      </Badge>
                      <Button size="sm" variant="outline" asChild>
                        <Link href={`/spv/verification?unitLog=${item.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <ClipboardCheck className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No pending verifications</h3>
                <p className="text-muted-foreground">
                  All entries have been processed. Great work!
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>
              Common project management tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start" asChild>
              <Link href="/spv/verification">
                <ClipboardCheck className="h-4 w-4 mr-2" />
                Review Verification Queue
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/verification?status=REJECTED">
                <AlertCircle className="h-4 w-4 mr-2" />
                Review Rejected Entries
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/analytics">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </Link>
            </Button>
            <Button variant="outline" className="w-full justify-start" asChild>
              <Link href="/spv/projects">
                <FileText className="h-4 w-4 mr-2" />
                Manage Projects
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Performance Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Verification Performance</CardTitle>
          <CardDescription>
            Your verification metrics and team performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Daily Target</span>
                <span>75%</span>
              </div>
              <Progress value={75} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {stats?.verifiedToday || 0} of {Math.round((stats?.verifiedToday || 0) / 0.75)} target
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Accuracy Rate</span>
                <span>{stats?.verificationAccuracy || 0}%</span>
              </div>
              <Progress value={stats?.verificationAccuracy || 0} className="h-2" />
              <p className="text-xs text-muted-foreground">
                Above 95% target
              </p>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span>Team Efficiency</span>
                <span>{stats?.teamProductivity || 0}%</span>
              </div>
              <Progress value={stats?.teamProductivity || 0} className="h-2" />
              <p className="text-xs text-muted-foreground">
                vs. last month
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
