"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, User, FolderOpen, Users } from "lucide-react";
import { toast } from "sonner";

const projectAssignmentSchema = z.object({
  spvUserId: z.string().min(1, "User is required"),
  projectIds: z.array(z.string()).min(1, "At least one project must be selected"),
});

type ProjectAssignmentFormValues = z.infer<typeof projectAssignmentSchema>;

interface SPVUser {
  id: string;
  role: string;
  user: {
    id: string;
    name: string;
    email: string;
    jobTitle?: string;
  };
  projectAssignments: {
    id: string;
    project: {
      id: string;
      name: string;
    };
  }[];
}

interface Project {
  id: string;
  name: string;
  type: string;
  status: string;
  location?: string;
  isAssignedToUser: boolean;
  assignedUsers: {
    id: string;
    userId: string;
    role: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
}

interface SPVProjectAssignmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  selectedUserId?: string; // Pre-select a user
}

export function SPVProjectAssignmentForm({
  isOpen,
  onClose,
  onSuccess,
  selectedUserId,
}: SPVProjectAssignmentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [users, setUsers] = useState<SPVUser[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [loadingProjects, setLoadingProjects] = useState(false);

  const form = useForm<ProjectAssignmentFormValues>({
    resolver: zodResolver(projectAssignmentSchema),
    defaultValues: {
      spvUserId: selectedUserId || "",
      projectIds: [],
    },
  });

  const selectedUserId_watch = form.watch("spvUserId");

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
      form.reset({
        spvUserId: selectedUserId || "",
        projectIds: [],
      });
    }
  }, [isOpen, selectedUserId, form]);

  useEffect(() => {
    if (selectedUserId_watch) {
      fetchProjects(selectedUserId_watch);
    }
  }, [selectedUserId_watch]);

  const fetchUsers = async () => {
    try {
      setLoadingUsers(true);
      const response = await fetch("/api/spv/users?limit=100");
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      } else {
        toast.error("Failed to fetch users");
      }
    } catch (error) {
      toast.error("Error fetching users");
      console.error("Users fetch error:", error);
    } finally {
      setLoadingUsers(false);
    }
  };

  const fetchProjects = async (userId: string) => {
    try {
      setLoadingProjects(true);
      const response = await fetch(`/api/spv/projects?limit=100&userId=${userId}`);
      if (response.ok) {
        const data = await response.json();
        setProjects(data.data.projects || []);
      } else {
        toast.error("Failed to fetch projects");
      }
    } catch (error) {
      toast.error("Error fetching projects");
      console.error("Projects fetch error:", error);
    } finally {
      setLoadingProjects(false);
    }
  };

  const onSubmit = async (data: ProjectAssignmentFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch("/api/spv/project-assignments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          spvUserId: data.spvUserId,
          projectIds: data.projectIds,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message || "Projects assigned successfully");
        onSuccess();
        onClose();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to assign projects");
      }
    } catch (error) {
      toast.error("Error assigning projects");
      console.error("Project assignment error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedUser = users.find(user => user.id === selectedUserId_watch);
  const selectedProjectIds = form.watch("projectIds") || [];
  const availableProjects = projects.filter(project => !project.isAssignedToUser);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Assign Projects to User</DialogTitle>
          <DialogDescription>
            Select a user and assign them to one or more projects.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* User Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Select User
                </CardTitle>
                <CardDescription>
                  Choose the user to assign projects to
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="spvUserId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>User *</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        value={field.value}
                        disabled={!!selectedUserId}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a user" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {loadingUsers ? (
                            <SelectItem value="loading" disabled>
                              Loading users...
                            </SelectItem>
                          ) : users.length > 0 ? (
                            users.map((user) => (
                              <SelectItem key={user.id} value={user.id}>
                                <div className="flex flex-col">
                                  <span>{user.user.name}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {user.user.email} • {user.role.replace('_', ' ')}
                                    {user.user.jobTitle && ` • ${user.user.jobTitle}`}
                                  </span>
                                </div>
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-users" disabled>
                              No users available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {selectedUser && (
                  <div className="mt-4 p-4 bg-muted rounded-lg">
                    <h4 className="font-medium mb-2">Selected User</h4>
                    <div className="space-y-1 text-sm">
                      <p><strong>Name:</strong> {selectedUser.user.name}</p>
                      <p><strong>Email:</strong> {selectedUser.user.email}</p>
                      <p><strong>Role:</strong> {selectedUser.role.replace('_', ' ')}</p>
                      {selectedUser.user.jobTitle && (
                        <p><strong>Job Title:</strong> {selectedUser.user.jobTitle}</p>
                      )}
                      <p><strong>Current Projects:</strong> {selectedUser.projectAssignments.length}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Project Selection */}
            {selectedUserId_watch && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FolderOpen className="h-5 w-5" />
                    Select Projects
                  </CardTitle>
                  <CardDescription>
                    Choose projects to assign to the selected user
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {loadingProjects ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin" />
                      <span className="ml-2">Loading projects...</span>
                    </div>
                  ) : availableProjects.length > 0 ? (
                    <FormField
                      control={form.control}
                      name="projectIds"
                      render={() => (
                        <FormItem>
                          <FormLabel>Available Projects</FormLabel>
                          <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto">
                            {availableProjects.map((project) => (
                              <FormField
                                key={project.id}
                                control={form.control}
                                name="projectIds"
                                render={({ field }) => {
                                  return (
                                    <FormItem
                                      key={project.id}
                                      className="flex flex-row items-start space-x-3 space-y-0"
                                    >
                                      <FormControl>
                                        <Checkbox
                                          checked={field.value?.includes(project.id)}
                                          onCheckedChange={(checked) => {
                                            return checked
                                              ? field.onChange([...field.value, project.id])
                                              : field.onChange(
                                                  field.value?.filter(
                                                    (value) => value !== project.id
                                                  )
                                                );
                                          }}
                                        />
                                      </FormControl>
                                      <div className="space-y-1 leading-none flex-1">
                                        <FormLabel className="font-normal">
                                          {project.name}
                                        </FormLabel>
                                        <p className="text-xs text-muted-foreground">
                                          {project.type} • {project.status}
                                          {project.location && ` • ${project.location}`}
                                        </p>
                                        {project.assignedUsers.length > 0 && (
                                          <p className="text-xs text-muted-foreground">
                                            Currently assigned to: {project.assignedUsers.map(u => u.user.name).join(", ")}
                                          </p>
                                        )}
                                      </div>
                                    </FormItem>
                                  );
                                }}
                              />
                            ))}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ) : (
                    <div className="text-center py-8">
                      <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <p className="text-sm text-muted-foreground">
                        No available projects to assign. All projects are already assigned to this user.
                      </p>
                    </div>
                  )}

                  {selectedProjectIds.length > 0 && (
                    <div className="mt-4">
                      <p className="text-sm font-medium mb-2">
                        Selected Projects ({selectedProjectIds.length}):
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {selectedProjectIds.map((projectId) => {
                          const project = projects.find(p => p.id === projectId);
                          return project ? (
                            <Badge key={projectId} variant="secondary">
                              {project.name}
                            </Badge>
                          ) : null;
                        })}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                type="submit" 
                disabled={isSubmitting || !selectedUserId_watch || selectedProjectIds.length === 0}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Assign Projects
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
