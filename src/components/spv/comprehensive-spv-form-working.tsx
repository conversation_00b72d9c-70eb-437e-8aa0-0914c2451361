"use client";

import React, { useState, useCallback } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  Building2, 
  Users, 
  DollarSign, 
  Plus, 
  Trash2, 
  Upload, 
  Settings,
  CheckCircle,
  AlertCircle,
  Loader2,
  X,
  FileText
} from "lucide-react";
import { comprehensiveSPVCreationSchema } from "@/lib/validation/schemas";
import { ComprehensiveSPVCreateData } from "@/types/spv";
import { toast } from "sonner";

type SPVFormValues = z.infer<typeof comprehensiveSPVCreationSchema>;

interface ComprehensiveSPVCreationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ComprehensiveSPVCreateData) => Promise<void>;
  isSubmitting?: boolean;
  initialData?: Partial<ComprehensiveSPVCreateData>;
  mode?: "user" | "admin";
  organizations?: Array<{ id: string; name: string }>;
}

interface DocumentUpload {
  fieldName: string;
  file: File;
  url?: string;
  uploading: boolean;
  error?: string;
}

const spvTypeOptions = [
  { value: "PRIVATE_LIMITED", label: "Private Limited Company" },
  { value: "LLP", label: "Limited Liability Partnership" },
  { value: "TRUST", label: "Trust" },
  { value: "PARTNERSHIP", label: "Partnership" },
];

const fundingSourceOptions = [
  { value: "EQUITY", label: "Equity" },
  { value: "DEBT", label: "Debt" },
  { value: "GRANT", label: "Grant" },
  { value: "MIXED", label: "Mixed Funding" },
  { value: "OTHER", label: "Other" },
];

export function ComprehensiveSPVCreationForm({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false,
  initialData,
  mode = "user",
  organizations = [],
}: ComprehensiveSPVCreationFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("basic");
  const [documentUploads, setDocumentUploads] = useState<DocumentUpload[]>([]);
  const [formProgress, setFormProgress] = useState(0);

  const form = useForm<SPVFormValues>({
    resolver: zodResolver(comprehensiveSPVCreationSchema),
    mode: "onChange",
    defaultValues: {
      name: initialData?.name || "",
      parentCompany: initialData?.parentCompany || "",
      spvType: initialData?.spvType || undefined,
      dateOfIncorporation: initialData?.dateOfIncorporation || "",
      cinNumber: initialData?.cinNumber || "",
      registeredAddress: initialData?.registeredAddress || "",
      stateOfIncorporation: initialData?.stateOfIncorporation || "",
      panNumber: initialData?.panNumber || "",
      gstNumber: initialData?.gstNumber || "",
      spvLogoUrl: initialData?.spvLogoUrl || "",
      purpose: initialData?.purpose || "",
      description: initialData?.description || "",
      promoterName: initialData?.promoterName || "",
      equityHolders: initialData?.equityHolders || [],
      boardMembers: initialData?.boardMembers || [],
      authorizedSignatory: initialData?.authorizedSignatory || "",
      registeredRepresentative: initialData?.registeredRepresentative || "",
      authorizedCapital: initialData?.authorizedCapital || undefined,
      paidUpCapital: initialData?.paidUpCapital || undefined,
      bankAccountDetails: initialData?.bankAccountDetails || {
        accountNumber: "",
        bankName: "",
        ifscCode: "",
        accountType: "",
      },
      debtProvider: initialData?.debtProvider || "",
      debtEquityRatio: initialData?.debtEquityRatio || "",
      fundingSource: initialData?.fundingSource || undefined,
      ppaSignedWith: initialData?.ppaSignedWith || "",
      adminName: initialData?.adminName || "",
      adminEmail: initialData?.adminEmail || "",
      customFields: initialData?.customFields || {},
    },
  });

  const { fields: equityHolderFields, append: appendEquityHolder, remove: removeEquityHolder } = useFieldArray({
    control: form.control,
    name: "equityHolders",
  });

  const { fields: boardMemberFields, append: appendBoardMember, remove: removeBoardMember } = useFieldArray({
    control: form.control,
    name: "boardMembers",
  });

  const addEquityHolder = () => {
    appendEquityHolder({ name: "", shareholding: 0 });
  };

  const addBoardMember = () => {
    appendBoardMember({ name: "", designation: "", din: "" });
  };

  // Document upload handler
  const handleDocumentUpload = useCallback(async (fieldName: string, file: File) => {
    if (!file) return;

    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      toast.error("Please upload only PDF, JPG, or PNG files");
      return;
    }

    if (file.size > maxSize) {
      toast.error("File size must be less than 5MB");
      return;
    }

    setDocumentUploads(prev => [...prev, { fieldName, file, uploading: true }]);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'spv-documents');
      formData.append('fieldName', fieldName);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload document');
      }
      
      const result = await response.json();
      
      setDocumentUploads(prev => prev.map(upload => 
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, url: result.url }
          : upload
      ));
      
      toast.success(`Document uploaded successfully`);
    } catch (error) {
      console.error("Document upload error:", error);
      
      setDocumentUploads(prev => prev.map(upload => 
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, error: error instanceof Error ? error.message : 'Upload failed' }
          : upload
      ));
      
      toast.error(`Failed to upload document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, []);

  // Form submission with validation
  const handleSubmit = async (data: SPVFormValues) => {
    try {
      setSubmitError(null);
      
      if (!data.name?.trim()) {
        setSubmitError("SPV name is required");
        setActiveTab("basic");
        return;
      }

      if (!data.adminEmail?.trim()) {
        setSubmitError("Admin email is required for credential generation");
        setActiveTab("financial");
        return;
      }

      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.adminEmail)) {
        setSubmitError("Please enter a valid admin email address");
        setActiveTab("financial");
        return;
      }

      if (data.cinNumber && !/^[A-Z]{1}[0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/.test(data.cinNumber)) {
        setSubmitError("Invalid CIN format. Expected format: L12345AB2023PLC123456");
        setActiveTab("basic");
        return;
      }

      if (data.panNumber && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(data.panNumber)) {
        setSubmitError("Invalid PAN format. Expected format: **********");
        setActiveTab("basic");
        return;
      }

      if (data.gstNumber && !/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(data.gstNumber)) {
        setSubmitError("Invalid GST format. Expected format: 12**********1Z5");
        setActiveTab("basic");
        return;
      }

      if (data.bankAccountDetails?.ifscCode && !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(data.bankAccountDetails.ifscCode)) {
        setSubmitError("Invalid IFSC code format. Expected format: ABCD0123456");
        setActiveTab("financial");
        return;
      }

      console.log("Submitting SPV data:", data);
      
      await onSubmit(data as ComprehensiveSPVCreateData);
      
      form.reset();
      setDocumentUploads([]);
      setFormProgress(0);
      onClose();
      
      toast.success("SPV created successfully!");
    } catch (error) {
      console.error("Error submitting SPV form:", error);
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setSubmitError(errorMessage);
      toast.error(`Failed to create SPV: ${errorMessage}`);
    }
  };

  // Calculate form progress
  const calculateProgress = useCallback(() => {
    const values = form.getValues();
    const totalFields = 10;
    let filledFields = 0;

    if (values.name) filledFields++;
    if (values.adminEmail) filledFields++;
    if (values.spvType) filledFields++;
    if (values.dateOfIncorporation) filledFields++;
    if (values.registeredAddress) filledFields++;
    if (values.purpose) filledFields++;
    if (values.promoterName) filledFields++;
    if (values.authorizedCapital) filledFields++;
    if (values.bankAccountDetails?.bankName) filledFields++;
    if (values.fundingSource) filledFields++;

    const progress = Math.round((filledFields / totalFields) * 100);
    setFormProgress(progress);
    return progress;
  }, [form]);

  // Update progress when form values change
  React.useEffect(() => {
    calculateProgress();
  }, [calculateProgress, form.watch()]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-blue-600" />
              <DialogTitle className="text-xl font-semibold">Create New SPV</DialogTitle>
            </div>
            <Button type="button" variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
          <DialogDescription className="text-gray-600">
            Create a comprehensive Special Purpose Vehicle with detailed information across all categories.
            All fields are optional initially and can be completed later.
          </DialogDescription>
          
          <div className="space-y-2 mt-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Form Completion</span>
              <span className="font-medium">{formProgress}%</span>
            </div>
            <Progress value={formProgress} className="h-2" />
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="h-full flex flex-col">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                <TabsList className="grid w-full grid-cols-3 mb-6">
                  <TabsTrigger value="basic" className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    Basic Information
                  </TabsTrigger>
                  <TabsTrigger value="ownership" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Ownership & Stakeholders
                  </TabsTrigger>
                  <TabsTrigger value="financial" className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Financial Details
                  </TabsTrigger>
                </TabsList>

                {/* Basic Information Tab */}
                <TabsContent value="basic" className="flex-1 space-y-6">
                  <Card className="border-0 shadow-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Building2 className="h-5 w-5 text-blue-600" />
                        Basic Information
                      </CardTitle>
                      <CardDescription className="text-gray-600">
                        Essential details about your SPV including legal structure and incorporation information.
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-8">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* SPV Name - Required */}
                        <FormField
                          control={form.control}
                          name="name"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium">
                                SPV Name <span className="text-red-500">*</span>
                              </FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="Enter SPV name" 
                                  {...field} 
                                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                />
                              </FormControl>
                              <FormMessage className="text-red-600" />
                            </FormItem>
                          )}
                        />

                        {/* Parent Company */}
                        <FormField
                          control={form.control}
                          name="parentCompany"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-sm font-medium">Parent Company / Holding Company</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="Enter parent company name" 
                                  {...field} 
                                  className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                />
                              </FormControl>
                              <FormMessage className="text-red-600" />
                            </FormItem>
                          )}
                        />
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

              {/* Error Display */}
              {submitError && (
                <Alert variant="destructive" className="mt-6">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="font-medium">
                    {submitError}
                  </AlertDescription>
                </Alert>
              )}

              {/* Form Actions */}
              <div className="flex items-center justify-between pt-6 border-t border-gray-200 mt-8">
                {/* Previous Button - Left Corner */}
                <div>
                  {activeTab !== "basic" && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        if (activeTab === "ownership") setActiveTab("basic");
                        if (activeTab === "financial") setActiveTab("ownership");
                      }}
                      disabled={isSubmitting}
                      className="px-6"
                    >
                      Previous
                    </Button>
                  )}
                </div>

                {/* Next/Create Button - Right Corner */}
                <div>
                  {activeTab !== "financial" ? (
                    <Button
                      type="button"
                      variant="default"
                      onClick={() => {
                        if (activeTab === "basic") setActiveTab("ownership");
                        if (activeTab === "ownership") setActiveTab("financial");
                      }}
                      disabled={isSubmitting}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-8"
                    >
                      Next
                    </Button>
                  ) : (
                    <Button
                      type="submit"
                      disabled={isSubmitting || !form.formState.isValid}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-8"
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Creating SPV...
                        </>
                      ) : (
                        <>
                          <Building2 className="h-4 w-4 mr-2" />
                          Create SPV
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
