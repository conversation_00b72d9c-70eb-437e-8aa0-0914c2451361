"use client";

import React, { useState, useCallback } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";

import {
  Building2,
  Users,
  DollarSign,
  Plus,
  Trash2,
  Upload,
  HelpCircle,
  FileText,
  Building,
  Settings,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react";
import { comprehensiveSPVCreationSchema } from "@/lib/validation/schemas";
import { ComprehensiveSPVCreateData } from "@/types/spv";
import { SPVCustomFieldsManager } from "./spv-custom-fields-manager";
import { toast } from "sonner";

type SPVFormValues = z.infer<typeof comprehensiveSPVCreationSchema>;

interface ComprehensiveSPVCreationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: ComprehensiveSPVCreateData) => Promise<void>;
  isSubmitting?: boolean;
  initialData?: Partial<ComprehensiveSPVCreateData>;
  mode?: "user" | "admin";
  organizations?: Array<{ id: string; name: string }>;
}

interface DocumentUpload {
  fieldName: string;
  file: File;
  url?: string;
  uploading: boolean;
  error?: string;
}

interface CustomField {
  id: string;
  name: string;
  label: string;
  type: "text" | "number" | "date" | "select" | "textarea" | "file" | "checkbox";
  required: boolean;
  placeholder?: string;
  helpText?: string;
  options?: string[];
  section: "BASIC_INFO" | "OWNERSHIP" | "FINANCIAL";
}

// Field descriptions for tooltips
const fieldDescriptions = {
  name: "The official name of your Special Purpose Vehicle",
  parentCompany: "Name of the parent or holding company (if applicable)",
  spvType: "Legal structure of your SPV organization",
  dateOfIncorporation: "Date when the SPV was officially incorporated",
  cinNumber: "Corporate Identity Number (21 characters) - Format: L12345AB2023PLC123456",
  registeredAddress: "Complete registered office address as per incorporation documents",
  stateOfIncorporation: "State or territory where the SPV is incorporated",
  panNumber: "Permanent Account Number (10 characters) - Format: **********",
  gstNumber: "Goods and Services Tax Number (15 characters) - Format: 12**********1Z5",
  spvLogoUrl: "URL to your SPV's logo image",
  promoterName: "Name of the main promoter or developer",
  authorizedSignatory: "Person authorized to sign legal documents on behalf of SPV",
  registeredRepresentative: "Designated person for legal and financial communications",
  authorizedCapital: "Maximum amount of capital the company is authorized to raise",
  paidUpCapital: "Actual amount of capital paid by shareholders",
  debtProvider: "Name of the primary debt provider or lender",
  debtEquityRatio: "Ratio of debt to equity financing (e.g., 70:30)",
  fundingSource: "Primary source of funding for the SPV",
  ppaSignedWith: "Entity with whom Power Purchase Agreement is signed (if applicable)",
  // Bank account field descriptions
  accountNumber: "Bank account number for the SPV",
  bankName: "Name of the bank where the account is held",
  ifscCode: "Indian Financial System Code (11 characters) - Format: ABCD0123456",
  accountType: "Type of bank account (Current, Savings, Escrow, etc.)",
};

const spvTypeOptions = [
  { value: "PRIVATE_LIMITED", label: "Private Limited Company" },
  { value: "LLP", label: "Limited Liability Partnership" },
  { value: "TRUST", label: "Trust" },
  { value: "PARTNERSHIP", label: "Partnership" },
];

const fundingSourceOptions = [
  { value: "EQUITY", label: "Equity" },
  { value: "DEBT", label: "Debt" },
  { value: "GRANT", label: "Grant" },
  { value: "MIXED", label: "Mixed Funding" },
  { value: "OTHER", label: "Other" },
];

const accountTypeOptions = [
  { value: "CURRENT", label: "Current Account" },
  { value: "SAVINGS", label: "Savings Account" },
  { value: "ESCROW", label: "Escrow Account" },
  { value: "OTHER", label: "Other" },
];

export function ComprehensiveSPVCreationForm({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false,
  initialData,
  mode = "user",
  organizations = [],
}: ComprehensiveSPVCreationFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("basic");
  const [documentUploads, setDocumentUploads] = useState<DocumentUpload[]>([]);
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [isCustomFieldsOpen, setIsCustomFieldsOpen] = useState(false);


  const form = useForm<SPVFormValues>({
    resolver: zodResolver(comprehensiveSPVCreationSchema),
    mode: "onSubmit",
    defaultValues: {
      // Basic Information
      name: initialData?.name || "",
      parentCompany: initialData?.parentCompany || "",
      spvType: initialData?.spvType || undefined,
      dateOfIncorporation: initialData?.dateOfIncorporation || "",
      cinNumber: initialData?.cinNumber || "",
      registeredAddress: initialData?.registeredAddress || "",
      stateOfIncorporation: initialData?.stateOfIncorporation || "",
      panNumber: initialData?.panNumber || "",
      gstNumber: initialData?.gstNumber || "",
      spvLogoUrl: initialData?.spvLogoUrl || "",

      description: initialData?.description || "",

      // Ownership & Stakeholders
      promoterName: initialData?.promoterName || "",
      equityHolders: initialData?.equityHolders || [],
      boardMembers: initialData?.boardMembers || [],
      authorizedSignatory: initialData?.authorizedSignatory || "",
      registeredRepresentative: initialData?.registeredRepresentative || "",

      // Financial Details
      authorizedCapital: initialData?.authorizedCapital || undefined,
      paidUpCapital: initialData?.paidUpCapital || undefined,
      bankAccountDetails: initialData?.bankAccountDetails || {
        accountNumber: "",
        bankName: "",
        ifscCode: "",
        accountType: "",
      },
      debtProvider: initialData?.debtProvider || "",
      debtEquityRatio: initialData?.debtEquityRatio || "",
      fundingSource: initialData?.fundingSource || undefined,
      ppaSignedWith: initialData?.ppaSignedWith || "",

      // Admin details
      adminName: initialData?.adminName || "",
      adminEmail: initialData?.adminEmail || "",

      // Custom fields
      customFields: initialData?.customFields || {},
    },
  });

  const { fields: equityHolderFields, append: appendEquityHolder, remove: removeEquityHolder } = useFieldArray({
    control: form.control,
    name: "equityHolders",
  });

  const { fields: boardMemberFields, append: appendBoardMember, remove: removeBoardMember } = useFieldArray({
    control: form.control,
    name: "boardMembers",
  });

  // Enhanced form submission with proper validation and error handling
  const handleSubmit = async (data: SPVFormValues) => {
    try {
      setSubmitError(null);

      // Validate required fields
      if (!data.name?.trim()) {
        setSubmitError("SPV name is required");
        setActiveTab("basic");
        return;
      }

      // Validate email format if provided
      if (data.adminEmail?.trim()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.adminEmail)) {
          setSubmitError("Please enter a valid admin email address");
          setActiveTab("financial");
          return;
        }
      }

      // Validate CIN format if provided
      if (data.cinNumber && !/^[A-Z]{1}[0-9]{5}[A-Z]{2}[0-9]{4}[A-Z]{3}[0-9]{6}$/.test(data.cinNumber)) {
        setSubmitError("Invalid CIN format. Expected format: L12345AB2023PLC123456");
        setActiveTab("basic");
        return;
      }

      // Validate PAN format if provided
      if (data.panNumber && !/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/.test(data.panNumber)) {
        setSubmitError("Invalid PAN format. Expected format: **********");
        setActiveTab("basic");
        return;
      }

      // Validate GST format if provided
      if (data.gstNumber && !/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/.test(data.gstNumber)) {
        setSubmitError("Invalid GST format. Expected format: 12**********1Z5");
        setActiveTab("basic");
        return;
      }

      // Validate IFSC code if provided
      if (data.bankAccountDetails?.ifscCode && !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(data.bankAccountDetails.ifscCode)) {
        setSubmitError("Invalid IFSC code format. Expected format: ABCD0123456");
        setActiveTab("financial");
        return;
      }

      // Include custom fields in submission
      // Type assertion is safe here because form validation ensures data structure is correct
      const submissionData: ComprehensiveSPVCreateData = {
        ...data,
        customFields: {
          ...data.customFields,
          _customFieldDefinitions: customFields
        }
      } as ComprehensiveSPVCreateData;

      console.log("Submitting SPV data:", submissionData);

      await onSubmit(submissionData);

      // Reset form and close on success
      form.reset();
      setDocumentUploads([]);
      setCustomFields([]);
      onClose();

      toast.success("SPV created successfully!");
    } catch (error) {
      console.error("Error submitting SPV form:", error);
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setSubmitError(errorMessage);
      toast.error(`Failed to create SPV: ${errorMessage}`);
    }
  };

  const addEquityHolder = () => {
    appendEquityHolder({ name: "", shareholding: 0 });
  };

  const addBoardMember = () => {
    appendBoardMember({ name: "", designation: "", din: "" });
  };

  // Document upload handler with proper error handling and progress tracking
  const handleDocumentUpload = useCallback(async (fieldName: string, file: File) => {
    if (!file) return;

    // Validate file type and size
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      toast.error("Please upload only PDF, JPG, or PNG files");
      return;
    }

    if (file.size > maxSize) {
      toast.error("File size must be less than 5MB");
      return;
    }

    // Add to upload tracking
    setDocumentUploads(prev => [...prev, {
      fieldName,
      file,
      uploading: true
    }]);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'spv-documents');
      formData.append('fieldName', fieldName);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload document');
      }

      const result = await response.json();

      // Update the form field with the uploaded file URL
      if (fieldName === 'spvLogoUrl') {
        form.setValue('spvLogoUrl', result.url);
      }

      // Update upload tracking
      setDocumentUploads(prev => prev.map(upload =>
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, url: result.url }
          : upload
      ));

      toast.success(`${fieldName} document uploaded successfully`);
    } catch (error) {
      console.error("Document upload error:", error);

      // Update upload tracking with error
      setDocumentUploads(prev => prev.map(upload =>
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, error: error instanceof Error ? error.message : 'Upload failed' }
          : upload
      ));

      toast.error(`Failed to upload ${fieldName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [form]);

  // Custom fields management
  const handleCustomFieldsSave = useCallback(async (fields: CustomField[]) => {
    setCustomFields(fields);
    setIsCustomFieldsOpen(false);
    toast.success("Custom fields updated successfully");
  }, []);



  // Helper component for field with tooltip
  const FieldWithTooltip = ({
    children,
    description,
    label,
    required = false
  }: {
    children: React.ReactNode;
    description: string;
    label: string;
    required?: boolean;
  }) => (
    <div className="space-y-2">
      <div className="flex items-center gap-2">
        <label className="text-sm font-medium text-gray-900">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <Tooltip>
          <TooltipTrigger asChild>
            <HelpCircle className="h-4 w-4 text-muted-foreground cursor-help hover:text-gray-600" />
          </TooltipTrigger>
          <TooltipContent className="max-w-xs bg-white border shadow-lg p-3">
            <p className="text-sm text-gray-700">{description}</p>
          </TooltipContent>
        </Tooltip>
      </div>
      {children}
    </div>
  );

  // Document upload component
  const DocumentUploadField = ({
    fieldName,
    label,
    description,
    accept = ".pdf,.jpg,.jpeg,.png",
    required = false
  }: {
    fieldName: string;
    label: string;
    description: string;
    accept?: string;
    required?: boolean;
  }) => {
    const upload = documentUploads.find(u => u.fieldName === fieldName);
    const inputId = `${fieldName}-upload`;

    return (
      <FieldWithTooltip label={label} description={description} required={required}>
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <input
              id={inputId}
              type="file"
              accept={accept}
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleDocumentUpload(fieldName, file);
              }}
              className="hidden"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById(inputId)?.click()}
              disabled={upload?.uploading}
              className="w-full justify-start"
            >
              {upload?.uploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : upload?.url ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                  Document Uploaded
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Document
                </>
              )}
            </Button>
          </div>
          {upload?.url && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => window.open(upload.url, '_blank')}
            >
              <FileText className="h-4 w-4" />
            </Button>
          )}
          {upload?.error && (
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-4 w-4" />
            </div>
          )}
        </div>
        {upload?.error && (
          <p className="text-sm text-red-600 mt-1">{upload.error}</p>
        )}
      </FieldWithTooltip>
    );
  };



  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-blue-600" />
              <DialogTitle className="text-xl font-semibold">Create New SPV</DialogTitle>
            </div>
            <div className="flex items-center gap-3 mr-4">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setIsCustomFieldsOpen(true)}
                className="flex items-center gap-2"
              >
                <Settings className="h-4 w-4" />
                Custom Fields
                {customFields.length > 0 && (
                  <Badge variant="secondary" className="ml-1">
                    {customFields.length}
                  </Badge>
                )}
              </Button>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <TooltipProvider>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="h-full flex flex-col">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="basic" className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Basic Info.
                    </TabsTrigger>
                    <TabsTrigger value="ownership" className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Ownership
                    </TabsTrigger>
                    <TabsTrigger value="financial" className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Financial Details
                    </TabsTrigger>
                  </TabsList>

                {/* Basic Information Tab */}
                <TabsContent value="basic" className="flex-1 space-y-6">
                  <Card className="border-0 shadow-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Building className="h-5 w-5 text-blue-600" />
                        Basic Information
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-8">
                      {/* Essential Information */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Essential Information</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* SPV Name - Required */}
                            <FormField
                              control={form.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="SPV Name"
                                    description={fieldDescriptions.name}
                                    required={true}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter SPV name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* Parent Company */}
                            <FormField
                              control={form.control}
                              name="parentCompany"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Parent Company"
                                    description={fieldDescriptions.parentCompany}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter parent company name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* SPV Type */}
                            <FormField
                              control={form.control}
                              name="spvType"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="SPV Type"
                                    description={fieldDescriptions.spvType}
                                  >
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <FormControl>
                                        <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                          <SelectValue placeholder="Select SPV type" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {spvTypeOptions.map((option) => (
                                          <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* Date of Incorporation */}
                            <FormField
                              control={form.control}
                              name="dateOfIncorporation"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Date of Incorporation"
                                    description={fieldDescriptions.dateOfIncorporation}
                                  >
                                    <FormControl>
                                      <Input
                                        type="date"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        <Separator />

                        {/* Legal Documentation */}
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Legal Documentation</h3>
                          <div className="space-y-6">
                            {/* CIN Number with Upload */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="cinNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="CIN Number"
                                      description={fieldDescriptions.cinNumber}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="L12345AB2023PLC123456"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />
                              <div>
                                <DocumentUploadField
                                  fieldName="cinDocument"
                                  label="CIN Certificate"
                                  description="Upload CIN certificate document"
                                />
                              </div>
                            </div>

                            {/* State of Incorporation with Upload */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="stateOfIncorporation"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="State of Incorporation"
                                      description={fieldDescriptions.stateOfIncorporation}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter state name"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />
                              <div>
                                <DocumentUploadField
                                  fieldName="incorporationDocument"
                                  label="Incorporation Certificate"
                                  description="Upload incorporation certificate"
                                />
                              </div>
                            </div>

                            {/* PAN Number with Upload */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="panNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="PAN Number"
                                      description={fieldDescriptions.panNumber}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="**********"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />
                              <div>
                                <DocumentUploadField
                                  fieldName="panDocument"
                                  label="PAN Card"
                                  description="Upload PAN card document"
                                />
                              </div>
                            </div>

                            {/* GST Number with Upload */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <FormField
                                control={form.control}
                                name="gstNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="GST Number"
                                      description={fieldDescriptions.gstNumber}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="12**********1Z5"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />
                              <div>
                                <DocumentUploadField
                                  fieldName="gstDocument"
                                  label="GST Certificate"
                                  description="Upload GST registration certificate"
                                />
                              </div>
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Address and Additional Information */}
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Address & Additional Information</h3>
                          <div className="space-y-6">
                            {/* Registered Office Address with Upload */}
                            <FormField
                              control={form.control}
                              name="registeredAddress"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Registered Office Address"
                                    description={fieldDescriptions.registeredAddress}
                                  >
                                    <FormControl>
                                      <Textarea
                                        placeholder="Enter complete registered office address"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 min-h-[80px]"
                                        rows={3}
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                  <div className="mt-2">
                                    <DocumentUploadField
                                      fieldName="addressDocument"
                                      label="Address Proof"
                                      description="Upload registered office address proof"
                                    />
                                  </div>
                                </FormItem>
                              )}
                            />

                            {/* SPV Logo Upload */}
                            <div>
                              <DocumentUploadField
                                fieldName="spvLogo"
                                label="SPV Logo (Optional)"
                                description="Upload your SPV's logo"
                                accept=".jpg,.jpeg,.png,.svg"
                              />
                            </div>

                            {/* Description */}
                            <FormField
                              control={form.control}
                              name="description"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Description"
                                    description="Detailed description of the SPV and its activities"
                                  >
                                    <FormControl>
                                      <Textarea
                                        placeholder="Provide detailed description"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 min-h-[100px]"
                                        rows={4}
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Custom Fields for Basic Info */}
                        {customFields.filter(field => field.section === "BASIC_INFO").length > 0 && (
                          <>
                            <Separator />
                            <div>
                              <h3 className="text-base font-semibold text-gray-900 mb-4">Additional Information</h3>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                {customFields
                                  .filter(field => field.section === "BASIC_INFO")
                                  .map((customField) => (
                                    <div key={customField.id}>
                                      <FieldWithTooltip
                                        label={customField.label}
                                        description={customField.helpText || `Custom field: ${customField.label}`}
                                        required={customField.required}
                                      >
                                        {customField.type === "select" ? (
                                          <Select>
                                            <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                              <SelectValue placeholder={customField.placeholder || `Select ${customField.label}`} />
                                            </SelectTrigger>
                                            <SelectContent>
                                              {customField.options?.map((option) => (
                                                <SelectItem key={option} value={option}>
                                                  {option}
                                                </SelectItem>
                                              ))}
                                            </SelectContent>
                                          </Select>
                                        ) : customField.type === "textarea" ? (
                                          <Textarea
                                            placeholder={customField.placeholder}
                                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                          />
                                        ) : (
                                          <Input
                                            type={customField.type}
                                            placeholder={customField.placeholder}
                                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                          />
                                        )}
                                      </FieldWithTooltip>
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Ownership & Stakeholders Tab */}
                <TabsContent value="ownership" className="space-y-6">
                  <Card className="border-0 shadow-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <Users className="h-5 w-5 text-blue-600" />
                        Ownership & Stakeholders
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-8">
                      {/* Key Personnel */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Key Personnel</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Promoter Name */}
                            <FormField
                              control={form.control}
                              name="promoterName"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Promoter / Developer"
                                    description={fieldDescriptions.promoterName}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter promoter name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* Authorized Signatory */}
                            <FormField
                              control={form.control}
                              name="authorizedSignatory"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Authorized Signatory"
                                    description={fieldDescriptions.authorizedSignatory}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter authorized signatory name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* Registered Representative */}
                            <FormField
                              control={form.control}
                              name="registeredRepresentative"
                              render={({ field }) => (
                                <FormItem className="md:col-span-2">
                                  <FieldWithTooltip
                                    label="Registered Representative"
                                    description={fieldDescriptions.registeredRepresentative}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter registered representative name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Equity Holders Section */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-base font-semibold text-gray-900">Equity Holders</h3>
                          <Button type="button" variant="outline" size="sm" onClick={addEquityHolder}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Equity Holder
                          </Button>
                        </div>

                        {equityHolderFields.map((field, index) => (
                          <Card key={field.id} className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                              <FormField
                                control={form.control}
                                name={`equityHolders.${index}.name`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                      <Input placeholder="Equity holder name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`equityHolders.${index}.shareholding`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Shareholding %</FormLabel>
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="0"
                                        min="0"
                                        max="100"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeEquityHolder(index)}
                                className="h-10"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </Card>
                        ))}
                      </div>

                      <Separator />

                      {/* Board Members Section */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-base font-semibold text-gray-900">Board Members</h3>
                          <Button type="button" variant="outline" size="sm" onClick={addBoardMember}>
                            <Plus className="h-4 w-4 mr-2" />
                            Add Board Member
                          </Button>
                        </div>

                        {boardMemberFields.map((field, index) => (
                          <Card key={field.id} className="p-4">
                            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                              <FormField
                                control={form.control}
                                name={`boardMembers.${index}.name`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Name</FormLabel>
                                    <FormControl>
                                      <Input placeholder="Board member name" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`boardMembers.${index}.designation`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>Designation</FormLabel>
                                    <FormControl>
                                      <Input placeholder="Director, Chairman, etc." {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <FormField
                                control={form.control}
                                name={`boardMembers.${index}.din`}
                                render={({ field }) => (
                                  <FormItem>
                                    <FormLabel>DIN (Optional)</FormLabel>
                                    <FormControl>
                                      <Input placeholder="Director Identification Number" {...field} />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeBoardMember(index)}
                                className="h-10"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* Financial Details Tab */}
                <TabsContent value="financial" className="space-y-6">
                  <Card className="border-0 shadow-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2 text-lg">
                        <DollarSign className="h-5 w-5 text-blue-600" />
                        Financial Details
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-8">
                      {/* Capital Structure */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Capital Structure</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Authorized Capital */}
                            <FormField
                              control={form.control}
                              name="authorizedCapital"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Authorized Capital"
                                    description={fieldDescriptions.authorizedCapital}
                                  >
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="0"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* Paid-up Capital */}
                            <FormField
                              control={form.control}
                              name="paidUpCapital"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Paid-up Capital"
                                    description={fieldDescriptions.paidUpCapital}
                                  >
                                    <FormControl>
                                      <Input
                                        type="number"
                                        placeholder="0"
                                        {...field}
                                        onChange={(e) => field.onChange(parseFloat(e.target.value) || undefined)}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Bank Account Details */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Bank Account Details</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="bankAccountDetails.accountNumber"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Account Number"
                                    description={fieldDescriptions.accountNumber}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter account number"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="bankAccountDetails.bankName"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Bank Name"
                                    description={fieldDescriptions.bankName}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter bank name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="bankAccountDetails.ifscCode"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="IFSC Code"
                                    description={fieldDescriptions.ifscCode}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="ABCD0123456"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="bankAccountDetails.accountType"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Account Type"
                                    description={fieldDescriptions.accountType}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Current, Savings, etc."
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Funding & Investment Details */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Funding & Investment Details</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {/* Debt Provider */}
                            <FormField
                              control={form.control}
                              name="debtProvider"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Debt Provider"
                                    description={fieldDescriptions.debtProvider}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter debt provider name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* Debt-Equity Ratio */}
                            <FormField
                              control={form.control}
                              name="debtEquityRatio"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Debt-Equity Ratio"
                                    description={fieldDescriptions.debtEquityRatio}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="e.g., 70:30"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* Funding Source */}
                            <FormField
                              control={form.control}
                              name="fundingSource"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Funding Source"
                                    description={fieldDescriptions.fundingSource}
                                  >
                                    <Select onValueChange={field.onChange} value={field.value}>
                                      <FormControl>
                                        <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                          <SelectValue placeholder="Select funding source" />
                                        </SelectTrigger>
                                      </FormControl>
                                      <SelectContent>
                                        {fundingSourceOptions.map((option) => (
                                          <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            {/* PPA Signed With */}
                            <FormField
                              control={form.control}
                              name="ppaSignedWith"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="PPA Signed With"
                                    description={fieldDescriptions.ppaSignedWith}
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter PPA signatory"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>

                      <Separator />

                      {/* Admin Details */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-base font-semibold text-gray-900 mb-4">Administrator Details</h3>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <FormField
                              control={form.control}
                              name="adminName"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Admin Name"
                                    description="Name of the person who will be the SPV administrator"
                                  >
                                    <FormControl>
                                      <Input
                                        placeholder="Enter admin name"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="adminEmail"
                              render={({ field }) => (
                                <FormItem>
                                  <FieldWithTooltip
                                    label="Admin Email"
                                    description="Email address where admin credentials will be sent automatically"
                                  >
                                    <FormControl>
                                      <Input
                                        type="email"
                                        placeholder="<EMAIL>"
                                        {...field}
                                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                      />
                                    </FormControl>
                                  </FieldWithTooltip>
                                  <FormMessage className="text-red-600" />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>

                {/* Error Display */}
                {submitError && (
                  <Alert variant="destructive" className="mt-6">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="font-medium">
                      {submitError}
                    </AlertDescription>
                  </Alert>
                )}

                {/* Form Actions */}
                <div className="flex items-center justify-between pt-6 border-t border-gray-200 mt-8">
                  {/* Previous Button - Left Corner */}
                  <div>
                    {activeTab !== "basic" && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => {
                          if (activeTab === "ownership") setActiveTab("basic");
                          if (activeTab === "financial") setActiveTab("ownership");
                        }}
                        disabled={isSubmitting}
                        className="px-6"
                      >
                        Previous
                      </Button>
                    )}
                  </div>

                  {/* Next/Create Button - Right Corner */}
                  <div>
                    {activeTab !== "financial" ? (
                      <Button
                        type="button"
                        variant="default"
                        onClick={() => {
                          if (activeTab === "basic") setActiveTab("ownership");
                          if (activeTab === "ownership") setActiveTab("financial");
                        }}
                        disabled={isSubmitting}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8"
                      >
                        Next
                      </Button>
                    ) : (
                      <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="bg-blue-600 hover:bg-blue-700 text-white px-8"
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                            Creating SPV...
                          </>
                        ) : (
                          <>
                            <Building2 className="h-4 w-4 mr-2" />
                            Create SPV
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </form>
            </Form>
          </TooltipProvider>
        </div>

        {/* Custom Fields Manager */}
        <SPVCustomFieldsManager
          isOpen={isCustomFieldsOpen}
          onClose={() => setIsCustomFieldsOpen(false)}
          onSave={handleCustomFieldsSave}
          existingFields={customFields}
        />
      </DialogContent>
    </Dialog>
  );
}
