"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

const addUserSchema = z.object({
  spvId: z.string().min(1, "Please select an SPV"),
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  role: z.enum(["SPV_ADMIN", "PROJECT_MANAGER", "SITE_WORKER"], {
    required_error: "Role is required",
  }),
  jobTitle: z.string().optional(),
  phoneNumber: z.string().optional(),
  projectIds: z.array(z.string()).optional(),
});

type AddUserFormValues = z.infer<typeof addUserSchema>;

interface SPV {
  id: string;
  name: string;
  status: string;
  purpose?: string;
  _count: {
    spvUsers: number;
    projects: number;
  };
}

interface Project {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
}

interface AddUserToSPVFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function AddUserToSPVForm({
  isOpen,
  onClose,
  onSuccess,
}: AddUserToSPVFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [spvs, setSPVs] = useState<SPV[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loadingSPVs, setLoadingSPVs] = useState(false);
  const [loadingProjects, setLoadingProjects] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showProjectAssignment, setShowProjectAssignment] = useState(false);

  const form = useForm<AddUserFormValues>({
    resolver: zodResolver(addUserSchema),
    defaultValues: {
      spvId: "",
      name: "",
      email: "",
      role: undefined,
      jobTitle: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      projectIds: [],
    },
  });

  const selectedSpvId = form.watch("spvId");
  const selectedRole = form.watch("role");

  useEffect(() => {
    if (isOpen) {
      fetchSPVs();
      form.reset();
      setShowProjectAssignment(false);
    }
  }, [isOpen, form]);

  useEffect(() => {
    if (selectedSpvId) {
      fetchProjects(selectedSpvId);
    } else {
      setProjects([]);
    }
  }, [selectedSpvId]);

  const fetchSPVs = async () => {
    setLoadingSPVs(true);
    try {
      const response = await fetch("/api/organizations/spvs");
      if (response.ok) {
        const data = await response.json();
        setSPVs(data.spvs || []);
      } else {
        console.error("Failed to fetch SPVs");
        toast.error("Failed to load SPVs");
      }
    } catch (error) {
      console.error("Error fetching SPVs:", error);
      toast.error("Failed to load SPVs");
    } finally {
      setLoadingSPVs(false);
    }
  };

  const fetchProjects = async (spvId: string) => {
    setLoadingProjects(true);
    try {
      const response = await fetch(`/api/organizations/spvs/${spvId}/projects`);
      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
      } else {
        console.error("Failed to fetch projects");
        toast.error("Failed to load projects for this SPV");
      }
    } catch (error) {
      console.error("Error fetching projects:", error);
      toast.error("Failed to load projects");
    } finally {
      setLoadingProjects(false);
    }
  };

  const generatePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setValue("password", password);
    form.setValue("confirmPassword", password);
    toast.success("Password generated successfully");
  };

  const onSubmit = async (data: AddUserFormValues) => {
    try {
      setIsSubmitting(true);

      console.log("Submitting user data to admin endpoint:", {
        spvId: data.spvId,
        name: data.name,
        email: data.email,
        role: data.role,
        jobTitle: data.jobTitle,
        phoneNumber: data.phoneNumber,
        projectIds: data.projectIds,
      });

      // Use the appropriate endpoint based on user context
      // For platform admins, use admin endpoint; for org admins and SPV admins, use SPV endpoint
      const endpoint = "/api/admin/spv-users"; // Using admin endpoint for broader compatibility

      const response = await fetch(endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          spvId: data.spvId,
          name: data.name,
          email: data.email,
          role: data.role,
          jobTitle: data.jobTitle || undefined,
          phoneNumber: data.phoneNumber || undefined,
          password: data.password,
          // Only send fields that the API expects - no confirmPassword or projectIds
        }),
      });

      console.log("Response status:", response.status);

      if (response.ok) {
        const result = await response.json();
        console.log("Success result:", result);
        toast.success("User added to SPV successfully");
        onSuccess();
        handleClose();
      } else {
        const errorData = await response.json();
        console.error("Error response:", errorData);
        toast.error(errorData.error || "Failed to add user to SPV");
      }
    } catch (error) {
      console.error("Error adding user to SPV:", error);
      toast.error("Failed to add user to SPV");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      setShowProjectAssignment(false);
      setShowPassword(false);
      onClose();
    }
  };

  const selectedSPV = spvs.find(spv => spv.id === selectedSpvId);



  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add User to SPV</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* User Details */}
            <Card>
              <CardContent className="space-y-4 pt-6">
                {/* SPV Selection - Compact */}
                <FormField
                  control={form.control}
                  name="spvId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SPV *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={loadingSPVs}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={loadingSPVs ? "Loading SPVs..." : "Select an SPV"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {spvs.map((spv) => (
                            <SelectItem key={spv.id} value={spv.id}>
                              {spv.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name *</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter full name" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address *</FormLabel>
                          <FormControl>
                            <Input type="email" placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role *</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="SPV_ADMIN">SPV Admin</SelectItem>
                            <SelectItem value="PROJECT_MANAGER">Project Manager</SelectItem>
                            <SelectItem value="SITE_WORKER">Site Worker</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="jobTitle"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Job Title</FormLabel>
                          <FormControl>
                            <Input placeholder="e.g., Senior Project Manager" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <Input placeholder="+****************" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Password Section */}
                  <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium">Login Credentials</h4>
                      <Button type="button" variant="outline" size="sm" onClick={generatePassword}>
                        Generate Password
                      </Button>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="password"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Password *</FormLabel>
                            <div className="relative">
                              <FormControl>
                                <Input
                                  type={showPassword ? "text" : "password"}
                                  placeholder="Enter password"
                                  {...field}
                                />
                              </FormControl>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                onClick={() => setShowPassword(!showPassword)}
                              >
                                {showPassword ? (
                                  <EyeOff className="h-4 w-4" />
                                ) : (
                                  <Eye className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Confirm Password *</FormLabel>
                            <FormControl>
                              <Input
                                type={showPassword ? "text" : "password"}
                                placeholder="Confirm password"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>

                  {/* Project Assignment Toggle */}
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="project-assignment"
                      checked={showProjectAssignment}
                      onCheckedChange={setShowProjectAssignment}
                    />
                    <label
                      htmlFor="project-assignment"
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      Assign to specific projects (optional)
                    </label>
                  </div>

                  {/* Project Assignment */}
                  {showProjectAssignment && (
                    <Card>
                      <CardContent className="pt-6">
                        {loadingProjects ? (
                          <div className="flex items-center justify-center py-4">
                            <Loader2 className="h-6 w-6 animate-spin" />
                          </div>
                        ) : projects.length > 0 ? (
                          <FormField
                            control={form.control}
                            name="projectIds"
                            render={() => (
                              <FormItem>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-40 overflow-y-auto">
                                  {projects.map((project) => (
                                    <FormField
                                      key={project.id}
                                      control={form.control}
                                      name="projectIds"
                                      render={({ field }) => {
                                        return (
                                          <FormItem
                                            key={project.id}
                                            className="flex flex-row items-start space-x-3 space-y-0 p-3 border rounded-lg hover:bg-gray-50"
                                          >
                                            <FormControl>
                                              <Checkbox
                                                checked={field.value?.includes(project.id)}
                                                onCheckedChange={(checked) => {
                                                  return checked
                                                    ? field.onChange([...field.value, project.id])
                                                    : field.onChange(
                                                        field.value?.filter(
                                                          (value) => value !== project.id
                                                        )
                                                      )
                                                }}
                                              />
                                            </FormControl>
                                            <div className="space-y-1 leading-none flex-1">
                                              <FormLabel className="text-sm font-medium cursor-pointer">
                                                {project.name}
                                              </FormLabel>
                                              {project.description && (
                                                <p className="text-xs text-muted-foreground">
                                                  {project.description}
                                                </p>
                                              )}
                                              <div className="flex gap-1 mt-1">
                                                <Badge variant="outline" className="text-xs">
                                                  {project.type}
                                                </Badge>
                                                <Badge variant="outline" className="text-xs">
                                                  {project.status}
                                                </Badge>
                                              </div>
                                            </div>
                                          </FormItem>
                                        )
                                      }}
                                    />
                                  ))}
                                </div>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        ) : (
                          <div className="text-center py-4 text-muted-foreground">
                            No projects available for this SPV
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  )}
                </CardContent>
              </Card>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Adding User...
                  </>
                ) : (
                  "Add User to SPV"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
