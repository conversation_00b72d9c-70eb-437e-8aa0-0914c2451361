"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { DataEntryEditModal } from "@/components/spv/data-entry-edit-modal";
import { Loader2, CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";

const verificationActionSchema = z.object({
  action: z.string().min(1, "Action is required"),
  notes: z.string().max(1000, "Notes must be less than 1000 characters").optional(),
  rejectionReason: z.string().max(500, "Rejection reason must be less than 500 characters").optional(),
}).refine((data) => {
  // If it's a rejection action, rejection reason is required
  if (data.action?.includes("REJECT") || data.action === "REJECT") {
    return data.rejectionReason && data.rejectionReason.trim().length > 0;
  }
  return true;
}, {
  message: "Rejection reason is required for rejection actions",
  path: ["rejectionReason"],
});

type VerificationActionFormValues = z.infer<typeof verificationActionSchema>;

interface UnitLogWithVerification {
  id: string;
  logDate: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  verificationStatus: string;
  verificationNotes?: string;
  createdAt: string;
  logger: {
    id: string;
    name: string;
    email: string;
  };
  verifier?: {
    id: string;
    name: string;
    email: string;
  };
  project: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  verificationLogs: {
    id: string;
    fromStatus: string;
    toStatus: string;
    verificationNotes?: string;
    createdAt: string;
    verifier: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
  }[];
}

interface VerificationActionModalProps {
  isOpen: boolean;
  onClose: () => void;
  unitLog: UnitLogWithVerification | null;
  userRole: string;
  availableActions: string[];
  onAction: (action: string, notes?: string, rejectionReason?: string) => Promise<void>;
}

export function VerificationActionModal({
  isOpen,
  onClose,
  unitLog,
  userRole,
  availableActions,
  onAction,
}: VerificationActionModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  const form = useForm<VerificationActionFormValues>({
    resolver: zodResolver(verificationActionSchema),
    defaultValues: {
      action: "",
      notes: "",
      rejectionReason: "",
    },
  });

  const selectedAction = form.watch("action");
  const isRejectionAction = selectedAction?.includes("REJECT") || selectedAction === "REJECT";

  const onSubmit = async (data: VerificationActionFormValues) => {
    try {
      setIsSubmitting(true);

      // Handle edit action differently - open edit modal instead of performing action
      if (data.action === "EDIT_AND_RESUBMIT") {
        setIsEditModalOpen(true);
        setIsSubmitting(false);
        return;
      }

      await onAction(data.action, data.notes, data.rejectionReason);
      form.reset();
    } catch (error) {
      console.error("Submit error:", error);
      // Don't close modal on error so user can see the error and retry
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      form.reset();
    }
  };

  const getActionLabel = (action: string) => {
    switch (action) {
      case "SUBMIT_FOR_VERIFICATION":
        return "Submit for Verification";
      case "PM_VERIFY":
        return "Verify";
      case "PM_REJECT":
        return "Reject";
      case "SPV_ADMIN_VERIFY":
        return "Verify";
      case "SPV_ADMIN_REJECT":
        return "Reject";
      case "SPV_ADMIN_VERIFY_AND_SUBMIT":
        return "Verify and Submit to Org Admin";
      case "VERIFY":
        return "Verify";
      case "REJECT":
        return "Reject";
      case "SPV_APPROVE":
        return "Verify";
      case "SPV_REJECT":
        return "Reject";
      case "SUBMIT_TO_ORG_ADMIN":
        return "Submit to Org Admin";
      case "SEND_BACK_FOR_CORRECTION":
        return "Send Back for Correction";
      case "EDIT_AND_RESUBMIT":
        return "Edit and Resubmit";
      default:
        return action.replace(/_/g, ' ');
    }
  };

  const getActionDescription = (action: string) => {
    switch (action) {
      case "SUBMIT_FOR_VERIFICATION":
        return "Submit this entry for verification by a project manager";
      case "PM_VERIFY":
        return "Verify this entry as a project manager and move to SPV admin review";
      case "PM_REJECT":
        return "Reject this entry and send back to site worker for revision";
      case "SPV_ADMIN_VERIFY":
        return "Verify this entry as SPV admin and mark as approved";
      case "SPV_ADMIN_REJECT":
        return "Reject this entry and send back to project manager for review";
      case "SPV_ADMIN_VERIFY_AND_SUBMIT":
        return "Verify this entry as SPV admin and submit directly to organization admin for final approval";
      case "SPV_ADMIN_VERIFY_AND_SUBMIT":
        return "Verify this entry as SPV admin and submit directly to organization admin for final approval";
      case "VERIFY":
        return "Mark this entry as verified and approved";
      case "REJECT":
        return "Reject this entry and send back for revision";
      case "SPV_APPROVE":
        return "Approve this verified entry at SPV level";
      case "SPV_REJECT":
        return "Reject this entry at SPV level";
      case "SUBMIT_TO_ORG_ADMIN":
        return "Submit this verified entry to organization admin for final approval";
      case "SEND_BACK_FOR_CORRECTION":
        return "Send this rejected entry back to the original data logger for corrections";
      case "EDIT_AND_RESUBMIT":
        return "Edit the data yourself and resubmit to organization admin";
      default:
        return "";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'SUBMITTED_FOR_VERIFICATION':
        return 'bg-yellow-100 text-yellow-800';
      case 'VERIFIED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'SPV_APPROVED':
        return 'bg-blue-100 text-blue-800';
      case 'SPV_REJECTED':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'VERIFIED':
      case 'SPV_APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REJECTED':
      case 'SPV_REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'SUBMITTED_FOR_VERIFICATION':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  if (!unitLog) return null;

  return (
    <>
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Review Data Entry</DialogTitle>
          <DialogDescription>
            Review and take action on this data entry
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Entry Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Entry Details</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Project</label>
                <div className="text-sm">{unitLog.project.name}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Date</label>
                <div className="text-sm">{new Date(unitLog.logDate).toLocaleDateString()}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Unit Type</label>
                <div className="text-sm">{unitLog.unitType}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Quantity</label>
                <div className="text-sm">{unitLog.quantity}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Data Source</label>
                <div className="text-sm">{unitLog.dataSource}</div>
              </div>
              <div>
                <label className="text-sm font-medium text-muted-foreground">Current Status</label>
                <div className="flex items-center gap-2">
                  {getStatusIcon(unitLog.verificationStatus)}
                  <Badge className={getStatusColor(unitLog.verificationStatus)}>
                    {unitLog.verificationStatus.replace('_', ' ')}
                  </Badge>
                </div>
              </div>
            </div>

            <div>
              <label className="text-sm font-medium text-muted-foreground">Logged By</label>
              <div className="text-sm">
                {unitLog.logger.name} ({unitLog.logger.email})
              </div>
            </div>

            {unitLog.verificationNotes && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Current Notes</label>
                <div className="text-sm bg-muted p-2 rounded">{unitLog.verificationNotes}</div>
              </div>
            )}
          </div>

          {/* Verification History */}
          {unitLog.verificationLogs.length > 0 && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Verification History</h3>
              <div className="space-y-2">
                {unitLog.verificationLogs.map((log) => (
                  <div key={log.id} className="border rounded p-3">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">
                          {log.fromStatus} → {log.toStatus}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          by {log.verifier.name}
                        </span>
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {new Date(log.createdAt).toLocaleString()}
                      </span>
                    </div>
                    {log.verificationNotes && (
                      <div className="text-sm text-muted-foreground">
                        {log.verificationNotes}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          <Separator />

          {/* Action Form */}
          {availableActions.length > 0 ? (
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="action"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Action</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select an action" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableActions.map((action) => (
                            <SelectItem key={action} value={action}>
                              {getActionLabel(action)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {form.watch("action") && getActionDescription(form.watch("action"))}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Add any notes or comments..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide additional context or feedback for this action
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {isRejectionAction && (
                  <FormField
                    control={form.control}
                    name="rejectionReason"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Rejection Reason *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Please provide a clear reason for rejection..."
                            className="min-h-[80px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Explain why this entry is being rejected so the submitter can make necessary corrections
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSubmitting || !form.watch("action")}>
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Perform Action
                  </Button>
                </div>
              </form>
            </Form>
          ) : (
            <div className="text-center py-6">
              <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Actions Available</h3>
              <p className="text-muted-foreground">
                No verification actions are available for this entry at this time.
              </p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>

    {/* Data Entry Edit Modal */}
    {unitLog && (
      <DataEntryEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        unitLogId={unitLog.id}
        onSuccess={() => {
          setIsEditModalOpen(false);
          onClose(); // Close the verification modal too
          // Refresh the verification queue
          window.location.reload();
        }}
      />
    )}
  </>
  );
}
