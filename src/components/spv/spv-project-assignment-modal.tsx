"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Loader2, FolderOpen, Search } from "lucide-react";
import { toast } from "sonner";

const spvProjectAssignmentSchema = z.object({
  projectIds: z.array(z.string()).min(1, "Please select at least one project"),
});

type SPVProjectAssignmentFormValues = z.infer<typeof spvProjectAssignmentSchema>;

interface Project {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  location?: string;
  country?: string;
  spvId?: string | null;
  createdAt: string;
  spv?: {
    id: string;
    name: string;
  } | null;
}

interface ProjectData {
  availableProjects: Project[];
}

interface SPVProjectAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  spvId: string;
  spvName: string;
}

export function SPVProjectAssignmentModal({
  isOpen,
  onClose,
  onSuccess,
  spvId,
  spvName,
}: SPVProjectAssignmentModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [projectData, setProjectData] = useState<ProjectData | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const form = useForm<SPVProjectAssignmentFormValues>({
    resolver: zodResolver(spvProjectAssignmentSchema),
    defaultValues: {
      projectIds: [],
    },
  });

  useEffect(() => {
    if (isOpen) {
      fetchProjectData();
      form.reset();
    }
  }, [isOpen, form]);

  const fetchProjectData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/organizations/spvs/${spvId}/assign-projects`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch project data");
      }
      
      const data = await response.json();
      setProjectData({ availableProjects: data.availableProjects });
    } catch (error) {
      console.error("Error fetching project data:", error);
      toast.error("Failed to load project data");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: SPVProjectAssignmentFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/organizations/spvs/${spvId}/assign-projects`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          projectIds: data.projectIds,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(result.message || "Projects assigned successfully");
        onSuccess();
        onClose();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to assign projects");
      }
    } catch (error) {
      console.error("Error assigning projects:", error);
      toast.error("Failed to assign projects");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter available projects based on search
  const filteredAvailableProjects = projectData?.availableProjects.filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.location?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  }) || [];

  const selectedProjectIds = form.watch("projectIds");

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Assign Projects to {spvName}
          </DialogTitle>
          <DialogDescription>
            Select projects from your organization to assign to this SPV.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : projectData ? (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search projects..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Available Projects Selection */}
              <FormField
                control={form.control}
                name="projectIds"
                render={() => (
                  <FormItem>
                    <FormLabel className="text-base">
                      Available Projects ({filteredAvailableProjects.length})
                    </FormLabel>
                    <FormControl>
                      <div className="max-h-80 overflow-y-auto border rounded-md p-3">
                        <div className="space-y-2">
                          {filteredAvailableProjects.map((project) => (
                            <FormField
                              key={project.id}
                              control={form.control}
                              name="projectIds"
                              render={({ field }) => (
                                <FormItem
                                  key={project.id}
                                  className="flex flex-row items-center space-x-3 space-y-0 p-2 hover:bg-muted/50 rounded-md"
                                >
                                  <FormControl>
                                    <Checkbox
                                      id={`project-${project.id}`}
                                      checked={field.value?.includes(project.id)}
                                      onCheckedChange={(checked) => {
                                        return checked
                                          ? field.onChange([...field.value, project.id])
                                          : field.onChange(
                                              field.value?.filter(
                                                (value) => value !== project.id
                                              )
                                            );
                                      }}
                                      className="!h-4 !w-4 !size-4"
                                      style={{ width: '16px', height: '16px' }}
                                    />
                                  </FormControl>
                                  <FormLabel
                                    htmlFor={`project-${project.id}`}
                                    className="text-sm font-normal cursor-pointer flex-1"
                                  >
                                    {project.name}
                                  </FormLabel>
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              
                <div className="flex justify-end gap-3 pt-6 border-t">
                  <Button type="button" variant="outline" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSubmitting || selectedProjectIds.length === 0}
                  >
                    {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    Assign {selectedProjectIds.length} Project
                  </Button>
                </div>
              
            </form>
          </Form>
        ) : filteredAvailableProjects.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-sm text-muted-foreground">
              {searchTerm ? "No projects found matching your search." : "No available projects to assign."}
            </p>
          </div>
        ) : null}
      </DialogContent>
    </Dialog>
  );
}
