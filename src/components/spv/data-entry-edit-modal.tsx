"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, DialogDescription, Di<PERSON>Footer, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, Save, X } from "lucide-react";
import { toast } from "sonner";

interface DataEntryEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  unitLogId: string;
  onSuccess: () => void;
}

interface UnitLogData {
  id: string;
  logDate: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  location?: string;
  notes?: string;
  rejectionReason?: string;
  project: {
    id: string;
    name: string;
    type: string;
  };
  logger: {
    id: string;
    name: string;
    email: string;
  };
}

export function DataEntryEditModal({
  isOpen,
  onClose,
  unitLogId,
  onSuccess,
}: DataEntryEditModalProps) {
  const [unitLog, setUnitLog] = useState<UnitLogData | null>(null);
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Form data
  const [formData, setFormData] = useState({
    logDate: "",
    unitType: "",
    quantity: 0,
    dataSource: "",
    location: "",
    notes: "",
  });

  // Fetch unit log data when modal opens
  useEffect(() => {
    if (isOpen && unitLogId) {
      fetchUnitLogData();
    }
  }, [isOpen, unitLogId]);

  const fetchUnitLogData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/spv/data-entry/${unitLogId}/edit`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch data entry");
      }

      const data = await response.json();
      setUnitLog(data.data);
      
      // Initialize form with current data
      setFormData({
        logDate: new Date(data.data.logDate).toISOString().split('T')[0],
        unitType: data.data.unitType,
        quantity: data.data.quantity,
        dataSource: data.data.dataSource,
        location: data.data.location || "",
        notes: data.data.notes || "",
      });
    } catch (error) {
      console.error("Error fetching unit log data:", error);
      toast.error("Failed to load data entry");
      onClose();
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSubmitting(true);
      
      const response = await fetch(`/api/spv/data-entry/${unitLogId}/edit`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update data entry");
      }

      toast.success("Data entry updated and resubmitted successfully");
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error updating data entry:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update data entry");
    } finally {
      setSubmitting(false);
    }
  };

  const handleInputChange = (field: string, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Loading Data Entry</DialogTitle>
            <DialogDescription>
              Please wait while we load the data entry for editing...
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Data Entry</DialogTitle>
          <DialogDescription>
            Edit the data entry and resubmit to organization admin
          </DialogDescription>
        </DialogHeader>

        {unitLog && (
          <div className="space-y-6">
            {/* Rejection Information */}
            {unitLog.rejectionReason && (
              <Card className="border-red-200 bg-red-50">
                <CardHeader>
                  <CardTitle className="text-red-800 flex items-center gap-2">
                    <AlertCircle className="h-5 w-5" />
                    Rejection Reason
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-red-700">{unitLog.rejectionReason}</p>
                </CardContent>
              </Card>
            )}

            {/* Original Entry Info */}
            <Card>
              <CardHeader>
                <CardTitle>Original Entry Information</CardTitle>
                <CardDescription>
                  Project: {unitLog.project.name} • Logger: {unitLog.logger.name}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Project Type:</span> {unitLog.project.type}
                  </div>
                  <div>
                    <span className="font-medium">Original Logger:</span> {unitLog.logger.email}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Edit Form */}
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="logDate">Log Date</Label>
                  <Input
                    id="logDate"
                    type="date"
                    value={formData.logDate}
                    onChange={(e) => handleInputChange("logDate", e.target.value)}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="unitType">Unit Type</Label>
                  <Select
                    value={formData.unitType}
                    onValueChange={(value) => handleInputChange("unitType", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select unit type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="RENEWABLE_ENERGY">Renewable Energy (MW)</SelectItem>
                      <SelectItem value="CARBON_OFFSET">Carbon Offset (tCO2e)</SelectItem>
                      <SelectItem value="ENERGY_EFFICIENCY">Energy Efficiency (MWh)</SelectItem>
                      <SelectItem value="FORESTRY">Forestry (hectares)</SelectItem>
                      <SelectItem value="WASTE_MANAGEMENT">Waste Management (tonnes)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">Quantity</Label>
                  <Input
                    id="quantity"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.quantity}
                    onChange={(e) => handleInputChange("quantity", parseFloat(e.target.value) || 0)}
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dataSource">Data Source</Label>
                  <Select
                    value={formData.dataSource}
                    onValueChange={(value) => handleInputChange("dataSource", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select data source" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="MANUAL_ENTRY">Manual Entry</SelectItem>
                      <SelectItem value="IOT_SENSOR">IoT Sensor</SelectItem>
                      <SelectItem value="CSV_IMPORT">CSV Import</SelectItem>
                      <SelectItem value="API_INTEGRATION">API Integration</SelectItem>
                      <SelectItem value="THIRD_PARTY">Third Party</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="location">Location (Optional)</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => handleInputChange("location", e.target.value)}
                  placeholder="Enter location details"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  placeholder="Add any additional notes or corrections"
                  rows={3}
                />
              </div>

              <DialogFooter className="gap-2">
                <Button type="button" variant="outline" onClick={onClose}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button type="submit" disabled={submitting}>
                  <Save className="h-4 w-4 mr-2" />
                  {submitting ? "Updating..." : "Update & Resubmit"}
                </Button>
              </DialogFooter>
            </form>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
