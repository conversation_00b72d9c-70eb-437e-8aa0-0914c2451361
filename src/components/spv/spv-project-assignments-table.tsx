"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  MoreHorizontal, 
  Trash2, 
  User, 
  FolderO<PERSON>, 
  Calendar,
  Search,
  Loader2,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { format } from "date-fns";

interface ProjectAssignment {
  id: string;
  assignedAt: string;
  project: {
    id: string;
    name: string;
    type: string;
    status: string;
    location?: string;
  };
  spvUser: {
    id: string;
    role: string;
    user: {
      id: string;
      name: string;
      email: string;
      jobTitle?: string;
    };
  };
  assignedByUser: {
    id: string;
    name: string;
    email: string;
  };
}

interface SPVProjectAssignmentsTableProps {
  onRefresh?: () => void;
}

export function SPVProjectAssignmentsTable({ onRefresh }: SPVProjectAssignmentsTableProps) {
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [deleteAssignment, setDeleteAssignment] = useState<ProjectAssignment | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  useEffect(() => {
    fetchAssignments();
  }, []);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/spv/project-assignments?limit=100");
      if (response.ok) {
        const data = await response.json();
        setAssignments(data.data.assignments || []);
      } else {
        toast.error("Failed to fetch project assignments");
      }
    } catch (error) {
      toast.error("Error fetching project assignments");
      console.error("Assignments fetch error:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAssignment = async () => {
    if (!deleteAssignment) return;

    try {
      setIsDeleting(true);
      const response = await fetch(
        `/api/spv/project-assignments?assignmentId=${deleteAssignment.id}`,
        {
          method: "DELETE",
        }
      );

      if (response.ok) {
        toast.success("Project assignment removed successfully");
        fetchAssignments();
        onRefresh?.();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to remove assignment");
      }
    } catch (error) {
      toast.error("Error removing assignment");
      console.error("Delete assignment error:", error);
    } finally {
      setIsDeleting(false);
      setDeleteAssignment(null);
    }
  };

  const filteredAssignments = assignments.filter(assignment => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      assignment.project.name.toLowerCase().includes(searchLower) ||
      assignment.spvUser.user.name.toLowerCase().includes(searchLower) ||
      assignment.spvUser.user.email.toLowerCase().includes(searchLower) ||
      assignment.project.type.toLowerCase().includes(searchLower) ||
      assignment.project.status.toLowerCase().includes(searchLower)
    );
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case "PROJECT_MANAGER":
        return "bg-blue-100 text-blue-800";
      case "SITE_WORKER":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "on_hold":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading project assignments...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Project Assignments
          </CardTitle>
          <CardDescription>
            Manage project assignments for SPV team members
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search */}
          <div className="mb-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search assignments by project, user, or status..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {filteredAssignments.length === 0 ? (
            <div className="text-center py-8">
              <FolderOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">
                {assignments.length === 0 
                  ? "No project assignments found."
                  : "No assignments match your search criteria."
                }
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    <TableHead>Assigned User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned Date</TableHead>
                    <TableHead>Assigned By</TableHead>
                    <TableHead className="w-[50px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment.project.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {assignment.project.type}
                            {assignment.project.location && ` • ${assignment.project.location}`}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium">{assignment.spvUser.user.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {assignment.spvUser.user.email}
                          </p>
                          {assignment.spvUser.user.jobTitle && (
                            <p className="text-xs text-muted-foreground">
                              {assignment.spvUser.user.jobTitle}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRoleColor(assignment.spvUser.role)}>
                          {assignment.spvUser.role.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(assignment.project.status)}>
                          {assignment.project.status.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">
                            {format(new Date(assignment.assignedAt), "MMM dd, yyyy")}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="text-sm">{assignment.assignedByUser.name}</p>
                          <p className="text-xs text-muted-foreground">
                            {assignment.assignedByUser.email}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => setDeleteAssignment(assignment)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Remove Assignment
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteAssignment} onOpenChange={() => setDeleteAssignment(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-500" />
              Remove Project Assignment
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove the assignment of{" "}
              <strong>{deleteAssignment?.spvUser.user.name}</strong> from project{" "}
              <strong>{deleteAssignment?.project.name}</strong>?
              <br />
              <br />
              This action cannot be undone. The user will lose access to this project.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteAssignment}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Remove Assignment
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
