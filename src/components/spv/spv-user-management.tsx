"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Users, 
  Plus, 
  Mail, 
  Phone, 
  Calendar,
  MoreHorizontal,
  Edit,
  Trash2,
  UserPlus
} from "lucide-react";
import { toast } from "sonner";
import { SPVWithDetails } from "@/types/spv";
import { AddUserToSPVForm } from "@/components/spv/add-user-to-spv-form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface SPVUser {
  id: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phoneNumber?: string;
    jobTitle?: string;
  };
}

interface SPVUserManagementProps {
  spv: SPVWithDetails;
}

export function SPVUserManagement({ spv }: SPVUserManagementProps) {
  const [users, setUsers] = useState<SPVUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddUserOpen, setIsAddUserOpen] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, [spv.id]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/spv/${spv.id}/users`);
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      } else {
        toast.error("Failed to fetch SPV users");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to fetch SPV users");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserAdded = () => {
    fetchUsers();
    setIsAddUserOpen(false);
    toast.success("User added successfully! Credentials have been sent via email.");
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm("Are you sure you want to remove this user from the SPV?")) {
      return;
    }

    try {
      const response = await fetch(`/api/spv/${spv.id}/users/${userId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("User removed successfully");
        fetchUsers();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to remove user");
      }
    } catch (error) {
      console.error("Error removing user:", error);
      toast.error("Failed to remove user");
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "SPV_ADMIN":
        return "bg-red-100 text-red-800";
      case "PROJECT_MANAGER":
        return "bg-blue-100 text-blue-800";
      case "SITE_WORKER":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                SPV Users ({users.length})
              </CardTitle>
              <CardDescription>
                Manage users assigned to this SPV
              </CardDescription>
            </div>
            <Dialog open={isAddUserOpen} onOpenChange={setIsAddUserOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add User
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add User to {spv.name}</DialogTitle>
                </DialogHeader>
                <AddUserToSPVForm
                  spvId={spv.id}
                  onSuccess={handleUserAdded}
                  onCancel={() => setIsAddUserOpen(false)}
                />
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <div className="text-center py-8">
              <UserPlus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No users assigned</h3>
              <p className="text-muted-foreground mb-4">
                Add users to this SPV to get started
              </p>
              <Button onClick={() => setIsAddUserOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add First User
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {users.map((spvUser) => (
                <Card key={spvUser.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <Users className="h-5 w-5 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{spvUser.user.name}</h3>
                            <Badge className={getRoleColor(spvUser.role)}>
                              {spvUser.role.replace('_', ' ')}
                            </Badge>
                            <Badge 
                              variant={spvUser.isActive ? "default" : "secondary"}
                              className={spvUser.isActive ? "bg-green-100 text-green-800" : ""}
                            >
                              {spvUser.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                          
                          <div className="space-y-1 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              {spvUser.user.email}
                            </div>
                            {spvUser.user.phoneNumber && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4" />
                                {spvUser.user.phoneNumber}
                              </div>
                            )}
                            {spvUser.user.jobTitle && (
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                {spvUser.user.jobTitle}
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              Added {new Date(spvUser.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-destructive"
                            onClick={() => handleDeleteUser(spvUser.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Remove User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
