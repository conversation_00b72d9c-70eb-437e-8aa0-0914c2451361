"use client";

import { Building2, Calendar, MapPin, FileText, MoreHorizontal, Edit, Trash2, Eye, Shield, AlertCircle, Check, Clock, X, Upload, Users, DollarSign, Building } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { SPV, SPVWithOrganization, SPVVerificationStatus } from "@/types/spv";
import { spvUtils } from "@/lib/api/spv";

interface SPVCardProps {
  spv: SPV | SPVWithOrganization;
  onEdit?: (spv: SPV | SPVWithOrganization) => void;
  onDelete?: (spv: SPV | SPVWithOrganization) => void;
  onView?: (spv: SPV | SPVWithOrganization) => void;
  onCompleteVerification?: (spv: SPV | SPVWithOrganization) => void;
  onUploadDocuments?: (spv: SPV | SPVWithOrganization) => void;
  showOrganization?: boolean;
  showActions?: boolean;
  showEditOption?: boolean;
}

// Verification flow commented out for now

export function SPVCard({
  spv,
  onEdit,
  onDelete,
  onView,
  onCompleteVerification,
  onUploadDocuments,
  showOrganization = false,
  showActions = true,
  showEditOption = true,
}: SPVCardProps) {
  const isWithOrganization = 'organization' in spv;
  // Verification flow commented out for now
  // const verificationStyle = spv.verificationStatus ? getVerificationStatusStyle(spv.verificationStatus) : null;
  const verificationStyle = null;

  // Get SPV logo or first letter
  const getLogoFallback = (name: string) => {
    return name.charAt(0).toUpperCase();
  };

  return (
    <Card
      className={`hover:shadow-md transition-shadow ${onView ? 'cursor-pointer' : ''}`}
      onClick={onView ? () => onView(spv) : undefined}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex items-start gap-3 flex-1">
            {/* SPV Logo/Avatar */}
            <Avatar className="h-12 w-12">
              {spv.spvLogoUrl ? (
                <AvatarImage src={spv.spvLogoUrl} alt={`${spv.name} logo`} />
              ) : null}
              <AvatarFallback className="bg-blue-100 text-blue-700 font-semibold text-lg">
                {getLogoFallback(spv.name)}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg truncate">
                {spv.name}
              </CardTitle>
              <CardDescription className="mt-1">
                {(spv.spvType || spv.legalStructure) || "SPV"}
                {(spv.spvType || spv.legalStructure) && spv.jurisdiction && " • "}
                {spv.jurisdiction || ""}
              </CardDescription>
              {showOrganization && isWithOrganization && (
                <CardDescription className="mt-1 text-xs flex items-center gap-1">
                  <Building className="h-3 w-3" />
                  {spv.organization.name}
                </CardDescription>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2 flex-wrap">
            {/* Verification Status Badge - Commented out for now */}

            {/* SPV Status Badge */}
            <Badge
              variant={spv.status === "ACTIVE" ? "default" : "secondary"}
              className={
                spv.status === "ACTIVE" ? "bg-green-100 text-green-800" :
                spv.status === "PENDING" ? "bg-yellow-100 text-yellow-800" :
                spv.status === "INACTIVE" ? "bg-gray-100 text-gray-800" :
                "bg-red-100 text-red-800"
              }
            >
              {spvUtils.formatStatus(spv.status)}
            </Badge>

            {showActions && (
              <div className="flex items-center gap-2">
                {/* Quick Action Buttons - Verification flow commented out */}

                <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onView && (
                    <DropdownMenuItem onClick={() => onView(spv)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                  )}

                  {/* Verification Actions - Commented out for now */}

                  {onEdit && showEditOption && (
                    <DropdownMenuItem onClick={() => onEdit(spv)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit Basic Info
                    </DropdownMenuItem>
                  )}
                  {onDelete && (
                    <DropdownMenuItem
                      className="text-destructive"
                      onClick={() => onDelete(spv)}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
                </DropdownMenu>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        {/* Purpose */}
        {spv.purpose && (
          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
            {spv.purpose}
          </p>
        )}

        {/* Key Information Grid */}
        <div className="grid grid-cols-1 gap-3 text-sm">
          {/* Parent Company */}
          {spv.parentCompany && (
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="font-medium">Parent:</span>
              <span className="truncate">{spv.parentCompany}</span>
            </div>
          )}

          {/* Incorporation Date */}
          {(spv.dateOfIncorporation || spv.establishedDate) && (
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="font-medium">Incorporated:</span>
              <span>{spvUtils.formatDate(spv.dateOfIncorporation || spv.establishedDate)}</span>
            </div>
          )}

          {/* State of Incorporation */}
          {spv.stateOfIncorporation && (
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="font-medium">State:</span>
              <span className="truncate">{spv.stateOfIncorporation}</span>
            </div>
          )}

          {/* Registration Number */}
          {spv.registrationNumber && (
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="font-medium">Registration:</span>
              <span className="truncate">{spv.registrationNumber}</span>
            </div>
          )}

          {/* Promoter */}
          {spv.promoterName && (
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="font-medium">Promoter:</span>
              <span className="truncate">{spv.promoterName}</span>
            </div>
          )}

          {/* Capital Information */}
          {(spv.authorizedCapital || spv.paidUpCapital) && (
            <div className="flex items-center gap-2">
              <DollarSign className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="font-medium">Capital:</span>
              <span className="truncate">
                {spv.authorizedCapital ? `₹${spv.authorizedCapital.toLocaleString()}` : ""}
                {spv.authorizedCapital && spv.paidUpCapital ? " / " : ""}
                {spv.paidUpCapital ? `₹${spv.paidUpCapital.toLocaleString()} paid` : ""}
              </span>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            <span className="font-medium">Created:</span>
            <span>{spvUtils.formatDate(spv.createdAt)}</span>
          </div>
        </div>

        {/* Project Categories */}
        {spv.projectCategories && spv.projectCategories.length > 0 && (
          <div className="mt-3 pt-3 border-t">
            <div className="flex flex-wrap gap-1">
              {spv.projectCategories.slice(0, 3).map((category, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {category}
                </Badge>
              ))}
              {spv.projectCategories.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{spv.projectCategories.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}

          {/* Verification Progress and Contact Info - Commented out for now */}
      </CardContent>
    </Card>
  );
}

interface SPVCardGridProps {
  spvs: (SPV | SPVWithOrganization)[];
  onEdit?: (spv: SPV | SPVWithOrganization) => void;
  onDelete?: (spv: SPV | SPVWithOrganization) => void;
  onView?: (spv: SPV | SPVWithOrganization) => void;
  onCompleteVerification?: (spv: SPV | SPVWithOrganization) => void;
  onUploadDocuments?: (spv: SPV | SPVWithOrganization) => void;
  showOrganization?: boolean;
  showActions?: boolean;
  showEditOption?: boolean;
  isLoading?: boolean;
  emptyState?: {
    title: string;
    description: string;
    action?: React.ReactNode;
  };
}

export function SPVCardGrid({
  spvs,
  onEdit,
  onDelete,
  onView,
  onCompleteVerification,
  onUploadDocuments,
  showOrganization = false,
  showActions = true,
  showEditOption = true,
  isLoading = false,
  emptyState,
}: SPVCardGridProps) {
  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (spvs.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Building2 className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">
            {emptyState?.title || "No SPVs found"}
          </h3>
          <p className="text-muted-foreground text-center mb-4">
            {emptyState?.description || "No SPVs have been created yet"}
          </p>
          {emptyState?.action}
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {spvs.map((spv) => (
        <SPVCard
          key={spv.id}
          spv={spv}
          onEdit={onEdit}
          onDelete={onDelete}
          onView={onView}
          onCompleteVerification={onCompleteVerification}
          onUploadDocuments={onUploadDocuments}
          showOrganization={showOrganization}
          showActions={showActions}
          showEditOption={showEditOption}
        />
      ))}
    </div>
  );
}
