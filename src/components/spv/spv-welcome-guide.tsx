"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { 
  CheckCircle, 
  Circle, 
  ArrowRight, 
  BookOpen, 
  Users, 
  FileText, 
  Upload,
  ClipboardCheck,
  BarChart3,
  X
} from "lucide-react";
import Link from "next/link";

interface SPVUser {
  id: string;
  role: string;
  user: {
    name: string;
    email: string;
  };
  spv: {
    name: string;
  };
  projectAssignments: any[];
}

interface SPVWelcomeGuideProps {
  spvUser: SPVUser;
  onDismiss: () => void;
}

interface GuideStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href?: string;
  completed: boolean;
  roles: string[];
}

export function SPVWelcomeGuide({ spvUser, onDismiss }: SPVWelcomeGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);

  // Define role-specific onboarding steps
  const getStepsForRole = (role: string): GuideStep[] => {
    const baseSteps: GuideStep[] = [
      {
        id: "profile",
        title: "Review Your Profile",
        description: "Check your profile information and SPV assignment",
        icon: Users,
        href: "/spv/settings",
        completed: true, // Always completed since they're logged in
        roles: ["SITE_WORKER", "PROJECT_MANAGER", "SPV_ADMIN"],
      },
      {
        id: "projects",
        title: "View Assigned Projects",
        description: "See the projects you have access to and their current status",
        icon: FileText,
        href: "/spv/projects",
        completed: spvUser.projectAssignments.length > 0,
        roles: ["SITE_WORKER", "PROJECT_MANAGER", "SPV_ADMIN"],
      },
    ];

    const roleSpecificSteps: Record<string, GuideStep[]> = {
      SITE_WORKER: [
        {
          id: "data-entry",
          title: "Start Data Entry",
          description: "Learn how to enter monitoring data for your assigned projects",
          icon: Upload,
          href: "/spv/data-entry",
          completed: false,
          roles: ["SITE_WORKER"],
        },
      ],
      PROJECT_MANAGER: [
        {
          id: "data-entry",
          title: "Data Entry Overview",
          description: "Understand the data entry process and your verification responsibilities",
          icon: Upload,
          href: "/spv/data-entry",
          completed: false,
          roles: ["PROJECT_MANAGER"],
        },
        {
          id: "verification",
          title: "Verification Queue",
          description: "Review and verify data entries from site workers",
          icon: ClipboardCheck,
          href: "/spv/verification",
          completed: false,
          roles: ["PROJECT_MANAGER"],
        },
        {
          id: "analytics",
          title: "Project Analytics",
          description: "Monitor project progress and data quality metrics",
          icon: BarChart3,
          href: "/spv/analytics",
          completed: false,
          roles: ["PROJECT_MANAGER"],
        },
      ],
      SPV_ADMIN: [
        {
          id: "user-management",
          title: "Manage Team Members",
          description: "Add and manage SPV users and their project assignments",
          icon: Users,
          href: "/spv/users",
          completed: false,
          roles: ["SPV_ADMIN"],
        },
        {
          id: "verification",
          title: "Verification Oversight",
          description: "Review verified data and provide SPV-level approvals",
          icon: ClipboardCheck,
          href: "/spv/verification",
          completed: false,
          roles: ["SPV_ADMIN"],
        },
        {
          id: "analytics",
          title: "SPV Analytics",
          description: "Access comprehensive analytics and generate reports",
          icon: BarChart3,
          href: "/spv/analytics",
          completed: false,
          roles: ["SPV_ADMIN"],
        },
      ],
    };

    return [...baseSteps, ...(roleSpecificSteps[role] || [])];
  };

  const steps = getStepsForRole(spvUser.role);
  const completedSteps = steps.filter(step => step.completed).length;
  const progressPercentage = (completedSteps / steps.length) * 100;

  const getRoleDescription = (role: string) => {
    const descriptions = {
      SITE_WORKER: "You're responsible for entering monitoring data for assigned projects. Your entries will be reviewed by project managers.",
      PROJECT_MANAGER: "You manage project data quality by verifying site worker entries and overseeing data verification workflows.",
      SPV_ADMIN: "You have administrative access to manage SPV users, oversee all verification processes, and access comprehensive analytics.",
    };
    return descriptions[role as keyof typeof descriptions] || "Welcome to the SPV Portal!";
  };

  const getRoleBadgeColor = (role: string) => {
    const colors = {
      SITE_WORKER: "bg-blue-100 text-blue-800",
      PROJECT_MANAGER: "bg-green-100 text-green-800", 
      SPV_ADMIN: "bg-purple-100 text-purple-800",
    };
    return colors[role as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <Card className="mb-6 border-l-4 border-l-blue-500">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <BookOpen className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <CardTitle className="text-lg">Welcome to SPV Portal</CardTitle>
              <CardDescription>
                Hello {spvUser.user.name}! Let's get you started with {spvUser.spv.name}
              </CardDescription>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onDismiss}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Role Information */}
        <Alert>
          <AlertDescription className="flex items-center gap-2">
            <Badge className={getRoleBadgeColor(spvUser.role)}>
              {spvUser.role.replace('_', ' ')}
            </Badge>
            <span className="text-sm">{getRoleDescription(spvUser.role)}</span>
          </AlertDescription>
        </Alert>

        {/* Progress Overview */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="font-medium">Setup Progress</span>
            <span className="text-muted-foreground">{completedSteps} of {steps.length} completed</span>
          </div>
          <Progress value={progressPercentage} className="h-2" />
        </div>

        {/* Quick Start Steps */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Quick Start Guide</h4>
          <div className="grid gap-3">
            {steps.map((step, index) => (
              <div
                key={step.id}
                className={`flex items-center gap-3 p-3 rounded-lg border transition-colors ${
                  step.completed 
                    ? "bg-green-50 border-green-200" 
                    : index === currentStep 
                    ? "bg-blue-50 border-blue-200" 
                    : "bg-gray-50 border-gray-200"
                }`}
              >
                <div className="flex-shrink-0">
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5 text-green-600" />
                  ) : (
                    <Circle className="h-5 w-5 text-gray-400" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2">
                    <step.icon className="h-4 w-4 text-gray-600" />
                    <h5 className="font-medium text-sm">{step.title}</h5>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">{step.description}</p>
                </div>

                {step.href && (
                  <Link href={step.href}>
                    <Button variant="ghost" size="sm" className="flex-shrink-0">
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2 pt-4 border-t">
          <Link href="/spv/projects">
            <Button variant="outline" size="sm">
              <FileText className="h-4 w-4 mr-2" />
              View Projects
            </Button>
          </Link>
          
          {(spvUser.role === "SITE_WORKER" || spvUser.role === "PROJECT_MANAGER") && (
            <Link href="/spv/data-entry">
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Data Entry
              </Button>
            </Link>
          )}
          
          {(spvUser.role === "PROJECT_MANAGER" || spvUser.role === "SPV_ADMIN") && (
            <Link href="/spv/verification">
              <Button variant="outline" size="sm">
                <ClipboardCheck className="h-4 w-4 mr-2" />
                Verification
              </Button>
            </Link>
          )}
          
          {spvUser.role === "SPV_ADMIN" && (
            <Link href="/spv/users">
              <Button variant="outline" size="sm">
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </Button>
            </Link>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
