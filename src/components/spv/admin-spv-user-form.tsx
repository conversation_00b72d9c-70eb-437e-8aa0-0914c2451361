"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

// Validation schema for admin SPV user creation
const adminSPVUserCreationSchema = z.object({
  organizationId: z.string().min(1, "Organization is required"),
  spvId: z.string().min(1, "SPV is required"),
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required"),
  role: z.enum(["SPV_ADMIN", "PROJECT_MANAGER", "SITE_WORKER"], {
    required_error: "Role is required",
  }),
  jobTitle: z.string().optional(),
  phoneNumber: z.string().optional(),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

type AdminSPVUserFormValues = z.infer<typeof adminSPVUserCreationSchema>;

interface Organization {
  id: string;
  name: string;
}

interface SPV {
  id: string;
  name: string;
  organizationId: string;
}

interface AdminSPVUserFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: AdminSPVUserFormValues) => Promise<void>;
  organizations: Organization[];
  spvs: SPV[];
  isSubmitting?: boolean;
}

export function AdminSPVUserForm({
  isOpen,
  onClose,
  onSubmit,
  organizations,
  spvs,
  isSubmitting = false,
}: AdminSPVUserFormProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [selectedOrgId, setSelectedOrgId] = useState<string>("");

  const form = useForm<AdminSPVUserFormValues>({
    resolver: zodResolver(adminSPVUserCreationSchema),
    defaultValues: {
      organizationId: "",
      spvId: "",
      email: "",
      name: "",
      role: "SITE_WORKER",
      jobTitle: "",
      phoneNumber: "",
      password: "",
    },
  });

  // Filter SPVs based on selected organization
  const filteredSPVs = spvs.filter(spv => spv.organizationId === selectedOrgId);

  const handleOrganizationChange = (orgId: string) => {
    setSelectedOrgId(orgId);
    form.setValue("organizationId", orgId);
    form.setValue("spvId", ""); // Reset SPV selection when org changes
  };

  const handleSubmit = async (values: AdminSPVUserFormValues) => {
    try {
      await onSubmit(values);
      form.reset();
      setSelectedOrgId("");
      onClose();
    } catch (error) {
      // Error handling is done in the parent component
    }
  };

  const handleClose = () => {
    form.reset();
    setSelectedOrgId("");
    onClose();
  };

  const generatePassword = () => {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*";
    let password = "";
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    form.setValue("password", password);
    toast.success("Password generated successfully");
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create SPV User</DialogTitle>
          <DialogDescription>
            Create a new user and assign them to an SPV with appropriate role and permissions.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Organization Selection */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Organization & SPV Selection</CardTitle>
                <CardDescription>
                  Select the organization and SPV for this user
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="organizationId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Organization *</FormLabel>
                      <Select onValueChange={handleOrganizationChange} value={selectedOrgId}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select organization" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {organizations.map((org) => (
                            <SelectItem key={org.id} value={org.id}>
                              {org.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="spvId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SPV *</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value} disabled={!selectedOrgId}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={selectedOrgId ? "Select SPV" : "Select organization first"} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {filteredSPVs.map((spv) => (
                            <SelectItem key={spv.id} value={spv.id}>
                              {spv.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* User Information */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">User Information</CardTitle>
                <CardDescription>
                  Basic information about the user
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address *</FormLabel>
                        <FormControl>
                          <Input type="email" placeholder="Enter email address" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SPV Role *</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="SPV_ADMIN">SPV Admin</SelectItem>
                            <SelectItem value="PROJECT_MANAGER">Project Manager</SelectItem>
                            <SelectItem value="SITE_WORKER">Site Worker</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="jobTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Job Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter job title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Password */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Login Credentials</CardTitle>
                <CardDescription>
                  Set the initial password for this user
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password *</FormLabel>
                      <div className="flex space-x-2">
                        <div className="relative flex-1">
                          <FormControl>
                            <Input
                              type={showPassword ? "text" : "password"}
                              placeholder="Enter password"
                              {...field}
                            />
                          </FormControl>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <Button type="button" variant="outline" onClick={generatePassword}>
                          Generate
                        </Button>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t">
              <Button type="button" variant="outline" onClick={handleClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating User...
                  </>
                ) : (
                  "Create SPV User"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
