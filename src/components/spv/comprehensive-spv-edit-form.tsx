"use client";

import { useState, useCallback } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>rovider, TooltipTrigger } from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Separator } from "@/components/ui/separator";
import {
  Loader2,
  Save,
  Settings,
  Info,
  Building2,
  Shield,
  Building,
  Users,
  DollarSign,
  Plus,
  Trash2,
  Upload,
  HelpCircle,
  FileText,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { toast } from "sonner";
import { SPVWithDetails } from "@/types/spv";
import { comprehensiveSPVCreationSchema } from "@/lib/validation/schemas";

// Comprehensive SPV edit schema matching the creation form structure
const comprehensiveSPVEditSchema = comprehensiveSPVCreationSchema.extend({
  // All fields are optional for editing except name
  name: z.string().min(3, "SPV name must be at least 3 characters").max(100, "SPV name must be less than 100 characters"),
}).partial().extend({
  // Keep name as required
  name: z.string().min(3, "SPV name must be at least 3 characters").max(100, "SPV name must be less than 100 characters"),
});

type SPVEditFormValues = z.infer<typeof comprehensiveSPVEditSchema>;

// Comprehensive field descriptions for info tooltips
const fieldDescriptions = {
  // Basic Information
  name: "The official name of your Special Purpose Vehicle",
  parentCompany: "Name of the parent organization or holding company",
  spvType: "Legal structure type of the SPV (Private Limited, LLP, Trust, Partnership)",
  dateOfIncorporation: "Date when the company was officially incorporated",
  cinNumber: "Corporate Identity Number assigned by MCA (21 characters)",
  registeredAddress: "Official registered address as per incorporation documents",
  stateOfIncorporation: "State where the SPV was incorporated",
  panNumber: "Permanent Account Number for tax purposes (10 characters)",
  gstNumber: "Goods and Services Tax identification number (15 digits)",
  spvLogoUrl: "Upload SPV logo for branding and identification",
  purpose: "Brief description of the SPV's business purpose and objectives",
  description: "Detailed description of the SPV's activities and scope",

  // Ownership & Stakeholders
  promoterName: "Name of the main promoter or founder of the SPV",
  equityHolders: "List of equity shareholders and their shareholding percentages",
  boardMembers: "Board of directors and their designations",
  authorizedSignatory: "Person authorized to sign documents on behalf of the SPV",
  registeredRepresentative: "Official representative for regulatory communications",

  // Financial Details
  authorizedCapital: "Maximum capital that the company is authorized to raise",
  paidUpCapital: "Actual capital paid by shareholders",
  bankAccountDetails: "Primary bank account details for the SPV",
  debtProvider: "Financial institutions providing debt funding",
  debtEquityRatio: "Ratio of debt to equity financing",
  fundingSource: "Sources of funding for the SPV",
  ppaSignedWith: "Power Purchase Agreement signatory details",

  // Admin Details
  adminName: "Name of the SPV administrator",
  adminEmail: "Email address for the SPV administrator account"
};

// SPV Type options
const spvTypeOptions = [
  { value: "PRIVATE_LIMITED", label: "Private Limited Company" },
  { value: "LLP", label: "Limited Liability Partnership (LLP)" },
  { value: "TRUST", label: "Trust" },
  { value: "PARTNERSHIP", label: "Partnership Firm" },
];

// State options for incorporation
const stateOptions = [
  { value: "andhra_pradesh", label: "Andhra Pradesh" },
  { value: "arunachal_pradesh", label: "Arunachal Pradesh" },
  { value: "assam", label: "Assam" },
  { value: "bihar", label: "Bihar" },
  { value: "chhattisgarh", label: "Chhattisgarh" },
  { value: "goa", label: "Goa" },
  { value: "gujarat", label: "Gujarat" },
  { value: "haryana", label: "Haryana" },
  { value: "himachal_pradesh", label: "Himachal Pradesh" },
  { value: "jharkhand", label: "Jharkhand" },
  { value: "karnataka", label: "Karnataka" },
  { value: "kerala", label: "Kerala" },
  { value: "madhya_pradesh", label: "Madhya Pradesh" },
  { value: "maharashtra", label: "Maharashtra" },
  { value: "manipur", label: "Manipur" },
  { value: "meghalaya", label: "Meghalaya" },
  { value: "mizoram", label: "Mizoram" },
  { value: "nagaland", label: "Nagaland" },
  { value: "odisha", label: "Odisha" },
  { value: "punjab", label: "Punjab" },
  { value: "rajasthan", label: "Rajasthan" },
  { value: "sikkim", label: "Sikkim" },
  { value: "tamil_nadu", label: "Tamil Nadu" },
  { value: "telangana", label: "Telangana" },
  { value: "tripura", label: "Tripura" },
  { value: "uttar_pradesh", label: "Uttar Pradesh" },
  { value: "uttarakhand", label: "Uttarakhand" },
  { value: "west_bengal", label: "West Bengal" },
  { value: "delhi", label: "Delhi" },
  { value: "chandigarh", label: "Chandigarh" },
  { value: "puducherry", label: "Puducherry" },
];

// Funding source options
const fundingSourceOptions = [
  { value: "EQUITY", label: "Equity Funding" },
  { value: "DEBT", label: "Debt Financing" },
  { value: "MIXED", label: "Mixed (Debt + Equity)" },
  { value: "GRANT", label: "Government Grants" },
  { value: "OTHER", label: "Other" },
];

// Field with tooltip component
const FieldWithTooltip = ({
  label,
  description,
  required = false,
  children,
}: {
  label: string;
  description: string;
  required?: boolean;
  children: React.ReactNode;
}) => (
  <div className="space-y-2">
    <div className="flex items-center gap-1">
      <FormLabel className="text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </FormLabel>
      <Tooltip>
        <TooltipTrigger asChild>
          <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
        </TooltipTrigger>
        <TooltipContent className="max-w-xs !bg-white !border !border-gray-200 !text-gray-900 shadow-lg">
          <p className="text-sm">{description}</p>
        </TooltipContent>
      </Tooltip>
    </div>
    {children}
  </div>
);

interface SPVEditFormProps {
  spv: SPVWithDetails;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

// Document upload tracking interface
interface DocumentUpload {
  fieldName: string;
  file: File;
  uploading: boolean;
  url?: string;
  error?: string;
}

export function ComprehensiveSPVEditForm({ spv, isOpen, onClose, onSuccess }: SPVEditFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("basic");
  const [documentUploads, setDocumentUploads] = useState<DocumentUpload[]>([]);

  const form = useForm<SPVEditFormValues>({
    resolver: zodResolver(comprehensiveSPVEditSchema),
    mode: "onSubmit",
    defaultValues: {
      // Basic Information
      name: spv.name || "",
      parentCompany: spv.parentCompany || undefined,
      spvType: (spv.spvType as "PRIVATE_LIMITED" | "LLP" | "TRUST" | "PARTNERSHIP") || undefined,
      dateOfIncorporation: spv.dateOfIncorporation ? spv.dateOfIncorporation.split('T')[0] : undefined,
      cinNumber: spv.cinNumber || undefined,
      registeredAddress: spv.registeredAddress || undefined,
      stateOfIncorporation: spv.stateOfIncorporation || undefined,
      panNumber: spv.panNumber || undefined,
      gstNumber: spv.gstNumber || undefined,
      spvLogoUrl: spv.spvLogoUrl || undefined,

      description: spv.description || undefined,

      // Ownership & Stakeholders
      promoterName: spv.promoterName || undefined,
      equityHolders: spv.equityHolders || [],
      boardMembers: spv.boardMembers || [],
      authorizedSignatory: spv.authorizedSignatory || undefined,
      registeredRepresentative: spv.registeredRepresentative || undefined,

      // Financial Details
      authorizedCapital: spv.authorizedCapital ? Number(spv.authorizedCapital) : undefined,
      paidUpCapital: spv.paidUpCapital ? Number(spv.paidUpCapital) : undefined,
      bankAccountDetails: spv.bankAccountDetails || {
        accountNumber: "",
        bankName: "",
        ifscCode: "",
        accountType: "",
      },
      debtProvider: spv.debtProvider || undefined,
      debtEquityRatio: spv.debtEquityRatio || undefined,
      fundingSource: (spv.fundingSource as "EQUITY" | "DEBT" | "GRANT" | "MIXED" | "OTHER") || undefined,
      ppaSignedWith: spv.ppaSignedWith || undefined,

      // Admin Details
      adminName: undefined, // Not stored in SPV model, would need to get from SPV admin user
      adminEmail: undefined, // Not stored in SPV model, would need to get from SPV admin user
    },
  });

  // Field arrays for dynamic sections
  const {
    fields: equityHolderFields,
    append: appendEquityHolder,
    remove: removeEquityHolder,
  } = useFieldArray({
    control: form.control,
    name: "equityHolders",
  });

  const {
    fields: boardMemberFields,
    append: appendBoardMember,
    remove: removeBoardMember,
  } = useFieldArray({
    control: form.control,
    name: "boardMembers",
  });

  // Helper functions for dynamic arrays
  const addEquityHolder = () => {
    appendEquityHolder({ name: "", shareholding: 0 });
  };

  const addBoardMember = () => {
    appendBoardMember({ name: "", designation: "", din: "" });
  };

  // Document upload handler
  const handleDocumentUpload = useCallback(async (fieldName: string, file: File) => {
    if (!file) return;

    // Validate file type and size
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      toast.error("Please upload only PDF, JPG, or PNG files");
      return;
    }

    if (file.size > maxSize) {
      toast.error("File size must be less than 5MB");
      return;
    }

    // Add to upload tracking
    setDocumentUploads(prev => [...prev, {
      fieldName,
      file,
      uploading: true
    }]);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'spv-documents');
      formData.append('fieldName', fieldName);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload document');
      }

      const result = await response.json();
      console.log("Upload result:", result);

      // Update the form field with the uploaded file URL
      if (fieldName === 'spvLogoUrl') {
        console.log("Setting spvLogoUrl to:", result.url);
        form.setValue('spvLogoUrl', result.url);

        // Verify the value was set
        const currentValue = form.getValues('spvLogoUrl');
        console.log("Current spvLogoUrl value after setting:", currentValue);
      }

      // Update upload tracking
      setDocumentUploads(prev => prev.map(upload =>
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, url: result.url }
          : upload
      ));

      toast.success(`${fieldName} document uploaded successfully`);
    } catch (error) {
      console.error("Document upload error:", error);

      // Update upload tracking with error
      setDocumentUploads(prev => prev.map(upload =>
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, error: error instanceof Error ? error.message : 'Upload failed' }
          : upload
      ));

      toast.error(`Failed to upload ${fieldName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [form]);

  // Document upload field component
  const DocumentUploadField = ({
    fieldName,
    label,
    description,
    accept = ".pdf,.jpg,.jpeg,.png",
    required = false,
  }: {
    fieldName: string;
    label: string;
    description: string;
    accept?: string;
    required?: boolean;
  }) => {
    const upload = documentUploads.find(u => u.fieldName === fieldName);
    const inputId = `${fieldName}-upload`;

    return (
      <FieldWithTooltip label={label} description={description} required={required}>
        <div className="flex items-center gap-2">
          <div className="flex-1">
            <input
              id={inputId}
              type="file"
              accept={accept}
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleDocumentUpload(fieldName, file);
              }}
              className="hidden"
            />
            <Button
              type="button"
              variant="outline"
              onClick={() => document.getElementById(inputId)?.click()}
              disabled={upload?.uploading}
              className="w-full justify-start"
            >
              {upload?.uploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : upload?.url ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 text-green-600" />
                  Document Uploaded
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Document
                </>
              )}
            </Button>
          </div>
          {upload?.url && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => window.open(upload.url, '_blank')}
            >
              <FileText className="h-4 w-4" />
            </Button>
          )}
          {upload?.error && (
            <div className="flex items-center text-red-600">
              <AlertCircle className="h-4 w-4" />
            </div>
          )}
        </div>
        {upload?.error && (
          <p className="text-sm text-red-600 mt-1">{upload.error}</p>
        )}
      </FieldWithTooltip>
    );
  };

  const handleSubmit = async (data: SPVEditFormValues) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);

      console.log("Form submission started with data:", data);

      // Check for form validation errors
      const formErrors = form.formState.errors;
      if (Object.keys(formErrors).length > 0) {
        console.error("Form validation errors:", formErrors);
        setSubmitError("Please fix the validation errors before submitting");
        return;
      }

      // Validate required fields
      if (!data.name?.trim()) {
        setSubmitError("SPV name is required");
        setActiveTab("basic");
        return;
      }

      // Clean up the data - remove undefined values and empty strings
      const cleanedData = Object.entries(data).reduce((acc, [key, value]) => {
        console.log(`Processing field ${key}:`, value, typeof value);

        if (value !== undefined && value !== "") {
          // Handle special cases for arrays and objects
          if (key === "equityHolders" || key === "boardMembers") {
            if (Array.isArray(value) && value.length > 0) {
              acc[key] = value;
              console.log(`Added ${key} to cleaned data:`, value);
            }
          } else if (key === "bankAccountDetails") {
            if (value && typeof value === "object") {
              const bankDetails = Object.entries(value).reduce((bankAcc, [bankKey, bankValue]) => {
                if (bankValue !== undefined && bankValue !== "") {
                  bankAcc[bankKey] = bankValue;
                }
                return bankAcc;
              }, {} as any);
              if (Object.keys(bankDetails).length > 0) {
                acc[key] = bankDetails;
                console.log(`Added ${key} to cleaned data:`, bankDetails);
              }
            }
          } else {
            // Handle all other fields including URLs
            acc[key] = value;
            console.log(`Added ${key} to cleaned data:`, value);
          }
        } else {
          console.log(`Skipped ${key} (undefined or empty):`, value);
        }
        return acc;
      }, {} as any);

      console.log("Updating SPV data:", cleanedData);

      const response = await fetch(`/api/organizations/spvs/${spv.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(cleanedData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update SPV");
      }

      const result = await response.json();
      console.log("SPV update response:", result);

      toast.success("SPV updated successfully!");
      onClose();
      onSuccess?.();
    } catch (error) {
      console.error("Error updating SPV:", error);
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setSubmitError(errorMessage);
      toast.error(`Failed to update SPV: ${errorMessage}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader className="flex-shrink-0 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-blue-600" />
              <DialogTitle className="text-xl font-semibold">Edit SPV Information</DialogTitle>
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          <TooltipProvider>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="h-full flex flex-col">
                {submitError && (
                  <Alert className="mb-4 border-red-200 bg-red-50">
                    <AlertCircle className="h-4 w-4 text-red-600" />
                    <AlertDescription className="text-red-800">
                      {submitError}
                    </AlertDescription>
                  </Alert>
                )}

                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="basic" className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Basic Info.
                    </TabsTrigger>
                    <TabsTrigger value="ownership" className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Ownership
                    </TabsTrigger>
                    <TabsTrigger value="financial" className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Financial Details
                    </TabsTrigger>
                  </TabsList>

                  {/* Basic Information Tab */}
                  <TabsContent value="basic" className="flex-1 space-y-6">
                    <Card className="border-0 shadow-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-2 text-lg">
                          <Building className="h-5 w-5 text-blue-600" />
                          Basic Information
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-8">
                        {/* Essential Information */}
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-base font-semibold text-gray-900 mb-4">Essential Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              {/* SPV Name - Required */}
                              <FormField
                                control={form.control}
                                name="name"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="SPV Name"
                                      description={fieldDescriptions.name}
                                      required={true}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter SPV name"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              {/* Parent Company */}
                              <FormField
                                control={form.control}
                                name="parentCompany"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Parent Company"
                                      description={fieldDescriptions.parentCompany}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter parent company name"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              {/* SPV Type */}
                              <FormField
                                control={form.control}
                                name="spvType"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="SPV Type"
                                      description={fieldDescriptions.spvType}
                                    >
                                      <Select onValueChange={field.onChange} value={field.value}>
                                        <FormControl>
                                          <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                            <SelectValue placeholder="Select SPV type" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {spvTypeOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              {/* Date of Incorporation */}
                              <FormField
                                control={form.control}
                                name="dateOfIncorporation"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Date of Incorporation"
                                      description={fieldDescriptions.dateOfIncorporation}
                                    >
                                      <FormControl>
                                        <Input
                                          type="date"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              {/* CIN Number */}
                              <FormField
                                control={form.control}
                                name="cinNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="CIN Number"
                                      description={fieldDescriptions.cinNumber}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter CIN number"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              {/* PAN Number */}
                              <FormField
                                control={form.control}
                                name="panNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="PAN Number"
                                      description={fieldDescriptions.panNumber}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter PAN number"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              {/* GST Number */}
                              <FormField
                                control={form.control}
                                name="gstNumber"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="GST Number"
                                      description={fieldDescriptions.gstNumber}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter GST number"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              {/* State of Incorporation */}
                              <FormField
                                control={form.control}
                                name="stateOfIncorporation"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="State of Incorporation"
                                      description={fieldDescriptions.stateOfIncorporation}
                                    >
                                      <Select onValueChange={field.onChange} value={field.value}>
                                        <FormControl>
                                          <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                            <SelectValue placeholder="Select state" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {stateOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>

                          {/* Address and Description */}
                          <div className="space-y-6">
                            <div>
                              <h3 className="text-base font-semibold text-gray-900 mb-4">Address & Description</h3>
                              <div className="grid grid-cols-1 gap-6">
                                {/* Registered Address */}
                                <FormField
                                  control={form.control}
                                  name="registeredAddress"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FieldWithTooltip
                                        label="Registered Address"
                                        description={fieldDescriptions.registeredAddress}
                                      >
                                        <FormControl>
                                          <Textarea
                                            placeholder="Enter registered address"
                                            {...field}
                                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                            rows={3}
                                          />
                                        </FormControl>
                                      </FieldWithTooltip>
                                      <FormMessage className="text-red-600" />
                                    </FormItem>
                                  )}
                                />

                                {/* Description */}
                                <FormField
                                  control={form.control}
                                  name="description"
                                  render={({ field }) => (
                                    <FormItem>
                                      <FieldWithTooltip
                                        label="Description"
                                        description={fieldDescriptions.description}
                                      >
                                        <FormControl>
                                          <Textarea
                                            placeholder="Enter detailed description"
                                            {...field}
                                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                            rows={3}
                                          />
                                        </FormControl>
                                      </FieldWithTooltip>
                                      <FormMessage className="text-red-600" />
                                    </FormItem>
                                  )}
                                />
                              </div>
                            </div>
                          </div>

                          {/* Document Upload */}
                          <div className="space-y-6">
                            <div>
                              <h3 className="text-base font-semibold text-gray-900 mb-4">Document Upload</h3>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <DocumentUploadField
                                  fieldName="spvLogoUrl"
                                  label="SPV Logo"
                                  description={fieldDescriptions.spvLogoUrl}
                                  accept=".jpg,.jpeg,.png"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Ownership & Stakeholders Tab */}
                  <TabsContent value="ownership" className="flex-1 space-y-6">
                    <Card className="border-0 shadow-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-2 text-lg">
                          <Users className="h-5 w-5 text-blue-600" />
                          Ownership & Stakeholders
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-8">
                        {/* Promoter Information */}
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-base font-semibold text-gray-900 mb-4">Promoter Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              <FormField
                                control={form.control}
                                name="promoterName"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Promoter Name"
                                      description={fieldDescriptions.promoterName}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter promoter name"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="authorizedSignatory"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Authorized Signatory"
                                      description={fieldDescriptions.authorizedSignatory}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter authorized signatory name"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Financial Details Tab */}
                  <TabsContent value="financial" className="flex-1 space-y-6">
                    <Card className="border-0 shadow-sm">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-2 text-lg">
                          <DollarSign className="h-5 w-5 text-blue-600" />
                          Financial Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-8">
                        {/* Capital Information */}
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-base font-semibold text-gray-900 mb-4">Capital Information</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              <FormField
                                control={form.control}
                                name="authorizedCapital"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Authorized Capital (₹)"
                                      description={fieldDescriptions.authorizedCapital}
                                    >
                                      <FormControl>
                                        <Input
                                          type="number"
                                          placeholder="Enter authorized capital"
                                          {...field}
                                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="paidUpCapital"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Paid-up Capital (₹)"
                                      description={fieldDescriptions.paidUpCapital}
                                    >
                                      <FormControl>
                                        <Input
                                          type="number"
                                          placeholder="Enter paid-up capital"
                                          {...field}
                                          onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : undefined)}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="fundingSource"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Funding Source"
                                      description={fieldDescriptions.fundingSource}
                                    >
                                      <Select onValueChange={field.onChange} value={field.value}>
                                        <FormControl>
                                          <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                            <SelectValue placeholder="Select funding source" />
                                          </SelectTrigger>
                                        </FormControl>
                                        <SelectContent>
                                          {fundingSourceOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="debtEquityRatio"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Debt-Equity Ratio"
                                      description={fieldDescriptions.debtEquityRatio}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter debt-equity ratio (e.g., 70:30)"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="debtProvider"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="Debt Provider"
                                      description={fieldDescriptions.debtProvider}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter debt provider name"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />

                              <FormField
                                control={form.control}
                                name="ppaSignedWith"
                                render={({ field }) => (
                                  <FormItem>
                                    <FieldWithTooltip
                                      label="PPA Signed With"
                                      description={fieldDescriptions.ppaSignedWith}
                                    >
                                      <FormControl>
                                        <Input
                                          placeholder="Enter PPA signatory"
                                          {...field}
                                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                                        />
                                      </FormControl>
                                    </FieldWithTooltip>
                                    <FormMessage className="text-red-600" />
                                  </FormItem>
                                )}
                              />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Form Actions */}
                  <div className="flex justify-end gap-3 pt-6 border-t bg-gray-50 px-6 py-4 -mx-6 -mb-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={onClose}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="bg-blue-600 hover:bg-blue-700"
                      onClick={async (e) => {
                        console.log("Submit button clicked");
                        console.log("Current form values:", form.getValues());
                        console.log("Form errors:", form.formState.errors);

                        // Trigger validation manually
                        const isValid = await form.trigger();
                        console.log("Form validation result:", isValid);

                        if (!isValid) {
                          e.preventDefault();
                          console.log("Form validation failed, preventing submission");
                          setSubmitError("Please fix the validation errors before submitting");
                          return;
                        }
                      }}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          Update SPV
                        </>
                      )}
                    </Button>
                  </div>
                </Tabs>
              </form>
            </Form>
          </TooltipProvider>
        </div>
      </DialogContent>
    </Dialog>
  );
}
