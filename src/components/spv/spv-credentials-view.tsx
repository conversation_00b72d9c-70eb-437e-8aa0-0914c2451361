"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { 
  Eye, 
  EyeOff, 
  Copy, 
  Mail, 
  Key, 
  User, 
  Shield,
  RefreshCw,
  Send
} from "lucide-react";
import { toast } from "sonner";

interface SPVCredential {
  id: string;
  email: string;
  password: string;
  role: string;
  isActive: boolean;
  user: {
    id: string;
    name: string;
    email: string;
  };
}

interface SPVCredentialsViewProps {
  spvId: string;
}

export function SPVCredentialsView({ spvId }: SPVCredentialsViewProps) {
  const [credentials, setCredentials] = useState<SPVCredential[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>({});
  const [isRegenerating, setIsRegenerating] = useState<Record<string, boolean>>({});
  const [isResending, setIsResending] = useState<Record<string, boolean>>({});

  useEffect(() => {
    fetchCredentials();
  }, [spvId]);

  const fetchCredentials = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/organizations/spvs/${spvId}/credentials`);
      if (response.ok) {
        const data = await response.json();
        setCredentials(data.credentials);
      } else {
        toast.error("Failed to fetch SPV credentials");
      }
    } catch (error) {
      console.error("Error fetching credentials:", error);
      toast.error("Failed to fetch SPV credentials");
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${type} copied to clipboard`);
    } catch (error) {
      toast.error(`Failed to copy ${type.toLowerCase()}`);
    }
  };

  const togglePasswordVisibility = (credentialId: string) => {
    setShowPasswords(prev => ({
      ...prev,
      [credentialId]: !prev[credentialId]
    }));
  };

  const regeneratePassword = async (credentialId: string) => {
    try {
      setIsRegenerating(prev => ({ ...prev, [credentialId]: true }));
      
      const response = await fetch(`/api/spv/${spvId}/credentials/${credentialId}/regenerate`, {
        method: "POST",
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("Password regenerated and sent via email");
        fetchCredentials(); // Refresh the list
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to regenerate password");
      }
    } catch (error) {
      console.error("Error regenerating password:", error);
      toast.error("Failed to regenerate password");
    } finally {
      setIsRegenerating(prev => ({ ...prev, [credentialId]: false }));
    }
  };

  const resendCredentials = async (credentialId: string) => {
    try {
      setIsResending(prev => ({ ...prev, [credentialId]: true }));
      
      const response = await fetch(`/api/spv/${spvId}/credentials/${credentialId}/resend`, {
        method: "POST",
      });

      if (response.ok) {
        toast.success("Credentials sent via email");
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to resend credentials");
      }
    } catch (error) {
      console.error("Error resending credentials:", error);
      toast.error("Failed to resend credentials");
    } finally {
      setIsResending(prev => ({ ...prev, [credentialId]: false }));
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            SPV Administrator Credentials
          </CardTitle>
          <CardDescription>
            View SPV administrator login credentials. Passwords are automatically generated and sent via email during account creation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {credentials.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-sm text-muted-foreground">No credentials found for this SPV</p>
            </div>
          ) : (
            credentials.map((credential) => (
              <Card key={credential.id} className="border-l-4 border-l-blue-500">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <User className="h-8 w-8 text-blue-500" />
                      <div>
                        <h3 className="font-semibold">{credential.user.name}</h3>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{credential.role.replace('_', ' ')}</Badge>
                          <Badge 
                            variant={credential.isActive ? "default" : "secondary"}
                            className={credential.isActive ? "bg-green-100 text-green-800" : ""}
                          >
                            {credential.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => resendCredentials(credential.id)}
                        disabled={isResending[credential.id]}
                      >
                        {isResending[credential.id] ? (
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <Send className="mr-2 h-4 w-4" />
                        )}
                        Resend
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => regeneratePassword(credential.id)}
                        disabled={isRegenerating[credential.id]}
                      >
                        {isRegenerating[credential.id] ? (
                          <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        ) : (
                          <RefreshCw className="mr-2 h-4 w-4" />
                        )}
                        Regenerate
                      </Button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Email
                      </Label>
                      <div className="flex gap-2">
                        <Input 
                          value={credential.email} 
                          readOnly 
                          className="bg-muted"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(credential.email, "Email")}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Key className="h-4 w-4" />
                        Password
                      </Label>
                      <div className="flex gap-2">
                        <Input
                          type="text"
                          value="Sent via email during account creation"
                          readOnly
                          className="bg-muted text-muted-foreground"
                          placeholder="Password sent via email"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => copyToClipboard(credential.email, "Email")}
                          title="Copy email address"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Password was automatically generated and sent to the administrator's email address
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </CardContent>
      </Card>
    </div>
  );
}
