"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, User, Mail, Phone, Briefcase, Key, FolderOpen } from "lucide-react";
import { toast } from "sonner";

const spvUserSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required"),
  role: z.enum(["SPV_ADMIN", "PROJECT_MANAGER", "SITE_WORKER"], {
    required_error: "Role is required",
  }),
  jobTitle: z.string().optional(),
  phoneNumber: z.string().optional(),
  password: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(8, "Please confirm your password"),
  spvId: z.string().optional(),
  projectIds: z.array(z.string()).optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type SPVUserFormValues = z.infer<typeof spvUserSchema>;

interface Project {
  id: string;
  name: string;
  type: string;
  status: string;
  location?: string;
  assignedUsers: {
    id: string;
    userId: string;
    role: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
}

interface SPVUserCreateFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (data: any) => void;
}

export function SPVUserCreateForm({
  isOpen,
  onClose,
  onSuccess,
}: SPVUserCreateFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [loadingProjects, setLoadingProjects] = useState(false);
  const [showProjectAssignment, setShowProjectAssignment] = useState(false);
  const [currentSpvId, setCurrentSpvId] = useState<string>("");

  const form = useForm<SPVUserFormValues>({
    resolver: zodResolver(spvUserSchema),
    defaultValues: {
      email: "",
      name: "",
      role: undefined,
      jobTitle: "",
      phoneNumber: "",
      password: "",
      confirmPassword: "",
      spvId: "",
      projectIds: [],
    },
  });

  useEffect(() => {
    if (isOpen) {
      fetchProjects();
      fetchCurrentSpv();
      form.reset();
      setShowProjectAssignment(false);
    }
  }, [isOpen, form]);

  const fetchProjects = async () => {
    try {
      setLoadingProjects(true);
      const response = await fetch("/api/spv/projects?limit=100");
      if (response.ok) {
        const data = await response.json();
        setProjects(data.data.projects || []);
      } else {
        toast.error("Failed to fetch projects");
      }
    } catch (error) {
      toast.error("Error fetching projects");
      console.error("Projects fetch error:", error);
    } finally {
      setLoadingProjects(false);
    }
  };

  const fetchCurrentSpv = async () => {
    try {
      const response = await fetch("/api/spv/current");
      if (response.ok) {
        const data = await response.json();
        if (data.spv?.id) {
          setCurrentSpvId(data.spv.id);
          form.setValue("spvId", data.spv.id);
        }
      } else {
        // Fallback: try to get SPVs and use the first one
        const spvsResponse = await fetch("/api/spvs");
        if (spvsResponse.ok) {
          const spvsData = await spvsResponse.json();
          if (spvsData.spvs && spvsData.spvs.length > 0) {
            const firstSpv = spvsData.spvs[0];
            setCurrentSpvId(firstSpv.id);
            form.setValue("spvId", firstSpv.id);
          }
        }
      }
    } catch (error) {
      console.error("Error fetching current SPV:", error);
    }
  };

  const onSubmit = async (data: SPVUserFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch("/api/spv/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: data.email,
          name: data.name,
          role: data.role,
          jobTitle: data.jobTitle,
          phoneNumber: data.phoneNumber,
          password: data.password,
          spvId: currentSpvId || data.spvId,
          projectIds: data.projectIds,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success("SPV user created successfully");
        onSuccess(result.data);
        onClose();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to create SPV user");
      }
    } catch (error) {
      toast.error("Error creating SPV user");
      console.error("Create SPV user error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedProjectIds = form.watch("projectIds") || [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create SPV User</DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* User Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  User Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Full Name *</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter full name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Address *</FormLabel>
                        <FormControl>
                          <Input 
                            type="email" 
                            placeholder="Enter email address" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="role"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Role *</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="SPV_ADMIN">SPV Admin</SelectItem>
                            <SelectItem value="PROJECT_MANAGER">Project Manager</SelectItem>
                            <SelectItem value="SITE_WORKER">Site Worker</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="jobTitle"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Job Title</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter job title" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter phone number" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Password Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Account Security
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password *</FormLabel>
                        <FormControl>
                          <Input 
                            type="password" 
                            placeholder="Enter password" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirm Password *</FormLabel>
                        <FormControl>
                          <Input 
                            type="password" 
                            placeholder="Confirm password" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Project Assignment Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FolderOpen className="h-5 w-5" />
                  Project Assignment
                  <Badge variant="secondary">Optional</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="show-projects"
                    checked={showProjectAssignment}
                    onCheckedChange={setShowProjectAssignment}
                  />
                  <label htmlFor="show-projects" className="text-sm font-medium">
                    Assign to projects now
                  </label>
                </div>

                {showProjectAssignment && (
                  <div className="space-y-4">
                    {loadingProjects ? (
                      <div className="flex items-center justify-center py-4">
                        <Loader2 className="h-6 w-6 animate-spin" />
                        <span className="ml-2">Loading projects...</span>
                      </div>
                    ) : projects.length > 0 ? (
                      <FormField
                        control={form.control}
                        name="projectIds"
                        render={() => (
                          <FormItem>
                            <FormLabel>Select Projects</FormLabel>
                            <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto">
                              {projects.map((project) => (
                                <FormField
                                  key={project.id}
                                  control={form.control}
                                  name="projectIds"
                                  render={({ field }) => {
                                    return (
                                      <FormItem
                                        key={project.id}
                                        className="flex flex-row items-start space-x-3 space-y-0"
                                      >
                                        <FormControl>
                                          <Checkbox
                                            checked={field.value?.includes(project.id)}
                                            onCheckedChange={(checked) => {
                                              return checked
                                                ? field.onChange([...field.value, project.id])
                                                : field.onChange(
                                                    field.value?.filter(
                                                      (value) => value !== project.id
                                                    )
                                                  );
                                            }}
                                          />
                                        </FormControl>
                                        <div className="space-y-1 leading-none">
                                          <FormLabel className="font-normal">
                                            {project.name}
                                          </FormLabel>
                                          <p className="text-xs text-muted-foreground">
                                            {project.type} • {project.status}
                                            {project.location && ` • ${project.location}`}
                                          </p>
                                          {project.assignedUsers.length > 0 && (
                                            <p className="text-xs text-muted-foreground">
                                              Assigned to: {project.assignedUsers.map(u => u.user.name).join(", ")}
                                            </p>
                                          )}
                                        </div>
                                      </FormItem>
                                    );
                                  }}
                                />
                              ))}
                            </div>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    ) : (
                      <p className="text-sm text-muted-foreground">
                        No projects available for assignment.
                      </p>
                    )}

                    {selectedProjectIds.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium mb-2">
                          Selected Projects ({selectedProjectIds.length}):
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {selectedProjectIds.map((projectId) => {
                            const project = projects.find(p => p.id === projectId);
                            return project ? (
                              <Badge key={projectId} variant="secondary">
                                {project.name}
                              </Badge>
                            ) : null;
                          })}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex justify-end space-x-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Create SPV User
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
