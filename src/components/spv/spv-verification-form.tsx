"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Loader2, Shield, FileText, Building2, User, CreditCard, MapPin } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { spvVerificationSchema } from "@/lib/validation/schemas";
import { SPVVerificationData } from "@/types/spv";
import { toast } from "sonner";

type SPVVerificationFormValues = z.infer<typeof spvVerificationSchema>;

const legalStructures = [
  { value: "Private Limited Company", label: "Private Limited Company" },
  { value: "Public Limited Company", label: "Public Limited Company" },
  { value: "Limited Liability Partnership", label: "Limited Liability Partnership (LLP)" },
  { value: "Partnership", label: "Partnership" },
  { value: "Sole Proprietorship", label: "Sole Proprietorship" },
  { value: "Trust", label: "Trust" },
  { value: "Society", label: "Society" },
  { value: "Other", label: "Other" },
];

const projectCategories = [
  { value: "Solar", label: "Solar Energy" },
  { value: "Wind", label: "Wind Energy" },
  { value: "Hybrid", label: "Hybrid Renewable" },
  { value: "Forestry", label: "Forestry & REDD+" },
  { value: "Energy Efficiency", label: "Energy Efficiency" },
  { value: "Waste Management", label: "Waste Management" },
  { value: "Transportation", label: "Transportation" },
  { value: "Other", label: "Other" },
];

interface SPVVerificationFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: SPVVerificationData) => Promise<void>;
  isSubmitting?: boolean;
  initialData?: Partial<SPVVerificationFormValues>;
  spvId?: string;
}

export function SPVVerificationForm({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting = false,
  initialData,
  spvId,
}: SPVVerificationFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<SPVVerificationFormValues>({
    resolver: zodResolver(spvVerificationSchema),
    defaultValues: {
      name: initialData?.name || "",
      purpose: initialData?.purpose || "",
      legalStructure: initialData?.legalStructure || "",
      registrationNumber: initialData?.registrationNumber || "",
      jurisdiction: initialData?.jurisdiction || "",
      country: initialData?.country || "",
      gstNumber: initialData?.gstNumber || "",
      cinNumber: initialData?.cinNumber || "",
      panNumber: initialData?.panNumber || "",
      incorporationDate: initialData?.incorporationDate || "",
      establishedDate: initialData?.establishedDate || "",
      registeredAddress: initialData?.registeredAddress || "",
      address: initialData?.address || "",
      contactPersonName: initialData?.contactPersonName || "",
      contactPersonEmail: initialData?.contactPersonEmail || "",
      contactPersonMobile: initialData?.contactPersonMobile || "",
      contact: initialData?.contact || "",
      bankAccountNumber: initialData?.bankAccountNumber || "",
      ifscCode: initialData?.ifscCode || "",
      projectCategories: initialData?.projectCategories || "",
      description: initialData?.description || "",
      taxId: initialData?.taxId || "",
      legalEntityId: initialData?.legalEntityId || "",
    },
  });

  const handleSubmit = async (values: SPVVerificationFormValues) => {
    try {
      setSubmitError(null);
      
      const submitData = {
        ...values,
        incorporationDate: values.incorporationDate || undefined,
        establishedDate: values.establishedDate || undefined,
      };

      await onSubmit(submitData);
      handleClose();
      toast.success("SPV verification details submitted successfully!");
    } catch (error) {
      console.error("Error submitting SPV verification form:", error);
      setSubmitError(error instanceof Error ? error.message : "Failed to submit verification details");
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
      setSubmitError(null);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            SPV Verification Details
          </DialogTitle>
          <DialogDescription>
            Complete the verification details for your SPV. All fields marked with * are required for verification.
          </DialogDescription>
        </DialogHeader>

        <Alert>
          <FileText className="h-4 w-4" />
          <AlertDescription>
            Please ensure all information matches your legal documents exactly. 
            You will need to upload supporting documents after submitting this form.
          </AlertDescription>
        </Alert>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="basic" className="flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Basic Info
                </TabsTrigger>
                <TabsTrigger value="legal" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Legal Details
                </TabsTrigger>
                <TabsTrigger value="contact" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Contact Info
                </TabsTrigger>
                <TabsTrigger value="banking" className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Banking
                </TabsTrigger>
              </TabsList>

              {/* Basic Information Tab */}
              <TabsContent value="basic" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Basic Information</CardTitle>
                    <CardDescription>
                      Enter the basic details of your SPV
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SPV Name *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter SPV legal name"
                              autoComplete="off"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Country *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g., India"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="jurisdiction"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Jurisdiction *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="e.g., Maharashtra"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="projectCategories"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Primary Project Category *</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select project category" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {projectCategories.map((category) => (
                                <SelectItem key={category.value} value={category.value}>
                                  {category.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="purpose"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Purpose</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Describe the purpose of this SPV"
                              className="min-h-[80px]"
                              autoComplete="off"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Additional Description</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Any additional information about the SPV"
                              className="min-h-[60px]"
                              autoComplete="off"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Legal Details Tab */}
              <TabsContent value="legal" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Legal & Registration Details</CardTitle>
                    <CardDescription>
                      Enter legal registration and compliance information
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="legalStructure"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Legal Structure *</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select structure" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {legalStructures.map((structure) => (
                                  <SelectItem key={structure.value} value={structure.value}>
                                    {structure.label}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="registrationNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Registration Number *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter registration number"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="incorporationDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Incorporation Date</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="establishedDate"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Established Date</FormLabel>
                            <FormControl>
                              <Input
                                type="date"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    {/* Indian Compliance Fields */}
                    <div className="space-y-4">
                      <h4 className="text-sm font-medium text-muted-foreground">
                        Indian Compliance Information (if applicable)
                      </h4>

                      <div className="grid grid-cols-3 gap-4">
                        <FormField
                          control={form.control}
                          name="panNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>PAN Number</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="**********"
                                  autoComplete="off"
                                  maxLength={10}
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>10 characters</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="gstNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>GST Number</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="22**********1Z5"
                                  autoComplete="off"
                                  maxLength={15}
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>15 characters</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="cinNumber"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>CIN Number</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="U12345MH2020PTC123456"
                                  autoComplete="off"
                                  maxLength={21}
                                  {...field}
                                />
                              </FormControl>
                              <FormDescription>21 characters</FormDescription>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="taxId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Tax ID</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter tax identification number"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="legalEntityId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Legal Entity ID</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter legal entity identifier"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Contact Information Tab */}
              <TabsContent value="contact" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Contact Information</CardTitle>
                    <CardDescription>
                      Enter contact details and address information
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="contactPersonName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contact Person Name *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter primary contact person name"
                              autoComplete="off"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="contactPersonEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contact Person Email *</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="contactPersonMobile"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Contact Person Mobile *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="+91 9876543210"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="contact"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Additional Contact Information</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Alternative contact details"
                              autoComplete="off"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="registeredAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Registered Address *</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter complete registered office address"
                              className="min-h-[80px]"
                              autoComplete="off"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            This should match the address on your incorporation documents
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Operational Address</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter operational/business address (if different from registered)"
                              className="min-h-[60px]"
                              autoComplete="off"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Banking Information Tab */}
              <TabsContent value="banking" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Banking Information</CardTitle>
                    <CardDescription>
                      Enter banking details for settlements and transactions
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="bankAccountNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Bank Account Number *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter bank account number"
                                autoComplete="off"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              Account for carbon credit settlements
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="ifscCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>IFSC Code *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="ABCD0123456"
                                autoComplete="off"
                                maxLength={11}
                                {...field}
                              />
                            </FormControl>
                            <FormDescription>
                              11-character IFSC code
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <Alert>
                      <CreditCard className="h-4 w-4" />
                      <AlertDescription>
                        Banking information will be used for carbon credit settlements and must match
                        the SPV's registered name. Please ensure the account is active and operational.
                      </AlertDescription>
                    </Alert>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Error Display */}
            {submitError && (
              <div className="p-3 rounded-md bg-destructive/10 border border-destructive/20">
                <p className="text-sm text-destructive">{submitError}</p>
              </div>
            )}

            {/* Form Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-border">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="min-w-[160px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  "Submit for Verification"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}

export { spvVerificationSchema };
export type { SPVVerificationFormValues };
