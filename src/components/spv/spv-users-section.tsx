"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  Users, 
  Plus, 
  Mail, 
  Phone, 
  Calendar,
  MoreHorizontal,
  Edit,
  Trash2,
  UserPlus,
  Shield,
  User
} from "lucide-react";
import { toast } from "sonner";
import { SPVWithDetails } from "@/types/spv";
import { CreateSPVUserForm } from "@/components/spv/create-spv-user-form";
import EditSPVUserForm from "@/components/spv/edit-spv-user-form";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface SPVUser {
  id: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    phoneNumber?: string;
    jobTitle?: string;
  };
}

interface SPVUsersSectionProps {
  spv: SPVWithDetails;
  onUserCountChange?: () => void; // Callback to refresh parent SPV data
}

export function SPVUsersSection({ spv, onUserCountChange }: SPVUsersSectionProps) {
  const [users, setUsers] = useState<SPVUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateUserOpen, setIsCreateUserOpen] = useState(false);
  const [isEditUserOpen, setIsEditUserOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<SPVUser | null>(null);

  useEffect(() => {
    fetchUsers();
  }, [spv.id]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      // Always fetch fresh data from API to ensure we have the latest users
      const response = await fetch(`/api/organizations/spvs/${spv.id}/users`);
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      } else {
        toast.error("Failed to fetch SPV users");
      }
    } catch (error) {
      console.error("Error fetching users:", error);
      toast.error("Failed to fetch SPV users");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserCreated = async () => {
    setIsCreateUserOpen(false); // Close the dialog first
    // Small delay to ensure the API call completes
    setTimeout(() => {
      fetchUsers(); // Refresh the users list
      onUserCountChange?.(); // Refresh parent SPV data to update counts
    }, 100);
  };

  const handleEditUser = (user: SPVUser) => {
    setSelectedUser(user);
    setIsEditUserOpen(true);
  };

  const handleUserUpdated = () => {
    fetchUsers();
    setIsEditUserOpen(false);
    setSelectedUser(null);
    toast.success("User updated successfully!");
  };

  const handleDeleteUser = async (userId: string) => {
    if (!confirm("Are you sure you want to remove this user from the SPV?")) {
      return;
    }

    try {
      const response = await fetch(`/api/organizations/spvs/${spv.id}/users/${userId}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("User removed successfully");
        fetchUsers();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to remove user");
      }
    } catch (error) {
      console.error("Error removing user:", error);
      toast.error("Failed to remove user");
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "SPV_ADMIN":
        return "bg-red-100 text-red-800";
      case "PROJECT_MANAGER":
        return "bg-blue-100 text-blue-800";
      case "SITE_WORKER":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case "SPV_ADMIN":
        return <Shield className="h-4 w-4" />;
      case "PROJECT_MANAGER":
        return <Users className="h-4 w-4" />;
      case "SITE_WORKER":
        return <User className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                SPV Users ({users.length})
              </CardTitle>
              <CardDescription>
                Manage users assigned to this SPV. Create Project Managers and Site Workers.
              </CardDescription>
            </div>
            <Dialog open={isCreateUserOpen} onOpenChange={setIsCreateUserOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Create User
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create User for {spv.name}</DialogTitle>
                </DialogHeader>
                <CreateSPVUserForm
                  spvId={spv.id}
                  onSuccess={handleUserCreated}
                  onCancel={() => setIsCreateUserOpen(false)}
                />
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {users.length === 0 ? (
            <div className="text-center py-8">
              <UserPlus className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No users assigned</h3>
              <p className="text-muted-foreground mb-4">
                Create Project Managers and Site Workers for this SPV
              </p>
              <Button onClick={() => setIsCreateUserOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Create First User
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {users.map((spvUser) => (
                <Card key={spvUser.id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          {getRoleIcon(spvUser.role)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold">{spvUser.user.name}</h3>
                            <Badge className={getRoleColor(spvUser.role)}>
                              {spvUser.role.replace('_', ' ')}
                            </Badge>
                            <Badge 
                              variant={spvUser.isActive ? "default" : "secondary"}
                              className={spvUser.isActive ? "bg-green-100 text-green-800" : ""}
                            >
                              {spvUser.isActive ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                          
                          <div className="space-y-1 text-sm text-muted-foreground">
                            <div className="flex items-center gap-2">
                              <Mail className="h-4 w-4" />
                              {spvUser.user.email}
                            </div>
                            {spvUser.user.phoneNumber && (
                              <div className="flex items-center gap-2">
                                <Phone className="h-4 w-4" />
                                {spvUser.user.phoneNumber}
                              </div>
                            )}
                            {spvUser.user.jobTitle && (
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                {spvUser.user.jobTitle}
                              </div>
                            )}
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              Added {new Date(spvUser.createdAt).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditUser(spvUser)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit User
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-destructive"
                            onClick={() => handleDeleteUser(spvUser.id)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Remove User
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit User Dialog */}
      <Dialog open={isEditUserOpen} onOpenChange={setIsEditUserOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit User - {selectedUser?.user.name}</DialogTitle>
          </DialogHeader>
          {selectedUser && (
            <EditSPVUserForm
              spvId={spv.id}
              user={selectedUser}
              onSuccess={handleUserUpdated}
              onCancel={() => setIsEditUserOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
