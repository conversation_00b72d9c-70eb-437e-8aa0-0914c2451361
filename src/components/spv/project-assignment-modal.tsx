"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, FolderOpen, Users, CheckCircle } from "lucide-react";
import { toast } from "sonner";

const projectAssignmentSchema = z.object({
  spvUserId: z.string().min(1, "Please select a user"),
  projectIds: z.array(z.string()).min(1, "Please select at least one project"),
});

type ProjectAssignmentFormValues = z.infer<typeof projectAssignmentSchema>;

interface Project {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  location?: string;
  country?: string;
}

interface SPVUser {
  id: string;
  role: string;
  user: {
    id: string;
    name: string;
    email: string;
    jobTitle?: string;
  };
  projectAssignments: {
    id: string;
    project: {
      id: string;
      name: string;
    };
  }[];
}

interface ProjectAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export function ProjectAssignmentModal({
  isOpen,
  onClose,
  onSuccess,
}: ProjectAssignmentModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [projects, setProjects] = useState<Project[]>([]);
  const [spvUsers, setSPVUsers] = useState<SPVUser[]>([]);
  const [loading, setLoading] = useState(false);

  const form = useForm<ProjectAssignmentFormValues>({
    resolver: zodResolver(projectAssignmentSchema),
    defaultValues: {
      spvUserId: "",
      projectIds: [],
    },
  });

  const selectedUserId = form.watch("spvUserId");
  const selectedUser = spvUsers.find(user => user.id === selectedUserId);

  useEffect(() => {
    if (isOpen) {
      fetchData();
      form.reset();
    }
  }, [isOpen, form]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [projectsRes, usersRes] = await Promise.all([
        fetch("/api/spv/projects"),
        fetch("/api/spv/users")
      ]);

      if (projectsRes.ok) {
        const projectsData = await projectsRes.json();
        setProjects(projectsData.projects || []);
      }

      if (usersRes.ok) {
        const usersData = await usersRes.json();
        setSPVUsers(usersData.users || []);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = async (data: ProjectAssignmentFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch("/api/spv/project-assignments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          spvUserId: data.spvUserId,
          projectIds: data.projectIds,
        }),
      });

      if (response.ok) {
        toast.success("Projects assigned successfully");
        onSuccess();
        onClose();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to assign projects");
      }
    } catch (error) {
      console.error("Error assigning projects:", error);
      toast.error("Failed to assign projects");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getAssignedProjectIds = (user: SPVUser) => {
    return user.projectAssignments.map(assignment => assignment.project.id);
  };

  const getAvailableProjects = () => {
    if (!selectedUser) return projects;
    const assignedIds = getAssignedProjectIds(selectedUser);
    return projects.filter(project => !assignedIds.includes(project.id));
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "SPV_ADMIN": return "bg-purple-100 text-purple-800";
      case "PROJECT_MANAGER": return "bg-green-100 text-green-800";
      case "SITE_WORKER": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FolderOpen className="h-5 w-5" />
            Assign Projects to Team Member
          </DialogTitle>
          <DialogDescription>
            Select a team member and assign projects to them. They will gain access to the selected projects.
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* User Selection */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Select Team Member
                  </CardTitle>
                  <CardDescription>
                    Choose which team member to assign projects to
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <FormField
                    control={form.control}
                    name="spvUserId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Team Member *</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a team member" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {spvUsers.map((user) => (
                              <SelectItem key={user.id} value={user.id}>
                                <div className="flex items-center gap-2">
                                  <span>{user.user.name}</span>
                                  <Badge className={getRoleBadgeColor(user.role)}>
                                    {user.role.replace('_', ' ')}
                                  </Badge>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Show current assignments for selected user */}
                  {selectedUser && selectedUser.projectAssignments.length > 0 && (
                    <div className="mt-4 p-3 bg-green-50 rounded-lg">
                      <h4 className="text-sm font-medium text-green-800 mb-2 flex items-center gap-1">
                        <CheckCircle className="h-4 w-4" />
                        Currently Assigned Projects
                      </h4>
                      <div className="flex flex-wrap gap-1">
                        {selectedUser.projectAssignments.map((assignment) => (
                          <Badge key={assignment.id} variant="outline" className="text-xs">
                            {assignment.project.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Project Selection */}
              {selectedUserId && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center gap-2">
                      <FolderOpen className="h-4 w-4" />
                      Select Projects
                    </CardTitle>
                    <CardDescription>
                      Choose which projects to assign to {selectedUser?.user.name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <FormField
                      control={form.control}
                      name="projectIds"
                      render={() => (
                        <FormItem>
                          <FormLabel>Available Projects *</FormLabel>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
                            {getAvailableProjects().length > 0 ? (
                              getAvailableProjects().map((project) => (
                                <FormField
                                  key={project.id}
                                  control={form.control}
                                  name="projectIds"
                                  render={({ field }) => {
                                    return (
                                      <FormItem
                                        key={project.id}
                                        className="flex flex-row items-start space-x-3 space-y-0 p-3 border rounded-lg hover:bg-gray-50"
                                      >
                                        <FormControl>
                                          <Checkbox
                                            checked={field.value?.includes(project.id)}
                                            onCheckedChange={(checked) => {
                                              return checked
                                                ? field.onChange([...field.value, project.id])
                                                : field.onChange(
                                                    field.value?.filter(
                                                      (value) => value !== project.id
                                                    )
                                                  )
                                            }}
                                          />
                                        </FormControl>
                                        <div className="space-y-1 leading-none flex-1">
                                          <FormLabel className="text-sm font-medium cursor-pointer">
                                            {project.name}
                                          </FormLabel>
                                          {project.description && (
                                            <p className="text-xs text-muted-foreground">
                                              {project.description}
                                            </p>
                                          )}
                                          <div className="flex gap-1 mt-1">
                                            <Badge variant="outline" className="text-xs">
                                              {project.type}
                                            </Badge>
                                            <Badge variant="outline" className="text-xs">
                                              {project.status}
                                            </Badge>
                                          </div>
                                        </div>
                                      </FormItem>
                                    )
                                  }}
                                />
                              ))
                            ) : (
                              <div className="col-span-2 text-center py-8 text-muted-foreground">
                                {selectedUser?.projectAssignments.length === projects.length
                                  ? "All projects are already assigned to this user"
                                  : "No projects available for assignment"
                                }
                              </div>
                            )}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Form Actions */}
              <div className="flex justify-end space-x-3 pt-6 border-t">
                <Button type="button" variant="outline" onClick={onClose} disabled={isSubmitting}>
                  Cancel
                </Button>
                <Button 
                  type="submit" 
                  disabled={isSubmitting || !selectedUserId || getAvailableProjects().length === 0}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Assigning...
                    </>
                  ) : (
                    "Assign Projects"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
}
