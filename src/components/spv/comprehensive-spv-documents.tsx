"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import {
  Upload,
  FileText,
  CheckCircle,
  AlertCircle,
  Clock,
  Download,
  Trash2,
  Eye,
  RefreshCw,
  Info,
  X
} from "lucide-react";
import { toast } from "sonner";
import { SPVWithDetails, SPVDocument, SPVDocumentType } from "@/types/spv";

// Document categories matching the comprehensive SPV creation form
const documentCategories = {
  legal: {
    title: "Legal Documentation",
    description: "Essential legal documents for SPV verification",
    documents: [
      {
        type: "CIN_CERTIFICATE" as SPVDocumentType,
        label: "CIN Certificate",
        description: "Corporate Identity Number certificate",
        required: true,
        fieldName: "cinDocument"
      },
      {
        type: "INCORPORATION_CERTIFICATE" as SPVDocumentType,
        label: "Incorporation Certificate",
        description: "Certificate of incorporation",
        required: true,
        fieldName: "incorporationDocument"
      },
      {
        type: "PAN_DOCUMENT" as SPVDocumentType,
        label: "PAN Card",
        description: "Permanent Account Number document",
        required: true,
        fieldName: "panDocument"
      },
      {
        type: "GST_CERTIFICATE" as SPVDocumentType,
        label: "GST Certificate",
        description: "GST registration certificate",
        required: false,
        fieldName: "gstDocument"
      }
    ]
  },
  address: {
    title: "Address & Identity",
    description: "Address proof and identity documents",
    documents: [
      {
        type: "ADDRESS_PROOF" as SPVDocumentType,
        label: "Address Proof",
        description: "Registered office address proof",
        required: true,
        fieldName: "addressDocument"
      }
    ]
  },
  branding: {
    title: "Branding & Identity",
    description: "SPV branding and visual identity",
    documents: [
      {
        type: "SPV_LOGO" as SPVDocumentType,
        label: "SPV Logo",
        description: "Company logo for branding",
        required: false,
        fieldName: "spvLogo"
      }
    ]
  },
  legacy: {
    title: "Additional Documents",
    description: "Other supporting documents",
    documents: [
      {
        type: "BOARD_RESOLUTION" as SPVDocumentType,
        label: "Board Resolution",
        description: "Board resolution documents",
        required: false,
        fieldName: "boardResolution"
      },
      {
        type: "BANK_PROOF" as SPVDocumentType,
        label: "Bank Account Proof",
        description: "Bank account verification documents",
        required: false,
        fieldName: "bankProof"
      },
      {
        type: "AUTHORIZED_SIGNATORY_ID" as SPVDocumentType,
        label: "Authorized Signatory ID",
        description: "ID proof of authorized signatory",
        required: false,
        fieldName: "signatoryId"
      },
      {
        type: "MOA_AOA" as SPVDocumentType,
        label: "MOA & AOA",
        description: "Memorandum and Articles of Association",
        required: false,
        fieldName: "moaAoa"
      }
    ]
  }
};

interface DocumentUpload {
  fieldName: string;
  file: File;
  uploading: boolean;
  url?: string;
  error?: string;
}

interface ComprehensiveSPVDocumentsProps {
  spv: SPVWithDetails;
  onDocumentUpdate?: () => void;
}

export function ComprehensiveSPVDocuments({ spv, onDocumentUpdate }: ComprehensiveSPVDocumentsProps) {
  const [documentUploads, setDocumentUploads] = useState<DocumentUpload[]>([]);
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    document: SPVDocument | null;
  }>({
    isOpen: false,
    document: null
  });

  // Debug: Log SPV documents
  console.log("SPV documents:", spv.documents);

  // Get document by type
  const getDocumentByType = (type: SPVDocumentType) => {
    return spv.documents?.find(doc => doc.documentType === type);
  };

  // Get upload status for a field
  const getUploadStatus = (fieldName: string) => {
    return documentUploads.find(upload => upload.fieldName === fieldName);
  };

  // Handle document upload
  const handleDocumentUpload = useCallback(async (fieldName: string, documentType: SPVDocumentType, file: File) => {
    if (!file) return;

    // Validate file type and size
    const allowedTypes = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
      toast.error("Please upload only PDF, JPG, or PNG files");
      return;
    }

    if (file.size > maxSize) {
      toast.error("File size must be less than 5MB");
      return;
    }

    // Add to upload tracking
    setDocumentUploads(prev => [...prev.filter(u => u.fieldName !== fieldName), {
      fieldName,
      file,
      uploading: true
    }]);

    try {
      // Upload file
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder', 'spv-documents');
      formData.append('fieldName', fieldName);

      const uploadResponse = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!uploadResponse.ok) {
        const errorData = await uploadResponse.json();
        throw new Error(errorData.error || 'Failed to upload file');
      }

      const uploadResult = await uploadResponse.json();
      console.log("Upload result:", uploadResult);

      // Save document metadata
      const documentData = {
        documentType,
        fileName: file.name,
        fileUrl: uploadResult.url,
        fileSize: file.size,
        mimeType: file.type,
        notes: `Uploaded via comprehensive documents tab`,
      };

      console.log("Sending document data:", documentData);

      const documentResponse = await fetch(`/api/organizations/spvs/${spv.id}/documents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(documentData),
      });

      if (!documentResponse.ok) {
        const errorData = await documentResponse.json();
        throw new Error(errorData.error || 'Failed to save document');
      }

      // Update upload tracking
      setDocumentUploads(prev => prev.map(upload =>
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, url: uploadResult.url }
          : upload
      ));

      toast.success(`${file.name} uploaded successfully`);

      // Clear upload tracking for this field after successful upload
      setDocumentUploads(prev => prev.filter(upload => upload.fieldName !== fieldName));

      // Refresh SPV data to show the new document
      onDocumentUpdate?.();
    } catch (error) {
      console.error("Document upload error:", error);

      // Update upload tracking with error
      setDocumentUploads(prev => prev.map(upload =>
        upload.fieldName === fieldName && upload.uploading
          ? { ...upload, uploading: false, error: error instanceof Error ? error.message : 'Upload failed' }
          : upload
      ));

      toast.error(`Failed to upload ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }, [spv.id, onDocumentUpdate]);

  // Handle document deletion
  const handleDocumentDelete = async (document: SPVDocument) => {
    try {
      const response = await fetch(`/api/organizations/spvs/${spv.id}/documents/${document.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete document');
      }

      toast.success(`${document.fileName} deleted successfully`);
      setDeleteDialog({ isOpen: false, document: null });
      onDocumentUpdate?.();
    } catch (error) {
      console.error("Document deletion error:", error);
      toast.error(`Failed to delete document: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  // Calculate completion stats
  const totalDocuments = Object.values(documentCategories).reduce((acc, category) => acc + category.documents.length, 0);
  const requiredDocuments = Object.values(documentCategories).reduce((acc, category) => 
    acc + category.documents.filter(doc => doc.required).length, 0);
  const uploadedDocuments = Object.values(documentCategories).reduce((acc, category) => 
    acc + category.documents.filter(doc => getDocumentByType(doc.type)).length, 0);
  const uploadedRequiredDocuments = Object.values(documentCategories).reduce((acc, category) => 
    acc + category.documents.filter(doc => doc.required && getDocumentByType(doc.type)).length, 0);

  return (
    <TooltipProvider>
      <div className="space-y-6">
        {/* Overview Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">{uploadedDocuments}/{totalDocuments}</p>
                  <p className="text-xs text-muted-foreground">Total Documents</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div>
                  <p className="text-sm font-medium">{uploadedRequiredDocuments}/{requiredDocuments}</p>
                  <p className="text-xs text-muted-foreground">Required Documents</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Clock className="h-8 w-8 text-orange-500" />
                <div>
                  <p className="text-sm font-medium">{requiredDocuments - uploadedRequiredDocuments}</p>
                  <p className="text-xs text-muted-foreground">Pending Required</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <AlertCircle className="h-8 w-8 text-red-500" />
                <div>
                  <p className="text-sm font-medium">
                    {uploadedRequiredDocuments === requiredDocuments ? "Complete" : "Incomplete"}
                  </p>
                  <p className="text-xs text-muted-foreground">Verification Status</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Completion Alert */}
        {uploadedRequiredDocuments < requiredDocuments && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {requiredDocuments - uploadedRequiredDocuments} required document(s) are still pending upload.
              Please upload all required documents to complete your SPV verification.
            </AlertDescription>
          </Alert>
        )}

        {/* Document Categories */}
        {Object.entries(documentCategories).map(([categoryKey, category]) => (
          <Card key={categoryKey}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {category.title}
              </CardTitle>
              <p className="text-sm text-muted-foreground">{category.description}</p>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {category.documents.map((docConfig) => {
                  const existingDocument = getDocumentByType(docConfig.type);
                  const uploadStatus = getUploadStatus(docConfig.fieldName);

                  return (
                    <div key={docConfig.type} className="border rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium">{docConfig.label}</h4>
                            {docConfig.required && (
                              <Badge variant="destructive" className="text-xs">Required</Badge>
                            )}
                            <Tooltip>
                              <TooltipTrigger>
                                <Info className="h-4 w-4 text-muted-foreground" />
                              </TooltipTrigger>
                              <TooltipContent>
                                <p className="max-w-xs">{docConfig.description}</p>
                              </TooltipContent>
                            </Tooltip>
                          </div>

                          {/* Document Status */}
                          <div className="flex items-center gap-2 mb-3">
                            {existingDocument ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-sm text-green-600">Uploaded</span>
                                <span className="text-xs text-muted-foreground">
                                  {new Date(existingDocument.uploadedAt).toLocaleDateString()}
                                </span>
                              </>
                            ) : uploadStatus?.uploading ? (
                              <>
                                <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />
                                <span className="text-sm text-blue-600">Uploading...</span>
                              </>
                            ) : uploadStatus?.error ? (
                              <>
                                <AlertCircle className="h-4 w-4 text-red-600" />
                                <span className="text-sm text-red-600">Upload failed</span>
                              </>
                            ) : (
                              <>
                                <Clock className="h-4 w-4 text-orange-600" />
                                <span className="text-sm text-orange-600">Pending upload</span>
                              </>
                            )}
                          </div>

                          {/* Error Message */}
                          {uploadStatus?.error && (
                            <p className="text-sm text-red-600 mb-2">{uploadStatus.error}</p>
                          )}

                          {/* Document Info */}
                          {existingDocument && (
                            <div className="text-sm text-muted-foreground">
                              <p>File: {existingDocument.fileName}</p>
                              <p>Size: {existingDocument.fileSize ? `${(existingDocument.fileSize / 1024 / 1024).toFixed(2)} MB` : 'Unknown'}</p>
                            </div>
                          )}
                        </div>

                        {/* Actions */}
                        <div className="flex items-center gap-2">
                          {existingDocument ? (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => window.open(existingDocument.fileUrl, '_blank')}
                              >
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  const link = document.createElement('a');
                                  link.href = existingDocument.fileUrl;
                                  link.download = existingDocument.fileName;
                                  link.click();
                                }}
                              >
                                <Download className="h-4 w-4 mr-1" />
                                Download
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                className="text-red-600 border-red-600 hover:bg-red-50"
                                onClick={() => setDeleteDialog({ isOpen: true, document: existingDocument })}
                              >
                                <Trash2 className="h-4 w-4 mr-1" />
                                Delete
                              </Button>
                            </>
                          ) : (
                            <div>
                              <input
                                type="file"
                                id={`upload-${docConfig.fieldName}`}
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) {
                                    handleDocumentUpload(docConfig.fieldName, docConfig.type, file);
                                  }
                                }}
                                className="hidden"
                              />
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => document.getElementById(`upload-${docConfig.fieldName}`)?.click()}
                                disabled={uploadStatus?.uploading}
                              >
                                <Upload className="h-4 w-4 mr-1" />
                                Upload
                              </Button>
                            </div>
                          )}

                          {/* Replace option for existing documents */}
                          {existingDocument && (
                            <div>
                              <input
                                type="file"
                                id={`replace-${docConfig.fieldName}`}
                                accept=".pdf,.jpg,.jpeg,.png"
                                onChange={(e) => {
                                  const file = e.target.files?.[0];
                                  if (file) {
                                    handleDocumentUpload(docConfig.fieldName, docConfig.type, file);
                                  }
                                }}
                                className="hidden"
                              />
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => document.getElementById(`replace-${docConfig.fieldName}`)?.click()}
                                disabled={uploadStatus?.uploading}
                              >
                                <RefreshCw className="h-4 w-4 mr-1" />
                                Replace
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        ))}

        {/* Delete Confirmation Dialog */}
        <Dialog open={deleteDialog.isOpen} onOpenChange={(open) => {
          if (!open) {
            setDeleteDialog({ isOpen: false, document: null });
          }
        }}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Trash2 className="h-5 w-5 text-red-600" />
                Delete Document
              </DialogTitle>
              <DialogDescription>
                Are you sure you want to delete "{deleteDialog.document?.fileName}"? This action cannot be undone.
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-end gap-3 mt-6">
              <Button
                variant="outline"
                onClick={() => setDeleteDialog({ isOpen: false, document: null })}
              >
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  if (deleteDialog.document) {
                    handleDocumentDelete(deleteDialog.document);
                  }
                }}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Document
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}
