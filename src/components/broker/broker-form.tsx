"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { brokerCreationSchema, brokerUpdateSchema } from "@/lib/validation/schemas";
import { BrokerCreateData, BrokerUpdateData } from "@/types/broker";
import { toast } from "sonner";

// Export the schemas for reuse
export { brokerCreationSchema, brokerUpdateSchema };

interface BrokerFormProps {
  initialData?: Partial<BrokerCreateData>;
  onSubmit: (data: BrokerCreateData | BrokerUpdateData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  mode?: "create" | "edit";
  showStatusFields?: boolean;
}

export function BrokerForm({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = "create",
  showStatusFields = false,
}: BrokerFormProps) {
  const [activeTab, setActiveTab] = useState("basic");

  const form = useForm<BrokerCreateData | BrokerUpdateData>({
    resolver: zodResolver(mode === "create" ? brokerCreationSchema : brokerUpdateSchema),
    defaultValues: {
      name: "",
      legalName: "",
      description: "",
      website: "",
      email: "",
      phoneNumber: "",
      address: "",
      city: "",
      state: "",
      postalCode: "",
      country: "",
      licenseNumber: "",
      licenseType: "",
      licenseIssuer: "",
      licenseExpiryDate: "",
      registrationNumber: "",
      taxId: "",
      operatingModel: "INDEPENDENT",
      commissionRate: 0.02,
      minimumCommission: undefined,
      maximumCommission: undefined,
      specializations: [],
      servicesOffered: [],
      targetMarkets: [],
      bondAmount: undefined,
      insuranceAmount: undefined,
      creditRating: "",
      establishedDate: "",
      ...initialData,
    },
  });

  const handleSubmit = async (data: BrokerCreateData | BrokerUpdateData) => {
    try {
      await onSubmit(data);
      toast.success(mode === "create" ? "Broker created successfully" : "Broker updated successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "An error occurred");
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>{mode === "create" ? "Create New Broker" : "Edit Broker"}</CardTitle>
        <CardDescription>
          {mode === "create" 
            ? "Fill in the details to create a new broker profile" 
            : "Update the broker information below"
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="contact">Contact</TabsTrigger>
              <TabsTrigger value="license">License</TabsTrigger>
              <TabsTrigger value="business">Business</TabsTrigger>
            </TabsList>

            <TabsContent value="basic" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Broker Name *</Label>
                  <Input
                    id="name"
                    {...form.register("name")}
                    placeholder="Enter broker name"
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="legalName">Legal Name</Label>
                  <Input
                    id="legalName"
                    {...form.register("legalName")}
                    placeholder="Enter legal name"
                  />
                  {form.formState.errors.legalName && (
                    <p className="text-sm text-destructive">{form.formState.errors.legalName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    {...form.register("email")}
                    placeholder="Enter email address"
                  />
                  {form.formState.errors.email && (
                    <p className="text-sm text-destructive">{form.formState.errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    {...form.register("website")}
                    placeholder="https://example.com"
                  />
                  {form.formState.errors.website && (
                    <p className="text-sm text-destructive">{form.formState.errors.website.message}</p>
                  )}
                </div>

                <div className="md:col-span-2 space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...form.register("description")}
                    placeholder="Describe the broker's services and expertise"
                    rows={3}
                  />
                  {form.formState.errors.description && (
                    <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="contact" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    {...form.register("phoneNumber")}
                    placeholder="Enter phone number"
                  />
                  {form.formState.errors.phoneNumber && (
                    <p className="text-sm text-destructive">{form.formState.errors.phoneNumber.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    {...form.register("country")}
                    placeholder="Enter country"
                  />
                  {form.formState.errors.country && (
                    <p className="text-sm text-destructive">{form.formState.errors.country.message}</p>
                  )}
                </div>

                <div className="md:col-span-2 space-y-2">
                  <Label htmlFor="address">Address</Label>
                  <Input
                    id="address"
                    {...form.register("address")}
                    placeholder="Enter street address"
                  />
                  {form.formState.errors.address && (
                    <p className="text-sm text-destructive">{form.formState.errors.address.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    {...form.register("city")}
                    placeholder="Enter city"
                  />
                  {form.formState.errors.city && (
                    <p className="text-sm text-destructive">{form.formState.errors.city.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="state">State/Province</Label>
                  <Input
                    id="state"
                    {...form.register("state")}
                    placeholder="Enter state or province"
                  />
                  {form.formState.errors.state && (
                    <p className="text-sm text-destructive">{form.formState.errors.state.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="postalCode">Postal Code</Label>
                  <Input
                    id="postalCode"
                    {...form.register("postalCode")}
                    placeholder="Enter postal code"
                  />
                  {form.formState.errors.postalCode && (
                    <p className="text-sm text-destructive">{form.formState.errors.postalCode.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="license" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="licenseNumber">License Number</Label>
                  <Input
                    id="licenseNumber"
                    {...form.register("licenseNumber")}
                    placeholder="Enter license number"
                  />
                  {form.formState.errors.licenseNumber && (
                    <p className="text-sm text-destructive">{form.formState.errors.licenseNumber.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="licenseType">License Type</Label>
                  <Input
                    id="licenseType"
                    {...form.register("licenseType")}
                    placeholder="Enter license type"
                  />
                  {form.formState.errors.licenseType && (
                    <p className="text-sm text-destructive">{form.formState.errors.licenseType.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="licenseIssuer">License Issuer</Label>
                  <Input
                    id="licenseIssuer"
                    {...form.register("licenseIssuer")}
                    placeholder="Enter issuing authority"
                  />
                  {form.formState.errors.licenseIssuer && (
                    <p className="text-sm text-destructive">{form.formState.errors.licenseIssuer.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="licenseExpiryDate">License Expiry Date</Label>
                  <Input
                    id="licenseExpiryDate"
                    type="date"
                    {...form.register("licenseExpiryDate")}
                  />
                  {form.formState.errors.licenseExpiryDate && (
                    <p className="text-sm text-destructive">{form.formState.errors.licenseExpiryDate.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="registrationNumber">Registration Number</Label>
                  <Input
                    id="registrationNumber"
                    {...form.register("registrationNumber")}
                    placeholder="Enter registration number"
                  />
                  {form.formState.errors.registrationNumber && (
                    <p className="text-sm text-destructive">{form.formState.errors.registrationNumber.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="taxId">Tax ID</Label>
                  <Input
                    id="taxId"
                    {...form.register("taxId")}
                    placeholder="Enter tax identification number"
                  />
                  {form.formState.errors.taxId && (
                    <p className="text-sm text-destructive">{form.formState.errors.taxId.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="business" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="operatingModel">Operating Model</Label>
                  <Select
                    value={form.watch("operatingModel")}
                    onValueChange={(value) => form.setValue("operatingModel", value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select operating model" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="INDEPENDENT">Independent</SelectItem>
                      <SelectItem value="ONIX_MANAGED">Onix Managed</SelectItem>
                      <SelectItem value="HYBRID">Hybrid</SelectItem>
                    </SelectContent>
                  </Select>
                  {form.formState.errors.operatingModel && (
                    <p className="text-sm text-destructive">{form.formState.errors.operatingModel.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="commissionRate">Commission Rate (%)</Label>
                  <Input
                    id="commissionRate"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    {...form.register("commissionRate", { valueAsNumber: true })}
                    placeholder="2.00"
                  />
                  {form.formState.errors.commissionRate && (
                    <p className="text-sm text-destructive">{form.formState.errors.commissionRate.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minimumCommission">Minimum Commission</Label>
                  <Input
                    id="minimumCommission"
                    type="number"
                    step="0.01"
                    min="0"
                    {...form.register("minimumCommission", { valueAsNumber: true })}
                    placeholder="Enter minimum commission"
                  />
                  {form.formState.errors.minimumCommission && (
                    <p className="text-sm text-destructive">{form.formState.errors.minimumCommission.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maximumCommission">Maximum Commission</Label>
                  <Input
                    id="maximumCommission"
                    type="number"
                    step="0.01"
                    min="0"
                    {...form.register("maximumCommission", { valueAsNumber: true })}
                    placeholder="Enter maximum commission"
                  />
                  {form.formState.errors.maximumCommission && (
                    <p className="text-sm text-destructive">{form.formState.errors.maximumCommission.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="establishedDate">Established Date</Label>
                  <Input
                    id="establishedDate"
                    type="date"
                    {...form.register("establishedDate")}
                  />
                  {form.formState.errors.establishedDate && (
                    <p className="text-sm text-destructive">{form.formState.errors.establishedDate.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="creditRating">Credit Rating</Label>
                  <Input
                    id="creditRating"
                    {...form.register("creditRating")}
                    placeholder="Enter credit rating"
                  />
                  {form.formState.errors.creditRating && (
                    <p className="text-sm text-destructive">{form.formState.errors.creditRating.message}</p>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>

          <div className="flex justify-end space-x-2 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : mode === "create" ? "Create Broker" : "Update Broker"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
