"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Edit, Trash2, Eye, ArrowUpDown, Search, Filter, Shield, CheckCircle, XCircle, Clock } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator, DropdownMenuLabel } from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BrokerWithOrganization, BrokerQueryParams } from "@/types/broker";
import { brokerUtils } from "@/lib/api/broker";

interface BrokerDataTableProps {
  brokers: BrokerWithOrganization[];
  pagination?: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters?: {
    search?: string;
    organizationId?: string;
    status?: string;
    operatingModel?: string;
    verificationStatus?: string;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
  onSort?: (sortBy: string) => void;
  onPageChange?: (page: number) => void;
  onEdit?: (broker: BrokerWithOrganization) => void;
  onDelete?: (broker: BrokerWithOrganization) => void;
  onView?: (broker: BrokerWithOrganization) => void;
  onVerificationChange?: (broker: BrokerWithOrganization, status: string) => void;
  showOrganization?: boolean;
  showActions?: boolean;
  showVerificationActions?: boolean;
  isLoading?: boolean;
}

export function BrokerDataTable({
  brokers,
  pagination,
  filters,
  onSort,
  onPageChange,
  onEdit,
  onDelete,
  onView,
  onVerificationChange,
  showOrganization = false,
  showActions = true,
  showVerificationActions = false,
  isLoading = false,
}: BrokerDataTableProps) {
  const handleSort = (sortBy: string) => {
    if (onSort) {
      onSort(sortBy);
    }
  };

  const getSortIcon = (column: string) => {
    if (filters?.sortBy === column) {
      return filters.sortOrder === "asc" ? "↑" : "↓";
    }
    return null;
  };

  const renderPagination = () => {
    if (!pagination || !onPageChange) return null;

    const { page, totalPages, hasNext, hasPrev } = pagination;
    const pages = [];
    
    // Calculate page range to show
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return (
      <div className="flex items-center justify-between px-2">
        <div className="flex items-center space-x-6 lg:space-x-8">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">
              Page {page} of {totalPages}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(page - 1)}
            disabled={!hasPrev}
          >
            Previous
          </Button>
          {pages.map((pageNum) => (
            <Button
              key={pageNum}
              variant={pageNum === page ? "default" : "outline"}
              size="sm"
              onClick={() => onPageChange(pageNum)}
            >
              {pageNum}
            </Button>
          ))}
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(page + 1)}
            disabled={!hasNext}
          >
            Next
          </Button>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading brokers...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (brokers.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h3 className="text-lg font-semibold mb-2">No brokers found</h3>
            <p className="text-muted-foreground">
              {filters?.search ? "Try adjusting your search criteria" : "No brokers have been created yet"}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Brokers</CardTitle>
        {pagination && (
          <CardDescription>
            Showing {brokers.length} of {pagination.totalCount} brokers
          </CardDescription>
        )}
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center space-x-1">
                    <span>Name</span>
                    <ArrowUpDown className="h-4 w-4" />
                    {getSortIcon("name") && <span className="text-xs">{getSortIcon("name")}</span>}
                  </div>
                </TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Operating Model</TableHead>
                <TableHead>Commission Rate</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("status")}
                >
                  <div className="flex items-center space-x-1">
                    <span>Status</span>
                    <ArrowUpDown className="h-4 w-4" />
                    {getSortIcon("status") && <span className="text-xs">{getSortIcon("status")}</span>}
                  </div>
                </TableHead>
                <TableHead>Verification</TableHead>
                {showOrganization && (
                  <TableHead 
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort("organization")}
                  >
                    <div className="flex items-center space-x-1">
                      <span>Organization</span>
                      <ArrowUpDown className="h-4 w-4" />
                      {getSortIcon("organization") && <span className="text-xs">{getSortIcon("organization")}</span>}
                    </div>
                  </TableHead>
                )}
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("createdAt")}
                >
                  <div className="flex items-center space-x-1">
                    <span>Created</span>
                    <ArrowUpDown className="h-4 w-4" />
                    {getSortIcon("createdAt") && <span className="text-xs">{getSortIcon("createdAt")}</span>}
                  </div>
                </TableHead>
                {showActions && <TableHead className="w-[70px]">Actions</TableHead>}
              </TableRow>
            </TableHeader>
            <TableBody>
              {brokers.map((broker) => (
                <TableRow key={broker.id}>
                  <TableCell className="font-medium">
                    <div>
                      <div className="font-semibold">{broker.name}</div>
                      {broker.legalName && (
                        <div className="text-sm text-muted-foreground">{broker.legalName}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{broker.email}</TableCell>
                  <TableCell>
                    <Badge variant="outline">
                      {brokerUtils.formatOperatingModel(broker.operatingModel)}
                    </Badge>
                  </TableCell>
                  <TableCell>{brokerUtils.formatCommissionRate(broker.commissionRate)}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={`border-${brokerUtils.getStatusColor(broker.status)}-500 text-${brokerUtils.getStatusColor(broker.status)}-700`}>
                      {brokerUtils.formatStatus(broker.status)}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={`border-${brokerUtils.getVerificationStatusColor(broker.verificationStatus)}-500 text-${brokerUtils.getVerificationStatusColor(broker.verificationStatus)}-700`}>
                      {brokerUtils.formatVerificationStatus(broker.verificationStatus)}
                    </Badge>
                  </TableCell>
                  {showOrganization && (
                    <TableCell>
                      {broker.organization ? (
                        <div>
                          <div className="font-medium">{broker.organization.name}</div>
                          {broker.organization.legalName && (
                            <div className="text-sm text-muted-foreground">{broker.organization.legalName}</div>
                          )}
                        </div>
                      ) : (
                        <span className="text-muted-foreground">Independent</span>
                      )}
                    </TableCell>
                  )}
                  <TableCell>
                    {new Date(broker.createdAt).toLocaleDateString()}
                  </TableCell>
                  {showActions && (
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          {onView && (
                            <DropdownMenuItem onClick={() => onView(broker)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                          )}
                          {onEdit && (
                            <DropdownMenuItem onClick={() => onEdit(broker)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          )}

                          {showVerificationActions && onVerificationChange && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuLabel>Verification</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() => onVerificationChange(broker, "VERIFIED")}
                                disabled={broker.verificationStatus === "VERIFIED"}
                              >
                                <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                Mark as Verified
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => onVerificationChange(broker, "IN_REVIEW")}
                                disabled={broker.verificationStatus === "IN_REVIEW"}
                              >
                                <Clock className="mr-2 h-4 w-4 text-blue-500" />
                                Mark as In Review
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() => onVerificationChange(broker, "REJECTED")}
                                disabled={broker.verificationStatus === "REJECTED"}
                              >
                                <XCircle className="mr-2 h-4 w-4 text-red-500" />
                                Reject Verification
                              </DropdownMenuItem>
                            </>
                          )}

                          {onDelete && (
                            <>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => onDelete(broker)}
                                className="text-destructive focus:text-destructive"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  )}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        {pagination && (
          <div className="mt-4">
            {renderPagination()}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
