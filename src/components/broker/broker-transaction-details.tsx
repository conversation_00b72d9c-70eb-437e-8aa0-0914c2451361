"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  User, 
  Building2, 
  DollarSign, 
  Percent,
  Calendar,
  FileText,
  Hash,
  Activity
} from "lucide-react";

interface BrokerTransactionDetailsProps {
  transaction: any;
}

export function BrokerTransactionDetails({ transaction }: BrokerTransactionDetailsProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "processing":
        return "bg-blue-100 text-blue-800";
      case "failed":
        return "bg-red-100 text-red-800";
      case "cancelled":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "CARBON_CREDIT_SALE":
        return "bg-green-100 text-green-800";
      case "CARBON_CREDIT_PURCHASE":
        return "bg-blue-100 text-blue-800";
      case "PROJECT_INVESTMENT":
        return "bg-purple-100 text-purple-800";
      case "CONSULTATION":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatTransactionType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  return (
    <div className="space-y-6">
      {/* Transaction Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-xl">Transaction Details</CardTitle>
                <CardDescription>Transaction ID: {transaction.id}</CardDescription>
              </div>
            </div>
            <div className="flex space-x-2">
              <Badge className={getTypeColor(transaction.transactionType)}>
                {formatTransactionType(transaction.transactionType)}
              </Badge>
              <Badge className={getStatusColor(transaction.status)}>
                {transaction.status}
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Transaction Amount</span>
              </div>
              <p className="text-2xl font-bold">${transaction.amount.toLocaleString()}</p>
              <p className="text-xs text-gray-500">{transaction.currency}</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Percent className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Commission Rate</span>
              </div>
              <p className="text-2xl font-bold">{(transaction.commissionRate * 100).toFixed(1)}%</p>
              <p className="text-xs text-gray-500">Applied rate</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Commission Amount</span>
              </div>
              <p className="text-2xl font-bold text-green-600">${transaction.commissionAmount.toLocaleString()}</p>
              <p className="text-xs text-gray-500">Earned commission</p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm text-gray-600">Transaction Date</span>
              </div>
              <p className="text-lg font-medium">{new Date(transaction.createdAt).toLocaleDateString()}</p>
              <p className="text-xs text-gray-500">{new Date(transaction.createdAt).toLocaleTimeString()}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Client Information */}
      {transaction.brokerClient && (
        <Card>
          <CardHeader>
            <CardTitle>Client Information</CardTitle>
            <CardDescription>
              Details about the client involved in this transaction
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-4 mb-6">
              <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                {transaction.brokerClient.clientType === "ORGANIZATION" ? (
                  <Building2 className="h-6 w-6 text-gray-600" />
                ) : (
                  <User className="h-6 w-6 text-gray-600" />
                )}
              </div>
              <div>
                <h3 className="text-lg font-medium">{transaction.brokerClient.clientName}</h3>
                <p className="text-gray-600">{transaction.brokerClient.clientEmail}</p>
                <Badge className="mt-1">
                  {transaction.brokerClient.clientType}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transaction Details */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Transaction Information</CardTitle>
            <CardDescription>
              Additional details about this transaction
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {transaction.description && (
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <FileText className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-900">Description</span>
                </div>
                <p className="text-sm text-gray-600 pl-6">{transaction.description}</p>
              </div>
            )}

            {transaction.referenceId && (
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Hash className="h-4 w-4 text-gray-400" />
                  <span className="text-sm font-medium text-gray-900">Reference ID</span>
                </div>
                <p className="text-sm text-gray-600 pl-6">{transaction.referenceId}</p>
              </div>
            )}

            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-900">Created</span>
              </div>
              <p className="text-sm text-gray-600 pl-6">
                {new Date(transaction.createdAt).toLocaleString()}
              </p>
            </div>

            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Calendar className="h-4 w-4 text-gray-400" />
                <span className="text-sm font-medium text-gray-900">Last Updated</span>
              </div>
              <p className="text-sm text-gray-600 pl-6">
                {new Date(transaction.updatedAt).toLocaleString()}
              </p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Commission Breakdown</CardTitle>
            <CardDescription>
              How the commission was calculated
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Base Amount</span>
                <span className="font-medium">${transaction.amount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Commission Rate</span>
                <span className="font-medium">{(transaction.commissionRate * 100).toFixed(1)}%</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-900">Commission Earned</span>
                  <span className="font-bold text-green-600">${transaction.commissionAmount.toLocaleString()}</span>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Calculation</h4>
              <p className="text-xs text-gray-600">
                ${transaction.amount.toLocaleString()} × {(transaction.commissionRate * 100).toFixed(1)}% = ${transaction.commissionAmount.toLocaleString()}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Metadata */}
      {transaction.metadata && Object.keys(transaction.metadata).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Additional Information</CardTitle>
            <CardDescription>
              Extra metadata associated with this transaction
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(transaction.metadata).map(([key, value]) => (
                <div key={key}>
                  <span className="text-sm font-medium text-gray-900 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </span>
                  <p className="text-sm text-gray-600 mt-1">
                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Transaction History */}
      <Card>
        <CardHeader>
          <CardTitle>Transaction History</CardTitle>
          <CardDescription>
            Status changes and updates for this transaction
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 border rounded-lg">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Activity className="h-4 w-4 text-blue-600" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium">Transaction Created</p>
                <p className="text-xs text-gray-500">
                  {new Date(transaction.createdAt).toLocaleString()}
                </p>
              </div>
              <Badge className={getStatusColor(transaction.status)}>
                {transaction.status}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
