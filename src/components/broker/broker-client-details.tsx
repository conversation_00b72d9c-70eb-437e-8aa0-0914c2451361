"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  User, 
  Building2, 
  Mail, 
  Phone, 
  Calendar, 
  Percent,
  FileText,
  Activity,
  DollarSign
} from "lucide-react";

interface BrokerClientDetailsProps {
  client: any;
}

export function BrokerClientDetails({ client }: BrokerClientDetailsProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "suspended":
        return "bg-yellow-100 text-yellow-800";
      case "terminated":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "organization":
        return "bg-blue-100 text-blue-800";
      case "individual":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Client Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  {client.clientType === "ORGANIZATION" ? (
                    <Building2 className="h-6 w-6 text-blue-600" />
                  ) : (
                    <User className="h-6 w-6 text-blue-600" />
                  )}
                </div>
                <div>
                  <CardTitle className="text-xl">{client.clientName}</CardTitle>
                  <CardDescription>{client.clientEmail}</CardDescription>
                </div>
              </div>
              <div className="flex space-x-2">
                <Badge className={getTypeColor(client.clientType)}>
                  {client.clientType}
                </Badge>
                <Badge className={getStatusColor(client.status)}>
                  {client.status}
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Contact Information</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">{client.clientEmail}</span>
                  </div>
                  {client.clientPhone && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{client.clientPhone}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Relationship Details</h4>
                <div className="space-y-3">
                  {client.relationshipType && (
                    <div className="flex items-center space-x-3">
                      <FileText className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{client.relationshipType}</span>
                    </div>
                  )}
                  {client.commissionRate && (
                    <div className="flex items-center space-x-3">
                      <Percent className="h-4 w-4 text-gray-400" />
                      <span className="text-sm">{(client.commissionRate * 100).toFixed(1)}% Commission</span>
                    </div>
                  )}
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-sm">
                      Client since {new Date(client.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {client.notes && (
              <div className="mt-6 pt-6 border-t">
                <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                <p className="text-sm text-gray-600">{client.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">Total Transactions</span>
                </div>
                <span className="font-medium">0</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Activity className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">Total Commission</span>
                </div>
                <span className="font-medium">$0</span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-600">Last Activity</span>
                </div>
                <span className="font-medium text-sm">Never</span>
              </div>
            </CardContent>
          </Card>

          {client.contractStartDate && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium">Contract Period</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <span className="text-xs text-gray-500">Start Date</span>
                  <p className="text-sm font-medium">
                    {new Date(client.contractStartDate).toLocaleDateString()}
                  </p>
                </div>
                {client.contractEndDate && (
                  <div>
                    <span className="text-xs text-gray-500">End Date</span>
                    <p className="text-sm font-medium">
                      {new Date(client.contractEndDate).toLocaleDateString()}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Organization Details (if applicable) */}
      {client.organization && (
        <Card>
          <CardHeader>
            <CardTitle>Organization Details</CardTitle>
            <CardDescription>
              Information about the client's organization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Organization Name</h4>
                <p className="text-sm text-gray-600">{client.organization.name}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Organization Type</h4>
                <p className="text-sm text-gray-600">{client.organization.type}</p>
              </div>
              {client.organization.email && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Organization Email</h4>
                  <p className="text-sm text-gray-600">{client.organization.email}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* User Details (if applicable) */}
      {client.user && (
        <Card>
          <CardHeader>
            <CardTitle>User Details</CardTitle>
            <CardDescription>
              Information about the individual user
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Full Name</h4>
                <p className="text-sm text-gray-600">{client.user.name}</p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Email Address</h4>
                <p className="text-sm text-gray-600">{client.user.email}</p>
              </div>
              {client.user.phone && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Phone Number</h4>
                  <p className="text-sm text-gray-600">{client.user.phone}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            Recent transactions and interactions with this client
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <h3 className="text-lg font-medium mb-2">No Recent Activity</h3>
            <p className="text-gray-600">No transactions or activities found for this client.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
