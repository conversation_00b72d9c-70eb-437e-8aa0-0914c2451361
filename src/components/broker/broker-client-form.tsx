"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useBrokerClientMutations } from "@/hooks/use-broker-clients";

const brokerClientSchema = z.object({
  clientType: z.enum(["ORGANIZATION", "INDIVIDUAL"]),
  clientName: z.string().min(2, "Client name must be at least 2 characters"),
  clientEmail: z.string().email("Invalid email address"),
  clientPhone: z.string().optional(),
  relationshipType: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  contractStartDate: z.string().optional(),
  contractEndDate: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "TERMINATED"]).optional(),
});

type BrokerClientFormData = z.infer<typeof brokerClientSchema>;

interface BrokerClientFormProps {
  client?: any;
  onSuccess: () => void;
  onCancel: () => void;
}

export function BrokerClientForm({ client, onSuccess, onCancel }: BrokerClientFormProps) {
  const { toast } = useToast();
  const { createClient, updateClient, isCreating, isUpdating } = useBrokerClientMutations();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!client;
  const isLoading = isCreating || isUpdating || isSubmitting;

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<BrokerClientFormData>({
    resolver: zodResolver(brokerClientSchema),
    defaultValues: {
      clientType: client?.clientType || "ORGANIZATION",
      clientName: client?.clientName || "",
      clientEmail: client?.clientEmail || "",
      clientPhone: client?.clientPhone || "",
      relationshipType: client?.relationshipType || "",
      commissionRate: client?.commissionRate || undefined,
      contractStartDate: client?.contractStartDate ? client.contractStartDate.split('T')[0] : "",
      contractEndDate: client?.contractEndDate ? client.contractEndDate.split('T')[0] : "",
      notes: client?.notes || "",
      status: client?.status || "ACTIVE",
    },
  });

  const clientType = watch("clientType");

  const onSubmit = async (data: BrokerClientFormData) => {
    try {
      setIsSubmitting(true);

      const submitData = {
        ...data,
        commissionRate: data.commissionRate ? Number(data.commissionRate) : undefined,
      };

      if (isEditing) {
        await updateClient(client.id, submitData);
        toast({
          title: "Client Updated",
          description: "Client information has been successfully updated.",
        });
      } else {
        await createClient(submitData);
        toast({
          title: "Client Created",
          description: "New client has been successfully created.",
        });
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving client:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: isEditing ? "Failed to update client." : "Failed to create client.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Client" : "Add New Client"}</CardTitle>
        <CardDescription>
          {isEditing ? "Update client information." : "Create a new client relationship."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientType">Client Type</Label>
                <Select
                  value={clientType}
                  onValueChange={(value) => setValue("clientType", value as "ORGANIZATION" | "INDIVIDUAL")}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select client type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ORGANIZATION">Organization</SelectItem>
                    <SelectItem value="INDIVIDUAL">Individual</SelectItem>
                  </SelectContent>
                </Select>
                {errors.clientType && (
                  <p className="text-sm text-red-600 mt-1">{errors.clientType.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={watch("status")}
                  onValueChange={(value) => setValue("status", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ACTIVE">Active</SelectItem>
                    <SelectItem value="INACTIVE">Inactive</SelectItem>
                    <SelectItem value="SUSPENDED">Suspended</SelectItem>
                    <SelectItem value="TERMINATED">Terminated</SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-600 mt-1">{errors.status.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientName">
                  {clientType === "ORGANIZATION" ? "Organization Name" : "Full Name"}
                </Label>
                <Input
                  id="clientName"
                  {...register("clientName")}
                  placeholder={clientType === "ORGANIZATION" ? "Enter organization name" : "Enter full name"}
                />
                {errors.clientName && (
                  <p className="text-sm text-red-600 mt-1">{errors.clientName.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="clientEmail">Email Address</Label>
                <Input
                  id="clientEmail"
                  type="email"
                  {...register("clientEmail")}
                  placeholder="Enter email address"
                />
                {errors.clientEmail && (
                  <p className="text-sm text-red-600 mt-1">{errors.clientEmail.message}</p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="clientPhone">Phone Number (Optional)</Label>
                <Input
                  id="clientPhone"
                  {...register("clientPhone")}
                  placeholder="Enter phone number"
                />
                {errors.clientPhone && (
                  <p className="text-sm text-red-600 mt-1">{errors.clientPhone.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="relationshipType">Relationship Type (Optional)</Label>
                <Input
                  id="relationshipType"
                  {...register("relationshipType")}
                  placeholder="e.g., Primary Broker, Consultant"
                />
                {errors.relationshipType && (
                  <p className="text-sm text-red-600 mt-1">{errors.relationshipType.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Commission and Contract */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Commission and Contract</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="commissionRate">Commission Rate (Optional)</Label>
                <Input
                  id="commissionRate"
                  type="number"
                  step="0.001"
                  min="0"
                  max="1"
                  {...register("commissionRate", { valueAsNumber: true })}
                  placeholder="0.025 (2.5%)"
                />
                {errors.commissionRate && (
                  <p className="text-sm text-red-600 mt-1">{errors.commissionRate.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="contractStartDate">Contract Start Date (Optional)</Label>
                <Input
                  id="contractStartDate"
                  type="date"
                  {...register("contractStartDate")}
                />
                {errors.contractStartDate && (
                  <p className="text-sm text-red-600 mt-1">{errors.contractStartDate.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="contractEndDate">Contract End Date (Optional)</Label>
                <Input
                  id="contractEndDate"
                  type="date"
                  {...register("contractEndDate")}
                />
                {errors.contractEndDate && (
                  <p className="text-sm text-red-600 mt-1">{errors.contractEndDate.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Notes */}
          <div>
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              {...register("notes")}
              placeholder="Add any additional notes about this client..."
              rows={3}
            />
            {errors.notes && (
              <p className="text-sm text-red-600 mt-1">{errors.notes.message}</p>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : isEditing ? "Update Client" : "Create Client"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
