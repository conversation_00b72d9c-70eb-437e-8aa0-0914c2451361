"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { useBrokerTransactionMutations } from "@/hooks/use-broker-transactions";
import { useBrokerClients } from "@/hooks/use-broker-clients";

const brokerTransactionSchema = z.object({
  clientId: z.string().min(1, "Client is required"),
  transactionType: z.enum(["CARBON_CREDIT_SALE", "CARBON_CREDIT_PURCHASE", "PROJECT_INVESTMENT", "CONSULTATION", "OTHER"]),
  amount: z.number().min(0.01, "Amount must be greater than 0"),
  currency: z.string().default("USD"),
  commissionRate: z.number().min(0).max(1, "Commission rate must be between 0 and 1"),
  description: z.string().optional(),
  referenceId: z.string().optional(),
});

type BrokerTransactionFormData = z.infer<typeof brokerTransactionSchema>;

interface BrokerTransactionFormProps {
  transaction?: any;
  onSuccess: () => void;
  onCancel: () => void;
}

export function BrokerTransactionForm({ transaction, onSuccess, onCancel }: BrokerTransactionFormProps) {
  const { toast } = useToast();
  const { createTransaction, updateTransaction, isCreating, isUpdating } = useBrokerTransactionMutations();
  const { clients } = useBrokerClients();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!transaction;
  const isLoading = isCreating || isUpdating || isSubmitting;

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<BrokerTransactionFormData>({
    resolver: zodResolver(brokerTransactionSchema),
    defaultValues: {
      clientId: transaction?.clientId || "",
      transactionType: transaction?.transactionType || "CARBON_CREDIT_SALE",
      amount: transaction?.amount || 0,
      currency: transaction?.currency || "USD",
      commissionRate: transaction?.commissionRate || 0.025,
      description: transaction?.description || "",
      referenceId: transaction?.referenceId || "",
    },
  });

  const selectedClientId = watch("clientId");
  const amount = watch("amount");
  const commissionRate = watch("commissionRate");

  // Calculate commission amount
  const commissionAmount = amount && commissionRate ? amount * commissionRate : 0;

  // Set default commission rate when client changes
  useEffect(() => {
    if (selectedClientId && clients) {
      const selectedClient = clients.find(client => client.id === selectedClientId);
      if (selectedClient && selectedClient.commissionRate) {
        setValue("commissionRate", selectedClient.commissionRate);
      }
    }
  }, [selectedClientId, clients, setValue]);

  const onSubmit = async (data: BrokerTransactionFormData) => {
    try {
      setIsSubmitting(true);

      if (isEditing) {
        await updateTransaction(transaction.id, data);
        toast({
          title: "Transaction Updated",
          description: "Transaction has been successfully updated.",
        });
      } else {
        await createTransaction(data);
        toast({
          title: "Transaction Created",
          description: "New transaction has been successfully created.",
        });
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving transaction:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: isEditing ? "Failed to update transaction." : "Failed to create transaction.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const transactionTypes = [
    { value: "CARBON_CREDIT_SALE", label: "Carbon Credit Sale" },
    { value: "CARBON_CREDIT_PURCHASE", label: "Carbon Credit Purchase" },
    { value: "PROJECT_INVESTMENT", label: "Project Investment" },
    { value: "CONSULTATION", label: "Consultation" },
    { value: "OTHER", label: "Other" },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>{isEditing ? "Edit Transaction" : "Create New Transaction"}</CardTitle>
        <CardDescription>
          {isEditing ? "Update transaction information." : "Record a new transaction for a client."}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Client and Transaction Type */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="clientId">Client</Label>
              <Select
                value={watch("clientId")}
                onValueChange={(value) => setValue("clientId", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a client" />
                </SelectTrigger>
                <SelectContent>
                  {clients?.map((client) => (
                    <SelectItem key={client.id} value={client.id}>
                      {client.clientName} ({client.clientEmail})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.clientId && (
                <p className="text-sm text-red-600 mt-1">{errors.clientId.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="transactionType">Transaction Type</Label>
              <Select
                value={watch("transactionType")}
                onValueChange={(value) => setValue("transactionType", value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select transaction type" />
                </SelectTrigger>
                <SelectContent>
                  {transactionTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.transactionType && (
                <p className="text-sm text-red-600 mt-1">{errors.transactionType.message}</p>
              )}
            </div>
          </div>

          {/* Amount and Currency */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="amount">Transaction Amount</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                {...register("amount", { valueAsNumber: true })}
                placeholder="Enter transaction amount"
              />
              {errors.amount && (
                <p className="text-sm text-red-600 mt-1">{errors.amount.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select
                value={watch("currency")}
                onValueChange={(value) => setValue("currency", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Currency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="GBP">GBP</SelectItem>
                  <SelectItem value="CAD">CAD</SelectItem>
                </SelectContent>
              </Select>
              {errors.currency && (
                <p className="text-sm text-red-600 mt-1">{errors.currency.message}</p>
              )}
            </div>
          </div>

          {/* Commission */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="commissionRate">Commission Rate</Label>
              <Input
                id="commissionRate"
                type="number"
                step="0.001"
                min="0"
                max="1"
                {...register("commissionRate", { valueAsNumber: true })}
                placeholder="0.025 (2.5%)"
              />
              {errors.commissionRate && (
                <p className="text-sm text-red-600 mt-1">{errors.commissionRate.message}</p>
              )}
            </div>

            <div>
              <Label>Commission Amount</Label>
              <div className="h-10 px-3 py-2 border border-gray-200 rounded-md bg-gray-50 flex items-center">
                <span className="text-sm text-gray-600">
                  ${commissionAmount.toFixed(2)} ({(commissionRate * 100).toFixed(1)}%)
                </span>
              </div>
            </div>
          </div>

          {/* Description and Reference */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder="Enter transaction description..."
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="referenceId">Reference ID (Optional)</Label>
              <Input
                id="referenceId"
                {...register("referenceId")}
                placeholder="Enter reference ID or external transaction ID"
              />
              {errors.referenceId && (
                <p className="text-sm text-red-600 mt-1">{errors.referenceId.message}</p>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <Button type="button" variant="outline" onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? "Saving..." : isEditing ? "Update Transaction" : "Create Transaction"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
