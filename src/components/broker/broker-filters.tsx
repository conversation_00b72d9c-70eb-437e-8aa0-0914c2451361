"use client";

import { useState } from "react";
import { Search, Filter, X, Building2, TrendingUp, Shield } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BrokerQueryParams } from "@/types/broker";

interface BrokerFiltersProps {
  filters: {
    search?: string;
    organizationId?: string;
    status?: string;
    operatingModel?: string;
    verificationStatus?: string;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
  organizations?: {
    id: string;
    name: string;
    legalName: string | null;
  }[];
  onFiltersChange: (filters: Partial<BrokerQueryParams>) => void;
  showOrganizationFilter?: boolean;
}

interface BrokerStatsProps {
  stats?: {
    total: number;
    active: number;
    inactive: number;
    suspended: number;
  };
}

export function BrokerStats({ stats }: BrokerStatsProps) {
  if (!stats) return null;

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Brokers</CardTitle>
          <Building2 className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.total}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Active</CardTitle>
          <TrendingUp className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">{stats.active}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Inactive</CardTitle>
          <Shield className="h-4 w-4 text-yellow-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-600">{stats.inactive}</div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Suspended</CardTitle>
          <X className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">{stats.suspended}</div>
        </CardContent>
      </Card>
    </div>
  );
}

export function BrokerFilters({
  filters,
  organizations = [],
  onFiltersChange,
  showOrganizationFilter = false,
}: BrokerFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [localFilters, setLocalFilters] = useState(filters);

  const handleFilterChange = (key: string, value: string | undefined) => {
    const newFilters = { ...localFilters, [key]: value };
    setLocalFilters(newFilters);
    onFiltersChange(newFilters);
  };

  const clearFilters = () => {
    const clearedFilters = {
      search: "",
      organizationId: "",
      status: "",
      operatingModel: "",
      verificationStatus: "",
      sortBy: "createdAt",
      sortOrder: "desc" as const,
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  const hasActiveFilters = !!(
    filters.search ||
    filters.organizationId ||
    filters.status ||
    filters.operatingModel ||
    filters.verificationStatus
  );

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
              {hasActiveFilters && (
                <Badge variant="secondary" className="ml-2">
                  {[
                    filters.search && "Search",
                    filters.organizationId && "Organization",
                    filters.status && "Status",
                    filters.operatingModel && "Model",
                    filters.verificationStatus && "Verification",
                  ].filter(Boolean).length} active
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Filter and search brokers
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <Button variant="outline" size="sm" onClick={clearFilters}>
                <X className="h-4 w-4 mr-1" />
                Clear
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? "Less" : "More"} Filters
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="flex items-center space-x-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search brokers by name, email, or organization..."
            value={filters.search || ""}
            onChange={(e) => handleFilterChange("search", e.target.value)}
            className="flex-1"
          />
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2 items-center">
          <Select
            value={filters.status || "ALL"}
            onValueChange={(value) => handleFilterChange("status", value === "ALL" ? undefined : value)}
          >
            <SelectTrigger className="w-fit min-w-[120px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Statuses</SelectItem>
              <SelectItem value="ACTIVE">Active</SelectItem>
              <SelectItem value="INACTIVE">Inactive</SelectItem>
              <SelectItem value="SUSPENDED">Suspended</SelectItem>
              <SelectItem value="TERMINATED">Terminated</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.operatingModel || "ALL"}
            onValueChange={(value) => handleFilterChange("operatingModel", value === "ALL" ? undefined : value)}
          >
            <SelectTrigger className="w-fit min-w-[140px]">
              <SelectValue placeholder="Operating Model" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Models</SelectItem>
              <SelectItem value="FULL_SERVICE">Full Service</SelectItem>
              <SelectItem value="EXECUTION_ONLY">Execution Only</SelectItem>
              <SelectItem value="ADVISORY_ONLY">Advisory Only</SelectItem>
              <SelectItem value="HYBRID">Hybrid</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={filters.verificationStatus || "ALL"}
            onValueChange={(value) => handleFilterChange("verificationStatus", value === "ALL" ? undefined : value)}
          >
            <SelectTrigger className="w-fit min-w-[120px]">
              <SelectValue placeholder="Verification" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">All Verification</SelectItem>
              <SelectItem value="PENDING">Pending</SelectItem>
              <SelectItem value="VERIFIED">Verified</SelectItem>
              <SelectItem value="REJECTED">Rejected</SelectItem>
              <SelectItem value="SUSPENDED">Suspended</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={`${filters.sortBy}-${filters.sortOrder}`}
            onValueChange={(value) => {
              const [sortBy, sortOrder] = value.split("-");
              handleFilterChange("sortBy", sortBy);
              handleFilterChange("sortOrder", sortOrder);
            }}
          >
            <SelectTrigger className="w-fit min-w-[140px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name-asc">Name (A-Z)</SelectItem>
              <SelectItem value="name-desc">Name (Z-A)</SelectItem>
              <SelectItem value="createdAt-desc">Newest First</SelectItem>
              <SelectItem value="createdAt-asc">Oldest First</SelectItem>
              <SelectItem value="status-asc">Status (A-Z)</SelectItem>
              <SelectItem value="status-desc">Status (Z-A)</SelectItem>
              <SelectItem value="commissionRate-desc">Commission (High-Low)</SelectItem>
              <SelectItem value="commissionRate-asc">Commission (Low-High)</SelectItem>
              {showOrganizationFilter && (
                <SelectItem value="organization-asc">Organization (A-Z)</SelectItem>
              )}
              {showOrganizationFilter && (
                <SelectItem value="organization-desc">Organization (Z-A)</SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Expanded Filters */}
        {isExpanded && (
          <div className="space-y-4 pt-4 border-t">
            {showOrganizationFilter && organizations.length > 0 && (
              <div className="space-y-2">
                <Label htmlFor="organization">Organization</Label>
                <Select
                  value={filters.organizationId || "ALL"}
                  onValueChange={(value) => handleFilterChange("organizationId", value === "ALL" ? undefined : value)}
                >
                  <SelectTrigger className="w-fit min-w-[200px]">
                    <SelectValue placeholder="Select organization" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Organizations</SelectItem>
                    {organizations.map((org) => (
                      <SelectItem key={org.id} value={org.id}>
                        {org.name}
                        {org.legalName && org.legalName !== org.name && (
                          <span className="text-muted-foreground ml-1">({org.legalName})</span>
                        )}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
