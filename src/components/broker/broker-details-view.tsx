"use client";

import { useState } from "react";
import { Edit, Mail, Phone, MapPin, Building, CreditCard, Users, TrendingUp } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { BrokerWithDetails } from "@/types/broker";
import { brokerUtils } from "@/lib/api/broker";

interface BrokerDetailsViewProps {
  broker: BrokerWithDetails;
  onEdit?: () => void;
  showEditButton?: boolean;
}

export function BrokerDetailsView({
  broker,
  onEdit,
  showEditButton = true,
}: BrokerDetailsViewProps) {
  const [activeTab, setActiveTab] = useState("overview");

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not specified";
    return new Date(dateString).toLocaleDateString();
  };

  const formatCurrency = (amount: number | null) => {
    if (!amount) return "Not specified";
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <h1 className="text-2xl font-bold">{broker.name}</h1>
            <Badge 
              variant="outline" 
              className={`border-${brokerUtils.getStatusColor(broker.status)}-500 text-${brokerUtils.getStatusColor(broker.status)}-700`}
            >
              {brokerUtils.formatStatus(broker.status)}
            </Badge>
            <Badge 
              variant="outline" 
              className={`border-${brokerUtils.getVerificationStatusColor(broker.verificationStatus)}-500 text-${brokerUtils.getVerificationStatusColor(broker.verificationStatus)}-700`}
            >
              {brokerUtils.formatVerificationStatus(broker.verificationStatus)}
            </Badge>
          </div>
          {broker.legalName && (
            <p className="text-muted-foreground">Legal Name: {broker.legalName}</p>
          )}
          {broker.description && (
            <p className="text-muted-foreground max-w-2xl">{broker.description}</p>
          )}
        </div>
        {showEditButton && onEdit && (
          <Button onClick={onEdit}>
            <Edit className="mr-2 h-4 w-4" />
            Edit Broker
          </Button>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="contact">Contact & Location</TabsTrigger>
          <TabsTrigger value="license">License & Registration</TabsTrigger>
          <TabsTrigger value="business">Business Details</TabsTrigger>
          <TabsTrigger value="clients">Clients ({broker._count.clients})</TabsTrigger>
          <TabsTrigger value="transactions">Transactions ({broker._count.transactions})</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Operating Model</CardTitle>
                <Building className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {brokerUtils.formatOperatingModel(broker.operatingModel)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Commission Rate</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {brokerUtils.formatCommissionRate(broker.commissionRate)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Clients</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{broker._count.clients}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{broker._count.transactions}</div>
              </CardContent>
            </Card>
          </div>

          {broker.organization && (
            <Card>
              <CardHeader>
                <CardTitle>Organization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-medium">{broker.organization.name}</div>
                  {broker.organization.legalName && (
                    <div className="text-sm text-muted-foreground">
                      Legal Name: {broker.organization.legalName}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {broker.user && (
            <Card>
              <CardHeader>
                <CardTitle>Associated User</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-medium">{broker.user.name || "Unnamed User"}</div>
                  <div className="text-sm text-muted-foreground">{broker.user.email}</div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="contact" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{broker.email}</span>
              </div>
              {broker.phoneNumber && (
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{broker.phoneNumber}</span>
                </div>
              )}
              {broker.website && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm">Website:</span>
                  <a 
                    href={broker.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:underline"
                  >
                    {broker.website}
                  </a>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Address</CardTitle>
            </CardHeader>
            <CardContent>
              {broker.address || broker.city || broker.state || broker.country ? (
                <div className="flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-1" />
                  <div className="space-y-1">
                    {broker.address && <div>{broker.address}</div>}
                    <div>
                      {[broker.city, broker.state, broker.postalCode].filter(Boolean).join(", ")}
                    </div>
                    {broker.country && <div>{broker.country}</div>}
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">No address specified</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="license" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>License Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">License Number</label>
                  <p>{broker.licenseNumber || "Not specified"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">License Type</label>
                  <p>{broker.licenseType || "Not specified"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">License Issuer</label>
                  <p>{broker.licenseIssuer || "Not specified"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Expiry Date</label>
                  <p>{formatDate(broker.licenseExpiryDate)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Registration Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Registration Number</label>
                  <p>{broker.registrationNumber || "Not specified"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tax ID</label>
                  <p>{broker.taxId || "Not specified"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Established Date</label>
                  <p>{formatDate(broker.establishedDate)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Credit Rating</label>
                  <p>{broker.creditRating || "Not specified"}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="business" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Commission Structure</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Commission Rate</label>
                  <p className="text-lg font-semibold">{brokerUtils.formatCommissionRate(broker.commissionRate)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Minimum Commission</label>
                  <p>{formatCurrency(broker.minimumCommission)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Maximum Commission</label>
                  <p>{formatCurrency(broker.maximumCommission)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Financial Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Bond Amount</label>
                  <p>{formatCurrency(broker.bondAmount)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Insurance Amount</label>
                  <p>{formatCurrency(broker.insuranceAmount)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {(broker.specializations || broker.servicesOffered || broker.targetMarkets) && (
            <Card>
              <CardHeader>
                <CardTitle>Business Focus</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {broker.specializations && broker.specializations.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Specializations</label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {broker.specializations.map((spec, index) => (
                        <Badge key={index} variant="secondary">{spec}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                {broker.servicesOffered && broker.servicesOffered.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Services Offered</label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {broker.servicesOffered.map((service, index) => (
                        <Badge key={index} variant="secondary">{service}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                {broker.targetMarkets && broker.targetMarkets.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Target Markets</label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {broker.targetMarkets.map((market, index) => (
                        <Badge key={index} variant="secondary">{market}</Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="clients" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Clients</CardTitle>
              <CardDescription>
                Showing the most recent clients managed by this broker
              </CardDescription>
            </CardHeader>
            <CardContent>
              {broker.clients.length > 0 ? (
                <div className="space-y-4">
                  {broker.clients.map((client) => (
                    <div key={client.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium">{client.clientName}</div>
                        <div className="text-sm text-muted-foreground">
                          {client.clientType} • {brokerUtils.formatStatus(client.status)}
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(client.createdAt)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No clients found</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="transactions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>
                Showing the most recent transactions facilitated by this broker
              </CardDescription>
            </CardHeader>
            <CardContent>
              {broker.transactions.length > 0 ? (
                <div className="space-y-4">
                  {broker.transactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium">{transaction.transactionType.replace('_', ' ')}</div>
                        <div className="text-sm text-muted-foreground">
                          Amount: {formatCurrency(transaction.amount)} • 
                          Commission: {formatCurrency(transaction.commissionAmount)}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge variant="outline" className={`border-${brokerUtils.getStatusColor(transaction.status)}-500`}>
                          {brokerUtils.formatStatus(transaction.status)}
                        </Badge>
                        <div className="text-sm text-muted-foreground mt-1">
                          {formatDate(transaction.transactionDate)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-muted-foreground">No transactions found</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
