"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, XCircle, AlertTriangle, RefreshCw } from "lucide-react";

interface EnvironmentCheck {
  name: string;
  status: 'success' | 'warning' | 'error';
  message: string;
  value?: string;
}

export function EnvironmentChecker() {
  const { data: session, status } = useSession();
  const [checks, setChecks] = useState<EnvironmentCheck[]>([]);
  const [isChecking, setIsChecking] = useState(false);

  const runChecks = async () => {
    setIsChecking(true);
    const newChecks: EnvironmentCheck[] = [];

    // Check 1: Environment Variables
    newChecks.push({
      name: "NEXT_PUBLIC_API_URL",
      status: process.env.NEXT_PUBLIC_API_URL ? 'success' : 'warning',
      message: process.env.NEXT_PUBLIC_API_URL ? 'Set correctly' : 'Not set - using fallback',
      value: process.env.NEXT_PUBLIC_API_URL || 'undefined'
    });

    newChecks.push({
      name: "NEXTAUTH_URL",
      status: process.env.NEXTAUTH_URL ? 'success' : 'error',
      message: process.env.NEXTAUTH_URL ? 'Set correctly' : 'Missing - required for authentication',
      value: process.env.NEXTAUTH_URL || 'undefined'
    });

    // Check 2: Session Status
    newChecks.push({
      name: "Authentication",
      status: session ? 'success' : status === 'loading' ? 'warning' : 'error',
      message: session ? 'User authenticated' : status === 'loading' ? 'Loading...' : 'Not authenticated',
      value: session?.user?.email || 'No session'
    });

    // Check 3: Network Connectivity
    try {
      const healthResponse = await fetch('/api/health', { 
        method: 'GET',
        credentials: 'include'
      });
      
      newChecks.push({
        name: "API Health Check",
        status: healthResponse.ok ? 'success' : 'error',
        message: healthResponse.ok ? 'API is responding' : `API error: ${healthResponse.status}`,
        value: `Status: ${healthResponse.status}`
      });
    } catch (error) {
      newChecks.push({
        name: "API Health Check",
        status: 'error',
        message: 'Failed to connect to API',
        value: error instanceof Error ? error.message : 'Unknown error'
      });
    }

    // Check 4: SPV API Endpoint Test
    if (session) {
      try {
        const spvResponse = await fetch('/api/organizations/spvs', {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include'
        });

        newChecks.push({
          name: "SPV API Endpoint",
          status: spvResponse.ok ? 'success' : 'error',
          message: spvResponse.ok ? 'SPV endpoint accessible' : `SPV API error: ${spvResponse.status}`,
          value: `Status: ${spvResponse.status}`
        });
      } catch (error) {
        newChecks.push({
          name: "SPV API Endpoint",
          status: 'error',
          message: 'Failed to reach SPV endpoint',
          value: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    // Check 5: Browser Environment
    if (typeof window !== 'undefined') {
      newChecks.push({
        name: "Browser Environment",
        status: 'success',
        message: 'Running in browser',
        value: `${window.location.protocol}//${window.location.host}`
      });

      // Check 6: Local Storage
      try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        newChecks.push({
          name: "Local Storage",
          status: 'success',
          message: 'Local storage available',
          value: 'Working'
        });
      } catch (error) {
        newChecks.push({
          name: "Local Storage",
          status: 'warning',
          message: 'Local storage not available',
          value: 'Blocked or disabled'
        });
      }

      // Check 7: Cookies
      const cookiesEnabled = navigator.cookieEnabled;
      newChecks.push({
        name: "Cookies",
        status: cookiesEnabled ? 'success' : 'error',
        message: cookiesEnabled ? 'Cookies enabled' : 'Cookies disabled',
        value: cookiesEnabled ? 'Enabled' : 'Disabled'
      });
    }

    setChecks(newChecks);
    setIsChecking(false);
  };

  useEffect(() => {
    runChecks();
  }, [session, status]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">OK</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      default:
        return null;
    }
  };

  const hasErrors = checks.some(check => check.status === 'error');
  const hasWarnings = checks.some(check => check.status === 'warning');

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Environment Diagnostics</CardTitle>
            <CardDescription>
              System health check for SPV Management functionality
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={runChecks}
            disabled={isChecking}
          >
            {isChecking ? (
              <RefreshCw className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Recheck
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {hasErrors && (
          <Alert variant="destructive">
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              Critical issues detected. SPV functionality may not work properly.
            </AlertDescription>
          </Alert>
        )}

        {hasWarnings && !hasErrors && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Some issues detected. Functionality may be limited.
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-3">
          {checks.map((check, index) => (
            <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                {getStatusIcon(check.status)}
                <div>
                  <div className="font-medium text-sm">{check.name}</div>
                  <div className="text-xs text-muted-foreground">{check.message}</div>
                  {check.value && (
                    <div className="text-xs font-mono bg-muted px-2 py-1 rounded mt-1">
                      {check.value}
                    </div>
                  )}
                </div>
              </div>
              {getStatusBadge(check.status)}
            </div>
          ))}
        </div>

        <div className="pt-4 border-t">
          <div className="text-xs text-muted-foreground space-y-1">
            <div><strong>Timestamp:</strong> {new Date().toISOString()}</div>
            <div><strong>User Agent:</strong> {typeof window !== 'undefined' ? navigator.userAgent.substring(0, 80) + '...' : 'Server-side'}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
