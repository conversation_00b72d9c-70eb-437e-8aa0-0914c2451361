import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { NextRequest } from 'next/server';
import { GET } from '@/app/api/audit-logs/route';
import { GET as AnalyticsGET } from '@/app/api/audit-logs/analytics/route';
import { GET as SearchGET } from '@/app/api/audit-logs/search/route';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

// Mock dependencies
jest.mock('@/lib/auth');
jest.mock('@/lib/db');
jest.mock('@/lib/logger');

const mockAuth = auth as jest.MockedFunction<typeof auth>;
const mockDb = db as jest.Mocked<typeof db>;

describe('Audit Logs API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('GET /api/audit-logs', () => {
    it('should return audit logs for organization admin', async () => {
      // Mock authenticated user
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      // Mock user lookup
      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'ORGANIZATION_ADMIN',
        organizationId: 'org-123',
        spvUser: null
      } as any);

      // Mock audit logs
      const mockLogs = [
        {
          id: 'log-1',
          type: 'LOGIN_SUCCESS',
          description: 'User logged in',
          createdAt: new Date(),
          user: { id: 'user-123', name: 'John Doe', email: '<EMAIL>' },
          organization: { id: 'org-123', name: 'Test Org' }
        }
      ];

      mockDb.auditLog.findMany.mockResolvedValue(mockLogs as any);
      mockDb.auditLog.count.mockResolvedValue(1);

      const request = new NextRequest('http://localhost:3000/api/audit-logs?page=1&limit=20');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.auditLogs).toHaveLength(1);
      expect(data.pagination.total).toBe(1);
    });

    it('should return 401 for unauthenticated users', async () => {
      mockAuth.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/audit-logs');
      const response = await GET(request);

      expect(response.status).toBe(401);
    });

    it('should return 403 for users without permission', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'USER'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'USER',
        organizationId: 'org-123'
      } as any);

      const request = new NextRequest('http://localhost:3000/api/audit-logs');
      const response = await GET(request);

      expect(response.status).toBe(403);
    });

    it('should handle export functionality', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'ORGANIZATION_ADMIN',
        organizationId: 'org-123'
      } as any);

      const mockLogs = [
        {
          id: 'log-1',
          type: 'LOGIN_SUCCESS',
          description: 'User logged in',
          createdAt: new Date(),
          user: { name: 'John Doe', email: '<EMAIL>' },
          organization: { name: 'Test Org' },
          severity: 'INFO',
          success: true
        }
      ];

      mockDb.auditLog.findMany.mockResolvedValue(mockLogs as any);
      mockDb.auditLog.create.mockResolvedValue({} as any); // For export logging

      const request = new NextRequest('http://localhost:3000/api/audit-logs?export=csv');
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(response.headers.get('content-type')).toBe('text/csv');
      expect(response.headers.get('content-disposition')).toContain('attachment');
    });

    it('should handle real-time updates', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'ORGANIZATION_ADMIN',
        organizationId: 'org-123'
      } as any);

      const mockLogs = [
        {
          id: 'log-new',
          type: 'DATA_ENTRY_CREATED',
          description: 'New data entry',
          createdAt: new Date()
        }
      ];

      mockDb.auditLog.findMany.mockResolvedValue(mockLogs as any);
      mockDb.auditLog.count.mockResolvedValue(1);

      const lastTimestamp = new Date(Date.now() - 60000).toISOString();
      const request = new NextRequest(
        `http://localhost:3000/api/audit-logs?realtime=true&lastTimestamp=${lastTimestamp}`
      );
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.auditLogs).toHaveLength(1);
    });

    it('should handle filtering parameters', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'ORGANIZATION_ADMIN',
        organizationId: 'org-123'
      } as any);

      mockDb.auditLog.findMany.mockResolvedValue([]);
      mockDb.auditLog.count.mockResolvedValue(0);

      const request = new NextRequest(
        'http://localhost:3000/api/audit-logs?severity=ERROR&category=AUTHENTICATION&success=false'
      );
      const response = await GET(request);

      expect(response.status).toBe(200);
      expect(mockDb.auditLog.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            organizationId: 'org-123',
            severity: 'ERROR',
            category: 'AUTHENTICATION',
            success: false
          })
        })
      );
    });
  });

  describe('GET /api/audit-logs/analytics', () => {
    it('should return analytics for organization admin', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'ORGANIZATION_ADMIN',
        organizationId: 'org-123'
      } as any);

      // Mock analytics data
      mockDb.auditLog.count
        .mockResolvedValueOnce(1000)  // total events
        .mockResolvedValueOnce(50)    // error events
        .mockResolvedValueOnce(25);   // security events

      mockDb.auditLog.groupBy
        .mockResolvedValueOnce([
          { type: 'LOGIN_SUCCESS', _count: { type: 500 } }
        ])
        .mockResolvedValueOnce([
          { severity: 'INFO', _count: { severity: 800 } }
        ])
        .mockResolvedValueOnce([
          { category: 'AUTHENTICATION', _count: { category: 600 } }
        ])
        .mockResolvedValueOnce([
          { userId: 'user-1', _count: { userId: 100 } }
        ]);

      mockDb.auditLog.aggregate.mockResolvedValue({
        _avg: { duration: 150 }
      });

      mockDb.user.findMany.mockResolvedValue([
        { id: 'user-1', name: 'John Doe' }
      ] as any);

      mockDb.auditLog.create.mockResolvedValue({} as any); // For access logging

      const startDate = new Date('2024-01-01').toISOString();
      const endDate = new Date('2024-01-31').toISOString();
      const request = new NextRequest(
        `http://localhost:3000/api/audit-logs/analytics?startDate=${startDate}&endDate=${endDate}`
      );
      const response = await AnalyticsGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.totalEvents).toBe(1000);
      expect(data.data.errorRate).toBe(5);
      expect(data.data.securityEvents).toBe(25);
      expect(data.data.averageResponseTime).toBe(150);
    });

    it('should return 403 for non-admin users', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'USER'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'USER'
      } as any);

      const request = new NextRequest('http://localhost:3000/api/audit-logs/analytics?startDate=2024-01-01T00:00:00Z&endDate=2024-01-31T23:59:59Z');
      const response = await AnalyticsGET(request);

      expect(response.status).toBe(403);
    });
  });

  describe('GET /api/audit-logs/search', () => {
    it('should search audit logs successfully', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'ORGANIZATION_ADMIN',
        organizationId: 'org-123'
      } as any);

      const mockSearchResults = [
        {
          id: 'log-1',
          type: 'LOGIN_SUCCESS',
          description: 'User login successful',
          createdAt: new Date(),
          user: { name: 'John Doe', email: '<EMAIL>' }
        }
      ];

      mockDb.auditLog.findMany.mockResolvedValue(mockSearchResults as any);
      mockDb.auditLog.count.mockResolvedValue(1);
      mockDb.auditLog.create.mockResolvedValue({} as any); // For search logging

      const request = new NextRequest(
        'http://localhost:3000/api/audit-logs/search?q=login&severity=INFO&page=1&limit=20'
      );
      const response = await SearchGET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.query).toBe('login');
      expect(data.auditLogs).toHaveLength(1);
      expect(data.pagination.total).toBe(1);
    });

    it('should return 400 for missing search query', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN'
        }
      } as any);

      const request = new NextRequest('http://localhost:3000/api/audit-logs/search');
      const response = await SearchGET(request);

      expect(response.status).toBe(400);
    });

    it('should handle search with filters', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'user-123',
        role: 'ORGANIZATION_ADMIN',
        organizationId: 'org-123'
      } as any);

      mockDb.auditLog.findMany.mockResolvedValue([]);
      mockDb.auditLog.count.mockResolvedValue(0);
      mockDb.auditLog.create.mockResolvedValue({} as any);

      const startDate = new Date('2024-01-01').toISOString();
      const endDate = new Date('2024-01-31').toISOString();
      const request = new NextRequest(
        `http://localhost:3000/api/audit-logs/search?q=error&severity=ERROR&category=SYSTEM&startDate=${startDate}&endDate=${endDate}`
      );
      const response = await SearchGET(request);

      expect(response.status).toBe(200);
      expect(mockDb.auditLog.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            organizationId: 'org-123',
            AND: expect.arrayContaining([
              expect.objectContaining({
                OR: expect.arrayContaining([
                  { description: { contains: 'error', mode: 'insensitive' } }
                ])
              })
            ])
          })
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      mockDb.user.findUnique.mockRejectedValue(new Error('Database connection failed'));

      const request = new NextRequest('http://localhost:3000/api/audit-logs');
      const response = await GET(request);

      expect(response.status).toBe(500);
    });

    it('should handle invalid query parameters', async () => {
      mockAuth.mockResolvedValue({
        user: {
          id: 'user-123',
          role: 'ORGANIZATION_ADMIN',
          organizationId: 'org-123'
        }
      } as any);

      const request = new NextRequest('http://localhost:3000/api/audit-logs?page=invalid&limit=abc');
      const response = await GET(request);

      expect(response.status).toBe(400);
    });
  });
});
