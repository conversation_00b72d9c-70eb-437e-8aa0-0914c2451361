import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { AuditService } from '@/lib/audit/service';
import { AuthAuditLogger } from '@/lib/audit/auth-logger';
import { SPVAuditLogger } from '@/lib/audit/spv-logger';
import { DataAuditLogger } from '@/lib/audit/data-logger';
import { AuditPerformanceOptimizer } from '@/lib/audit/performance';
import { db } from '@/lib/db';
import { AuditLogType } from '@prisma/client';

// Mock the database
jest.mock('@/lib/db', () => ({
  auditLog: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    count: jest.fn(),
    groupBy: jest.fn(),
    aggregate: jest.fn(),
    createMany: jest.fn(),
    deleteMany: jest.fn(),
    update: jest.fn()
  },
  user: {
    findUnique: jest.fn()
  },
  organization: {
    findUnique: jest.fn()
  }
}));

// Mock the logger
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    withContext: jest.fn(() => ({
      audit: jest.fn(),
      info: jest.fn(),
      error: jest.fn()
    }))
  }
}));

describe('AuditService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('createAuditLog', () => {
    it('should create an audit log successfully', async () => {
      const mockAuditLog = {
        id: 'test-id',
        type: AuditLogType.LOGIN_SUCCESS,
        description: 'User logged in successfully',
        userId: 'user-123',
        organizationId: 'org-123',
        createdAt: new Date(),
        severity: 'INFO',
        category: 'AUTHENTICATION',
        success: true
      };

      (db.auditLog.create as jest.Mock).mockResolvedValue(mockAuditLog);

      const result = await AuditService.createAuditLog({
        type: AuditLogType.LOGIN_SUCCESS,
        description: 'User logged in successfully',
        userId: 'user-123',
        organizationId: 'org-123',
        severity: 'INFO',
        category: 'AUTHENTICATION',
        success: true
      });

      expect(result).toEqual(mockAuditLog);
      expect(db.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          type: AuditLogType.LOGIN_SUCCESS,
          description: 'User logged in successfully',
          severity: 'INFO',
          category: 'AUTHENTICATION',
          success: true
        })
      });
    });

    it('should handle errors gracefully', async () => {
      (db.auditLog.create as jest.Mock).mockRejectedValue(new Error('Database error'));

      await expect(AuditService.createAuditLog({
        type: AuditLogType.LOGIN_SUCCESS,
        description: 'Test log'
      })).rejects.toThrow('Failed to create audit log: Database error');
    });
  });

  describe('getAuditLogsForOrganizationAdmin', () => {
    it('should return audit logs for organization admin', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          type: AuditLogType.LOGIN_SUCCESS,
          description: 'User logged in',
          organizationId: 'org-123',
          createdAt: new Date()
        }
      ];

      (db.auditLog.findMany as jest.Mock).mockResolvedValue(mockLogs);
      (db.auditLog.count as jest.Mock).mockResolvedValue(1);

      const result = await AuditService.getAuditLogsForOrganizationAdmin('org-123');

      expect(result.auditLogs).toEqual(mockLogs);
      expect(result.pagination.total).toBe(1);
      expect(db.auditLog.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            organizationId: 'org-123'
          })
        })
      );
    });
  });

  describe('getAuditAnalytics', () => {
    it('should return comprehensive analytics', async () => {
      const timeRange = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      (db.auditLog.count as jest.Mock)
        .mockResolvedValueOnce(100) // total events
        .mockResolvedValueOnce(10)  // error events
        .mockResolvedValueOnce(5);  // security events

      (db.auditLog.groupBy as jest.Mock)
        .mockResolvedValueOnce([
          { type: 'LOGIN_SUCCESS', _count: { type: 50 } },
          { type: 'DATA_ENTRY_CREATED', _count: { type: 30 } }
        ])
        .mockResolvedValueOnce([
          { severity: 'INFO', _count: { severity: 80 } },
          { severity: 'ERROR', _count: { severity: 20 } }
        ])
        .mockResolvedValueOnce([
          { category: 'AUTHENTICATION', _count: { category: 60 } },
          { category: 'DATA', _count: { category: 40 } }
        ])
        .mockResolvedValueOnce([
          { userId: 'user-1', _count: { userId: 25 } },
          { userId: 'user-2', _count: { userId: 20 } }
        ]);

      (db.auditLog.aggregate as jest.Mock).mockResolvedValue({
        _avg: { duration: 150 }
      });

      (db.user.findMany as jest.Mock).mockResolvedValue([
        { id: 'user-1', name: 'John Doe' },
        { id: 'user-2', name: 'Jane Smith' }
      ]);

      const result = await AuditService.getAuditAnalytics('org-123', timeRange);

      expect(result.totalEvents).toBe(100);
      expect(result.errorRate).toBe(10);
      expect(result.securityEvents).toBe(5);
      expect(result.averageResponseTime).toBe(150);
      expect(result.eventsByType).toEqual({
        'LOGIN_SUCCESS': 50,
        'DATA_ENTRY_CREATED': 30
      });
      expect(result.topUsers).toHaveLength(2);
    });
  });

  describe('searchAuditLogs', () => {
    it('should search audit logs with query and filters', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          type: AuditLogType.LOGIN_SUCCESS,
          description: 'User login successful',
          organizationId: 'org-123'
        }
      ];

      (db.auditLog.findMany as jest.Mock).mockResolvedValue(mockLogs);
      (db.auditLog.count as jest.Mock).mockResolvedValue(1);

      const result = await AuditService.searchAuditLogs(
        'org-123',
        'login',
        { severity: 'INFO' }
      );

      expect(result.auditLogs).toEqual(mockLogs);
      expect(db.auditLog.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          where: expect.objectContaining({
            organizationId: 'org-123',
            AND: expect.arrayContaining([
              expect.objectContaining({
                OR: expect.arrayContaining([
                  { description: { contains: 'login', mode: 'insensitive' } }
                ])
              })
            ])
          })
        })
      );
    });
  });

  describe('exportAuditLogs', () => {
    it('should export audit logs as CSV', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          type: AuditLogType.LOGIN_SUCCESS,
          description: 'User logged in',
          createdAt: new Date('2024-01-01T10:00:00Z'),
          user: { name: 'John Doe', email: '<EMAIL>' },
          organization: { name: 'Test Org' },
          severity: 'INFO',
          category: 'AUTHENTICATION',
          success: true
        }
      ];

      (db.auditLog.findMany as jest.Mock).mockResolvedValue(mockLogs);

      const result = await AuditService.exportAuditLogs({}, 'csv');

      expect(result).toContain('ID,Type,Description');
      expect(result).toContain('log-1,LOGIN_SUCCESS,"User logged in"');
      expect(result).toContain('John Doe,<EMAIL>');
    });

    it('should export audit logs as JSON', async () => {
      const mockLogs = [
        {
          id: 'log-1',
          type: AuditLogType.LOGIN_SUCCESS,
          description: 'User logged in'
        }
      ];

      (db.auditLog.findMany as jest.Mock).mockResolvedValue(mockLogs);

      const result = await AuditService.exportAuditLogs({}, 'json');
      const parsed = JSON.parse(result);

      expect(parsed.data).toEqual(mockLogs);
      expect(parsed.totalRecords).toBe(1);
      expect(parsed.exportedAt).toBeDefined();
    });
  });
});

describe('AuthAuditLogger', () => {
  it('should log login success', async () => {
    (db.auditLog.create as jest.Mock).mockResolvedValue({});

    await AuthAuditLogger.logLoginSuccess({
      userId: 'user-123',
      organizationId: 'org-123',
      email: '<EMAIL>',
      ipAddress: '***********',
      userAgent: 'Mozilla/5.0'
    });

    expect(db.auditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        type: AuditLogType.LOGIN_SUCCESS,
        description: 'User successfully logged in: <EMAIL>',
        severity: 'INFO',
        category: 'AUTHENTICATION',
        success: true
      })
    });
  });

  it('should log login failure', async () => {
    (db.auditLog.create as jest.Mock).mockResolvedValue({});

    await AuthAuditLogger.logLoginFailure({
      email: '<EMAIL>',
      ipAddress: '***********'
    }, 'Invalid password');

    expect(db.auditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        type: AuditLogType.LOGIN_FAILED,
        description: 'Failed login attempt for: <EMAIL> - Invalid password',
        severity: 'WARN',
        category: 'AUTHENTICATION',
        success: false,
        errorCode: 'AUTH_FAILED'
      })
    });
  });
});

describe('SPVAuditLogger', () => {
  it('should log SPV creation', async () => {
    (db.auditLog.create as jest.Mock).mockResolvedValue({});

    await SPVAuditLogger.logSPVCreated({
      userId: 'user-123',
      organizationId: 'org-123',
      userRole: 'ORGANIZATION_ADMIN'
    }, {
      id: 'spv-123',
      name: 'Test SPV',
      type: 'LLC'
    });

    expect(db.auditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        type: AuditLogType.SPV_CREATED,
        description: 'SPV created: Test SPV',
        spvId: 'spv-123',
        category: 'SPV',
        success: true
      })
    });
  });
});

describe('DataAuditLogger', () => {
  it('should log data entry creation', async () => {
    (db.auditLog.create as jest.Mock).mockResolvedValue({});

    await DataAuditLogger.logDataEntryCreated({
      userId: 'user-123',
      organizationId: 'org-123',
      projectId: 'project-123'
    }, 'CARBON_CREDIT', 'entry-123', { amount: 100 });

    expect(db.auditLog.create).toHaveBeenCalledWith({
      data: expect.objectContaining({
        type: AuditLogType.DATA_ENTRY_CREATED,
        description: 'CARBON_CREDIT data entry created',
        resourceType: 'data_entry',
        resourceId: 'entry-123',
        category: 'DATA',
        success: true
      })
    });
  });
});

describe('AuditPerformanceOptimizer', () => {
  it('should get audit statistics', async () => {
    (db.auditLog.count as jest.Mock)
      .mockResolvedValueOnce(1000)  // total
      .mockResolvedValueOnce(50)    // last 24h
      .mockResolvedValueOnce(200)   // last 7d
      .mockResolvedValueOnce(10);   // errors

    (db.auditLog.groupBy as jest.Mock).mockResolvedValue([
      { type: 'LOGIN_SUCCESS', _count: { type: 500 } }
    ]);

    (db.auditLog.aggregate as jest.Mock).mockResolvedValue({
      _avg: { duration: 120 }
    });

    const stats = await AuditPerformanceOptimizer.getStatistics();

    expect(stats.totalLogs).toBe(1000);
    expect(stats.logsLast24h).toBe(50);
    expect(stats.logsLast7d).toBe(200);
    expect(stats.errorRate).toBe(1);
    expect(stats.averageResponseTime).toBe(120);
  });

  it('should monitor performance and detect issues', async () => {
    // Mock high error rate scenario
    jest.spyOn(AuditPerformanceOptimizer, 'getStatistics').mockResolvedValue({
      totalLogs: 1000,
      logsLast24h: 50,
      logsLast7d: 200,
      errorRate: 15, // High error rate
      topEventTypes: [],
      averageResponseTime: 1500, // Slow response
      databaseSize: '6 GB' // Large size
    });

    const performance = await AuditPerformanceOptimizer.monitorPerformance();

    expect(performance.status).toBe('critical');
    expect(performance.issues).toContain('High error rate: 15.0%');
    expect(performance.issues).toContain('Slow response time: 1500ms');
    expect(performance.recommendations).toContain('Investigate failed audit log operations');
  });

  it('should clean up old logs', async () => {
    (db.auditLog.deleteMany as jest.Mock).mockResolvedValue({ count: 100 });

    const result = await AuditPerformanceOptimizer.cleanupOldLogs(30);

    expect(result).toBe(100);
    expect(db.auditLog.deleteMany).toHaveBeenCalledWith({
      where: expect.objectContaining({
        createdAt: expect.objectContaining({
          lt: expect.any(Date)
        })
      })
    });
  });
});
