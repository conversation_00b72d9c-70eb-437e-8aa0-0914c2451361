import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { GET, POST, DELETE } from '@/app/api/spv/project-assignments/route';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';

// Mock dependencies
vi.mock('@/lib/auth');
vi.mock('@/lib/db');
vi.mock('@/lib/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

const mockAuth = vi.mocked(auth);
const mockDb = vi.mocked(db);

describe('/api/spv/project-assignments', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('GET /api/spv/project-assignments', () => {
    it('should return 401 if user is not authenticated', async () => {
      mockAuth.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments');
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 403 if user is not SPV admin', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'user1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvuser1',
        role: 'SITE_WORKER',
        spvId: 'spv1',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments');
      const response = await GET(request);

      expect(response.status).toBe(403);
      const data = await response.json();
      expect(data.error).toBe('Access denied. SPV admin role required.');
    });

    it('should return project assignments for SPV admin', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      const mockAssignments = [
        {
          id: 'assignment1',
          assignedAt: new Date(),
          project: {
            id: 'project1',
            name: 'Test Project',
            type: 'SOLAR',
            status: 'ACTIVE',
          },
          spvUser: {
            id: 'spvuser1',
            role: 'PROJECT_MANAGER',
            user: {
              id: 'user1',
              name: 'John Doe',
              email: '<EMAIL>',
            },
          },
          assignedByUser: {
            id: 'admin1',
            name: 'Admin User',
            email: '<EMAIL>',
          },
        },
      ];

      mockDb.projectAssignment.findMany.mockResolvedValue(mockAssignments as any);
      mockDb.projectAssignment.count.mockResolvedValue(1);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments');
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.assignments).toHaveLength(1);
      expect(data.data.pagination.totalCount).toBe(1);
    });
  });

  describe('POST /api/spv/project-assignments', () => {
    const validAssignmentData = {
      spvUserId: 'spvuser1',
      projectId: 'project1',
      permissions: {},
    };

    const validBulkAssignmentData = {
      spvUserId: 'spvuser1',
      projectIds: ['project1', 'project2'],
      permissions: {},
    };

    it('should return 401 if user is not authenticated', async () => {
      mockAuth.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments', {
        method: 'POST',
        body: JSON.stringify(validAssignmentData),
      });
      const response = await POST(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 403 if user is not SPV admin', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'user1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvuser1',
        role: 'SITE_WORKER',
        spvId: 'spv1',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments', {
        method: 'POST',
        body: JSON.stringify(validAssignmentData),
      });
      const response = await POST(request);

      expect(response.status).toBe(403);
      const data = await response.json();
      expect(data.error).toBe('Access denied. SPV admin role required.');
    });

    it('should create single project assignment successfully', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      // Mock target SPV user
      mockDb.sPVUser.findFirst.mockResolvedValueOnce({
        id: 'spvuser1',
        spvId: 'spv1',
        user: {
          id: 'user1',
          name: 'John Doe',
          email: '<EMAIL>',
        },
      } as any);

      // Mock project
      mockDb.project.findFirst.mockResolvedValue({
        id: 'project1',
        name: 'Test Project',
        spvId: 'spv1',
      } as any);

      // Mock no existing assignment
      mockDb.projectAssignment.findFirst.mockResolvedValue(null);

      const mockCreatedAssignment = {
        id: 'assignment1',
        projectId: 'project1',
        spvUserId: 'spvuser1',
        assignedBy: 'admin1',
        project: {
          id: 'project1',
          name: 'Test Project',
        },
        spvUser: {
          user: {
            id: 'user1',
            name: 'John Doe',
          },
        },
        assignedByUser: {
          id: 'admin1',
          name: 'Admin User',
        },
      };

      mockDb.projectAssignment.create.mockResolvedValue(mockCreatedAssignment as any);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments', {
        method: 'POST',
        body: JSON.stringify(validAssignmentData),
      });
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.id).toBe('assignment1');
      expect(data.message).toBe('Project assigned successfully');
    });

    it('should create bulk project assignments successfully', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      // Mock target SPV user
      mockDb.sPVUser.findFirst.mockResolvedValueOnce({
        id: 'spvuser1',
        spvId: 'spv1',
        user: {
          id: 'user1',
          name: 'John Doe',
          email: '<EMAIL>',
        },
      } as any);

      // Mock projects
      mockDb.project.findMany.mockResolvedValue([
        { id: 'project1', name: 'Project 1', spvId: 'spv1' },
        { id: 'project2', name: 'Project 2', spvId: 'spv1' },
      ] as any);

      // Mock no existing assignments
      mockDb.projectAssignment.findMany.mockResolvedValue([]);

      mockDb.projectAssignment.createMany.mockResolvedValue({ count: 2 });

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments', {
        method: 'POST',
        body: JSON.stringify(validBulkAssignmentData),
      });
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.assignmentsCreated).toBe(2);
      expect(data.message).toBe('2 projects assigned successfully');
    });

    it('should return 400 if project already assigned', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      // Mock target SPV user
      mockDb.sPVUser.findFirst.mockResolvedValueOnce({
        id: 'spvuser1',
        spvId: 'spv1',
        user: {
          id: 'user1',
          name: 'John Doe',
          email: '<EMAIL>',
        },
      } as any);

      // Mock project
      mockDb.project.findFirst.mockResolvedValue({
        id: 'project1',
        name: 'Test Project',
        spvId: 'spv1',
      } as any);

      // Mock existing assignment
      mockDb.projectAssignment.findFirst.mockResolvedValue({
        id: 'existing-assignment',
        projectId: 'project1',
        spvUserId: 'spvuser1',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments', {
        method: 'POST',
        body: JSON.stringify(validAssignmentData),
      });
      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Project is already assigned to this user');
    });
  });

  describe('DELETE /api/spv/project-assignments', () => {
    it('should return 401 if user is not authenticated', async () => {
      mockAuth.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments?assignmentId=assignment1', {
        method: 'DELETE',
      });
      const response = await DELETE(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 400 if assignment ID is missing', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments', {
        method: 'DELETE',
      });
      const response = await DELETE(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Assignment ID is required');
    });

    it('should delete project assignment successfully', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      const mockAssignment = {
        id: 'assignment1',
        projectId: 'project1',
        spvUserId: 'spvuser1',
        project: {
          id: 'project1',
          name: 'Test Project',
        },
        spvUser: {
          user: {
            id: 'user1',
            name: 'John Doe',
            email: '<EMAIL>',
          },
        },
      };

      mockDb.projectAssignment.findFirst.mockResolvedValue(mockAssignment as any);
      mockDb.projectAssignment.update.mockResolvedValue(mockAssignment as any);

      const request = new NextRequest('http://localhost:3000/api/spv/project-assignments?assignmentId=assignment1', {
        method: 'DELETE',
      });
      const response = await DELETE(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.message).toBe('Project assignment removed successfully');
    });
  });
});
