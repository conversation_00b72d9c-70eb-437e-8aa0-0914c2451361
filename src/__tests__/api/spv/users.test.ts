import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { GET, POST } from '@/app/api/spv/users/route';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { hashPassword } from '@/lib/auth/password';

// Mock dependencies
vi.mock('@/lib/auth');
vi.mock('@/lib/db');
vi.mock('@/lib/auth/password');
vi.mock('@/lib/logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
  },
}));

const mockAuth = vi.mocked(auth);
const mockDb = vi.mocked(db);
const mockHashPassword = vi.mocked(hashPassword);

describe('/api/spv/users', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('GET /api/spv/users', () => {
    it('should return 401 if user is not authenticated', async () => {
      mockAuth.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/spv/users');
      const response = await GET(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 404 if SPV user is not found', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'user1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/spv/users');
      const response = await GET(request);

      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.error).toBe('SPV user not found');
    });

    it('should return 403 if user is not SPV admin', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'user1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvuser1',
        role: 'SITE_WORKER',
        spvId: 'spv1',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/users');
      const response = await GET(request);

      expect(response.status).toBe(403);
      const data = await response.json();
      expect(data.error).toBe('Insufficient permissions');
    });

    it('should return SPV users for SPV admin', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      const mockUsers = [
        {
          id: 'spvuser1',
          role: 'PROJECT_MANAGER',
          isActive: true,
          createdAt: new Date(),
          user: {
            id: 'user1',
            email: '<EMAIL>',
            name: 'Project Manager',
            jobTitle: 'PM',
          },
          spv: {
            id: 'spv1',
            name: 'Test SPV',
            status: 'ACTIVE',
            organization: {
              id: 'org1',
              name: 'Test Org',
            },
          },
          projectAssignments: [],
        },
      ];

      mockDb.sPVUser.findMany.mockResolvedValue(mockUsers as any);
      mockDb.sPVUser.count.mockResolvedValue(1);

      const request = new NextRequest('http://localhost:3000/api/spv/users');
      const response = await GET(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.users).toHaveLength(1);
      expect(data.totalCount).toBe(1);
    });
  });

  describe('POST /api/spv/users', () => {
    const validUserData = {
      email: '<EMAIL>',
      name: 'New User',
      role: 'PROJECT_MANAGER',
      jobTitle: 'Project Manager',
      phoneNumber: '+1234567890',
      password: 'password123',
      projectIds: ['project1'],
    };

    it('should return 401 if user is not authenticated', async () => {
      mockAuth.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify(validUserData),
      });
      const response = await POST(request);

      expect(response.status).toBe(401);
      const data = await response.json();
      expect(data.error).toBe('Unauthorized');
    });

    it('should return 404 if SPV user is not found', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'user1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify(validUserData),
      });
      const response = await POST(request);

      expect(response.status).toBe(404);
      const data = await response.json();
      expect(data.error).toBe('SPV user not found');
    });

    it('should return 403 if user is not SPV admin', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'user1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvuser1',
        role: 'SITE_WORKER',
        spvId: 'spv1',
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify(validUserData),
      });
      const response = await POST(request);

      expect(response.status).toBe(403);
      const data = await response.json();
      expect(data.error).toBe('Access denied. SPV admin role required.');
    });

    it('should return 400 if user with email already exists', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
        spv: {
          organizationId: 'org1',
        },
      } as any);

      mockDb.user.findUnique.mockResolvedValue({
        id: 'existinguser',
        email: validUserData.email,
      } as any);

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify(validUserData),
      });
      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('User with this email already exists');
    });

    it('should create SPV user successfully', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
        spv: {
          organizationId: 'org1',
        },
      } as any);

      mockDb.user.findUnique.mockResolvedValue(null);
      mockHashPassword.mockResolvedValue('hashedpassword');

      const mockCreatedUser = {
        id: 'newuser1',
        email: validUserData.email,
        name: validUserData.name,
      };

      const mockCreatedSpvUser = {
        id: 'newspvuser1',
        userId: 'newuser1',
        spvId: 'spv1',
        role: validUserData.role,
        user: mockCreatedUser,
        spv: {
          id: 'spv1',
          name: 'Test SPV',
          organization: {
            id: 'org1',
            name: 'Test Org',
          },
        },
      };

      mockDb.$transaction.mockImplementation(async (callback) => {
        const tx = {
          user: {
            create: vi.fn().mockResolvedValue(mockCreatedUser),
          },
          sPVUser: {
            create: vi.fn().mockResolvedValue(mockCreatedSpvUser),
          },
          project: {
            findMany: vi.fn().mockResolvedValue([{ id: 'project1' }]),
          },
          projectAssignment: {
            createMany: vi.fn().mockResolvedValue({ count: 1 }),
          },
        };
        return await callback(tx as any);
      });

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify(validUserData),
      });
      const response = await POST(request);

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.data.id).toBe('newspvuser1');
      expect(data.message).toBe('SPV user created successfully');
    });

    it('should validate required fields', async () => {
      mockAuth.mockResolvedValue({
        user: { id: 'admin1', role: 'SPV_USER' },
      } as any);

      mockDb.sPVUser.findFirst.mockResolvedValue({
        id: 'spvadmin1',
        role: 'SPV_ADMIN',
        spvId: 'spv1',
      } as any);

      const invalidData = {
        email: 'invalid-email',
        name: '',
        role: 'INVALID_ROLE',
        password: '123', // too short
      };

      const request = new NextRequest('http://localhost:3000/api/spv/users', {
        method: 'POST',
        body: JSON.stringify(invalidData),
      });
      const response = await POST(request);

      expect(response.status).toBe(400);
      const data = await response.json();
      expect(data.error).toBe('Validation error');
      expect(data.details).toBeDefined();
    });
  });
});
