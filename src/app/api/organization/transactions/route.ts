import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * Get transaction history for an organization
 */
async function getTransactionsHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view transaction history",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to view transactions",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  try {
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const type = searchParams.get("type"); // Filter by transaction type
    const search = searchParams.get("search"); // Search term
    
    const offset = (page - 1) * limit;

    // Build query for wallets - admin users can see all wallets, others see only their organization's wallets
    let walletQuery: any = {};

    if (session.user.role === "ADMIN") {
      // Admin users can see all wallets
      walletQuery = {};
    } else {
      // Regular users see only their organization's wallets
      walletQuery = {
        organizationId: session.user.organizationId,
      };
    }

    const organizationWallets = await db.wallet.findMany({
      where: walletQuery,
      select: {
        id: true,
      },
    });

    const walletIds = organizationWallets.map(w => w.id);

    if (walletIds.length === 0) {
      return NextResponse.json({
        transactions: [],
        pagination: {
          total: 0,
          page,
          limit,
          hasMore: false,
        },
      });
    }

    // Build transaction query
    let transactionQuery: any = {
      where: {
        walletId: {
          in: walletIds,
        },
        ...(type && { type }),
      },
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
      skip: offset,
      include: {
        wallet: {
          select: {
            id: true,
            name: true,
            address: true,
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        order: {
          include: {
            carbonCredit: {
              select: {
                id: true,
                name: true,
                vintage: true,
                standard: true,
                methodology: true,
              },
            },
            buyer: {
              select: {
                id: true,
                name: true,
              },
            },
            seller: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    };

    // Add search filter if provided
    if (search) {
      transactionQuery.where.OR = [
        {
          notes: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          counterpartyName: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          transactionHash: {
            contains: search,
            mode: "insensitive",
          },
        },
      ];
    }

    // Get transactions
    const transactions = await db.transaction.findMany(transactionQuery);

    // Get total count for pagination
    const totalCountQuery = {
      where: transactionQuery.where,
    };
    const totalCount = await db.transaction.count(totalCountQuery);

    // Format transactions for frontend
    const formattedTransactions = transactions.map(transaction => ({
      id: transaction.id,
      type: transaction.type,
      amount: transaction.amount,
      fee: transaction.fee,
      status: transaction.status,
      transactionHash: transaction.transactionHash,
      blockNumber: transaction.blockNumber,
      network: transaction.network,
      category: transaction.category,
      notes: transaction.notes,
      counterpartyName: transaction.counterpartyName,
      createdAt: transaction.createdAt,
      wallet: transaction.wallet,
      order: transaction.order,
      // Additional computed fields
      totalAmount: transaction.amount + transaction.fee,
      feeAmount: transaction.fee,
      quantity: transaction.order?.quantity || 0,
      price: transaction.order ? transaction.amount / transaction.order.quantity : 0,
      carbonCredit: transaction.order?.carbonCredit || null,
      buyer: transaction.order?.buyer || null,
      seller: transaction.order?.seller || null,
    }));

    logger.info(`Retrieved ${transactions.length} transactions for ${session.user.role === "ADMIN" ? "admin user (all organizations)" : `organization ${session.user.organizationId}`}`);

    return NextResponse.json({
      transactions: formattedTransactions,
      pagination: {
        total: totalCount,
        page,
        limit,
        hasMore: offset + limit < totalCount,
      },
    });
  } catch (error) {
    logger.error("Error getting organization transaction history:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "Failed to get transaction history",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handler with error handling
const wrappedHandler = withErrorHandling(getTransactionsHandler);

export const GET = wrappedHandler;
