import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

interface RouteParams {
  params: Promise<{ id: string }>;
}

/**
 * GET /api/organization/projects/[id]/audit
 * Get audit data for a specific project (Organization Admin access)
 */
export async function GET(req: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access audit data" },
        { status: 401 }
      );
    }

    // Check if user is organization admin
    if (session.user.role !== "ORGANIZATION_ADMIN") {
      return NextResponse.json(
        { error: "You must be an organization admin to access this data" },
        { status: 403 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization to access audit data" },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const projectId = resolvedParams.id;

    // Verify the project belongs to the user's organization
    const project = await db.project.findFirst({
      where: {
        id: projectId,
        organizationId: session.user.organizationId,
      },
      select: {
        id: true,
        name: true,
        organizationId: true,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found or you don't have access to it" },
        { status: 404 }
      );
    }

    // Fetch audit logs related to this project
    const auditLogs = await db.auditLog.findMany({
      where: {
        OR: [
          {
            // Direct project-related logs
            metadata: {
              path: ["projectId"],
              equals: projectId
            }
          },
          {
            // Organization-level logs that might affect this project
            organizationId: project.organizationId,
            type: {
              in: ["PROJECT_CREATED", "PROJECT_UPDATED", "PROJECT_DELETED", "DOCUMENT_UPLOADED", "VERIFICATION_APPROVED", "VERIFICATION_REJECTED"]
            }
          }
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 100, // Limit to last 100 audit logs
    });

    // Fetch verification history for this project
    const verificationHistory = await db.dataVerificationLog.findMany({
      where: {
        unitLog: {
          projectId: projectId,
        },
      },
      include: {
        unitLog: {
          select: {
            id: true,
            logDate: true,
            unitType: true,
            quantity: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 50, // Limit to last 50 verification logs
    });

    // Fetch compliance documents related to this project
    // Note: ComplianceDocument doesn't have a direct projectId field in the schema
    // For now, we'll return an empty array and can enhance this later
    const complianceDocuments: any[] = [];

    // Fetch data verification logs for unit logs in this project
    const dataVerificationLogs = await db.dataVerificationLog.findMany({
      where: {
        unitLog: {
          projectId: projectId,
        },
      },
      include: {
        unitLog: {
          select: {
            id: true,
            logDate: true,
            unitType: true,
            quantity: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 50, // Limit to last 50 verification logs
    });

    logger.info(`Organization admin ${session.user.id} retrieved audit data for project ${projectId}`);

    return NextResponse.json({
      auditLogs: auditLogs.map(log => ({
        id: log.id,
        type: log.type,
        description: log.description,
        metadata: log.metadata,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        createdAt: log.createdAt,
        user: log.user,
      })),
      verificationHistory: verificationHistory.map(verification => ({
        id: verification.id,
        fromStatus: verification.fromStatus,
        toStatus: verification.toStatus,
        verificationNotes: verification.verificationNotes,
        metadata: verification.metadata,
        createdAt: verification.createdAt,
        verifiedBy: verification.verifiedBy,
        unitLog: verification.unitLog,
      })),
      complianceDocuments: complianceDocuments,
      dataVerificationLogs: dataVerificationLogs.map(log => ({
        id: log.id,
        fromStatus: log.fromStatus,
        toStatus: log.toStatus,
        verificationNotes: log.verificationNotes,
        metadata: log.metadata,
        createdAt: log.createdAt,
        verifiedBy: log.verifiedBy,
        unitLog: log.unitLog,
      })),
      message: "Audit data retrieved successfully",
    });
  } catch (error) {
    logger.error("Error retrieving organization project audit data:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving audit data" },
      { status: 500 }
    );
  }
}
