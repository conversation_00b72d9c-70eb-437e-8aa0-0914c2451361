import { NextResponse } from "next/server";
import { z } from "zod";
import { resendOTP } from "@/lib/otp";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

const resendOtpSchema = z.object({
  email: z.string().email(),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    // Validate request body
    const { email } = resendOtpSchema.parse(body);

    // Get user's name for the email
    const user = await db.user.findUnique({
      where: { email },
      select: { name: true },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Resend OTP
    const result = await resendOTP(email, user.name || "User");

    if (!result.success) {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: result.message },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error resending OTP:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while resending OTP" },
      { status: 500 }
    );
  }
}
