/**
 * Broker Earnings API Endpoint
 * 
 * Provides comprehensive earnings summary and analytics for brokers.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for query parameters
const querySchema = z.object({
  period: z.enum(["week", "month", "quarter", "year"]).optional().default("month"),
  year: z.string().optional().transform(val => val ? parseInt(val) : new Date().getFullYear()),
  month: z.string().optional().transform(val => val ? parseInt(val) : new Date().getMonth() + 1),
});

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true, commissionRate: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    const { period, year, month } = querySchema.parse(queryParams);

    // Calculate date ranges based on period
    const now = new Date();
    let startDate: Date;
    let endDate: Date;
    let previousStartDate: Date;
    let previousEndDate: Date;

    switch (period) {
      case "week":
        const startOfWeek = new Date(now);
        startOfWeek.setDate(now.getDate() - now.getDay());
        startOfWeek.setHours(0, 0, 0, 0);
        startDate = startOfWeek;
        endDate = new Date(startOfWeek);
        endDate.setDate(startOfWeek.getDate() + 6);
        endDate.setHours(23, 59, 59, 999);
        
        previousStartDate = new Date(startDate);
        previousStartDate.setDate(startDate.getDate() - 7);
        previousEndDate = new Date(endDate);
        previousEndDate.setDate(endDate.getDate() - 7);
        break;

      case "month":
        startDate = new Date(year, month - 1, 1);
        endDate = new Date(year, month, 0, 23, 59, 59, 999);
        
        previousStartDate = new Date(year, month - 2, 1);
        previousEndDate = new Date(year, month - 1, 0, 23, 59, 59, 999);
        break;

      case "quarter":
        const quarterStartMonth = Math.floor((month - 1) / 3) * 3;
        startDate = new Date(year, quarterStartMonth, 1);
        endDate = new Date(year, quarterStartMonth + 3, 0, 23, 59, 59, 999);
        
        previousStartDate = new Date(year, quarterStartMonth - 3, 1);
        previousEndDate = new Date(year, quarterStartMonth, 0, 23, 59, 59, 999);
        break;

      case "year":
        startDate = new Date(year, 0, 1);
        endDate = new Date(year, 11, 31, 23, 59, 59, 999);
        
        previousStartDate = new Date(year - 1, 0, 1);
        previousEndDate = new Date(year - 1, 11, 31, 23, 59, 59, 999);
        break;
    }

    // Get current period earnings
    const currentEarnings = await db.brokerCommission.aggregate({
      where: {
        brokerId: broker.id,
        createdAt: {
          gte: startDate,
          lte: endDate,
        }
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    });

    // Get previous period earnings for comparison
    const previousEarnings = await db.brokerCommission.aggregate({
      where: {
        brokerId: broker.id,
        createdAt: {
          gte: previousStartDate,
          lte: previousEndDate,
        }
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    });

    // Get earnings by status for current period
    const earningsByStatus = await db.brokerCommission.groupBy({
      by: ['status'],
      where: {
        brokerId: broker.id,
        createdAt: {
          gte: startDate,
          lte: endDate,
        }
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    });

    // Get top clients by commission for current period
    const topClients = await db.brokerCommission.groupBy({
      by: ['transactionId'],
      where: {
        brokerId: broker.id,
        createdAt: {
          gte: startDate,
          lte: endDate,
        }
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        _sum: {
          amount: 'desc'
        }
      },
      take: 5
    });

    // Get client details for top clients
    const topClientDetails = await Promise.all(
      topClients.map(async (client) => {
        if (!client.transactionId) return null;
        
        const transaction = await db.brokerTransaction.findUnique({
          where: { id: client.transactionId },
          include: {
            brokerClient: {
              select: {
                id: true,
                clientName: true,
                clientType: true,
              }
            }
          }
        });

        return {
          clientId: transaction?.brokerClient?.id,
          clientName: transaction?.brokerClient?.clientName || "Unknown",
          clientType: transaction?.brokerClient?.clientType,
          totalCommission: client._sum.amount || 0,
          transactionCount: client._count.id,
        };
      })
    );

    // Calculate percentage changes
    const currentAmount = currentEarnings._sum.amount || 0;
    const previousAmount = previousEarnings._sum.amount || 0;
    const amountChange = previousAmount > 0 
      ? Math.round(((currentAmount - previousAmount) / previousAmount) * 100)
      : currentAmount > 0 ? 100 : 0;

    const currentCount = currentEarnings._count.id || 0;
    const previousCount = previousEarnings._count.id || 0;
    const countChange = previousCount > 0
      ? Math.round(((currentCount - previousCount) / previousCount) * 100)
      : currentCount > 0 ? 100 : 0;

    // Get all-time totals
    const allTimeEarnings = await db.brokerCommission.aggregate({
      where: {
        brokerId: broker.id,
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    });

    const earnings = {
      period: {
        type: period,
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        year,
        month: period === "month" ? month : undefined,
      },
      current: {
        totalAmount: currentAmount,
        commissionCount: currentCount,
        averageCommission: currentCount > 0 ? currentAmount / currentCount : 0,
      },
      previous: {
        totalAmount: previousAmount,
        commissionCount: previousCount,
        averageCommission: previousCount > 0 ? previousAmount / previousCount : 0,
      },
      changes: {
        amountChange,
        countChange,
      },
      breakdown: {
        byStatus: earningsByStatus.map(item => ({
          status: item.status,
          amount: item._sum.amount || 0,
          count: item._count.id,
          percentage: currentAmount > 0 ? Math.round(((item._sum.amount || 0) / currentAmount) * 100) : 0,
        })),
        topClients: topClientDetails.filter(client => client !== null),
      },
      allTime: {
        totalAmount: allTimeEarnings._sum.amount || 0,
        commissionCount: allTimeEarnings._count.id || 0,
        averageCommission: allTimeEarnings._count.id > 0 
          ? (allTimeEarnings._sum.amount || 0) / allTimeEarnings._count.id 
          : 0,
      },
      brokerInfo: {
        defaultCommissionRate: broker.commissionRate,
      }
    };

    logger.info("Broker earnings retrieved", {
      brokerId: broker.id,
      userId: session.user.id,
      period,
      currentAmount,
      currentCount,
    });

    return NextResponse.json({
      success: true,
      earnings,
    });

  } catch (error) {
    logger.error("Error fetching broker earnings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
