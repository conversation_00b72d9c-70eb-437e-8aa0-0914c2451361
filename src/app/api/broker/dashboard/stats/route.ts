/**
 * Broker Dashboard Stats API Endpoint
 * 
 * Provides real-time statistics for broker dashboard including
 * client count, transaction metrics, commission data, and pending items.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Get current date for filtering
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

    // Get total clients count
    const totalClients = await db.brokerClient.count({
      where: {
        brokerId: broker.id,
        status: "ACTIVE"
      }
    });

    // Get clients from last month for comparison
    const lastMonthClients = await db.brokerClient.count({
      where: {
        brokerId: broker.id,
        status: "ACTIVE",
        createdAt: {
          gte: startOfLastMonth,
          lte: endOfLastMonth
        }
      }
    });

    // Get active transactions count
    const activeTransactions = await db.brokerTransaction.count({
      where: {
        brokerId: broker.id,
        status: {
          in: ["PENDING", "PROCESSING"]
        }
      }
    });

    // Get monthly commission (current month)
    const monthlyCommissionResult = await db.brokerCommission.aggregate({
      where: {
        brokerId: broker.id,
        status: {
          in: ["CALCULATED", "APPROVED", "PAID"]
        },
        createdAt: {
          gte: startOfMonth
        }
      },
      _sum: {
        amount: true
      }
    });

    const monthlyCommission = monthlyCommissionResult._sum.amount || 0;

    // Get last month commission for comparison
    const lastMonthCommissionResult = await db.brokerCommission.aggregate({
      where: {
        brokerId: broker.id,
        status: {
          in: ["CALCULATED", "APPROVED", "PAID"]
        },
        createdAt: {
          gte: startOfLastMonth,
          lte: endOfLastMonth
        }
      },
      _sum: {
        amount: true
      }
    });

    const lastMonthCommission = lastMonthCommissionResult._sum.amount || 0;

    // Get pending documents count (broker documents with pending status)
    const pendingDocuments = await db.brokerDocument.count({
      where: {
        brokerId: broker.id,
        status: "PENDING"
      }
    });

    // Get recent transactions (last 5)
    const recentTransactions = await db.brokerTransaction.findMany({
      where: {
        brokerId: broker.id
      },
      include: {
        brokerClient: {
          select: {
            clientName: true
          }
        }
      },
      orderBy: {
        createdAt: "desc"
      },
      take: 5
    });

    // Get recent commissions (last 5)
    const recentCommissions = await db.brokerCommission.findMany({
      where: {
        brokerId: broker.id
      },
      orderBy: {
        createdAt: "desc"
      },
      take: 5
    });

    // Calculate percentage changes
    const clientsChange = lastMonthClients > 0 
      ? Math.round(((totalClients - lastMonthClients) / lastMonthClients) * 100)
      : totalClients > 0 ? 100 : 0;

    const commissionChange = lastMonthCommission > 0
      ? Math.round(((monthlyCommission - lastMonthCommission) / lastMonthCommission) * 100)
      : monthlyCommission > 0 ? 100 : 0;

    const stats = {
      totalClients,
      activeTransactions,
      monthlyCommission,
      pendingDocuments,
      clientsChange,
      commissionChange,
      recentTransactions: recentTransactions.map(transaction => ({
        id: transaction.id,
        client: transaction.brokerClient?.clientName || "Unknown Client",
        type: transaction.transactionType,
        amount: transaction.amount,
        commission: transaction.commissionAmount,
        status: transaction.status.toLowerCase(),
        date: transaction.createdAt.toISOString().split('T')[0]
      })),
      recentCommissions: recentCommissions.map(commission => ({
        id: commission.id,
        amount: commission.amount,
        rate: commission.rate,
        baseAmount: commission.baseAmount,
        status: commission.status.toLowerCase(),
        date: commission.createdAt.toISOString().split('T')[0]
      }))
    };

    logger.info("Broker dashboard stats retrieved", {
      brokerId: broker.id,
      userId: session.user.id,
      stats: {
        totalClients: stats.totalClients,
        activeTransactions: stats.activeTransactions,
        monthlyCommission: stats.monthlyCommission,
        pendingDocuments: stats.pendingDocuments
      }
    });

    return NextResponse.json({
      success: true,
      stats
    });

  } catch (error) {
    logger.error("Error fetching broker dashboard stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
