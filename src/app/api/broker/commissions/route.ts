/**
 * Broker Commissions API Endpoint
 * 
 * Handles broker commission tracking and history.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for query parameters
const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  status: z.enum(["PENDING", "CALCULATED", "APPROVED", "PAID", "DISPUTED"]).optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.enum(["amount", "createdAt", "status", "rate"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    const { 
      page, 
      limit, 
      status, 
      dateFrom, 
      dateTo, 
      sortBy, 
      sortOrder 
    } = querySchema.parse(queryParams);

    // Build where clause
    const where: any = {
      brokerId: broker.id,
    };

    if (status) {
      where.status = status;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    // Get total count for pagination
    const totalCount = await db.brokerCommission.count({ where });

    // Get commissions with pagination
    const commissions = await db.brokerCommission.findMany({
      where,
      include: {
        brokerTransaction: {
          include: {
            brokerClient: {
              select: {
                id: true,
                clientName: true,
                clientEmail: true,
                clientType: true,
              }
            }
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    const totalPages = Math.ceil(totalCount / limit);

    // Calculate summary statistics
    const summaryStats = await db.brokerCommission.aggregate({
      where: {
        brokerId: broker.id,
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    });

    // Get status breakdown
    const statusBreakdown = await db.brokerCommission.groupBy({
      by: ['status'],
      where: {
        brokerId: broker.id,
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    });

    // Get monthly breakdown for current year
    const currentYear = new Date().getFullYear();
    const monthlyBreakdown = await db.brokerCommission.groupBy({
      by: ['createdAt'],
      where: {
        brokerId: broker.id,
        createdAt: {
          gte: new Date(`${currentYear}-01-01`),
          lt: new Date(`${currentYear + 1}-01-01`),
        }
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      }
    });

    // Process monthly data
    const monthlyData = Array.from({ length: 12 }, (_, i) => {
      const month = i + 1;
      const monthData = monthlyBreakdown.filter(item => 
        new Date(item.createdAt).getMonth() === i
      );
      
      return {
        month,
        monthName: new Date(currentYear, i, 1).toLocaleString('default', { month: 'long' }),
        amount: monthData.reduce((sum, item) => sum + (item._sum.amount || 0), 0),
        count: monthData.reduce((sum, item) => sum + item._count.id, 0),
      };
    });

    logger.info("Broker commissions retrieved", {
      brokerId: broker.id,
      userId: session.user.id,
      commissionCount: commissions.length,
      totalCount,
      page,
      limit
    });

    return NextResponse.json({
      success: true,
      commissions,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        status,
        dateFrom,
        dateTo,
        sortBy,
        sortOrder,
      },
      summary: {
        totalAmount: summaryStats._sum.amount || 0,
        totalCommissions: summaryStats._count.id || 0,
        statusBreakdown: statusBreakdown.map(item => ({
          status: item.status,
          amount: item._sum.amount || 0,
          count: item._count.id,
        })),
        monthlyBreakdown: monthlyData,
      }
    });

  } catch (error) {
    logger.error("Error fetching broker commissions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
