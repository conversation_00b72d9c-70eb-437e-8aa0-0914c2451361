import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: {
        id: true,
        name: true,
        legalName: true,
        description: true,
        email: true,
        phoneNumber: true,
        address: true,
        city: true,
        state: true,
        postalCode: true,
        country: true,
        licenseNumber: true,
        licenseType: true,
        licenseIssuer: true,
        licenseExpiryDate: true,
        operatingModel: true,
        commissionRate: true,
        specializations: true,
        status: true,
        verificationStatus: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      broker,
    });
  } catch (error) {
    logger.error("Error fetching broker profile", error);
    
    return NextResponse.json(
      { error: "Failed to fetch broker profile" },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    
    // Find existing broker profile
    const existingBroker = await db.broker.findUnique({
      where: { userId: session.user.id }
    });

    if (!existingBroker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Update broker profile (excluding sensitive fields like license info)
    const updatedBroker = await db.broker.update({
      where: { userId: session.user.id },
      data: {
        name: body.name,
        description: body.description,
        phoneNumber: body.phoneNumber,
        address: body.address,
        city: body.city,
        state: body.state,
        postalCode: body.postalCode,
        country: body.country,
        specializations: body.specializations,
        // Note: License info and other sensitive fields should be updated through admin approval
      },
    });

    logger.info("Broker profile updated", {
      brokerId: updatedBroker.id,
      userId: session.user.id,
      email: session.user.email,
    });

    return NextResponse.json({
      success: true,
      broker: updatedBroker,
    });
  } catch (error) {
    logger.error("Error updating broker profile", error);
    
    return NextResponse.json(
      { error: "Failed to update broker profile" },
      { status: 500 }
    );
  }
}
