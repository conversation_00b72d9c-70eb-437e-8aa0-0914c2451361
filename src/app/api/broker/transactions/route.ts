/**
 * Broker Transactions API Endpoint
 * 
 * Handles broker transaction management and history.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for creating a new broker transaction
const brokerTransactionCreateSchema = z.object({
  clientId: z.string().min(1, "Client ID is required"),
  transactionType: z.enum(["CARBON_CREDIT_SALE", "CARBON_CREDIT_PURCHASE", "PROJECT_INVESTMENT", "CONSULTATION", "OTHER"]),
  amount: z.number().min(0, "Amount must be positive"),
  currency: z.string().default("USD"),
  commissionRate: z.number().min(0).max(1),
  description: z.string().optional(),
  referenceId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// Schema for query parameters
const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  status: z.enum(["PENDING", "PROCESSING", "COMPLETED", "FAILED", "CANCELLED"]).optional(),
  transactionType: z.enum(["CARBON_CREDIT_SALE", "CARBON_CREDIT_PURCHASE", "PROJECT_INVESTMENT", "CONSULTATION", "OTHER"]).optional(),
  clientId: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  sortBy: z.enum(["amount", "createdAt", "status", "transactionType"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    const { 
      page, 
      limit, 
      search, 
      status, 
      transactionType, 
      clientId, 
      dateFrom, 
      dateTo, 
      sortBy, 
      sortOrder 
    } = querySchema.parse(queryParams);

    // Build where clause
    const where: any = {
      brokerId: broker.id,
    };

    if (status) {
      where.status = status;
    }

    if (transactionType) {
      where.transactionType = transactionType;
    }

    if (clientId) {
      where.clientId = clientId;
    }

    if (dateFrom || dateTo) {
      where.createdAt = {};
      if (dateFrom) {
        where.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        where.createdAt.lte = new Date(dateTo);
      }
    }

    if (search) {
      where.OR = [
        { description: { contains: search, mode: "insensitive" } },
        { referenceId: { contains: search, mode: "insensitive" } },
        { brokerClient: { clientName: { contains: search, mode: "insensitive" } } },
      ];
    }

    // Get total count for pagination
    const totalCount = await db.brokerTransaction.count({ where });

    // Get transactions with pagination
    const transactions = await db.brokerTransaction.findMany({
      where,
      include: {
        brokerClient: {
          select: {
            id: true,
            clientName: true,
            clientEmail: true,
            clientType: true,
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    const totalPages = Math.ceil(totalCount / limit);

    // Calculate summary statistics
    const summaryStats = await db.brokerTransaction.aggregate({
      where: {
        brokerId: broker.id,
        status: "COMPLETED"
      },
      _sum: {
        amount: true,
        commissionAmount: true,
      },
      _count: {
        id: true,
      }
    });

    logger.info("Broker transactions retrieved", {
      brokerId: broker.id,
      userId: session.user.id,
      transactionCount: transactions.length,
      totalCount,
      page,
      limit
    });

    return NextResponse.json({
      success: true,
      transactions,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        search,
        status,
        transactionType,
        clientId,
        dateFrom,
        dateTo,
        sortBy,
        sortOrder,
      },
      summary: {
        totalAmount: summaryStats._sum.amount || 0,
        totalCommission: summaryStats._sum.commissionAmount || 0,
        completedTransactions: summaryStats._count.id || 0,
      }
    });

  } catch (error) {
    logger.error("Error fetching broker transactions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const data = brokerTransactionCreateSchema.parse(body);

    // Verify client belongs to this broker
    const client = await db.brokerClient.findFirst({
      where: {
        id: data.clientId,
        brokerId: broker.id,
        status: "ACTIVE"
      }
    });

    if (!client) {
      return NextResponse.json(
        { error: "Client not found or inactive" },
        { status: 400 }
      );
    }

    // Calculate commission amount
    const commissionAmount = data.amount * data.commissionRate;

    // Create new broker transaction
    const newTransaction = await db.brokerTransaction.create({
      data: {
        brokerId: broker.id,
        clientId: data.clientId,
        transactionType: data.transactionType,
        amount: data.amount,
        currency: data.currency,
        commissionAmount,
        commissionRate: data.commissionRate,
        description: data.description,
        referenceId: data.referenceId,
        metadata: data.metadata,
        status: "PENDING",
      },
      include: {
        brokerClient: {
          select: {
            id: true,
            clientName: true,
            clientEmail: true,
            clientType: true,
          }
        }
      }
    });

    // Create corresponding commission record
    await db.brokerCommission.create({
      data: {
        brokerId: broker.id,
        amount: commissionAmount,
        rate: data.commissionRate,
        baseAmount: data.amount,
        transactionId: newTransaction.id,
        currency: data.currency,
        status: "PENDING",
      }
    });

    logger.info("Broker transaction created", {
      brokerId: broker.id,
      transactionId: newTransaction.id,
      clientId: data.clientId,
      amount: data.amount,
      commissionAmount,
      userId: session.user.id,
    });

    return NextResponse.json({
      success: true,
      transaction: newTransaction,
    }, { status: 201 });

  } catch (error) {
    logger.error("Error creating broker transaction:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map(e => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
