import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

const brokerOnboardingSchema = z.object({
  // Business Information
  businessName: z.string().min(2, "Business name must be at least 2 characters"),
  legalName: z.string().min(2, "Legal name must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  
  // Contact Information
  phoneNumber: z.string().min(10, "Please enter a valid phone number"),
  address: z.string().min(5, "Please enter a complete address"),
  city: z.string().min(2, "City is required"),
  state: z.string().min(2, "State/Province is required"),
  postalCode: z.string().min(3, "Postal code is required"),
  country: z.string().min(2, "Country is required"),
  
  // License Information
  licenseNumber: z.string().min(3, "License number is required"),
  licenseType: z.string().min(3, "License type is required"),
  licenseIssuer: z.string().min(2, "License issuer is required"),
  licenseExpiryDate: z.string().min(1, "License expiry date is required"),
  
  // Business Model
  operatingModel: z.enum(["INDEPENDENT", "ONIX_MANAGED"]),
  commissionRate: z.number().min(0.001).max(0.5),
  specializations: z.array(z.string()).min(1, "Please select at least one specialization"),
});

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Only broker users can complete broker onboarding" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const data = brokerOnboardingSchema.parse(body);

    // Check if broker profile already exists
    const existingBroker = await db.broker.findUnique({
      where: { userId: session.user.id }
    });

    if (existingBroker) {
      return NextResponse.json(
        { error: "Broker profile already exists" },
        { status: 400 }
      );
    }

    // Create broker profile
    const broker = await db.broker.create({
      data: {
        name: data.businessName,
        legalName: data.legalName,
        description: data.description,
        email: session.user.email!,
        phoneNumber: data.phoneNumber,
        address: data.address,
        city: data.city,
        state: data.state,
        postalCode: data.postalCode,
        country: data.country,
        licenseNumber: data.licenseNumber,
        licenseType: data.licenseType,
        licenseIssuer: data.licenseIssuer,
        licenseExpiryDate: new Date(data.licenseExpiryDate),
        operatingModel: data.operatingModel,
        commissionRate: data.commissionRate,
        specializations: data.specializations,
        status: "ACTIVE",
        verificationStatus: "PENDING", // Will need admin verification
        userId: session.user.id,
        // Note: organizationId is optional for brokers
      },
    });

    logger.info("Broker profile created", {
      brokerId: broker.id,
      userId: session.user.id,
      email: session.user.email,
      businessName: data.businessName,
    });

    return NextResponse.json({
      success: true,
      broker: {
        id: broker.id,
        name: broker.name,
        status: broker.status,
        verificationStatus: broker.verificationStatus,
      },
    });
  } catch (error) {
    logger.error("Error creating broker profile", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data provided", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create broker profile" },
      { status: 500 }
    );
  }
}
