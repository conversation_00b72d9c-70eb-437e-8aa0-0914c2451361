/**
 * Broker Client by ID API Endpoint
 * 
 * Handles individual broker client operations (GET, PUT, DELETE).
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for updating a broker client
const brokerClientUpdateSchema = z.object({
  clientName: z.string().min(2, "Client name must be at least 2 characters").optional(),
  clientEmail: z.string().email("Invalid email address").optional(),
  clientPhone: z.string().optional(),
  relationshipType: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  contractStartDate: z.string().optional(),
  contractEndDate: z.string().optional(),
  notes: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "TERMINATED"]).optional(),
});

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Get client by ID
    const client = await db.brokerClient.findFirst({
      where: {
        id: params.id,
        brokerId: broker.id, // Ensure client belongs to this broker
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true,
            email: true,
            phone: true,
            address: true,
            city: true,
            state: true,
            country: true,
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          }
        }
      }
    });

    if (!client) {
      return NextResponse.json(
        { error: "Client not found" },
        { status: 404 }
      );
    }

    logger.info("Broker client retrieved", {
      brokerId: broker.id,
      clientId: client.id,
      userId: session.user.id,
    });

    return NextResponse.json({
      success: true,
      client,
    });

  } catch (error) {
    logger.error("Error fetching broker client:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Check if client exists and belongs to this broker
    const existingClient = await db.brokerClient.findFirst({
      where: {
        id: params.id,
        brokerId: broker.id,
      }
    });

    if (!existingClient) {
      return NextResponse.json(
        { error: "Client not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const data = brokerClientUpdateSchema.parse(body);

    // Update client
    const updatedClient = await db.brokerClient.update({
      where: { id: params.id },
      data: {
        ...(data.clientName && { clientName: data.clientName }),
        ...(data.clientEmail && { clientEmail: data.clientEmail }),
        ...(data.clientPhone !== undefined && { clientPhone: data.clientPhone }),
        ...(data.relationshipType !== undefined && { relationshipType: data.relationshipType }),
        ...(data.commissionRate !== undefined && { commissionRate: data.commissionRate }),
        ...(data.contractStartDate && { contractStartDate: new Date(data.contractStartDate) }),
        ...(data.contractEndDate && { contractEndDate: new Date(data.contractEndDate) }),
        ...(data.notes !== undefined && { notes: data.notes }),
        ...(data.status && { status: data.status }),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true,
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    logger.info("Broker client updated", {
      brokerId: broker.id,
      clientId: updatedClient.id,
      userId: session.user.id,
      changes: Object.keys(data),
    });

    return NextResponse.json({
      success: true,
      client: updatedClient,
    });

  } catch (error) {
    logger.error("Error updating broker client:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map(e => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Check if client exists and belongs to this broker
    const existingClient = await db.brokerClient.findFirst({
      where: {
        id: params.id,
        brokerId: broker.id,
      }
    });

    if (!existingClient) {
      return NextResponse.json(
        { error: "Client not found" },
        { status: 404 }
      );
    }

    // Delete client (this will cascade delete related records)
    await db.brokerClient.delete({
      where: { id: params.id }
    });

    logger.info("Broker client deleted", {
      brokerId: broker.id,
      clientId: params.id,
      userId: session.user.id,
    });

    return NextResponse.json({
      success: true,
      message: "Client deleted successfully",
    });

  } catch (error) {
    logger.error("Error deleting broker client:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
