/**
 * Broker Clients API Endpoint
 * 
 * Handles CRUD operations for broker client management.
 * Allows brokers to manage their client relationships.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for creating a new broker client
const brokerClientCreateSchema = z.object({
  clientType: z.enum(["ORGANIZATION", "INDIVIDUAL"]),
  clientName: z.string().min(2, "Client name must be at least 2 characters"),
  clientEmail: z.string().email("Invalid email address"),
  clientPhone: z.string().optional(),
  organizationId: z.string().optional(),
  userId: z.string().optional(),
  relationshipType: z.string().optional(),
  commissionRate: z.number().min(0).max(1).optional(),
  contractStartDate: z.string().optional(),
  contractEndDate: z.string().optional(),
  notes: z.string().optional(),
});

// Schema for query parameters
const querySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "SUSPENDED", "TERMINATED"]).optional(),
  clientType: z.enum(["ORGANIZATION", "INDIVIDUAL"]).optional(),
  sortBy: z.enum(["clientName", "createdAt", "status"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    const { page, limit, search, status, clientType, sortBy, sortOrder } = querySchema.parse(queryParams);

    // Build where clause
    const where: any = {
      brokerId: broker.id,
    };

    if (status) {
      where.status = status;
    }

    if (clientType) {
      where.clientType = clientType;
    }

    if (search) {
      where.OR = [
        { clientName: { contains: search, mode: "insensitive" } },
        { clientEmail: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get total count for pagination
    const totalCount = await db.brokerClient.count({ where });

    // Get clients with pagination
    const clients = await db.brokerClient.findMany({
      where,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true,
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip: (page - 1) * limit,
      take: limit,
    });

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("Broker clients retrieved", {
      brokerId: broker.id,
      userId: session.user.id,
      clientCount: clients.length,
      totalCount,
      page,
      limit
    });

    return NextResponse.json({
      success: true,
      clients,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        search,
        status,
        clientType,
        sortBy,
        sortOrder,
      }
    });

  } catch (error) {
    logger.error("Error fetching broker clients:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Verify user has BROKER role
    if (session.user.role !== "BROKER") {
      return NextResponse.json(
        { error: "Access denied. Broker role required." },
        { status: 403 }
      );
    }

    // Find broker profile for the current user
    const broker = await db.broker.findUnique({
      where: { userId: session.user.id },
      select: { id: true }
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker profile not found" },
        { status: 404 }
      );
    }

    const body = await req.json();
    const data = brokerClientCreateSchema.parse(body);

    // Check if client already exists
    const existingClient = await db.brokerClient.findFirst({
      where: {
        brokerId: broker.id,
        clientEmail: data.clientEmail,
      }
    });

    if (existingClient) {
      return NextResponse.json(
        { error: "Client with this email already exists" },
        { status: 400 }
      );
    }

    // Create new broker client
    const newClient = await db.brokerClient.create({
      data: {
        brokerId: broker.id,
        clientType: data.clientType,
        clientName: data.clientName,
        clientEmail: data.clientEmail,
        clientPhone: data.clientPhone,
        organizationId: data.organizationId,
        userId: data.userId,
        relationshipType: data.relationshipType,
        commissionRate: data.commissionRate,
        contractStartDate: data.contractStartDate ? new Date(data.contractStartDate) : null,
        contractEndDate: data.contractEndDate ? new Date(data.contractEndDate) : null,
        notes: data.notes,
        status: "ACTIVE",
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            type: true,
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    });

    logger.info("Broker client created", {
      brokerId: broker.id,
      clientId: newClient.id,
      clientName: newClient.clientName,
      clientEmail: newClient.clientEmail,
      userId: session.user.id,
    });

    return NextResponse.json({
      success: true,
      client: newClient,
    }, { status: 201 });

  } catch (error) {
    logger.error("Error creating broker client:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors.map(e => e.message).join(", ") },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
