/**
 * Verification Audit Trail API
 * 
 * Provides audit trail functionality for verification actions
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for audit log filters
const auditLogFiltersSchema = z.object({
  unitLogId: z.string().optional(),
  projectId: z.string().optional(),
  organizationId: z.string().optional(),
  type: z.string().optional(),
  search: z.string().optional(),
  dateFilter: z.enum(["all", "today", "week", "month", "quarter"]).optional(),
  page: z.string().transform(Number).optional(),
  limit: z.string().transform(Number).optional(),
  export: z.string().optional(),
});

/**
 * GET /api/audit/verification
 * Get verification audit logs with filtering and pagination
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access audit logs" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const filters = auditLogFiltersSchema.parse(Object.fromEntries(searchParams));

    const page = filters.page || 1;
    const limit = filters.limit || 20;
    const skip = (page - 1) * limit;

    // Build where clause based on filters
    const whereClause: any = {
      type: {
        in: [
          "DATA_VERIFIED",
          "DATA_REJECTED",
          "VERIFICATION_REQUESTED",
          "VVB_VERIFICATION_APPROVED",
          "VVB_VERIFICATION_REJECTED",
          "ORG_ADMIN_VERIFICATION_APPROVED",
          "ORG_ADMIN_VERIFICATION_REJECTED",
          "DATA_ENTRY_MANUAL",
          "DATA_ENTRY_CSV",
          "DATA_ENTRY_API",
          "DATA_ENTRY_IOT",
          "DATA_EDITED",
          "DATA_DELETED",
        ],
      },
    };

    // Apply role-based filtering
    if (session.user.role === "ORGANIZATION_ADMIN" && session.user.organizationId) {
      whereClause.organizationId = session.user.organizationId;
    } else if (session.user.role === "SPV_USER") {
      // SPV users can only see logs for their assigned projects
      const spvUser = await db.sPVUser.findFirst({
        where: { userId: session.user.id },
        include: {
          projectAssignments: {
            where: { isActive: true },
            select: { projectId: true },
          },
        },
      });

      if (spvUser) {
        const projectIds = spvUser.projectAssignments.map((pa) => pa.projectId);
        whereClause.metadata = {
          path: ["projectId"],
          in: projectIds,
        };
      }
    }

    // Apply specific filters
    if (filters.unitLogId) {
      whereClause.metadata = {
        ...whereClause.metadata,
        path: ["unitLogId"],
        equals: filters.unitLogId,
      };
    }

    if (filters.projectId) {
      whereClause.metadata = {
        ...whereClause.metadata,
        path: ["projectId"],
        equals: filters.projectId,
      };
    }

    if (filters.organizationId && session.user.role === "ADMIN") {
      whereClause.organizationId = filters.organizationId;
    }

    if (filters.type && filters.type !== "all") {
      whereClause.type = filters.type;
    }

    if (filters.search) {
      whereClause.OR = [
        { description: { contains: filters.search, mode: "insensitive" } },
        { user: { name: { contains: filters.search, mode: "insensitive" } } },
        { user: { email: { contains: filters.search, mode: "insensitive" } } },
      ];
    }

    // Apply date filter
    if (filters.dateFilter && filters.dateFilter !== "all") {
      const now = new Date();
      let startDate: Date;

      switch (filters.dateFilter) {
        case "today":
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          break;
        case "week":
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "month":
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          break;
        case "quarter":
          const quarterStart = Math.floor(now.getMonth() / 3) * 3;
          startDate = new Date(now.getFullYear(), quarterStart, 1);
          break;
        default:
          startDate = new Date(0);
      }

      whereClause.createdAt = {
        gte: startDate,
      };
    }

    // Handle export request
    if (filters.export === "true") {
      const allLogs = await db.auditLog.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
      });

      // Convert to CSV
      const csvHeaders = [
        "Date",
        "Time",
        "Type",
        "Description",
        "User Name",
        "User Email",
        "User Role",
        "Organization",
        "IP Address",
        "Unit Log ID",
        "Project ID",
        "Action",
        "From Status",
        "To Status",
        "Notes",
        "Rejection Reason",
      ];

      const csvRows = allLogs.map((log) => [
        new Date(log.createdAt).toLocaleDateString(),
        new Date(log.createdAt).toLocaleTimeString(),
        log.type,
        log.description,
        log.user?.name || "",
        log.user?.email || "",
        log.user?.role || "",
        log.organization?.name || "",
        log.ipAddress || "",
        (log.metadata as any)?.unitLogId || "",
        (log.metadata as any)?.projectId || "",
        (log.metadata as any)?.action || "",
        (log.metadata as any)?.fromStatus || "",
        (log.metadata as any)?.toStatus || "",
        (log.metadata as any)?.notes || "",
        (log.metadata as any)?.rejectionReason || "",
      ]);

      const csvContent = [csvHeaders, ...csvRows]
        .map((row) => row.map((field) => `"${field}"`).join(","))
        .join("\n");

      return new NextResponse(csvContent, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="verification-audit-logs-${new Date().toISOString().split("T")[0]}.csv"`,
        },
      });
    }

    // Get audit logs with pagination
    const auditLogs = await db.auditLog.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: { createdAt: "desc" },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await db.auditLog.count({
      where: whereClause,
    });

    // Get statistics
    const stats = await db.auditLog.groupBy({
      by: ["type"],
      where: whereClause,
      _count: {
        id: true,
      },
    });

    const statistics = stats.reduce((acc, stat) => {
      acc[stat.type] = stat._count.id;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      auditLogs,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
      statistics,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error fetching verification audit logs", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/audit/verification
 * Create a manual audit log entry (for admin use)
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create audit logs" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only platform admins can create manual audit logs" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { type, description, metadata, organizationId } = body;

    const auditLog = await db.auditLog.create({
      data: {
        type,
        description,
        userId: session.user.id,
        organizationId,
        metadata,
        ipAddress: req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    logger.info("Manual audit log created", {
      auditLogId: auditLog.id,
      type,
      userId: session.user.id,
    });

    return NextResponse.json({
      success: true,
      auditLog,
      message: "Audit log created successfully",
    });

  } catch (error) {
    logger.error("Error creating manual audit log", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
