import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

/**
 * GET /api/organizations/spvs/[id]/credentials
 * Get credentials for all users of the SPV
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;

    // Verify SPV exists and belongs to user's organization (for org admins)
    const whereClause: any = { id: spvId };
    if (session.user.role === "ORGANIZATION_ADMIN") {
      whereClause.organizationId = session.user.organizationId;
    }

    const spv = await db.sPV.findFirst({
      where: whereClause,
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Get SPV users with their credentials
    const spvUsers = await db.sPVUser.findMany({
      where: {
        spvId: spv.id,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            password: true, // Include password for credentials view
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Format credentials for response
    const credentials = spvUsers.map((spvUser) => ({
      id: spvUser.id,
      email: spvUser.user.email,
      password: spvUser.user.password, // This is the hashed password - we'll need to store plain text separately for viewing
      role: spvUser.role,
      isActive: spvUser.isActive,
      user: {
        id: spvUser.user.id,
        name: spvUser.user.name,
        email: spvUser.user.email,
      },
    }));

    logger.info("SPV credentials fetched", {
      userId: session.user.id,
      spvId: spv.id,
      credentialsCount: credentials.length,
    });

    return NextResponse.json({ credentials });

  } catch (error) {
    logger.error("Error fetching SPV credentials:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
