import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

import { hashPassword } from "@/lib/auth/password";
import { emailService } from "@/lib/email";

const createUserSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email address"),
  role: z.enum(["SPV_ADMIN", "PROJECT_MANAGER", "SITE_WORKER"], {
    required_error: "Role is required",
  }),
  jobTitle: z.string().optional(),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

/**
 * GET /api/organizations/spvs/[id]/users
 * Get all users for the SPV
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;

    // Build where clause based on user role
    const whereClause: any = { id: spvId };

    // For organization admins, restrict to their organization
    if (session.user.role === "ORGANIZATION_ADMIN") {
      if (!session.user.organizationId) {
        return NextResponse.json(
          { error: "Organization admin must have an organization ID" },
          { status: 400 }
        );
      }
      whereClause.organizationId = session.user.organizationId;
    }

    // Check if SPV exists and user has access
    const spv = await db.sPV.findFirst({
      where: whereClause,
      include: {
        spvUsers: {
          where: {
            isActive: true,
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                jobTitle: true,
                createdAt: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      users: spv.spvUsers,
    });

  } catch (error) {
    logger.error("Error fetching SPV users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/organizations/spvs/[id]/users
 * Create a new user for the SPV
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;
    const body = await request.json();
    const validatedData = createUserSchema.parse(body);

    // Verify SPV exists and belongs to user's organization (for org admins)
    const whereClause: any = { id: spvId };
    if (session.user.role === "ORGANIZATION_ADMIN") {
      whereClause.organizationId = session.user.organizationId;
    }

    const spv = await db.sPV.findFirst({
      where: whereClause,
      include: {
        organization: {
          select: {
            name: true,
            legalName: true,
          },
        },
      },
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Check if user with this email already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
      include: {
        spvUser: true, // Check if they're already an SPV user
      },
    });

    if (existingUser) {
      // If user exists and is already an SPV user, they can't be added to another SPV
      if (existingUser.spvUser) {
        return NextResponse.json(
          { error: "This user is already associated with an SPV" },
          { status: 400 }
        );
      }

      // If user exists but is not an SPV user, we can add them to this SPV
      // We'll handle this case below
    }

    // Use provided password and hash it (only needed for new users)
    const hashedPassword = existingUser ? null : await hashPassword(validatedData.password);

    // Create user and SPV user relationship in a transaction
    const result = await db.$transaction(async (tx) => {
      let user;

      if (existingUser) {
        // Update existing user to be an SPV user
        user = await tx.user.update({
          where: { id: existingUser.id },
          data: {
            role: "SPV_USER", // Update their role to SPV_USER
            jobTitle: validatedData.jobTitle || existingUser.jobTitle,
            // Don't update password for existing users
          },
        });
      } else {
        // Create new user
        user = await tx.user.create({
          data: {
            name: validatedData.name,
            email: validatedData.email,
            password: hashedPassword!,
            role: "SPV_USER", // All SPV users have this role in the users table
            jobTitle: validatedData.jobTitle,
            organizationId: spv.organizationId,
          },
        });
      }

      // Create SPV user relationship
      const spvUser = await tx.sPVUser.create({
        data: {
          userId: user.id,
          spvId: spv.id,
          role: validatedData.role,
          isActive: true,
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              jobTitle: true,
            },
          },
        },
      });

      return { user, spvUser, password: validatedData.password };
    });

    // Send credentials via email
    try {
      await emailService.sendEmail({
        to: validatedData.email,
        subject: `Welcome to ${spv.organization.name} - SPV Access Credentials`,
        html: `
          <h2>Welcome to ${spv.organization.name}</h2>
          <p>You have been granted access to the SPV: <strong>${spv.name}</strong></p>
          <p>Your role: <strong>${validatedData.role.replace('_', ' ')}</strong></p>
          
          <h3>Login Credentials:</h3>
          <p><strong>Email:</strong> ${validatedData.email}</p>
          <p><strong>Password:</strong> ${result.password}</p>
          
          <p>Please log in to the platform and change your password after your first login.</p>
          <p>If you have any questions, please contact your administrator.</p>
        `,
      });
    } catch (emailError) {
      logger.error("Failed to send credentials email:", emailError);
      // Don't fail the request if email fails
    }

    logger.info("SPV user created successfully", {
      userId: session.user.id,
      spvId: spv.id,
      newUserId: result.user.id,
      role: validatedData.role,
    });

    return NextResponse.json({
      message: "User created successfully",
      user: result.spvUser,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error creating SPV user:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}