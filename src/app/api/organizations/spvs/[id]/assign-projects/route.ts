import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";
import { UserRole } from "@prisma/client";

// Validation schema for SPV project assignment
const spvProjectAssignmentSchema = z.object({
  projectIds: z.array(z.string()).min(1, "At least one project ID is required"),
});

type SPVProjectAssignmentData = z.infer<typeof spvProjectAssignmentSchema>;

/**
 * POST /api/organizations/spvs/[id]/assign-projects
 * Assign projects to an SPV (Organization admin only)
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;

    if (!spvId) {
      return NextResponse.json(
        { error: "SPV ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData: SPVProjectAssignmentData = spvProjectAssignmentSchema.parse(body);

    // Verify the SPV exists and belongs to the organization admin's organization
    const spv = await db.sPV.findFirst({
      where: {
        id: spvId,
        organizationId: session.user.organizationId,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Verify all projects exist and belong to the organization admin's organization
    const projects = await db.project.findMany({
      where: {
        id: { in: validatedData.projectIds },
        organizationId: session.user.organizationId,
      },
      select: {
        id: true,
        name: true,
        spvId: true,
      },
    });

    if (projects.length !== validatedData.projectIds.length) {
      return NextResponse.json(
        { error: "Some projects not found or access denied" },
        { status: 404 }
      );
    }

    // Check if any projects are already assigned to other SPVs
    const alreadyAssignedProjects = projects.filter(project => 
      project.spvId && project.spvId !== spvId
    );

    if (alreadyAssignedProjects.length > 0) {
      return NextResponse.json(
        { 
          error: "Some projects are already assigned to other SPVs",
          alreadyAssigned: alreadyAssignedProjects.map(p => ({ id: p.id, name: p.name }))
        },
        { status: 400 }
      );
    }

    // Assign projects to the SPV
    const result = await db.$transaction(async (tx) => {
      // Update projects to assign them to the SPV
      const updatedProjects = await tx.project.updateMany({
        where: {
          id: { in: validatedData.projectIds },
        },
        data: {
          spvId: spvId,
        },
      });

      return {
        assignedCount: updatedProjects.count,
        spvId: spvId,
        projectIds: validatedData.projectIds,
      };
    });

    logger.info("Projects assigned to SPV by organization admin", {
      assignedBy: session.user.id,
      organizationId: session.user.organizationId,
      spvId: spvId,
      projectIds: validatedData.projectIds,
      assignedCount: result.assignedCount,
    });

    return NextResponse.json({
      message: `Successfully assigned ${result.assignedCount} project(s) to SPV`,
      data: result,
    });

  } catch (error) {
    logger.error("Error assigning projects to SPV:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/organizations/spvs/[id]/assign-projects
 * Get available projects for assignment to SPV (Organization admin only)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;

    if (!spvId) {
      return NextResponse.json(
        { error: "SPV ID is required" },
        { status: 400 }
      );
    }

    // Verify the SPV exists and belongs to the organization admin's organization
    const spv = await db.sPV.findFirst({
      where: {
        id: spvId,
        organizationId: session.user.organizationId,
      },
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Get all projects from the organization
    const allProjects = await db.project.findMany({
      where: {
        organizationId: session.user.organizationId,
      },
      select: {
        id: true,
        name: true,
        description: true,
        type: true,
        status: true,
        location: true,
        country: true,
        spvId: true,
        createdAt: true,
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Separate projects into categories
    const availableProjects = allProjects.filter(project => !project.spvId);
    const assignedToThisSPV = allProjects.filter(project => project.spvId === spvId);
    const assignedToOtherSPVs = allProjects.filter(project => project.spvId && project.spvId !== spvId);

    logger.info("Available projects fetched for SPV assignment", {
      organizationId: session.user.organizationId,
      spvId: spvId,
      totalProjects: allProjects.length,
      availableProjects: availableProjects.length,
      assignedToThisSPV: assignedToThisSPV.length,
      assignedToOtherSPVs: assignedToOtherSPVs.length,
    });

    return NextResponse.json({
      availableProjects,
      assignedToThisSPV,
      assignedToOtherSPVs,
      summary: {
        total: allProjects.length,
        available: availableProjects.length,
        assignedToThisSPV: assignedToThisSPV.length,
        assignedToOtherSPVs: assignedToOtherSPVs.length,
      },
    });

  } catch (error) {
    logger.error("Error fetching available projects for SPV assignment:", error);

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
