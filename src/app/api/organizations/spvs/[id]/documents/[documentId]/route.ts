import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * DELETE /api/organizations/spvs/[id]/documents/[documentId]
 * Delete a specific SPV document
 */
async function deleteSPVDocumentHandler(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; documentId: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to delete SPV documents",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    const { id: spvId, documentId } = await params;

    // Get document with SPV details
    const document = await db.sPVDocument.findUnique({
      where: { id: documentId },
      include: {
        spv: {
          select: {
            id: true,
            name: true,
            organizationId: true,
            verificationStatus: true,
          },
        },
        uploader: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!document) {
      throw new ApiError("Document not found", ErrorType.NOT_FOUND, 404);
    }

    if (document.spv.id !== spvId) {
      throw new ApiError("Document does not belong to this SPV", ErrorType.VALIDATION, 400);
    }

    // Check authorization
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" && 
                      session.user.organizationId === document.spv.organizationId;
    const isPlatformAdmin = session.user.role === "ADMIN";
    const isSPVUser = session.user.role === "SPV_USER";
    const isDocumentUploader = document.uploadedBy === session.user.id;

    if (!isOrgAdmin && !isPlatformAdmin && !(isSPVUser && isDocumentUploader)) {
      throw new ApiError(
        "You don't have permission to delete this document",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // If SPV user, check if they belong to this SPV and uploaded the document
    if (isSPVUser) {
      const spvUser = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          spvId: spvId,
          isActive: true,
        },
      });

      if (!spvUser) {
        throw new ApiError(
          "You don't have access to this SPV",
          ErrorType.AUTHORIZATION,
          403
        );
      }

      if (!isDocumentUploader) {
        throw new ApiError(
          "You can only delete documents you uploaded",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // If this is an SPV logo, also clear the SPV's spvLogoUrl field
    if (document.documentType === "SPV_LOGO") {
      await db.sPV.update({
        where: { id: spvId },
        data: {
          spvLogoUrl: null,
        },
      });

      logger.info(`SPV logo cleared: ${spvId}`);
    }

    // Delete the document
    await db.sPVDocument.delete({
      where: { id: documentId },
    });

    logger.info(`SPV document deleted: ${documentId} from SPV ${spvId} by user ${session.user.id}`);

    return NextResponse.json({
      message: "Document deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting SPV document:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while deleting the document",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const DELETE = withErrorHandling(deleteSPVDocumentHandler);
