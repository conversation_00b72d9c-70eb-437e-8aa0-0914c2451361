import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { spvVerificationSchema } from "@/lib/validation/schemas";

/**
 * GET /api/organizations/spvs/[id]/verification
 * Get SPV verification details
 */
async function getSPVVerificationHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to access SPV verification details",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    const spvId = params.id;

    // Get SPV with verification details
    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        documents: {
          select: {
            id: true,
            documentType: true,
            fileName: true,
            fileUrl: true,
            fileSize: true,
            mimeType: true,
            uploadedAt: true,
            verified: true,
            verifiedAt: true,
            notes: true,
          },
          orderBy: {
            uploadedAt: 'desc',
          },
        },
      },
    });

    if (!spv) {
      throw new ApiError("SPV not found", ErrorType.NOT_FOUND, 404);
    }

    // Check authorization
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" && 
                      session.user.organizationId === spv.organizationId;
    const isPlatformAdmin = session.user.role === "ADMIN";
    const isSPVUser = session.user.role === "SPV_USER";

    if (!isOrgAdmin && !isPlatformAdmin && !isSPVUser) {
      throw new ApiError(
        "You don't have permission to access this SPV",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // If SPV user, check if they belong to this SPV
    if (isSPVUser) {
      const spvUser = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          spvId: spvId,
          isActive: true,
        },
      });

      if (!spvUser) {
        throw new ApiError(
          "You don't have access to this SPV",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    return NextResponse.json({
      spv,
      canEdit: isOrgAdmin || (isSPVUser && spv.verificationStatus === "PENDING_VERIFICATION"),
      canVerify: isPlatformAdmin,
    });
  } catch (error) {
    logger.error("Error fetching SPV verification details:", error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while fetching SPV verification details",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PUT /api/organizations/spvs/[id]/verification
 * Update SPV verification details
 */
async function updateSPVVerificationHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to update SPV verification details",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    const spvId = params.id;
    const body = await request.json();
    const validatedData = spvVerificationSchema.parse(body);

    // Get SPV to check permissions
    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      select: {
        id: true,
        organizationId: true,
        verificationStatus: true,
      },
    });

    if (!spv) {
      throw new ApiError("SPV not found", ErrorType.NOT_FOUND, 404);
    }

    // Check authorization
    const isOrgAdmin = session.user.role === "ORGANIZATION_ADMIN" && 
                      session.user.organizationId === spv.organizationId;
    const isSPVUser = session.user.role === "SPV_USER";

    if (!isOrgAdmin && !isSPVUser) {
      throw new ApiError(
        "You don't have permission to update this SPV",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // If SPV user, check if they belong to this SPV
    if (isSPVUser) {
      const spvUser = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          spvId: spvId,
          isActive: true,
        },
      });

      if (!spvUser) {
        throw new ApiError(
          "You don't have access to this SPV",
          ErrorType.AUTHORIZATION,
          403
        );
      }
    }

    // Only allow updates if SPV is in editable state
    if (!["PENDING_VERIFICATION", "NEEDS_MORE_INFO"].includes(spv.verificationStatus)) {
      throw new ApiError(
        "SPV verification details cannot be updated in current status",
        ErrorType.VALIDATION,
        400
      );
    }

    // Update SPV with verification details
    const updatedSPV = await db.sPV.update({
      where: { id: spvId },
      data: {
        name: validatedData.name,
        purpose: validatedData.purpose || null,
        legalStructure: validatedData.legalStructure,
        registrationNumber: validatedData.registrationNumber,
        jurisdiction: validatedData.jurisdiction,
        country: validatedData.country,
        gstNumber: validatedData.gstNumber || null,
        cinNumber: validatedData.cinNumber || null,
        panNumber: validatedData.panNumber || null,
        incorporationDate: validatedData.incorporationDate ? new Date(validatedData.incorporationDate) : null,
        establishedDate: validatedData.establishedDate ? new Date(validatedData.establishedDate) : null,
        registeredAddress: validatedData.registeredAddress,
        address: validatedData.address || null,
        contactPersonName: validatedData.contactPersonName,
        contactPersonEmail: validatedData.contactPersonEmail,
        contactPersonMobile: validatedData.contactPersonMobile,
        contact: validatedData.contact || null,
        bankAccountNumber: validatedData.bankAccountNumber,
        ifscCode: validatedData.ifscCode,
        projectCategories: [validatedData.projectCategories],
        description: validatedData.description || null,
        taxId: validatedData.taxId || null,
        legalEntityId: validatedData.legalEntityId || null,
        verificationStatus: "IN_REVIEW", // Move to review status after details are submitted
        updatedAt: new Date(),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
      },
    });

    logger.info(`SPV verification details updated: ${spvId} by user ${session.user.id}`);

    return NextResponse.json({
      spv: updatedSPV,
      message: "SPV verification details updated successfully and submitted for review",
    });
  } catch (error) {
    logger.error("Error updating SPV verification details:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      throw new ApiError(
        "Invalid verification data provided",
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while updating SPV verification details",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getSPVVerificationHandler);
export const PUT = withErrorHandling(updateSPVVerificationHandler);
