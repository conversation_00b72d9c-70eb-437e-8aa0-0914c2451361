import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * DELETE /api/organizations/spvs/[id]/projects/[projectId]
 * Unassign a project from an SPV (Organization admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; projectId: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId, projectId } = await params;

    // Verify SPV exists and belongs to user's organization (for org admins)
    const whereClause: any = { id: spvId };
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      whereClause.organizationId = session.user.organizationId;
    }

    const spv = await db.sPV.findFirst({
      where: whereClause,
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Verify project exists and is assigned to the SPV
    const project = await db.project.findFirst({
      where: {
        id: projectId,
        spvId: spvId,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found or not assigned to this SPV" },
        { status: 404 }
      );
    }

    // For organization admins, ensure the project belongs to their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      if (project.organizationId !== session.user.organizationId) {
        return NextResponse.json(
          { error: "Access denied. Project does not belong to your organization." },
          { status: 403 }
        );
      }
    }

    // Unassign the project from the SPV and deactivate all related assignments
    await db.$transaction(async (tx) => {
      // Remove SPV assignment from project
      await tx.project.update({
        where: { id: projectId },
        data: { spvId: null },
      });

      // Deactivate all project assignments for this project
      await tx.projectAssignment.updateMany({
        where: {
          projectId: projectId,
          isActive: true,
        },
        data: { isActive: false },
      });
    });

    logger.info("Project unassigned from SPV", {
      userId: session.user.id,
      organizationId: session.user.organizationId,
      spvId,
      projectId,
    });

    return NextResponse.json({
      success: true,
      message: "Project unassigned from SPV successfully",
    });

  } catch (error) {
    logger.error("Error unassigning project from SPV:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
