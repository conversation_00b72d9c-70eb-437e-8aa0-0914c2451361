import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

/**
 * GET /api/organizations/spvs/[id]/projects
 * Get projects for a specific SPV (for organization admins)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Allow organization admins and platform admins
    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;

    if (!spvId) {
      return NextResponse.json(
        { error: "SPV ID is required" },
        { status: 400 }
      );
    }

    // For organization admins, verify they own the SPV
    if (session.user.role === "ORGANIZATION_ADMIN") {
      const spv = await db.sPV.findFirst({
        where: {
          id: spvId,
          organizationId: session.user.organizationId,
        },
      });

      if (!spv) {
        return NextResponse.json(
          { error: "SPV not found or access denied" },
          { status: 404 }
        );
      }
    }

    // Get projects for the SPV
    const projects = await db.project.findMany({
      where: {
        spvId: spvId,
      },
      select: {
        id: true,
        name: true,
        description: true,
        type: true,
        status: true,
        location: true,
        country: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    logger.info("SPV projects fetched for organization admin", {
      userId: session.user.id,
      spvId: spvId,
      projectCount: projects.length,
    });

    return NextResponse.json({
      success: true,
      projects: projects,
    });

  } catch (error) {
    logger.error("Error fetching SPV projects:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
