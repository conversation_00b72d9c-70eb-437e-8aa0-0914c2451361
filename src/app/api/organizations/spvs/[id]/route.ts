import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Import the comprehensive schema for updates
import { comprehensiveSPVCreationSchema } from "@/lib/validation/schemas";

// Comprehensive schema for SPV updates - make all fields optional
const comprehensiveSpvUpdateSchema = comprehensiveSPVCreationSchema.partial();

// Legacy schema for backward compatibility
const legacySpvUpdateSchema = z.object({
  // Basic Information
  name: z.string().min(1, "SPV name is required").optional(),
  purpose: z.string().optional(),
  legalStructure: z.string().optional(),
  projectCategories: z.string().optional(),
  adminName: z.string().optional(),
  adminEmail: z.string().optional(),

  // Legal & Compliance
  jurisdiction: z.string().optional(),
  country: z.string().optional(),
  gstNumber: z.string().optional(),
  cinNumber: z.string().optional(),
  panNumber: z.string().optional(),
  incorporationDate: z.string().optional(),
  registeredAddress: z.string().optional(),
  description: z.string().optional(),

  // Legacy fields (for backward compatibility)
  registrationNumber: z.string().optional(),
  taxId: z.string().optional(),
  address: z.string().optional(),
  legalEntityId: z.string().optional(),
  contact: z.string().optional(),
});

/**
 * GET /api/organizations/spvs/[id]
 * Get a specific SPV with detailed information for the organization
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;

    if (!spvId) {
      return NextResponse.json(
        { error: "SPV ID is required" },
        { status: 400 }
      );
    }

    // Build where clause based on user role
    const whereClause: any = { id: spvId };

    // For organization admins, restrict to their organization
    if (session.user.role === "ORGANIZATION_ADMIN") {
      if (!session.user.organizationId) {
        return NextResponse.json(
          { error: "Organization admin must have an organization ID" },
          { status: 400 }
        );
      }
      whereClause.organizationId = session.user.organizationId;
    }
    // Platform admins can access any SPV

    // Get the SPV with detailed information
    const spv = await db.sPV.findFirst({
      where: whereClause,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
            status: true,
            verificationStatus: true,
            country: true,
            industry: true,
          },
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
            type: true,
            createdAt: true,
            estimatedReductions: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        spvUsers: {
          where: {
            isActive: true,
          },
          select: {
            id: true,
            role: true,
            isActive: true,
            createdAt: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        documents: {
          select: {
            id: true,
            documentType: true,
            fileName: true,
            fileUrl: true,
            fileSize: true,
            mimeType: true,
            uploadedAt: true,
            verified: true,
            verifiedBy: true,
            verifiedAt: true,
            notes: true,
            uploader: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: {
            uploadedAt: "desc",
          },
        },
        _count: {
          select: {
            projects: true,
            spvUsers: true,
            documents: true,
          },
        },
      },
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    logger.info("SPV details fetched for organization admin", {
      userId: session.user.id,
      spvId: spvId,
      organizationId: session.user.organizationId,
    });

    return NextResponse.json({ spv });

  } catch (error) {
    logger.error("Error fetching SPV details:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/organizations/spvs/[id]
 * Update SPV details
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN" && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: spvId } = await params;
    const body = await request.json();

    console.log("PUT /api/organizations/spvs/[id] - Request data:", {
      spvId,
      body,
      userRole: session.user.role,
      organizationId: session.user.organizationId
    });

    // Try to validate with comprehensive schema first, fallback to legacy
    let validatedData: any;
    let isComprehensiveForm = false;

    try {
      validatedData = comprehensiveSpvUpdateSchema.parse(body);
      isComprehensiveForm = true;
      console.log("Using comprehensive schema for SPV update");
    } catch (error) {
      console.log("Comprehensive schema failed, trying legacy schema:", error);
      try {
        validatedData = legacySpvUpdateSchema.parse(body);
        console.log("Using legacy schema for SPV update");
      } catch (legacyError) {
        console.error("Both schemas failed:", { comprehensive: error, legacy: legacyError });
        return NextResponse.json(
          { error: "Invalid form data. Please check all required fields." },
          { status: 400 }
        );
      }
    }

    // Build where clause based on user role
    const whereClause: any = { id: spvId };

    // For organization admins, restrict to their organization
    if (session.user.role === "ORGANIZATION_ADMIN") {
      if (!session.user.organizationId) {
        return NextResponse.json(
          { error: "Organization admin must have an organization ID" },
          { status: 400 }
        );
      }
      whereClause.organizationId = session.user.organizationId;
    }

    // Check if SPV exists and user has access
    const existingSPV = await db.sPV.findFirst({
      where: whereClause,
    });

    if (!existingSPV) {
      return NextResponse.json(
        { error: "SPV not found or access denied" },
        { status: 404 }
      );
    }

    // Prepare update data based on form type
    const updateData: any = {};

    console.log("Processing update data for comprehensive form:", isComprehensiveForm);
    console.log("Validated data keys:", Object.keys(validatedData));

    if (isComprehensiveForm) {
      // Handle comprehensive form fields
      if (validatedData.name) updateData.name = validatedData.name;
      if (validatedData.parentCompany !== undefined) updateData.parentCompany = validatedData.parentCompany || null;
      if (validatedData.spvType !== undefined) updateData.spvType = validatedData.spvType || null;
      if (validatedData.dateOfIncorporation !== undefined) {
        updateData.dateOfIncorporation = validatedData.dateOfIncorporation ? new Date(validatedData.dateOfIncorporation) : null;
      }
      if (validatedData.cinNumber !== undefined) updateData.cinNumber = validatedData.cinNumber || null;
      if (validatedData.registeredAddress !== undefined) updateData.registeredAddress = validatedData.registeredAddress || null;
      if (validatedData.stateOfIncorporation !== undefined) updateData.stateOfIncorporation = validatedData.stateOfIncorporation || null;
      if (validatedData.panNumber !== undefined) updateData.panNumber = validatedData.panNumber || null;
      if (validatedData.gstNumber !== undefined) updateData.gstNumber = validatedData.gstNumber || null;
      if (validatedData.spvLogoUrl !== undefined) updateData.spvLogoUrl = validatedData.spvLogoUrl || null;
      if (validatedData.description !== undefined) updateData.description = validatedData.description || null;

      // Ownership & Stakeholders
      if (validatedData.promoterName !== undefined) updateData.promoterName = validatedData.promoterName || null;
      if (validatedData.equityHolders !== undefined) updateData.equityHolders = validatedData.equityHolders || null;
      if (validatedData.boardMembers !== undefined) updateData.boardMembers = validatedData.boardMembers || null;
      if (validatedData.authorizedSignatory !== undefined) updateData.authorizedSignatory = validatedData.authorizedSignatory || null;
      if (validatedData.registeredRepresentative !== undefined) updateData.registeredRepresentative = validatedData.registeredRepresentative || null;

      // Financial Details - Handle Decimal conversion
      if (validatedData.authorizedCapital !== undefined) {
        updateData.authorizedCapital = validatedData.authorizedCapital ? Number(validatedData.authorizedCapital) : null;
      }
      if (validatedData.paidUpCapital !== undefined) {
        updateData.paidUpCapital = validatedData.paidUpCapital ? Number(validatedData.paidUpCapital) : null;
      }
      if (validatedData.bankAccountDetails !== undefined) updateData.bankAccountDetails = validatedData.bankAccountDetails || null;
      if (validatedData.debtProvider !== undefined) updateData.debtProvider = validatedData.debtProvider || null;
      if (validatedData.debtEquityRatio !== undefined) updateData.debtEquityRatio = validatedData.debtEquityRatio || null;
      if (validatedData.fundingSource !== undefined) updateData.fundingSource = validatedData.fundingSource || null;
      if (validatedData.ppaSignedWith !== undefined) updateData.ppaSignedWith = validatedData.ppaSignedWith || null;

      // Custom fields
      if (validatedData.customFields !== undefined) updateData.customFields = validatedData.customFields || null;
    } else {
      // Handle legacy form fields
      if (validatedData.name) updateData.name = validatedData.name;
      if (validatedData.purpose !== undefined) updateData.purpose = validatedData.purpose;
      if (validatedData.legalStructure !== undefined) updateData.legalStructure = validatedData.legalStructure;
      if (validatedData.projectCategories) updateData.projectCategories = [validatedData.projectCategories];
      if (validatedData.jurisdiction !== undefined) updateData.jurisdiction = validatedData.jurisdiction;
      if (validatedData.country !== undefined) updateData.country = validatedData.country;
      if (validatedData.gstNumber !== undefined) updateData.gstNumber = validatedData.gstNumber;
      if (validatedData.cinNumber !== undefined) updateData.cinNumber = validatedData.cinNumber;
      if (validatedData.panNumber !== undefined) updateData.panNumber = validatedData.panNumber;
      if (validatedData.incorporationDate !== undefined) {
        updateData.incorporationDate = validatedData.incorporationDate ? new Date(validatedData.incorporationDate) : null;
      }
      if (validatedData.registeredAddress !== undefined) updateData.registeredAddress = validatedData.registeredAddress;
      if (validatedData.description !== undefined) updateData.description = validatedData.description;
      if (validatedData.registrationNumber !== undefined) updateData.registrationNumber = validatedData.registrationNumber;
      if (validatedData.taxId !== undefined) updateData.taxId = validatedData.taxId;
      if (validatedData.address !== undefined) updateData.address = validatedData.address;
      if (validatedData.legalEntityId !== undefined) updateData.legalEntityId = validatedData.legalEntityId;
      if (validatedData.contact !== undefined) updateData.contact = validatedData.contact;
    }

    console.log("Final update data being sent to database:", updateData);

    // Update the SPV
    const updatedSPV = await db.sPV.update({
      where: { id: spvId },
      data: updateData,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
      },
    });

    console.log("SPV updated successfully:", updatedSPV.id);

    // Update admin user if admin fields are provided
    if (validatedData.adminName !== undefined || validatedData.adminEmail !== undefined) {
      try {
        // Find the SPV admin user
        const spvAdmin = await db.sPVUser.findFirst({
          where: {
            spvId: spvId,
            role: "SPV_ADMIN"
          },
          include: {
            user: true
          }
        });

        if (spvAdmin) {
          // Update the admin user
          await db.user.update({
            where: { id: spvAdmin.user.id },
            data: {
              ...(validatedData.adminName !== undefined && { name: validatedData.adminName }),
              ...(validatedData.adminEmail !== undefined && { email: validatedData.adminEmail }),
            }
          });
        }
      } catch (adminUpdateError) {
        // Log the error but don't fail the SPV update
        logger.warn(`Failed to update admin user for SPV ${spvId}:`, adminUpdateError);
      }
    }

    logger.info(`SPV ${spvId} updated by user ${session.user.id}`);

    return NextResponse.json({
      message: "SPV updated successfully",
      spv: updatedSPV,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid input data", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error updating SPV:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
