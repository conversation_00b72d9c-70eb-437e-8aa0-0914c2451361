import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/organizations/projects/available
 * Get available projects for assignment by organization admin
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const spvUserId = searchParams.get("spvUserId");
    const unassignedOnly = searchParams.get("unassignedOnly") === "true";

    // Get all projects belonging to the organization
    const whereClause: any = {
      organizationId: session.user.organizationId,
    };

    // If we want only unassigned projects
    if (unassignedOnly) {
      whereClause.spvId = null;
    }

    const projects = await db.project.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        description: true,
        type: true,
        status: true,
        location: true,
        country: true,
        spvId: true,
        createdAt: true,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // If spvUserId is provided, filter out projects already assigned to that user
    let availableProjects = projects;
    if (spvUserId) {
      const existingAssignments = await db.projectAssignment.findMany({
        where: {
          spvUserId: spvUserId,
          isActive: true,
        },
        select: {
          projectId: true,
        },
      });

      const assignedProjectIds = existingAssignments.map(a => a.projectId);
      availableProjects = projects.filter(p => !assignedProjectIds.includes(p.id));
    }

    logger.info("Available projects fetched for organization admin", {
      userId: session.user.id,
      organizationId: session.user.organizationId,
      projectCount: availableProjects.length,
      spvUserId,
      unassignedOnly,
    });

    return NextResponse.json({
      success: true,
      data: {
        projects: availableProjects,
      },
    });

  } catch (error) {
    logger.error("Error fetching available projects for organization:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
