import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/organizations/projects/[id]
 * Get a specific project by ID for organization admin
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN && session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin or platform admin role required." },
        { status: 403 }
      );
    }

    const { id: projectId } = await params;

    // Debug logging
    logger.info("Organization project fetch request", {
      projectId,
      userId: session.user.id,
      userRole: session.user.role,
      organizationId: session.user.organizationId,
    });

    // Build where clause for organization admin
    const whereClause: any = { id: projectId };
    
    // For organization admins, ensure the project belongs to their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      whereClause.organizationId = session.user.organizationId;
    }

    logger.info("Executing database query", {
      projectId,
      whereClause: JSON.stringify(whereClause),
      userId: session.user.id,
    });

    const project = await db.project.findFirst({
      where: whereClause,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    logger.info("Database query completed", {
      projectId,
      projectFound: !!project,
      projectName: project?.name,
    });

    if (!project) {
      logger.warn("Project not found for organization admin", {
        projectId,
        userId: session.user.id,
        userRole: session.user.role,
        organizationId: session.user.organizationId,
      });
      
      return NextResponse.json(
        { error: "Project not found or access denied" },
        { status: 404 }
      );
    }

    logger.info("Project found successfully for organization admin", {
      projectId: project.id,
      projectName: project.name,
      projectOrganizationId: project.organizationId,
      userOrganizationId: session.user.organizationId,
    });

    return NextResponse.json({
      success: true,
      data: {
        project,
      },
    });

  } catch (error) {
    logger.error("Error fetching organization project:", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      userId: session?.user?.id,
      userRole: session?.user?.role,
      projectId: (await params).id,
    });

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
