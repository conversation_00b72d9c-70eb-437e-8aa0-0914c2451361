import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/organizations/spv-users
 * Get SPV users for organization admin's SPVs
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const spvId = searchParams.get("spvId");

    // Build where clause for organization admin's SPV users
    const whereClause: any = {
      spv: {
        organizationId: session.user.organizationId,
      },
      isActive: true,
    };

    // Filter by specific SPV if provided
    if (spvId) {
      whereClause.spvId = spvId;
    }

    const spvUsers = await db.sPVUser.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            jobTitle: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
        projectAssignments: {
          where: {
            isActive: true,
          },
          include: {
            project: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    logger.info("Organization SPV users fetched", {
      userId: session.user.id,
      organizationId: session.user.organizationId,
      spvUserCount: spvUsers.length,
      spvId,
    });

    return NextResponse.json({
      success: true,
      data: {
        spvUsers,
      },
    });

  } catch (error) {
    logger.error("Error fetching organization SPV users:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
