import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { z } from "zod";

const projectAssignmentSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
  spvUserId: z.string().min(1, "SPV User ID is required"),
  permissions: z.record(z.boolean()).optional(),
});

const bulkProjectAssignmentSchema = z.object({
  projectIds: z.array(z.string()).min(1, "At least one project ID is required"),
  spvUserId: z.string().min(1, "SPV User ID is required"),
  permissions: z.record(z.boolean()).optional(),
});

/**
 * GET /api/organizations/project-assignments
 * Get project assignments for organization admin's SPVs
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "50");
    const spvId = searchParams.get("spvId");

    const skip = (page - 1) * limit;

    // Build where clause for organization admin's SPVs
    const whereClause: any = {
      spvUser: {
        spv: {
          organizationId: session.user.organizationId,
        },
      },
      isActive: true,
    };

    // Filter by specific SPV if provided
    if (spvId) {
      whereClause.spvUser.spvId = spvId;
    }

    const assignments = await db.projectAssignment.findMany({
      where: whereClause,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
            status: true,
            location: true,
          },
        },
        spvUser: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                jobTitle: true,
              },
            },
            spv: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        assignedAt: "desc",
      },
      skip,
      take: limit,
    });

    const totalCount = await db.projectAssignment.count({
      where: whereClause,
    });

    logger.info("Organization project assignments fetched", {
      userId: session.user.id,
      organizationId: session.user.organizationId,
      assignmentCount: assignments.length,
      totalCount,
    });

    return NextResponse.json({
      success: true,
      data: {
        assignments,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages: Math.ceil(totalCount / limit),
        },
      },
    });

  } catch (error) {
    logger.error("Error fetching organization project assignments:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/organizations/project-assignments
 * Create a new project assignment for organization admin's SPV users
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin role required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Check if it's bulk assignment or single assignment
    const isBulk = Array.isArray(body.projectIds);
    const validatedData = isBulk 
      ? bulkProjectAssignmentSchema.parse(body)
      : projectAssignmentSchema.parse(body);

    // Verify the SPV user exists and belongs to the organization
    const spvUser = await db.sPVUser.findFirst({
      where: {
        id: validatedData.spvUserId,
        spv: {
          organizationId: session.user.organizationId,
        },
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            jobTitle: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found or not in your organization" },
        { status: 404 }
      );
    }

    if (isBulk) {
      const bulkData = validatedData as z.infer<typeof bulkProjectAssignmentSchema>;

      // Verify all projects belong to the organization
      const projects = await db.project.findMany({
        where: {
          id: { in: bulkData.projectIds },
          organizationId: session.user.organizationId,
        },
      });

      if (projects.length !== bulkData.projectIds.length) {
        return NextResponse.json(
          { error: "Some projects not found or not accessible" },
          { status: 404 }
        );
      }

      // Create bulk assignments and update projects' spvId in a transaction
      const assignments = await db.$transaction(async (tx) => {
        // Create the project assignments
        const newAssignments = await tx.projectAssignment.createMany({
          data: bulkData.projectIds.map(projectId => ({
            projectId,
            spvUserId: validatedData.spvUserId,
            assignedBy: session.user.id,
            permissions: bulkData.permissions || {},
          })),
        });

        // Update all projects' spvId to link them to the SPV
        await tx.project.updateMany({
          where: { id: { in: bulkData.projectIds } },
          data: { spvId: spvUser.spv.id },
        });

        return newAssignments;
      });

      logger.info("Bulk project assignments created by organization admin", {
        assignedBy: session.user.id,
        organizationId: session.user.organizationId,
        spvUserId: validatedData.spvUserId,
        projectCount: bulkData.projectIds.length,
      });

      return NextResponse.json({
        success: true,
        message: `${bulkData.projectIds.length} projects assigned successfully`,
        data: { assignments },
      });

    } else {
      const singleData = validatedData as z.infer<typeof projectAssignmentSchema>;

      // Verify the project belongs to the organization
      const project = await db.project.findFirst({
        where: {
          id: singleData.projectId,
          organizationId: session.user.organizationId,
        },
      });

      if (!project) {
        return NextResponse.json(
          { error: "Project not found or not accessible" },
          { status: 404 }
        );
      }

      // Create the assignment and update project's spvId in a transaction
      const assignment = await db.$transaction(async (tx) => {
        // Create the project assignment
        const newAssignment = await tx.projectAssignment.create({
          data: {
            projectId: singleData.projectId,
            spvUserId: validatedData.spvUserId,
            assignedBy: session.user.id,
            permissions: singleData.permissions || {},
          },
        });

        // Update the project's spvId to link it to the SPV
        await tx.project.update({
          where: { id: singleData.projectId },
          data: { spvId: spvUser.spv.id },
        });

        return newAssignment;
      });

      logger.info("Project assignment created by organization admin", {
        assignedBy: session.user.id,
        organizationId: session.user.organizationId,
        projectId: singleData.projectId,
        spvUserId: validatedData.spvUserId,
      });

      return NextResponse.json({
        success: true,
        message: "Project assigned successfully",
        data: { assignment },
      });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error creating organization project assignment:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/organizations/project-assignments
 * Remove a project assignment (Organization admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Organization admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get("assignmentId");

    if (!assignmentId) {
      return NextResponse.json(
        { error: "Assignment ID is required" },
        { status: 400 }
      );
    }

    // Verify the assignment exists and belongs to the organization
    const assignment = await db.projectAssignment.findFirst({
      where: {
        id: assignmentId,
        spvUser: {
          spv: {
            organizationId: session.user.organizationId,
          },
        },
        isActive: true,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        spvUser: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "Assignment not found or access denied" },
        { status: 404 }
      );
    }

    // Soft delete the assignment
    await db.projectAssignment.update({
      where: { id: assignmentId },
      data: { isActive: false },
    });

    logger.info("Project assignment removed by organization admin", {
      removedBy: session.user.id,
      organizationId: session.user.organizationId,
      assignmentId,
      projectId: assignment.project.id,
      spvUserId: assignment.spvUserId,
    });

    return NextResponse.json({
      success: true,
      message: "Project assignment removed successfully",
    });

  } catch (error) {
    logger.error("Error removing organization project assignment", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
