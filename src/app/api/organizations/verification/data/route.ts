/**
 * Organization Admin Data Verification API
 * 
 * Handles verification of SPV admin approved data by organization admins
 * and submission to VVB for final verification
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";
import { ProjectAuditService } from "@/lib/audit/project-audit";
import { DataVerificationStatus } from "@prisma/client";

// Schema for org admin verification action
const orgVerificationActionSchema = z.object({
  unitLogIds: z.array(z.string()).min(1, "At least one unit log ID is required"),
  action: z.enum([
    "ORG_ADMIN_VERIFY",    // Organization Admin verification
    "ORG_ADMIN_REJECT",    // Organization Admin rejection
    "SUBMIT_TO_VVB",       // Submit to VVB for final verification
  ]),
  notes: z.string().max(1000, "Notes must be less than 1000 characters").optional(),
  rejectionReason: z.string().max(500, "Rejection reason must be less than 500 characters").optional(),
}).refine((data) => {
  // If it's a rejection action, rejection reason is required
  if (data.action === "ORG_ADMIN_REJECT") {
    return data.rejectionReason && data.rejectionReason.trim().length > 0;
  }
  return true;
}, {
  message: "Rejection reason is required for rejection actions",
  path: ["rejectionReason"],
});

/**
 * GET /api/organizations/verification/data
 * Get data entries pending organization admin verification
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access verification data" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN") {
      return NextResponse.json(
        { error: "Only organization admins can access this endpoint" },
        { status: 403 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(req.url);
    const status = searchParams.get("status") || "SUBMITTED_TO_ORG_ADMIN";
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    // Get unit logs pending org admin verification
    const unitLogs = await db.unitLog.findMany({
      where: {
        verificationStatus: status as DataVerificationStatus,
        project: {
          organizationId: session.user.organizationId,
        },
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
            status: true,
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        logger: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        verifier: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        rejectedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        verificationLogs: {
          include: {
            verifier: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await db.unitLog.count({
      where: {
        verificationStatus: status as DataVerificationStatus,
        project: {
          organizationId: session.user.organizationId,
        },
      },
    });

    // Get statistics
    const stats = await db.unitLog.groupBy({
      by: ["verificationStatus"],
      where: {
        project: {
          organizationId: session.user.organizationId,
        },
      },
      _count: {
        id: true,
      },
    });

    const statistics = stats.reduce((acc, stat) => {
      acc[stat.verificationStatus] = stat._count.id;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      success: true,
      data: {
        unitLogs,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit),
        },
        statistics,
      },
    });

  } catch (error) {
    logger.error("Error fetching org verification data", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/organizations/verification/data
 * Perform verification action on data entries
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to perform verification actions" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ORGANIZATION_ADMIN") {
      return NextResponse.json(
        { error: "Only organization admins can perform verification actions" },
        { status: 403 }
      );
    }

    if (!session.user.organizationId) {
      return NextResponse.json(
        { error: "You must be part of an organization" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const validatedData = orgVerificationActionSchema.parse(body);

    const results = [];
    const now = new Date();

    // Process each unit log
    for (const unitLogId of validatedData.unitLogIds) {
      // Get the unit log with project info
      const unitLog = await db.unitLog.findFirst({
        where: {
          id: unitLogId,
          project: {
            organizationId: session.user.organizationId,
          },
        },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              organizationId: true,
            },
          },
          logger: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!unitLog) {
        continue; // Skip if not found or not in user's organization
      }

      // Validate action based on current status
      const canPerformAction = validateOrgVerificationAction(
        validatedData.action,
        unitLog.verificationStatus,
        unitLog.loggedBy === session.user.id
      );

      if (!canPerformAction.allowed) {
        results.push({
          unitLogId,
          success: false,
          error: canPerformAction.reason,
        });
        continue;
      }

      // Determine new status and update data
      const updateData = getOrgVerificationStatusUpdate(
        validatedData.action,
        session.user.id,
        now,
        validatedData.notes,
        validatedData.rejectionReason
      );

      // Update the unit log
      const updatedUnitLog = await db.unitLog.update({
        where: { id: unitLogId },
        data: updateData,
      });

      // Create verification log
      await db.dataVerificationLog.create({
        data: {
          unitLogId,
          fromStatus: unitLog.verificationStatus,
          toStatus: updateData.verificationStatus,
          verifiedBy: session.user.id,
          verificationNotes: validatedData.notes,
          metadata: {
            action: validatedData.action,
            rejectionReason: validatedData.rejectionReason,
          },
        },
      });

      // Log audit event for organization admin verification action
      try {
        // Get SPV context if the data entry was made by an SPV user
        let spvId: string | undefined;
        if (unitLog.logger) {
          try {
            const spvUser = await db.sPVUser.findFirst({
              where: { userId: unitLog.logger.id },
              select: { spvId: true }
            });
            spvId = spvUser?.spvId;
          } catch (spvError) {
            // SPV context not found, continue without it
          }
        }

        await ProjectAuditService.logVerificationAction(
          unitLogId,
          unitLog.project.id,
          session.user.id,
          unitLog.project.organizationId,
          validatedData.action,
          unitLog.verificationStatus,
          updateData.verificationStatus,
          "ORGANIZATION_ADMIN",
          validatedData.notes,
          validatedData.rejectionReason,
          req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined,
          req.headers.get('user-agent') || undefined,
          spvId // Include SPV context if available
        );
      } catch (auditError) {
        // Don't fail the main operation if audit logging fails
        logger.error("Failed to log organization admin verification audit event:", auditError);
      }

      results.push({
        unitLogId,
        success: true,
        data: updatedUnitLog,
      });
    }

    return NextResponse.json({
      success: true,
      message: `${validatedData.action.replace('_', ' ').toLowerCase()} completed successfully`,
      results,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error performing org verification action", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// Helper function to validate org verification actions
function validateOrgVerificationAction(
  action: string,
  currentStatus: string,
  isOwner: boolean
): { allowed: boolean; reason?: string } {
  const transitions: Record<string, { fromStatuses: string[]; prohibitsOwnership?: boolean }> = {
    ORG_ADMIN_VERIFY: {
      fromStatuses: ["SPV_ADMIN_VERIFIED", "SUBMITTED_TO_ORG_ADMIN"],
      prohibitsOwnership: true,
    },
    ORG_ADMIN_REJECT: {
      fromStatuses: ["SPV_ADMIN_VERIFIED", "SUBMITTED_TO_ORG_ADMIN", "ORG_REJECTED"],
      prohibitsOwnership: true,
    },
    SUBMIT_TO_VVB: {
      fromStatuses: ["ORG_APPROVED", "VERIFIED"],
    },
  };

  const transition = transitions[action];
  if (!transition) {
    return { allowed: false, reason: "Invalid action" };
  }

  if (!transition.fromStatuses.includes(currentStatus)) {
    return { allowed: false, reason: `Cannot ${action} from current status: ${currentStatus}` };
  }

  if (transition.prohibitsOwnership && isOwner) {
    return { allowed: false, reason: "You cannot verify your own entries" };
  }

  return { allowed: true };
}

// Helper function to get status update data
function getOrgVerificationStatusUpdate(
  action: string,
  userId: string,
  now: Date,
  notes?: string,
  rejectionReason?: string
) {
  switch (action) {
    case "ORG_ADMIN_VERIFY":
      return {
        verificationStatus: "ORG_APPROVED" as any,
        orgApprovedBy: userId,
        orgApprovedAt: now,
        orgApprovalNotes: notes,
        updatedAt: now,
      };
    case "ORG_ADMIN_REJECT":
      return {
        verificationStatus: "ORG_REJECTED" as any,
        orgApprovedBy: null,
        orgApprovedAt: null,
        orgApprovalNotes: null,
        rejectedBy: userId,
        rejectedAt: now,
        rejectionReason: rejectionReason || notes,
        updatedAt: now,
      };
    case "SUBMIT_TO_VVB":
      return {
        verificationStatus: "SUBMITTED_TO_VVB" as any,
        verifiedBy: userId,
        verifiedAt: now,
        verificationNotes: notes,
        updatedAt: now,
      };
    default:
      throw new Error(`Unknown action: ${action}`);
  }
}
