/**
 * Organization Brokers API Endpoints
 * 
 * Handles CRUD operations for brokers within an organization context.
 * Follows the same patterns as SPV management.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { brokerCreationSchema, brokerQuerySchema } from "@/lib/validation/schemas";
import { ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";

/**
 * GET /api/organizations/brokers
 * Get all brokers for the current user's organization
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (!session.user.organizationId) {
      throw new ApiError(
        "Organization membership required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedParams = brokerQuerySchema.parse(queryParams);

    // Build where clause
    const where: any = {
      organizationId: session.user.organizationId,
    };

    if (validatedParams.search) {
      where.OR = [
        { name: { contains: validatedParams.search, mode: "insensitive" } },
        { legalName: { contains: validatedParams.search, mode: "insensitive" } },
        { email: { contains: validatedParams.search, mode: "insensitive" } },
      ];
    }

    if (validatedParams.status) {
      where.status = validatedParams.status;
    }

    if (validatedParams.operatingModel) {
      where.operatingModel = validatedParams.operatingModel;
    }

    if (validatedParams.verificationStatus) {
      where.verificationStatus = validatedParams.verificationStatus;
    }

    // Build order by clause
    const orderBy: any = {};
    if (validatedParams.sortBy === "organization") {
      orderBy.organization = { name: validatedParams.sortOrder };
    } else {
      orderBy[validatedParams.sortBy] = validatedParams.sortOrder;
    }

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;

    // Get total count
    const totalCount = await db.broker.count({ where });

    // Get brokers with organization details
    const brokers = await db.broker.findMany({
      where,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            clients: true,
            transactions: true,
          },
        },
      },
      orderBy,
      skip,
      take: validatedParams.limit,
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNext = validatedParams.page < totalPages;
    const hasPrev = validatedParams.page > 1;

    return NextResponse.json({
      brokers,
      pagination: {
        page: validatedParams.page,
        limit: validatedParams.limit,
        totalCount,
        totalPages,
        hasNext,
        hasPrev,
      },
      filters: {
        search: validatedParams.search,
        status: validatedParams.status,
        operatingModel: validatedParams.operatingModel,
        verificationStatus: validatedParams.verificationStatus,
        sortBy: validatedParams.sortBy,
        sortOrder: validatedParams.sortOrder,
      },
    });
  } catch (error) {
    console.error("Error fetching organization brokers:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/organizations/brokers
 * Create a new broker for the current user's organization
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (!session.user.organizationId) {
      throw new ApiError(
        "Organization membership required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = brokerCreationSchema.parse(body);

    // Check if broker with same email already exists
    const existingBroker = await db.broker.findUnique({
      where: { email: validatedData.email },
    });

    if (existingBroker) {
      throw new ApiError(
        "A broker with this email already exists",
        ErrorType.VALIDATION_ERROR,
        400
      );
    }

    // Create the broker
    const broker = await db.broker.create({
      data: {
        name: validatedData.name,
        legalName: validatedData.legalName || null,
        description: validatedData.description || null,
        website: validatedData.website || null,
        email: validatedData.email,
        phoneNumber: validatedData.phoneNumber || null,
        address: validatedData.address || null,
        city: validatedData.city || null,
        state: validatedData.state || null,
        postalCode: validatedData.postalCode || null,
        country: validatedData.country || null,
        licenseNumber: validatedData.licenseNumber || null,
        licenseType: validatedData.licenseType || null,
        licenseIssuer: validatedData.licenseIssuer || null,
        licenseExpiryDate: validatedData.licenseExpiryDate ? new Date(validatedData.licenseExpiryDate) : null,
        registrationNumber: validatedData.registrationNumber || null,
        taxId: validatedData.taxId || null,
        operatingModel: validatedData.operatingModel || "INDEPENDENT",
        commissionRate: validatedData.commissionRate || 0.02,
        minimumCommission: validatedData.minimumCommission || null,
        maximumCommission: validatedData.maximumCommission || null,
        specializations: validatedData.specializations || null,
        servicesOffered: validatedData.servicesOffered || null,
        targetMarkets: validatedData.targetMarkets || null,
        bondAmount: validatedData.bondAmount || null,
        insuranceAmount: validatedData.insuranceAmount || null,
        creditRating: validatedData.creditRating || null,
        establishedDate: validatedData.establishedDate ? new Date(validatedData.establishedDate) : null,
        organizationId: session.user.organizationId,
        status: "PENDING",
        verificationStatus: "PENDING",
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      broker,
      message: "Broker created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
