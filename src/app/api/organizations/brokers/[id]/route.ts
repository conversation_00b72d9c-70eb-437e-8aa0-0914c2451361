/**
 * Organization Broker Individual API Endpoints
 * 
 * Handles individual broker operations within an organization context.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { brokerUpdateSchema } from "@/lib/validation/schemas";
import { ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";

/**
 * GET /api/organizations/brokers/[id]
 * Get a specific broker by ID within the organization
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (!session.user.organizationId) {
      throw new ApiError(
        "Organization membership required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    const brokerId = params.id;

    // Get broker with full details
    const broker = await db.broker.findFirst({
      where: {
        id: brokerId,
        organizationId: session.user.organizationId,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        clients: {
          select: {
            id: true,
            clientName: true,
            clientType: true,
            status: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10, // Limit to recent clients
        },
        transactions: {
          select: {
            id: true,
            transactionType: true,
            amount: true,
            commissionAmount: true,
            status: true,
            transactionDate: true,
          },
          orderBy: {
            transactionDate: "desc",
          },
          take: 10, // Limit to recent transactions
        },
        _count: {
          select: {
            clients: true,
            transactions: true,
          },
        },
      },
    });

    if (!broker) {
      throw new ApiError(
        "Broker not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    return NextResponse.json({
      broker,
    });
  } catch (error) {
    console.error("Error fetching broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/organizations/brokers/[id]
 * Update a specific broker within the organization
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (!session.user.organizationId) {
      throw new ApiError(
        "Organization membership required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    const brokerId = params.id;

    // Check if broker exists and belongs to organization
    const existingBroker = await db.broker.findFirst({
      where: {
        id: brokerId,
        organizationId: session.user.organizationId,
      },
    });

    if (!existingBroker) {
      throw new ApiError(
        "Broker not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = brokerUpdateSchema.parse(body);

    // Check if email is being changed and if it conflicts with another broker
    if (validatedData.email && validatedData.email !== existingBroker.email) {
      const emailConflict = await db.broker.findFirst({
        where: {
          email: validatedData.email,
          id: { not: brokerId },
        },
      });

      if (emailConflict) {
        throw new ApiError(
          "A broker with this email already exists",
          ErrorType.VALIDATION_ERROR,
          400
        );
      }
    }

    // Update the broker
    const updatedBroker = await db.broker.update({
      where: { id: brokerId },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.legalName !== undefined && { legalName: validatedData.legalName }),
        ...(validatedData.description !== undefined && { description: validatedData.description }),
        ...(validatedData.website !== undefined && { website: validatedData.website }),
        ...(validatedData.email && { email: validatedData.email }),
        ...(validatedData.phoneNumber !== undefined && { phoneNumber: validatedData.phoneNumber }),
        ...(validatedData.address !== undefined && { address: validatedData.address }),
        ...(validatedData.city !== undefined && { city: validatedData.city }),
        ...(validatedData.state !== undefined && { state: validatedData.state }),
        ...(validatedData.postalCode !== undefined && { postalCode: validatedData.postalCode }),
        ...(validatedData.country !== undefined && { country: validatedData.country }),
        ...(validatedData.licenseNumber !== undefined && { licenseNumber: validatedData.licenseNumber }),
        ...(validatedData.licenseType !== undefined && { licenseType: validatedData.licenseType }),
        ...(validatedData.licenseIssuer !== undefined && { licenseIssuer: validatedData.licenseIssuer }),
        ...(validatedData.licenseExpiryDate !== undefined && { 
          licenseExpiryDate: validatedData.licenseExpiryDate ? new Date(validatedData.licenseExpiryDate) : null 
        }),
        ...(validatedData.registrationNumber !== undefined && { registrationNumber: validatedData.registrationNumber }),
        ...(validatedData.taxId !== undefined && { taxId: validatedData.taxId }),
        ...(validatedData.operatingModel && { operatingModel: validatedData.operatingModel }),
        ...(validatedData.commissionRate !== undefined && { commissionRate: validatedData.commissionRate }),
        ...(validatedData.minimumCommission !== undefined && { minimumCommission: validatedData.minimumCommission }),
        ...(validatedData.maximumCommission !== undefined && { maximumCommission: validatedData.maximumCommission }),
        ...(validatedData.specializations !== undefined && { specializations: validatedData.specializations }),
        ...(validatedData.servicesOffered !== undefined && { servicesOffered: validatedData.servicesOffered }),
        ...(validatedData.targetMarkets !== undefined && { targetMarkets: validatedData.targetMarkets }),
        ...(validatedData.bondAmount !== undefined && { bondAmount: validatedData.bondAmount }),
        ...(validatedData.insuranceAmount !== undefined && { insuranceAmount: validatedData.insuranceAmount }),
        ...(validatedData.creditRating !== undefined && { creditRating: validatedData.creditRating }),
        ...(validatedData.establishedDate !== undefined && { 
          establishedDate: validatedData.establishedDate ? new Date(validatedData.establishedDate) : null 
        }),
        ...(validatedData.status && { status: validatedData.status }),
        ...(validatedData.verificationStatus && { verificationStatus: validatedData.verificationStatus }),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      broker: updatedBroker,
      message: "Broker updated successfully",
    });
  } catch (error) {
    console.error("Error updating broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/organizations/brokers/[id]
 * Delete a specific broker within the organization
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (!session.user.organizationId) {
      throw new ApiError(
        "Organization membership required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    const brokerId = params.id;

    // Check if broker exists and belongs to organization
    const existingBroker = await db.broker.findFirst({
      where: {
        id: brokerId,
        organizationId: session.user.organizationId,
      },
    });

    if (!existingBroker) {
      throw new ApiError(
        "Broker not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Delete the broker (cascade will handle related records)
    await db.broker.delete({
      where: { id: brokerId },
    });

    return NextResponse.json({
      message: "Broker deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
