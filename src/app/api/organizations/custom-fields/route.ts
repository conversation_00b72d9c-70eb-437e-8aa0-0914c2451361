import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withTenantIsolation } from "@/lib/tenant-isolation";

// Schema for custom field definition
const customFieldSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, "Field name is required"),
  label: z.string().min(1, "Field label is required"),
  type: z.enum(["text", "number", "date", "select", "textarea", "file", "checkbox"]),
  required: z.boolean().default(false),
  placeholder: z.string().optional(),
  helpText: z.string().optional(),
  options: z.array(z.string()).optional(), // For select fields
  validation: z.object({
    min: z.number().optional(),
    max: z.number().optional(),
    pattern: z.string().optional(),
  }).optional(),
  step: z.string().optional(), // Which step to show this field in
  projectTypes: z.array(z.string()).optional(), // Which project types this applies to
  projectSubtypes: z.array(z.string()).optional(), // Which project subtypes this applies to
});

const customFieldsConfigSchema = z.object({
  projectType: z.string().min(1, "Project type is required"),
  projectSubtype: z.string().optional(),
  fields: z.array(customFieldSchema),
});

/**
 * GET /api/organizations/custom-fields
 * Get custom fields configuration for the organization
 */
async function getCustomFieldsHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to access custom fields",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to access custom fields",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  const { searchParams } = new URL(req.url);
  const projectType = searchParams.get("projectType");
  const projectSubtype = searchParams.get("projectSubtype");

  try {
    // Get organization's custom fields configuration
    const organization = await db.organization.findUnique({
      where: {
        id: session.user.organizationId,
      },
      select: {
        customFieldsConfig: true,
      },
    });

    if (!organization) {
      throw new ApiError(
        "Organization not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    let customFields = organization.customFieldsConfig as any || {};

    // Filter by project type and subtype if provided
    if (projectType) {
      const typeKey = projectSubtype ? `${projectType}_${projectSubtype}` : projectType;
      customFields = customFields[typeKey] || { fields: [] };
    }

    return NextResponse.json({
      success: true,
      data: customFields,
    });
  } catch (error) {
    logger.error("Error fetching custom fields:", error);
    throw error;
  }
}

/**
 * POST /api/organizations/custom-fields
 * Create or update custom fields configuration
 */
async function updateCustomFieldsHandler(req: NextRequest) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to update custom fields",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  if (!session.user.organizationId) {
    throw new ApiError(
      "You must be part of an organization to update custom fields",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  // Check if user is organization admin
  const userRole = await db.user.findUnique({
    where: { id: session.user.id },
    select: { role: true },
  });

  if (userRole?.role !== "ORGANIZATION_ADMIN") {
    throw new ApiError(
      "Only organization admins can update custom fields",
      ErrorType.AUTHORIZATION,
      403
    );
  }

  try {
    const body = await req.json();
    const configData = customFieldsConfigSchema.parse(body);

    // Get current configuration
    const organization = await db.organization.findUnique({
      where: {
        id: session.user.organizationId,
      },
      select: {
        customFieldsConfig: true,
      },
    });

    if (!organization) {
      throw new ApiError(
        "Organization not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    const currentConfig = organization.customFieldsConfig as any || {};
    const typeKey = configData.projectSubtype 
      ? `${configData.projectType}_${configData.projectSubtype}` 
      : configData.projectType;

    // Update the configuration
    currentConfig[typeKey] = {
      projectType: configData.projectType,
      projectSubtype: configData.projectSubtype,
      fields: configData.fields,
      updatedAt: new Date().toISOString(),
      updatedBy: session.user.id,
    };

    // Save to database
    await db.organization.update({
      where: {
        id: session.user.organizationId,
      },
      data: {
        customFieldsConfig: currentConfig,
      },
    });

    logger.info(`Custom fields updated for organization ${session.user.organizationId} by user ${session.user.id}`);

    return NextResponse.json({
      success: true,
      message: "Custom fields configuration updated successfully",
      data: currentConfig[typeKey],
    });
  } catch (error) {
    logger.error("Error updating custom fields:", error);
    throw error;
  }
}

// Wrap handlers with error handling and tenant isolation
const wrappedGetHandler = withErrorHandling(getCustomFieldsHandler);
const wrappedPostHandler = withErrorHandling(updateCustomFieldsHandler);

export const GET = withTenantIsolation(wrappedGetHandler);
export const POST = withTenantIsolation(wrappedPostHandler);
