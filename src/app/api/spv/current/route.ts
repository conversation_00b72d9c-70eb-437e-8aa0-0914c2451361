import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

/**
 * GET /api/spv/current
 * Get the current SPV for the logged-in user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // For SPV users, get their SPV
    if (session.user.role === "SPV_USER") {
      const spvUser = await db.sPVUser.findFirst({
        where: {
          userId: session.user.id,
          isActive: true,
        },
        include: {
          spv: {
            include: {
              organization: {
                select: {
                  id: true,
                  name: true,
                },
              },
            },
          },
        },
      });

      if (!spvUser) {
        return NextResponse.json(
          { error: "SPV user not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        spv: spvUser.spv,
        role: spvUser.role,
      });
    }

    // For organization admins, they might need to select an SPV
    // For now, return the first SPV from their organization
    if (session.user.role === "ORGANIZATION_ADMIN") {
      const spv = await db.sPV.findFirst({
        where: {
          organizationId: session.user.organizationId,
          status: "ACTIVE",
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!spv) {
        return NextResponse.json(
          { error: "No active SPV found for your organization" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        spv: spv,
        role: "ORGANIZATION_ADMIN",
      });
    }

    // For admins, they can access any SPV but need to specify which one
    if (session.user.role === "ADMIN") {
      // Return the first active SPV for now
      const spv = await db.sPV.findFirst({
        where: {
          status: "ACTIVE",
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!spv) {
        return NextResponse.json(
          { error: "No active SPV found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        spv: spv,
        role: "ADMIN",
      });
    }

    return NextResponse.json(
      { error: "User role not supported for SPV operations" },
      { status: 403 }
    );

  } catch (error) {
    logger.error("Error fetching current SPV:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
