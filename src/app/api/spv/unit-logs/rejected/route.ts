import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/spv/unit-logs/rejected
 * Get rejected unit logs for the current user to resubmit
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Get project IDs based on user role
    let projectIds: string[] = [];

    if (spvUser.role === "SPV_ADMIN") {
      // SPV admins can access all projects within their SPV
      const spvProjects = await db.project.findMany({
        where: { spvId: spvUser.spvId },
        select: { id: true },
      });
      projectIds = spvProjects.map(p => p.id);
    } else {
      // Other roles need specific project assignments
      projectIds = spvUser.projectAssignments.map(a => a.projectId);
    }

    // Get rejected unit logs based on user role
    let whereCondition: any = {
      projectId: { in: projectIds },
      rejectedBy: { not: null }, // Has been rejected
    };

    // For site workers, only show their own rejected entries
    if (spvUser.role === "SITE_WORKER") {
      whereCondition.loggedBy = session.user.id;
      whereCondition.verificationStatus = { in: ["REJECTED", "SPV_REJECTED", "ORG_REJECTED"] }; // Show PM, SPV admin, and org admin rejections
    }
    // For project managers, show entries rejected by SPV admin or org admin
    else if (spvUser.role === "PROJECT_MANAGER") {
      whereCondition.verificationStatus = { in: ["SPV_REJECTED", "ORG_REJECTED"] }; // SPV admin and org admin rejected entries
    }
    // For SPV admins, they can see all rejected entries in their SPV
    else if (spvUser.role === "SPV_ADMIN") {
      whereCondition.verificationStatus = { in: ["REJECTED", "SPV_REJECTED", "ORG_REJECTED"] }; // Show all rejections
    }

    const rejectedUnitLogs = await db.unitLog.findMany({
      where: whereCondition,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
        logger: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        rejectedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        rejectedAt: 'desc',
      },
    });

    // Transform the data to include rejection feedback
    const rejectedEntriesWithFeedback = rejectedUnitLogs.map(unitLog => ({
      id: unitLog.id,
      project: unitLog.project,
      rejectionFeedback: {
        unitLogId: unitLog.id,
        rejectionReason: unitLog.rejectionReason || "No reason provided",
        rejectedBy: unitLog.rejectedByUser?.name || "Unknown",
        rejectedAt: unitLog.rejectedAt?.toISOString() || "",
        originalData: {
          logDate: unitLog.logDate.toISOString(),
          unitType: unitLog.unitType,
          quantity: unitLog.quantity,
          frequency: unitLog.frequency,
          metadata: unitLog.metadata,
        },
      },
      verificationStatus: unitLog.verificationStatus,
      createdAt: unitLog.createdAt,
      updatedAt: unitLog.updatedAt,
    }));

    logger.info("Rejected unit logs fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      count: rejectedEntriesWithFeedback.length,
    });

    return NextResponse.json({
      success: true,
      data: rejectedEntriesWithFeedback,
    });

  } catch (error) {
    logger.error("Error fetching rejected unit logs:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
