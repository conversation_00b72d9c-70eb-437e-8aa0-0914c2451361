import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/spv/projects/[id]/access
 * Check if current SPV user has access to a specific project
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    const projectId = params.id;

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    if (!projectId) {
      return NextResponse.json(
        { error: "Project ID is required" },
        { status: 400 }
      );
    }

    // Get SPV user
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Check if user has an active assignment to this project
    const projectAssignment = await db.projectAssignment.findFirst({
      where: {
        projectId: projectId,
        spvUserId: spvUser.id,
        isActive: true,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            description: true,
            type: true,
            status: true,
            location: true,
            country: true,
          },
        },
      },
    });

    const hasAccess = !!projectAssignment;

    logger.info("Project access checked", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      projectId,
      hasAccess,
    });

    return NextResponse.json({
      success: true,
      data: {
        hasAccess,
        project: projectAssignment?.project || null,
        assignment: projectAssignment ? {
          id: projectAssignment.id,
          assignedAt: projectAssignment.assignedAt,
          permissions: projectAssignment.permissions,
        } : null,
      },
    });

  } catch (error) {
    logger.error("Error checking project access", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
