import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@prisma/client";

/**
 * GET /api/spv/projects/[id]/audit
 * Get audit trail data for a specific project (SPV users only)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const projectId = params.id;

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { 
            projectId: projectId,
            isActive: true 
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Check if user has access to this project
    if (spvUser.projectAssignments.length === 0) {
      return NextResponse.json(
        { error: "Access denied. Project not assigned to you." },
        { status: 403 }
      );
    }

    // Check if user has permission to view audit data
    const canViewAudit = spvUser.role === "SPV_ADMIN" || spvUser.role === "PROJECT_MANAGER";
    
    if (!canViewAudit) {
      return NextResponse.json(
        { error: "Access denied. You don't have permission to view audit data." },
        { status: 403 }
      );
    }

    // Get project to verify it exists
    const project = await db.project.findUnique({
      where: { id: projectId },
      select: { 
        id: true, 
        name: true, 
        organizationId: true 
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    // Fetch audit logs related to this project
    const auditLogs = await db.auditLog.findMany({
      where: {
        OR: [
          {
            // Direct project-related logs
            metadata: {
              path: ["projectId"],
              equals: projectId
            }
          },
          {
            // Organization-level logs that might affect this project
            organizationId: project.organizationId,
            type: {
              in: ["PROJECT_CREATED", "PROJECT_UPDATED", "PROJECT_DELETED", "DOCUMENT_UPLOADED", "VERIFICATION_APPROVED", "VERIFICATION_REJECTED"]
            }
          }
        ]
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 100, // Limit to last 100 audit logs
    });

    // Fetch project verification history
    const verificationHistory = await db.projectVerification.findMany({
      where: {
        projectId: projectId,
      },
      orderBy: {
        timestamp: "desc",
      },
    });

    // Fetch compliance documents related to this project
    // Note: ComplianceDocument doesn't have a direct projectId field in the schema
    // For now, we'll return an empty array and can enhance this later
    const complianceDocuments: any[] = [];

    // Fetch data verification logs for unit logs in this project
    const dataVerificationLogs = await db.dataVerificationLog.findMany({
      where: {
        unitLog: {
          projectId: projectId,
        },
      },
      include: {
        unitLog: {
          select: {
            id: true,
            logDate: true,
            unitType: true,
            quantity: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 50, // Limit to last 50 verification logs
    });

    logger.info(`SPV user ${session.user.id} retrieved audit data for project ${projectId}`);

    return NextResponse.json({
      auditLogs: auditLogs.map(log => ({
        id: log.id,
        type: log.type,
        description: log.description,
        metadata: log.metadata,
        ipAddress: log.ipAddress,
        userAgent: log.userAgent,
        createdAt: log.createdAt,
        user: log.user,
      })),
      verificationHistory: verificationHistory.map(verification => ({
        id: verification.id,
        status: verification.status,
        verifier: verification.verifier,
        verifierEmail: verification.verifierEmail,
        notes: verification.notes,
        timestamp: verification.timestamp,
        metadata: verification.metadata,
      })),
      complianceDocuments: complianceDocuments,
      dataVerificationLogs: dataVerificationLogs.map(log => ({
        id: log.id,
        fromStatus: log.fromStatus,
        toStatus: log.toStatus,
        verificationNotes: log.verificationNotes,
        metadata: log.metadata,
        createdAt: log.createdAt,
        verifiedBy: log.verifiedBy,
        unitLog: log.unitLog,
      })),
      message: "Audit data retrieved successfully",
    });
  } catch (error) {
    logger.error("Error retrieving project audit data:", error);
    return NextResponse.json(
      { error: "An error occurred while retrieving audit data" },
      { status: 500 }
    );
  }
}
