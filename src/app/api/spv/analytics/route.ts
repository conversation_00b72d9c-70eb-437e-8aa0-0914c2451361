import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@prisma/client";

/**
 * GET /api/spv/analytics
 * Get analytics data for SPV user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // For SPV_ADMIN, get all projects belonging to the SPV
    // For other roles, get only assigned projects
    let assignedProjectIds: string[];

    if (spvUser.role === "SPV_ADMIN") {
      // Get all projects for this SPV
      const spvProjects = await db.project.findMany({
        where: {
          spvId: spvUser.spvId,
        },
        select: {
          id: true,
        },
      });
      assignedProjectIds = spvProjects.map(p => p.id);
    } else {
      // Get only assigned projects for non-admin users
      assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);
    }

    // Parse query parameters
    const url = new URL(request.url);
    const timeRange = url.searchParams.get('timeRange') || '30d';
    const projectId = url.searchParams.get('projectId');

    // Calculate date range
    const now = new Date();
    let startDate = new Date();
    
    switch (timeRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(now.getDate() - 30);
        break;
      case '90d':
        startDate.setDate(now.getDate() - 90);
        break;
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate.setDate(now.getDate() - 30);
    }

    // Build where clause
    let whereClause: any = {
      projectId: { in: assignedProjectIds },
      createdAt: {
        gte: startDate,
        lte: now,
      },
    };

    if (projectId && assignedProjectIds.includes(projectId)) {
      whereClause.projectId = projectId;
    }

    // Get overview metrics
    const [
      totalProjects,
      totalUnitLogs,
      verifiedCount,
      approvedCount,
      rejectedCount,
      pendingCount,
      submittedCount,
      previousPeriodCount
    ] = await Promise.all([
      // Total projects
      db.project.count({
        where: {
          id: { in: assignedProjectIds },
          status: "ACTIVE",
        },
      }),

      // Total unit logs in time range
      db.unitLog.count({ where: whereClause }),

      // Verified count
      db.unitLog.count({
        where: {
          ...whereClause,
          verificationStatus: {
            in: ["VERIFIED", "SPV_APPROVED", "ORG_APPROVED", "VVB_VERIFIED"]
          },
        },
      }),

      // Approved count
      db.unitLog.count({
        where: {
          ...whereClause,
          verificationStatus: {
            in: ["SPV_APPROVED", "ORG_APPROVED", "VVB_VERIFIED"]
          },
        },
      }),

      // Rejected count
      db.unitLog.count({
        where: {
          ...whereClause,
          verificationStatus: {
            in: ["REJECTED", "SPV_REJECTED", "ORG_REJECTED", "VVB_REJECTED"]
          },
        },
      }),

      // Pending count
      db.unitLog.count({
        where: {
          ...whereClause,
          verificationStatus: {
            in: ["DRAFT", "SUBMITTED_FOR_VERIFICATION"]
          },
        },
      }),

      // Submitted count
      db.unitLog.count({
        where: {
          ...whereClause,
          verificationStatus: "SUBMITTED_FOR_VERIFICATION",
        },
      }),

      // Previous period for growth calculation
      db.unitLog.count({
        where: {
          projectId: { in: assignedProjectIds },
          createdAt: {
            gte: new Date(startDate.getTime() - (now.getTime() - startDate.getTime())),
            lt: startDate,
          },
        },
      }),
    ]);

    // Calculate metrics
    const verificationRate = totalUnitLogs > 0 ? Math.round((verifiedCount / totalUnitLogs) * 100) : 0;
    const monthlyGrowth = previousPeriodCount > 0 ? Math.round(((totalUnitLogs - previousPeriodCount) / previousPeriodCount) * 100) : 0;

    // Get top performers (users with highest approval rates)
    const topPerformers = await db.user.findMany({
      where: {
        unitLogsLogged: {
          some: {
            projectId: { in: assignedProjectIds },
            createdAt: {
              gte: startDate,
              lte: now,
            },
          },
        },
      },
      select: {
        id: true,
        name: true,
        email: true,
        _count: {
          select: {
            unitLogsLogged: {
              where: {
                projectId: { in: assignedProjectIds },
                createdAt: {
                  gte: startDate,
                  lte: now,
                },
              },
            },
          },
        },
      },
      take: 10,
    });

    // Calculate approval rates for top performers
    const topPerformersWithRates = await Promise.all(
      topPerformers.map(async (user) => {
        const userApprovedCount = await db.unitLog.count({
          where: {
            loggedBy: user.id,
            projectId: { in: assignedProjectIds },
            verificationStatus: {
              in: ["SPV_APPROVED", "ORG_APPROVED", "VVB_VERIFIED"]
            },
            createdAt: {
              gte: startDate,
              lte: now,
            },
          },
        });

        const approvalRate = user._count.unitLogsLogged > 0 ? Math.round((userApprovedCount / user._count.unitLogsLogged) * 100) : 0;

        return {
          id: user.id,
          name: user.name || user.email,
          entriesCount: user._count.unitLogsLogged,
          approvalRate,
        };
      })
    );

    // Sort by approval rate and take top 5
    const sortedPerformers = topPerformersWithRates
      .sort((a, b) => b.approvalRate - a.approvalRate)
      .slice(0, 5);

    // Get project progress
    const projectProgress = await Promise.all(
      assignedProjectIds.map(async (projectId) => {
        const [project, totalEntries, verifiedEntries] = await Promise.all([
          db.project.findUnique({
            where: { id: projectId },
            select: {
              id: true,
              name: true,
              type: true,
            },
          }),
          db.unitLog.count({
            where: {
              projectId: projectId,
              createdAt: {
                gte: startDate,
                lte: now,
              },
            },
          }),
          db.unitLog.count({
            where: {
              projectId: projectId,
              verificationStatus: {
                in: ["VERIFIED", "SPV_APPROVED", "ORG_APPROVED", "VVB_VERIFIED"]
              },
              createdAt: {
                gte: startDate,
                lte: now,
              },
            },
          }),
        ]);

        if (!project) return null;

        const completionRate = totalEntries > 0 ? Math.round((verifiedEntries / totalEntries) * 100) : 0;

        return {
          id: project.id,
          name: project.name,
          type: project.type,
          completionRate,
          totalEntries,
          verifiedEntries,
        };
      })
    );

    const filteredProjectProgress = projectProgress.filter(Boolean);

    // Compile analytics data
    const analyticsData = {
      overview: {
        totalProjects,
        totalUnitLogs,
        verificationRate,
        averageProcessingTime: 24, // Placeholder - would need to calculate from verification logs
        monthlyGrowth,
      },
      verificationMetrics: {
        submitted: submittedCount,
        verified: verifiedCount,
        approved: approvedCount,
        rejected: rejectedCount,
        pending: pendingCount,
      },
      userPerformance: {
        activeUsers: topPerformers.length,
        topPerformers: sortedPerformers,
      },
      projectProgress: filteredProjectProgress,
      timeSeriesData: [], // Placeholder for time series data
    };

    logger.info("SPV analytics data fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      timeRange,
      projectId,
      totalUnitLogs,
    });

    return NextResponse.json({
      success: true,
      data: analyticsData,
    });

  } catch (error) {
    logger.error("Error fetching SPV analytics:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
