import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get SPV user info
    const spvUser = await db.sPVUser.findFirst({
      where: { userId: session.user.id },
      include: {
        user: true,
        spv: {
          include: {
            organization: true,
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Get user notification preferences
    const notificationPrefs = await db.notificationPreference.findUnique({
      where: { userId: session.user.id },
    });

    // Parse user preferences from JSON field
    const userPrefs = spvUser.user.preferences as any || {};

    const settings = {
      notifications: {
        emailNotifications: notificationPrefs?.email ?? true,
        dataEntryAlerts: userPrefs.dataEntryAlerts ?? true,
        verificationUpdates: userPrefs.verificationUpdates ?? true,
        weeklyReports: userPrefs.weeklyReports ?? false,
      },
      preferences: {
        timezone: userPrefs.timezone ?? "UTC",
        dateFormat: userPrefs.dateFormat ?? "MM/DD/YYYY",
        language: userPrefs.language ?? "en",
      },
      profile: {
        name: spvUser.user.name || "",
        email: spvUser.user.email || "",
        phone: spvUser.user.phoneNumber || "",
        jobTitle: spvUser.user.jobTitle || "",
        department: spvUser.user.departmentName || "",
      },
    };

    logger.info("SPV settings retrieved", {
      userId: session.user.id,
      spvUserId: spvUser.id,
    });

    return NextResponse.json(settings);
  } catch (error) {
    console.error("Error fetching SPV settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get SPV user info
    const spvUser = await db.sPVUser.findFirst({
      where: { userId: session.user.id },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const body = await request.json();
    const { notifications, preferences, profile } = body;

    // Get current user preferences
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { preferences: true },
    });
    const currentPrefs = (currentUser?.preferences as any) || {};

    // Update user profile and preferences
    const updateData: any = {};

    if (profile) {
      updateData.name = profile.name;
      updateData.phoneNumber = profile.phone;
      updateData.jobTitle = profile.jobTitle;
      updateData.departmentName = profile.department;
    }

    if (notifications || preferences) {
      updateData.preferences = {
        ...currentPrefs,
        ...(notifications && {
          dataEntryAlerts: notifications.dataEntryAlerts,
          verificationUpdates: notifications.verificationUpdates,
          weeklyReports: notifications.weeklyReports,
        }),
        ...(preferences && {
          timezone: preferences.timezone,
          dateFormat: preferences.dateFormat,
          language: preferences.language,
        }),
      };
    }

    if (Object.keys(updateData).length > 0) {
      await db.user.update({
        where: { id: session.user.id },
        data: updateData,
      });
    }

    // Update notification preferences
    if (notifications?.emailNotifications !== undefined) {
      await db.notificationPreference.upsert({
        where: { userId: session.user.id },
        create: {
          userId: session.user.id,
          email: notifications.emailNotifications,
        },
        update: {
          email: notifications.emailNotifications,
        },
      });
    }

    logger.info("SPV settings updated", {
      userId: session.user.id,
      spvUserId: spvUser.id,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating SPV settings:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
