import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";
import { UserRole } from "@prisma/client";

// Schema for report generation
const reportGenerationSchema = z.object({
  templateId: z.string().min(1, "Template ID is required"),
  dateRange: z.object({
    from: z.string(),
    to: z.string(),
  }),
  projectIds: z.array(z.string()).optional(),
  format: z.enum(["PDF", "CSV", "XLSX"]).default("PDF"),
  options: z.object({
    includeCharts: z.boolean().default(true),
    includeRawData: z.boolean().default(false),
  }).optional(),
});

/**
 * POST /api/spv/reports/generate
 * Generate reports for SPV users
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Check if user has permission to generate reports
    const canGenerateReports = spvUser.role === "SPV_ADMIN" || spvUser.role === "PROJECT_MANAGER";
    
    if (!canGenerateReports) {
      return NextResponse.json(
        { error: "Access denied. You don't have permission to generate reports." },
        { status: 403 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const reportConfig = reportGenerationSchema.parse(body);

    // Get assigned project IDs
    let assignedProjectIds: string[];
    if (spvUser.role === "SPV_ADMIN") {
      // Get all projects for this SPV
      const spvProjects = await db.project.findMany({
        where: {
          spvId: spvUser.spvId,
        },
        select: {
          id: true,
        },
      });
      assignedProjectIds = spvProjects.map(p => p.id);
    } else {
      // Get only assigned projects for non-admin users
      assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);
    }

    // Filter project IDs if specified in request
    const targetProjectIds = reportConfig.projectIds 
      ? reportConfig.projectIds.filter(id => assignedProjectIds.includes(id))
      : assignedProjectIds;

    if (targetProjectIds.length === 0) {
      return NextResponse.json(
        { error: "No accessible projects found for report generation" },
        { status: 400 }
      );
    }

    // Parse date range
    const startDate = new Date(reportConfig.dateRange.from);
    const endDate = new Date(reportConfig.dateRange.to);

    // Generate report data based on template
    let reportData: any = {};

    switch (reportConfig.templateId) {
      case 'verification_summary':
        reportData = await generateVerificationSummary(targetProjectIds, startDate, endDate);
        break;
      case 'project_progress':
        reportData = await generateProjectProgress(targetProjectIds, startDate, endDate);
        break;
      case 'user_performance':
        if (spvUser.role === "SPV_ADMIN" || spvUser.role === "PROJECT_MANAGER") {
          reportData = await generateUserPerformance(targetProjectIds, startDate, endDate);
        } else {
          return NextResponse.json(
            { error: "Access denied for user performance reports" },
            { status: 403 }
          );
        }
        break;
      case 'compliance_report':
        if (spvUser.role === "SPV_ADMIN") {
          reportData = await generateComplianceReport(targetProjectIds, startDate, endDate);
        } else {
          return NextResponse.json(
            { error: "Access denied for compliance reports" },
            { status: 403 }
          );
        }
        break;
      default:
        return NextResponse.json(
          { error: "Invalid report template" },
          { status: 400 }
        );
    }

    // Format the report based on requested format
    const formattedReport = await formatReport(reportData, reportConfig.format, reportConfig.options);

    logger.info(`SPV user ${session.user.id} generated report ${reportConfig.templateId}`);

    // Return the formatted report
    return new NextResponse(formattedReport.content, {
      headers: {
        'Content-Type': formattedReport.contentType,
        'Content-Disposition': `attachment; filename="${formattedReport.filename}"`,
      },
    });

  } catch (error) {
    logger.error("Error generating SPV report:", error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid report configuration", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while generating the report" },
      { status: 500 }
    );
  }
}

// Helper functions for generating different types of reports
async function generateVerificationSummary(projectIds: string[], startDate: Date, endDate: Date) {
  const unitLogs = await db.unitLog.findMany({
    where: {
      projectId: { in: projectIds },
      createdAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          type: true,
        },
      },
      logger: {
        select: {
          id: true,
          name: true,
          email: true,
        },
      },
    },
  });

  const summary = {
    totalEntries: unitLogs.length,
    verifiedEntries: unitLogs.filter(log => ["VERIFIED", "SPV_APPROVED", "ORG_APPROVED"].includes(log.verificationStatus)).length,
    pendingEntries: unitLogs.filter(log => ["DRAFT", "SUBMITTED_FOR_VERIFICATION"].includes(log.verificationStatus)).length,
    rejectedEntries: unitLogs.filter(log => log.verificationStatus.includes("REJECTED")).length,
  };

  return {
    summary,
    details: unitLogs,
    reportType: "Verification Summary",
    generatedAt: new Date().toISOString(),
  };
}

async function generateProjectProgress(projectIds: string[], startDate: Date, endDate: Date) {
  const projects = await db.project.findMany({
    where: {
      id: { in: projectIds },
    },
    include: {
      unitLogs: {
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate,
          },
        },
      },
      organization: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  const projectProgress = projects.map(project => {
    const totalEntries = project.unitLogs.length;
    const verifiedEntries = project.unitLogs.filter(log => 
      ["VERIFIED", "SPV_APPROVED", "ORG_APPROVED"].includes(log.verificationStatus)
    ).length;

    return {
      id: project.id,
      name: project.name,
      type: project.type,
      organization: project.organization.name,
      totalEntries,
      verifiedEntries,
      completionRate: totalEntries > 0 ? Math.round((verifiedEntries / totalEntries) * 100) : 0,
    };
  });

  return {
    projects: projectProgress,
    reportType: "Project Progress",
    generatedAt: new Date().toISOString(),
  };
}

async function generateUserPerformance(projectIds: string[], startDate: Date, endDate: Date) {
  // Implementation for user performance report
  return {
    reportType: "User Performance",
    generatedAt: new Date().toISOString(),
    message: "User performance report generation not yet implemented",
  };
}

async function generateComplianceReport(projectIds: string[], startDate: Date, endDate: Date) {
  // Implementation for compliance report
  return {
    reportType: "Compliance Report",
    generatedAt: new Date().toISOString(),
    message: "Compliance report generation not yet implemented",
  };
}

async function formatReport(data: any, format: string, options?: any) {
  // Simple JSON format for now - can be extended to support PDF, CSV, XLSX
  const content = JSON.stringify(data, null, 2);
  
  return {
    content: Buffer.from(content),
    contentType: 'application/json',
    filename: `spv_report_${Date.now()}.json`,
  };
}
