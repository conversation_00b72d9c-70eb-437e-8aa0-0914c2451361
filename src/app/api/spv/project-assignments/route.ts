import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Validation schema for project assignment by SPV admin
const projectAssignmentSchema = z.object({
  spvUserId: z.string().min(1, "SPV user ID is required"),
  projectId: z.string().min(1, "Project ID is required"),
  permissions: z.record(z.any()).optional(),
});

// Validation schema for bulk project assignment
const bulkProjectAssignmentSchema = z.object({
  spvUserId: z.string().min(1, "SPV user ID is required"),
  projectIds: z.array(z.string()).min(1, "At least one project ID is required"),
  permissions: z.record(z.any()).optional(),
});

/**
 * GET /api/spv/project-assignments
 * Get project assignments for the SPV (SPV admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get SPV user info to verify permissions
    const spvUser = await db.sPVUser.findFirst({
      where: { 
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    if (spvUser.role !== "SPV_ADMIN") {
      return NextResponse.json(
        { error: "Access denied. SPV admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const projectId = searchParams.get("projectId");
    const userId = searchParams.get("userId");

    const skip = (page - 1) * limit;

    // Build where clause - only show assignments for this SPV
    const where: any = {
      spvUser: {
        spvId: spvUser.spvId,
      },
      isActive: true,
    };

    if (projectId) {
      where.projectId = projectId;
    }

    if (userId) {
      where.spvUser = {
        ...where.spvUser,
        userId: userId,
      };
    }

    // Get assignments with pagination
    const [assignments, totalCount] = await Promise.all([
      db.projectAssignment.findMany({
        where,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              description: true,
              type: true,
              status: true,
              location: true,
              country: true,
            },
          },
          spvUser: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true,
                  jobTitle: true,
                },
              },
            },
          },
          assignedByUser: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: {
          assignedAt: "desc",
        },
      }),
      db.projectAssignment.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      success: true,
      data: {
        assignments,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });

  } catch (error) {
    logger.error("Error fetching project assignments", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/spv/project-assignments
 * Create a new project assignment (SPV admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get SPV user info to verify permissions
    const spvUser = await db.sPVUser.findFirst({
      where: { 
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    if (spvUser.role !== "SPV_ADMIN") {
      return NextResponse.json(
        { error: "Access denied. SPV admin role required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    
    // Check if it's bulk assignment or single assignment
    const isBulk = Array.isArray(body.projectIds);
    const validatedData = isBulk 
      ? bulkProjectAssignmentSchema.parse(body)
      : projectAssignmentSchema.parse(body);

    // Verify the SPV user exists and belongs to the same SPV
    const targetSpvUser = await db.sPVUser.findFirst({
      where: {
        id: validatedData.spvUserId,
        spvId: spvUser.spvId,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            jobTitle: true,
          },
        },
      },
    });

    if (!targetSpvUser) {
      return NextResponse.json(
        { error: "Target SPV user not found or not in the same SPV" },
        { status: 404 }
      );
    }

    if (isBulk) {
      // Handle bulk assignment
      const bulkData = validatedData as z.infer<typeof bulkProjectAssignmentSchema>;
      
      // Verify all projects exist and belong to the SPV
      const projects = await db.project.findMany({
        where: {
          id: { in: bulkData.projectIds },
          spvId: spvUser.spvId,
        },
      });

      if (projects.length !== bulkData.projectIds.length) {
        return NextResponse.json(
          { error: "Some projects do not belong to this SPV or do not exist" },
          { status: 400 }
        );
      }

      // Check for existing assignments
      const existingAssignments = await db.projectAssignment.findMany({
        where: {
          projectId: { in: bulkData.projectIds },
          spvUserId: validatedData.spvUserId,
          isActive: true,
        },
      });

      if (existingAssignments.length > 0) {
        const existingProjectIds = existingAssignments.map(a => a.projectId);
        return NextResponse.json(
          { 
            error: "Some projects are already assigned to this user",
            existingProjectIds,
          },
          { status: 400 }
        );
      }

      // Create bulk assignments and update projects' spvId in a transaction
      const assignments = await db.$transaction(async (tx) => {
        // Create the project assignments
        const newAssignments = await tx.projectAssignment.createMany({
          data: bulkData.projectIds.map(projectId => ({
            projectId,
            spvUserId: validatedData.spvUserId,
            assignedBy: session.user.id,
            permissions: bulkData.permissions || {},
          })),
        });

        // Update all projects' spvId to link them to the SPV
        await tx.project.updateMany({
          where: { id: { in: bulkData.projectIds } },
          data: { spvId: spvUser.spvId },
        });

        return newAssignments;
      });

      logger.info("Bulk project assignments created", {
        assignedBy: session.user.id,
        spvUserId: validatedData.spvUserId,
        projectIds: bulkData.projectIds,
        count: assignments.count,
        spvId: spvUser.spvId,
      });

      return NextResponse.json({
        success: true,
        data: { assignmentsCreated: assignments.count },
        message: `${assignments.count} projects assigned successfully`,
      });

    } else {
      // Handle single assignment
      const singleData = validatedData as z.infer<typeof projectAssignmentSchema>;
      
      // Verify project exists and belongs to the SPV
      const project = await db.project.findFirst({
        where: {
          id: singleData.projectId,
          spvId: spvUser.spvId,
        },
      });

      if (!project) {
        return NextResponse.json(
          { error: "Project not found or does not belong to this SPV" },
          { status: 404 }
        );
      }

      // Check if assignment already exists
      const existingAssignment = await db.projectAssignment.findFirst({
        where: {
          projectId: singleData.projectId,
          spvUserId: validatedData.spvUserId,
          isActive: true,
        },
      });

      if (existingAssignment) {
        return NextResponse.json(
          { error: "Project is already assigned to this user" },
          { status: 400 }
        );
      }

      // Create the assignment and update project's spvId in a transaction
      const assignment = await db.$transaction(async (tx) => {
        // Create the project assignment
        const newAssignment = await tx.projectAssignment.create({
          data: {
            projectId: singleData.projectId,
            spvUserId: validatedData.spvUserId,
            assignedBy: session.user.id,
            permissions: singleData.permissions || {},
          },
        });

        // Update the project's spvId to link it to the SPV
        await tx.project.update({
          where: { id: singleData.projectId },
          data: { spvId: spvUser.spvId },
        });

        return newAssignment;
      });

      // Fetch the assignment with full details
      const assignmentWithDetails = await db.projectAssignment.findUnique({
        where: { id: assignment.id },
        include: {
          project: {
            select: {
              id: true,
              name: true,
              description: true,
              type: true,
              status: true,
              location: true,
              country: true,
            },
          },
          spvUser: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true,
                  jobTitle: true,
                },
              },
            },
          },
          assignedByUser: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      logger.info("Project assignment created", {
        assignedBy: session.user.id,
        assignmentId: assignment.id,
        projectId: singleData.projectId,
        spvUserId: validatedData.spvUserId,
        spvId: spvUser.spvId,
      });

      return NextResponse.json({
        success: true,
        data: assignmentWithDetails,
        message: "Project assigned successfully",
      });
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error creating project assignment", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/spv/project-assignments
 * Remove a project assignment (SPV admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get SPV user info to verify permissions
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    if (spvUser.role !== "SPV_ADMIN") {
      return NextResponse.json(
        { error: "Access denied. SPV admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const assignmentId = searchParams.get("assignmentId");

    if (!assignmentId) {
      return NextResponse.json(
        { error: "Assignment ID is required" },
        { status: 400 }
      );
    }

    // Find the assignment and verify it belongs to this SPV
    const assignment = await db.projectAssignment.findFirst({
      where: {
        id: assignmentId,
        spvUser: {
          spvId: spvUser.spvId,
        },
        isActive: true,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
          },
        },
        spvUser: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "Assignment not found or does not belong to this SPV" },
        { status: 404 }
      );
    }

    // Soft delete the assignment
    await db.projectAssignment.update({
      where: { id: assignmentId },
      data: { isActive: false },
    });

    logger.info("Project assignment removed", {
      removedBy: session.user.id,
      assignmentId,
      projectId: assignment.project.id,
      spvUserId: assignment.spvUserId,
    });

    return NextResponse.json({
      success: true,
      message: "Project assignment removed successfully",
    });

  } catch (error) {
    logger.error("Error removing project assignment", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
