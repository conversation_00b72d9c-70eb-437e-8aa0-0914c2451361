import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/spv/profile
 * Get current SPV user profile
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            jobTitle: true,
            phoneNumber: true,
            profileImage: true,
            bio: true,
            createdAt: true,
            updatedAt: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
            purpose: true,
            jurisdiction: true,
            status: true,
            legalStructure: true,
            address: true,
            description: true,
            organizationId: true,
            organization: {
              select: {
                id: true,
                name: true,
                legalName: true,
                country: true,
                industry: true,
              },
            },
          },
        },
        projectAssignments: {
          where: {
            isActive: true,
          },
          include: {
            project: {
              select: {
                id: true,
                name: true,
                description: true,
                type: true,
                status: true,
                location: true,
                country: true,
                startDate: true,
                endDate: true,
              },
            },
          },
          orderBy: {
            assignedAt: 'desc',
          },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    logger.info("SPV profile retrieved", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      spvId: spvUser.spvId,
    });

    return NextResponse.json({
      success: true,
      data: {
        ...spvUser,
        // Ensure role is included at the top level for easy access
        role: spvUser.role,
      },
    });

  } catch (error) {
    logger.error("Error retrieving SPV profile", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/spv/profile
 * Update SPV user profile
 */
export async function PATCH(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, jobTitle, phoneNumber, bio } = body;

    // Get SPV user
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Update user profile
    const updatedUser = await db.user.update({
      where: {
        id: session.user.id,
      },
      data: {
        name: name || undefined,
        jobTitle: jobTitle || undefined,
        phoneNumber: phoneNumber || undefined,
        bio: bio || undefined,
      },
      select: {
        id: true,
        email: true,
        name: true,
        jobTitle: true,
        phoneNumber: true,
        profileImage: true,
        bio: true,
        updatedAt: true,
      },
    });

    logger.info("SPV profile updated", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      updatedFields: Object.keys(body),
    });

    return NextResponse.json({
      success: true,
      data: updatedUser,
      message: "Profile updated successfully",
    });

  } catch (error) {
    logger.error("Error updating SPV profile", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
