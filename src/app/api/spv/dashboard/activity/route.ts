import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/spv/dashboard/activity
 * Get recent activity for SPV user dashboard
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
      include: {
        projectAssignments: {
          where: { isActive: true },
          select: { projectId: true },
        },
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    const assignedProjectIds = spvUser.projectAssignments.map(pa => pa.projectId);

    // If no projects assigned, return empty activity
    if (assignedProjectIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          recentVerificationLogs: [],
          recentUnitLogs: [],
          recentProjectUpdates: [],
        },
      });
    }

    // Get recent verification logs for assigned projects
    const recentVerificationLogs = await db.dataVerificationLog.findMany({
      where: {
        unitLog: {
          projectId: { in: assignedProjectIds },
        },
      },
      include: {
        unitLog: {
          include: {
            project: {
              select: {
                id: true,
                name: true,
                type: true,
              },
            },
          },
        },
        verifier: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 20,
    });

    // Get recent unit logs created by this user
    const recentUnitLogs = await db.unitLog.findMany({
      where: {
        loggedBy: session.user.id,
        projectId: { in: assignedProjectIds },
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
      take: 10,
    });

    // Combine and format activity data
    const activities: any[] = [];

    // Add verification activities
    recentVerificationLogs.forEach(log => {
      const activityType = getActivityType(log.fromStatus, log.toStatus);
      activities.push({
        id: `verification-${log.id}`,
        type: activityType,
        description: getActivityDescription(log.fromStatus, log.toStatus, log.verifier?.name),
        timestamp: log.createdAt.toISOString(),
        projectName: log.unitLog.project.name,
        projectId: log.unitLog.project.id,
        unitLogId: log.unitLogId,
      });
    });

    // Add data entry activities
    recentUnitLogs.forEach(unitLog => {
      activities.push({
        id: `data-entry-${unitLog.id}`,
        type: 'DATA_ENTRY',
        description: `Data entry created: ${unitLog.unitType} - ${unitLog.quantity} units`,
        timestamp: unitLog.createdAt.toISOString(),
        projectName: unitLog.project.name,
        projectId: unitLog.project.id,
        unitLogId: unitLog.id,
      });
    });

    // Sort by timestamp and take latest 15
    const sortedActivities = activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 15);

    logger.info("SPV dashboard activity fetched", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      activityCount: sortedActivities.length,
    });

    return NextResponse.json({
      success: true,
      data: sortedActivities,
    });

  } catch (error) {
    logger.error("Error fetching SPV dashboard activity:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

function getActivityType(fromStatus: string, toStatus: string): string {
  if (toStatus.includes('VERIFIED') && !toStatus.includes('REJECTED')) {
    return 'VERIFICATION';
  }
  if (toStatus.includes('APPROVED')) {
    return 'APPROVAL';
  }
  if (toStatus.includes('REJECTED')) {
    return 'REJECTION';
  }
  return 'VERIFICATION';
}

function getActivityDescription(fromStatus: string, toStatus: string, verifierName?: string | null): string {
  const verifier = verifierName || 'System';
  
  if (toStatus === 'VERIFIED') {
    return `Data verified by ${verifier}`;
  }
  if (toStatus === 'SPV_APPROVED') {
    return `SPV approval by ${verifier}`;
  }
  if (toStatus === 'ORG_APPROVED') {
    return `Organization approval by ${verifier}`;
  }
  if (toStatus === 'REJECTED') {
    return `Data rejected by ${verifier}`;
  }
  if (toStatus === 'SPV_REJECTED') {
    return `SPV rejection by ${verifier}`;
  }
  if (toStatus === 'ORG_REJECTED') {
    return `Organization rejection by ${verifier}`;
  }
  if (toStatus === 'SUBMITTED_FOR_VERIFICATION') {
    return `Data submitted for verification`;
  }
  
  return `Status changed from ${fromStatus} to ${toStatus}`;
}
