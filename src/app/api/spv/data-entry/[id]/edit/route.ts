/**
 * SPV Data Entry Edit API
 * 
 * Allows SPV admins to edit data entries that were rejected by organization admin
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for editing data entry
const editDataEntrySchema = z.object({
  logDate: z.string().optional(),
  unitType: z.string().optional(),
  quantity: z.number().positive().optional(),
  dataSource: z.string().optional(),
  location: z.string().optional(),
  notes: z.string().max(1000).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET /api/spv/data-entry/[id]/edit
 * Get data entry details for editing
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access data entries" },
        { status: 401 }
      );
    }

    // Get SPV user info
    const spvUser = await db.sPVUser.findFirst({
      where: { userId: session.user.id },
      include: {
        spv: {
          select: {
            id: true,
            name: true,
            organizationId: true,
          },
        },
      },
    });

    if (!spvUser || spvUser.role !== "SPV_ADMIN") {
      return NextResponse.json(
        { error: "Only SPV admins can edit data entries" },
        { status: 403 }
      );
    }

    // Get the data entry
    const unitLog = await db.unitLog.findFirst({
      where: {
        id: params.id,
        project: {
          spvId: spvUser.spvId,
        },
        verificationStatus: { in: ["ORG_REJECTED", "REJECTED", "SPV_REJECTED"] }, // Allow editing any rejected entries
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            type: true,
            spvId: true,
          },
        },
        logger: {
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
          },
        },
        verificationLogs: {
          include: {
            verifier: {
              select: {
                id: true,
                name: true,
                email: true,
                role: true,
              },
            },
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
    });

    if (!unitLog) {
      return NextResponse.json(
        { error: "Data entry not found or not available for editing" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: unitLog,
    });

  } catch (error) {
    logger.error("Error fetching data entry for editing", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/spv/data-entry/[id]/edit
 * Update data entry and resubmit to org admin
 */
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to edit data entries" },
        { status: 401 }
      );
    }

    // Get SPV user info
    const spvUser = await db.sPVUser.findFirst({
      where: { userId: session.user.id },
      include: {
        spv: {
          select: {
            id: true,
            name: true,
            organizationId: true,
          },
        },
      },
    });

    if (!spvUser || spvUser.role !== "SPV_ADMIN") {
      return NextResponse.json(
        { error: "Only SPV admins can edit data entries" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const validatedData = editDataEntrySchema.parse(body);

    // Get the current data entry
    const currentUnitLog = await db.unitLog.findFirst({
      where: {
        id: params.id,
        project: {
          spvId: spvUser.spvId,
        },
        verificationStatus: "ORG_REJECTED",
      },
    });

    if (!currentUnitLog) {
      return NextResponse.json(
        { error: "Data entry not found or not available for editing" },
        { status: 404 }
      );
    }

    const now = new Date();

    // Update the data entry with new values and resubmit
    const updatedUnitLog = await db.unitLog.update({
      where: { id: params.id },
      data: {
        // Update only provided fields
        ...(validatedData.logDate && { logDate: new Date(validatedData.logDate) }),
        ...(validatedData.unitType && { unitType: validatedData.unitType }),
        ...(validatedData.quantity && { quantity: validatedData.quantity }),
        ...(validatedData.dataSource && { dataSource: validatedData.dataSource }),
        ...(validatedData.location && { location: validatedData.location }),
        ...(validatedData.notes && { notes: validatedData.notes }),
        ...(validatedData.metadata && { metadata: validatedData.metadata }),
        
        // Update verification status and clear rejection data
        verificationStatus: "SUBMITTED_TO_ORG_ADMIN",
        rejectedBy: null,
        rejectedAt: null,
        rejectionReason: null,
        verifiedBy: session.user.id,
        verifiedAt: now,
        verificationNotes: "Edited and resubmitted by SPV Admin",
        updatedAt: now,
      },
    });

    // Create verification log
    await db.dataVerificationLog.create({
      data: {
        unitLogId: params.id,
        fromStatus: "ORG_REJECTED",
        toStatus: "SUBMITTED_TO_ORG_ADMIN",
        verifiedBy: session.user.id,
        verificationNotes: "Data edited and resubmitted by SPV Admin",
        metadata: {
          action: "EDIT_AND_RESUBMIT",
          editedFields: Object.keys(validatedData),
          originalRejectionReason: currentUnitLog.rejectionReason,
        },
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "DATA_EDITED",
        description: `Data entry edited and resubmitted by SPV Admin ${session.user.name}`,
        userId: session.user.id,
        organizationId: spvUser.spv.organizationId,
        metadata: {
          unitLogId: params.id,
          action: "EDIT_AND_RESUBMIT",
          editedFields: Object.keys(validatedData),
          fromStatus: "ORG_REJECTED",
          toStatus: "SUBMITTED_TO_ORG_ADMIN",
        },
        ipAddress: req.headers.get("x-forwarded-for") || req.headers.get("x-real-ip") || "unknown",
        userAgent: req.headers.get("user-agent") || "unknown",
      },
    });

    logger.info(`Data entry ${params.id} edited and resubmitted by SPV Admin ${session.user.id}`);

    return NextResponse.json({
      success: true,
      data: updatedUnitLog,
      message: "Data entry updated and resubmitted successfully",
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error editing data entry", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
