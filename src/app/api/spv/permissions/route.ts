import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

/**
 * GET /api/spv/permissions
 * Get current SPV user permissions
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.SPV_USER) {
      return NextResponse.json(
        { error: "Access denied. SPV user role required." },
        { status: 403 }
      );
    }

    // Get SPV user details
    const spvUser = await db.sPVUser.findFirst({
      where: {
        userId: session.user.id,
        isActive: true,
      },
    });

    if (!spvUser) {
      return NextResponse.json(
        { error: "SPV user not found" },
        { status: 404 }
      );
    }

    // Base permissions for all SPV users
    const basePermissions = [
      "view:spv_dashboard",
      "read:assigned_projects",
      "create:unit_log",
      "update:unit_log:own",
      "upload:data_files",
      "submit:data_for_verification",
    ];

    // Role-based permissions
    const rolePermissions: Record<string, string[]> = {
      SITE_WORKER: [],
      PROJECT_MANAGER: [
        "verify:unit_log",
        "reject:unit_log",
        "approve:data_entry",
        "manage:project_data",
        "view:verification_queue",
        "view:spv_analytics",
        "export:project_reports",
      ],
      SPV_ADMIN: [
        "verify:unit_log",
        "reject:unit_log",
        "approve:data_entry",
        "manage:project_data",
        "view:verification_queue",
        "review:verified_data",
        "approve:spv_level",
        "manage:spv_users",
        "view:spv_analytics",
        "export:project_reports",
      ],
    };

    // Get role-specific permissions
    const additionalPermissions = rolePermissions[spvUser.role] || [];

    // Get custom permissions from SPV user record
    const customPermissions = spvUser.permissions ? Object.keys(spvUser.permissions) : [];

    // Combine all permissions
    const allPermissions = [
      ...basePermissions,
      ...additionalPermissions,
      ...customPermissions,
    ];

    // Remove duplicates
    const uniquePermissions = [...new Set(allPermissions)];

    logger.info("SPV permissions retrieved", {
      userId: session.user.id,
      spvUserId: spvUser.id,
      role: spvUser.role,
      permissionCount: uniquePermissions.length,
    });

    return NextResponse.json({
      success: true,
      data: {
        permissions: uniquePermissions,
        role: spvUser.role,
        basePermissions,
        rolePermissions: additionalPermissions,
        customPermissions,
      },
    });

  } catch (error) {
    logger.error("Error retrieving SPV permissions", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
