import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { withResourceIsolation, getTenantContext, withTenantQuery } from "@/lib/tenant-isolation";

// Schema for updating a project
const updateProjectSchema = z.object({
  name: z.string().min(1, "Project name is required").optional(),
  description: z.string().optional(),
  type: z.enum([
    "RENEWABLE_ENERGY",
    "FORESTRY",
    "METHANE_REDUCTION",
    "ENERGY_EFFICIENCY",
    "WASTE_MANAGEMENT",
    "AGRICULTURE",
    "TRANSPORTATION",
    "INDUSTRIAL",
    "OTHER"
  ]).optional(),
  status: z.enum([
    "PENDING",
    "ACTIVE",
    "COMPLETED",
    "SUSPENDED",
    "CANCELLED"
  ]).optional(),
  startDate: z.string().datetime().optional().nullable(),
  endDate: z.string().datetime().optional().nullable(),
  location: z.string().optional().nullable(),
  country: z.string().optional().nullable(),
  coordinates: z.string().optional().nullable(),
  area: z.number().positive().optional().nullable(),
  externalProjectId: z.string().optional().nullable(),
  registryId: z.string().optional().nullable(),
  standard: z.string().optional().nullable(),
  methodology: z.string().optional().nullable(),
  methodologyVersion: z.string().optional().nullable(),
  estimatedReductions: z.number().positive().optional().nullable(),
  actualReductions: z.number().positive().optional().nullable(),
  verifier: z.string().optional().nullable(),
  validator: z.string().optional().nullable(),
  images: z.array(z.string().url()).optional(),
  budget: z.number().positive().optional().nullable(),
  roi: z.number().optional().nullable(),
  sdgs: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET /api/projects/[id]
 * Get a specific project by ID
 */
async function getProjectHandler(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to view project details",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  const resolvedParams = await params;

  try {

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Debug logging
    logger.info("Project fetch request", {
      projectId: resolvedParams.id,
      userId: session.user.id,
      userRole: session.user.role,
      organizationId: tenantContext.organizationId,
      isAdmin: tenantContext.isAdmin,
      sessionOrganizationId: session.user.organizationId,
    });

    // Create base query
    let query: any = {
      where: {
        id: resolvedParams.id,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        carbonCredits: {
          select: {
            id: true,
            name: true,
            vintage: true,
            quantity: true,
            availableQuantity: true,
            status: true,
          },
        },
        documents: true,
        verificationHistory: {
          orderBy: {
            timestamp: 'desc',
          },
        },
        wallets: {
          select: {
            id: true,
            name: true,
            address: true,
            network: true,
            chainId: true,
            balance: true,
          },
        },
        financialMetrics: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 10,
        },
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      query = withTenantQuery(query, tenantContext);
      logger.info("Applied tenant isolation", {
        modifiedQuery: JSON.stringify(query, null, 2),
        tenantContext: tenantContext,
      });
    } else {
      logger.info("Skipping tenant isolation for admin user", {
        userId: session.user.id,
        userRole: session.user.role,
      });
    }

    // Get the project
    const project = await db.project.findUnique(query);

    if (!project) {
      logger.warn("Project not found", {
        projectId: resolvedParams.id,
        userId: session.user.id,
        userRole: session.user.role,
        organizationId: tenantContext.organizationId,
        query: JSON.stringify(query, null, 2),
      });

      throw new ApiError(
        "Project not found or access denied",
        ErrorType.NOT_FOUND,
        404
      );
    }

    logger.info("Project found successfully", {
      projectId: project.id,
      projectName: project.name,
      projectOrganizationId: project.organizationId,
      userOrganizationId: tenantContext.organizationId,
    });

    return NextResponse.json({ project });
  } catch (error) {
    logger.error(`Error fetching project ${resolvedParams.id}:`, {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      projectId: resolvedParams.id,
      userId: session?.user?.id,
      userRole: session?.user?.role,
    });

    // If it's already an ApiError, re-throw it
    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while fetching the project",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PATCH /api/projects/[id]
 * Update a specific project
 */
async function updateProjectHandler(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to update a project",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  const resolvedParams = await params;

  try {

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: resolvedParams.id,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantQuery(projectQuery, tenantContext);
    }

    const existingProject = await db.project.findUnique(projectQuery);

    if (!existingProject) {
      throw new ApiError(
        "Project not found or you don't have permission to update it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse request body
    const body = await req.json();
    const updateData = updateProjectSchema.parse(body);

    // Update the project
    const updatedProject = await db.project.update({
      where: {
        id: resolvedParams.id,
      },
      data: updateData,
    });

    logger.info(`User ${session.user.id} updated project ${resolvedParams.id}`);

    return NextResponse.json({
      project: updatedProject,
      message: "Project updated successfully",
    });
  } catch (error) {
    logger.error(`Error updating project ${resolvedParams.id}:`, error);

    if (error instanceof z.ZodError) {
      throw new ApiError(
        "Invalid project data",
        ErrorType.VALIDATION,
        400,
        error.errors
      );
    }

    throw new ApiError(
      "An error occurred while updating the project",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * DELETE /api/projects/[id]
 * Delete a specific project
 */
async function deleteProjectHandler(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await auth();

  if (!session?.user) {
    throw new ApiError(
      "You must be logged in to delete a project",
      ErrorType.AUTHENTICATION,
      401
    );
  }

  const resolvedParams = await params;

  try {

    // Get tenant context
    const tenantContext = await getTenantContext(session.user.id);

    // Check if the project exists and user has access
    let projectQuery: any = {
      where: {
        id: resolvedParams.id,
      },
      include: {
        carbonCredits: true,
      },
    };

    // Apply tenant isolation if not an admin
    if (!tenantContext.isAdmin) {
      projectQuery = withTenantQuery(projectQuery, tenantContext);
    }

    const existingProject = await db.project.findUnique(projectQuery);

    if (!existingProject) {
      throw new ApiError(
        "Project not found or you don't have permission to delete it",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if the project has carbon credits
    if (existingProject.carbonCredits.length > 0) {
      throw new ApiError(
        "Cannot delete a project with associated carbon credits",
        ErrorType.VALIDATION,
        400
      );
    }

    // Delete the project
    await db.project.delete({
      where: {
        id: resolvedParams.id,
      },
    });

    logger.info(`User ${session.user.id} deleted project ${resolvedParams.id}`);

    return NextResponse.json({
      message: "Project deleted successfully",
    });
  } catch (error) {
    logger.error(`Error deleting project ${resolvedParams.id}:`, error);

    throw new ApiError(
      "An error occurred while deleting the project",
      ErrorType.INTERNAL,
      500
    );
  }
}

// Wrap handlers with error handling
const wrappedGetHandler = withErrorHandling(getProjectHandler);
const wrappedPatchHandler = withErrorHandling(updateProjectHandler);
const wrappedDeleteHandler = withErrorHandling(deleteProjectHandler);

// Export the handlers with resource isolation middleware
export const GET = withResourceIsolation('project', 'id')(wrappedGetHandler);
export const PATCH = withResourceIsolation('project', 'id')(wrappedPatchHandler);
export const DELETE = withResourceIsolation('project', 'id')(wrappedDeleteHandler);
