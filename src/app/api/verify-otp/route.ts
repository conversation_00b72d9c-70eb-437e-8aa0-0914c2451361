import { NextResponse } from "next/server";
import { z } from "zod";
import { verifyOTP } from "@/lib/otp";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";

const verifyOtpSchema = z.object({
  email: z.string().email(),
  otp: z.string().length(6),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    
    // Validate request body
    const { email, otp } = verifyOtpSchema.parse(body);

    // Verify the OTP
    const verificationResult = await verifyOTP(email, otp);

    if (!verificationResult.success) {
      return NextResponse.json(
        { error: verificationResult.message },
        { status: 400 }
      );
    }

    // If OTP is verified, mark the user's email as verified
    if (verificationResult.userId) {
      await db.user.update({
        where: {
          id: verificationResult.userId,
        },
        data: {
          emailVerified: new Date(),
        },
      });

      logger.info(`Email verified for user: ${verificationResult.userId}`);
    }

    // Check if user has an organization for onboarding determination
    let requiresOnboarding = true;
    if (verificationResult.userId) {
      const user = await db.user.findUnique({
        where: { id: verificationResult.userId },
        select: { organizationId: true },
      });
      requiresOnboarding = !user?.organizationId;
    }

    return NextResponse.json(
      {
        message: verificationResult.message,
        verified: true,
        requiresOnboarding,
      },
      { status: 200 }
    );
  } catch (error) {
    logger.error("Error verifying OTP:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred during OTP verification" },
      { status: 500 }
    );
  }
}
