import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';

/**
 * GET /api/rbac/audit
 * Get RBAC audit logs for the organization
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canView = await hasPermission('view:rbac:audit', context);
    if (!canView) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const userId = searchParams.get('userId') || '';
    const action = searchParams.get('action') || '';
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const skip = (page - 1) * limit;

    // Build where clause for permission usage logs
    const whereClause: any = {};

    if (userId) {
      whereClause.userId = userId;
    }

    if (action) {
      whereClause.action = { contains: action, mode: 'insensitive' };
    }

    if (startDate || endDate) {
      whereClause.timestamp = {};
      if (startDate) {
        whereClause.timestamp.gte = new Date(startDate);
      }
      if (endDate) {
        whereClause.timestamp.lte = new Date(endDate);
      }
    }

    // Get permission usage logs (this serves as our audit trail)
    // Note: PermissionUsageLog doesn't have foreign key relationships, so we need to fetch separately
    const [logs, totalCount] = await Promise.all([
      db.permissionUsageLog.findMany({
        where: whereClause,
        orderBy: { timestamp: 'desc' },
        skip,
        take: limit,
      }),
      db.permissionUsageLog.count({ where: whereClause }),
    ]);

    // Fetch user and permission data separately
    const userIds = [...new Set(logs.map(log => log.userId))];
    const permissionIds = [...new Set(logs.map(log => log.permissionId))];

    const [users, permissions] = await Promise.all([
      userIds.length > 0 ? db.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true, email: true },
      }) : [],
      permissionIds.length > 0 ? db.permission.findMany({
        where: { id: { in: permissionIds } },
        select: { id: true, name: true, displayName: true, category: true },
      }) : [],
    ]);

    // Create lookup maps
    const userMap = new Map(users.map(user => [user.id, user]));
    const permissionMap = new Map(permissions.map(perm => [perm.id, perm]));

    // Get role assignment changes (from UserCustomRole table)
    const roleChanges = await db.userCustomRole.findMany({
      where: {
        user: {
          organizationId: currentUser.organizationId,
        },
        ...(userId && { userId }),
        ...(startDate || endDate ? {
          createdAt: {
            ...(startDate && { gte: new Date(startDate) }),
            ...(endDate && { lte: new Date(endDate) }),
          },
        } : {}),
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        role: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
      orderBy: { createdAt: 'desc' },
      take: limit,
    });

    // Transform permission usage logs
    const transformedLogs = logs.map(log => ({
      id: log.id,
      type: 'permission_usage',
      action: log.action || 'Permission Check',
      user: userMap.get(log.userId) || { id: log.userId, name: 'Unknown User', email: '<EMAIL>' },
      permission: permissionMap.get(log.permissionId) || { id: log.permissionId, name: 'Unknown Permission', displayName: 'Unknown Permission', category: 'unknown' },
      resourceType: log.resourceType,
      resourceId: log.resourceId,
      granted: log.success, // Note: field is called 'success' in the schema
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      metadata: log.metadata,
      timestamp: log.timestamp, // Note: field is called 'timestamp' in the schema
    }));

    // Transform role changes
    const transformedRoleChanges = roleChanges.map(change => ({
      id: change.id,
      type: 'role_assignment',
      action: 'Role Assigned',
      user: change.user,
      role: {
        ...change.role,
        displayName: change.role.description || change.role.name, // Use description as displayName fallback
      },
      expiresAt: change.expiresAt,
      timestamp: change.createdAt,
    }));

    // Combine and sort all audit entries
    const allAuditEntries = [...transformedLogs, ...transformedRoleChanges]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);

    // Get summary statistics
    // Note: PermissionUsageLog doesn't have organization filtering, so we'll get all for now
    const stats = {
      totalPermissionChecks: await db.permissionUsageLog.count({
        where: {
          timestamp: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
      totalRoleAssignments: await db.userCustomRole.count({
        where: {
          user: {
            organizationId: currentUser.organizationId,
          },
        },
      }),
      activeUsers: await db.user.count({
        where: {
          organizationId: currentUser.organizationId,
          lastLoginAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
      }),
    };

    return NextResponse.json({
      success: true,
      data: {
        auditEntries: allAuditEntries,
        stats,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit),
        },
      },
    });
  } catch (error) {
    logger.error('Error fetching audit logs:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
