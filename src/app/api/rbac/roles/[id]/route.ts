import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';

// Schema for role update
const updateRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(50, 'Role name too long').optional(),
  displayName: z.string().min(1, 'Display name is required').max(100, 'Display name too long').optional(),
  description: z.string().optional(),
  permissions: z.array(z.string()).optional(),
  parentRoleId: z.string().optional(),
});

/**
 * GET /api/rbac/roles/[id]
 * Get a specific role by ID
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canRead = await hasPermission('read:role', context);
    if (!canRead) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Get the role
    const role = await db.customRole.findFirst({
      where: {
        id: params.id,
        organizationId: user.organizationId,
      },
      include: {
        permissions: {
          include: {
            permission: {
              select: {
                name: true,
                displayName: true,
                description: true,
                category: true,
              },
            },
          },
        },
        userRoles: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
                jobTitle: true,
                createdAt: true,
              },
            },
          },
        },
        parentRole: {
          select: {
            id: true,
            name: true,
            displayName: true,
          },
        },
        childRoles: {
          select: {
            id: true,
            name: true,
            displayName: true,
          },
        },
      },
    });

    if (!role) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    // Transform the data
    const transformedRole = {
      id: role.id,
      name: role.name,
      displayName: role.displayName,
      description: role.description,
      isSystemRole: role.isSystemRole,
      parentRole: role.parentRole,
      childRoles: role.childRoles,
      permissions: role.permissions.map(rp => rp.permission),
      users: role.userRoles.map(ur => ur.user),
      createdAt: role.createdAt,
      updatedAt: role.updatedAt,
    };

    return NextResponse.json({
      success: true,
      data: transformedRole,
    });
  } catch (error) {
    logger.error('Error fetching role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/rbac/roles/[id]
 * Update a role
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canUpdate = await hasPermission('update:role', context);
    if (!canUpdate) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await req.json();
    const validatedData = updateRoleSchema.parse(body);

    // Check if role exists and belongs to organization
    const existingRole = await db.customRole.findFirst({
      where: {
        id: params.id,
        organizationId: user.organizationId,
      },
    });

    if (!existingRole) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    // Check if it's a system role
    if (existingRole.isSystemRole) {
      return NextResponse.json(
        { error: 'Cannot modify system roles' },
        { status: 400 }
      );
    }

    // Check if new name conflicts with existing roles
    if (validatedData.name && validatedData.name !== existingRole.name) {
      const nameConflict = await db.customRole.findFirst({
        where: {
          name: validatedData.name,
          organizationId: user.organizationId,
          id: { not: params.id },
        },
      });

      if (nameConflict) {
        return NextResponse.json(
          { error: 'Role name already exists' },
          { status: 400 }
        );
      }
    }

    // Validate permissions if provided
    if (validatedData.permissions) {
      const existingPermissions = await db.permission.findMany({
        where: {
          name: { in: validatedData.permissions },
        },
        select: { name: true },
      });

      const existingPermissionNames = existingPermissions.map(p => p.name);
      const invalidPermissions = validatedData.permissions.filter(
        p => !existingPermissionNames.includes(p)
      );

      if (invalidPermissions.length > 0) {
        return NextResponse.json(
          { error: `Invalid permissions: ${invalidPermissions.join(', ')}` },
          { status: 400 }
        );
      }
    }

    // Update role in transaction
    const result = await db.$transaction(async (tx) => {
      // Update role basic info
      const updatedRole = await tx.customRole.update({
        where: { id: params.id },
        data: {
          name: validatedData.name,
          displayName: validatedData.displayName,
          description: validatedData.description,
          parentRoleId: validatedData.parentRoleId,
        },
      });

      // Update permissions if provided
      if (validatedData.permissions !== undefined) {
        // Remove existing permissions
        await tx.rolePermission.deleteMany({
          where: { roleId: params.id },
        });

        // Add new permissions
        if (validatedData.permissions.length > 0) {
          const permissions = await tx.permission.findMany({
            where: { name: { in: validatedData.permissions } },
            select: { id: true },
          });

          await tx.rolePermission.createMany({
            data: permissions.map(permission => ({
              roleId: params.id,
              permissionId: permission.id,
            })),
          });
        }
      }

      return updatedRole;
    });

    logger.info('Role updated successfully', {
      roleId: params.id,
      roleName: result.name,
      organizationId: user.organizationId,
      updatedBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error updating role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/rbac/roles/[id]
 * Delete a role
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!user?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: user.organizationId,
    };

    const canDelete = await hasPermission('delete:role', context);
    if (!canDelete) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Check if role exists and belongs to organization
    const existingRole = await db.customRole.findFirst({
      where: {
        id: params.id,
        organizationId: user.organizationId,
      },
      include: {
        userRoles: true,
        childRoles: true,
      },
    });

    if (!existingRole) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    // Check if it's a system role
    if (existingRole.isSystemRole) {
      return NextResponse.json(
        { error: 'Cannot delete system roles' },
        { status: 400 }
      );
    }

    // Check if role has users assigned
    if (existingRole.userRoles.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete role with assigned users' },
        { status: 400 }
      );
    }

    // Check if role has child roles
    if (existingRole.childRoles.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete role with child roles' },
        { status: 400 }
      );
    }

    // Delete the role
    await db.customRole.delete({
      where: { id: params.id },
    });

    logger.info('Role deleted successfully', {
      roleId: params.id,
      roleName: existingRole.name,
      organizationId: user.organizationId,
      deletedBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      message: 'Role deleted successfully',
    });
  } catch (error) {
    logger.error('Error deleting role:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
