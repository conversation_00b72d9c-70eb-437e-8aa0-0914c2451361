import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';

// Schema for user update
const updateUserSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  jobTitle: z.string().optional(),
  departmentName: z.string().optional(),
  phoneNumber: z.string().optional(),
  role: z.enum(['ORGANIZATION_ADMIN', 'ORGANIZATION_USER']).optional(),
});

/**
 * GET /api/rbac/users/[id]
 * Get a specific user with their roles and permissions
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canRead = await hasPermission('read:user', context);
    if (!canRead) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Get the user with their roles and permissions
    const user = await db.user.findFirst({
      where: {
        id: params.id,
        organizationId: currentUser.organizationId,
      },
      include: {
        customRoles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: {
                      select: {
                        name: true,
                        displayName: true,
                        description: true,
                        category: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
        permissionGrants: {
          include: {
            permission: {
              select: {
                name: true,
                displayName: true,
                description: true,
                category: true,
              },
            },
          },
        },
        temporaryPermissions: {
          where: {
            expiresAt: { gt: new Date() },
          },
          include: {
            permission: {
              select: {
                name: true,
                displayName: true,
                description: true,
                category: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Transform the data
    const transformedUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      jobTitle: user.jobTitle,
      departmentName: user.departmentName,
      phoneNumber: user.phoneNumber,
      systemRole: user.role,
      lastLoginAt: user.lastLoginAt,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      customRoles: user.customRoles.map(ur => ({
        id: ur.role.id,
        name: ur.role.name,
        displayName: ur.role.displayName,
        description: ur.role.description,
        isSystemRole: ur.role.isSystemRole,
        permissions: ur.role.permissions.map(rp => rp.permission),
        assignedAt: ur.createdAt,
        expiresAt: ur.expiresAt,
      })),
      directPermissions: user.permissionGrants.map(pg => pg.permission),
      temporaryPermissions: user.temporaryPermissions.map(tp => ({
        ...tp.permission,
        expiresAt: tp.expiresAt,
      })),
    };

    return NextResponse.json({
      success: true,
      data: transformedUser,
    });
  } catch (error) {
    logger.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/rbac/users/[id]
 * Update a user's basic information
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canUpdate = await hasPermission('update:user', context);
    if (!canUpdate) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await req.json();
    const validatedData = updateUserSchema.parse(body);

    // Check if user exists and belongs to organization
    const existingUser = await db.user.findFirst({
      where: {
        id: params.id,
        organizationId: currentUser.organizationId,
      },
    });

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Update the user
    const updatedUser = await db.user.update({
      where: { id: params.id },
      data: validatedData,
    });

    logger.info('User updated successfully', {
      userId: params.id,
      updatedBy: session.user.id,
      organizationId: currentUser.organizationId,
    });

    // Return user without password
    const { password, ...userWithoutPassword } = updatedUser;

    return NextResponse.json({
      success: true,
      data: userWithoutPassword,
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/rbac/users/[id]
 * Delete a user (soft delete by removing from organization)
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canRemove = await hasPermission('remove:user', context);
    if (!canRemove) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Check if user exists and belongs to organization
    const existingUser = await db.user.findFirst({
      where: {
        id: params.id,
        organizationId: currentUser.organizationId,
      },
    });

    if (!existingUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Prevent self-deletion
    if (params.id === session.user.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Remove user from organization (soft delete)
    await db.$transaction(async (tx) => {
      // Remove all custom role assignments
      await tx.userCustomRole.deleteMany({
        where: { userId: params.id },
      });

      // Remove all direct permission grants
      await tx.permissionGrant.deleteMany({
        where: { userId: params.id },
      });

      // Remove all temporary permissions
      await tx.temporaryPermission.deleteMany({
        where: { userId: params.id },
      });

      // Remove user from organization
      await tx.user.update({
        where: { id: params.id },
        data: {
          organizationId: null,
          role: 'USER',
        },
      });
    });

    logger.info('User removed from organization', {
      userId: params.id,
      removedBy: session.user.id,
      organizationId: currentUser.organizationId,
    });

    return NextResponse.json({
      success: true,
      message: 'User removed from organization successfully',
    });
  } catch (error) {
    logger.error('Error removing user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
