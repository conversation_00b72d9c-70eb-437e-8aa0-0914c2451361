import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { hasPermission } from '@/lib/rbac/rbac-service';
import { PermissionContext } from '@/lib/rbac/types';

// Schema for role assignment
const assignRoleSchema = z.object({
  roleIds: z.array(z.string()).min(1, 'At least one role is required'),
  expiresAt: z.string().datetime().optional(),
});

// Schema for role revocation
const revokeRoleSchema = z.object({
  roleIds: z.array(z.string()).min(1, 'At least one role is required'),
});

/**
 * GET /api/rbac/users/[id]/roles
 * Get all roles assigned to a user
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canRead = await hasPermission('read:user', context);
    if (!canRead) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    // Get target user and verify they belong to the same organization
    const targetUser = await db.user.findFirst({
      where: {
        id: params.id,
        organizationId: currentUser.organizationId,
      },
      include: {
        customRoles: {
          include: {
            role: {
              include: {
                permissions: {
                  include: {
                    permission: {
                      select: {
                        name: true,
                        displayName: true,
                        description: true,
                        category: true,
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Transform the data
    const userRoles = targetUser.customRoles.map(userRole => ({
      id: userRole.role.id,
      name: userRole.role.name,
      displayName: userRole.role.displayName,
      description: userRole.role.description,
      isSystemRole: userRole.role.isSystemRole,
      permissions: userRole.role.permissions.map(rp => rp.permission),
      assignedAt: userRole.createdAt,
      expiresAt: userRole.expiresAt,
    }));

    return NextResponse.json({
      success: true,
      data: {
        userId: targetUser.id,
        userName: targetUser.name,
        userEmail: targetUser.email,
        systemRole: targetUser.role,
        customRoles: userRoles,
      },
    });
  } catch (error) {
    logger.error('Error fetching user roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/rbac/users/[id]/roles
 * Assign roles to a user
 */
export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization and role
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true, role: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canAssign = await hasPermission('assign:role', context);
    if (!canAssign) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await req.json();
    const validatedData = assignRoleSchema.parse(body);

    // Verify target user exists and belongs to the same organization
    const targetUser = await db.user.findFirst({
      where: {
        id: params.id,
        organizationId: currentUser.organizationId,
      },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Verify all roles exist and belong to the organization
    const roles = await db.customRole.findMany({
      where: {
        id: { in: validatedData.roleIds },
        organizationId: currentUser.organizationId,
      },
      select: { id: true, name: true, isSystemRole: true },
    });

    if (roles.length !== validatedData.roleIds.length) {
      const foundRoleIds = roles.map(r => r.id);
      const missingRoleIds = validatedData.roleIds.filter(id => !foundRoleIds.includes(id));
      return NextResponse.json(
        { error: `Roles not found: ${missingRoleIds.join(', ')}` },
        { status: 400 }
      );
    }

    // Check if current user can assign these roles
    for (const role of roles) {
      // Organization admins cannot assign platform admin roles
      if (currentUser.role === 'ORGANIZATION_ADMIN' && role.isSystemRole && role.name === 'ADMIN') {
        return NextResponse.json(
          { error: 'Permission denied: Cannot assign platform administrator role' },
          { status: 403 }
        );
      }

      // Regular users cannot assign any system roles
      if (currentUser.role === 'USER' && role.isSystemRole) {
        return NextResponse.json(
          { error: 'Permission denied: Cannot assign system roles' },
          { status: 403 }
        );
      }
    }

    // Check for existing role assignments
    const existingAssignments = await db.userCustomRole.findMany({
      where: {
        userId: params.id,
        roleId: { in: validatedData.roleIds },
      },
    });

    const existingRoleIds = existingAssignments.map(a => a.roleId);
    const newRoleIds = validatedData.roleIds.filter(id => !existingRoleIds.includes(id));

    if (newRoleIds.length === 0) {
      return NextResponse.json(
        { error: 'All roles are already assigned to this user' },
        { status: 400 }
      );
    }

    // Assign new roles
    const expiresAt = validatedData.expiresAt ? new Date(validatedData.expiresAt) : null;
    
    await db.userCustomRole.createMany({
      data: newRoleIds.map(roleId => ({
        userId: params.id,
        roleId,
        expiresAt,
      })),
    });

    logger.info('Roles assigned to user', {
      targetUserId: params.id,
      roleIds: newRoleIds,
      assignedBy: session.user.id,
      organizationId: currentUser.organizationId,
    });

    return NextResponse.json({
      success: true,
      message: `${newRoleIds.length} role(s) assigned successfully`,
      data: {
        assignedRoles: newRoleIds,
        skippedRoles: existingRoleIds,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error assigning roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/rbac/users/[id]/roles
 * Revoke roles from a user
 */
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's organization
    const currentUser = await db.user.findUnique({
      where: { id: session.user.id },
      select: { organizationId: true },
    });

    if (!currentUser?.organizationId) {
      return NextResponse.json({ error: 'User not associated with organization' }, { status: 400 });
    }

    // Check permission
    const context: PermissionContext = {
      userId: session.user.id,
      organizationId: currentUser.organizationId,
    };

    const canRevoke = await hasPermission('revoke:role', context);
    if (!canRevoke) {
      return NextResponse.json({ error: 'Permission denied' }, { status: 403 });
    }

    const body = await req.json();
    const validatedData = revokeRoleSchema.parse(body);

    // Verify target user exists and belongs to the same organization
    const targetUser = await db.user.findFirst({
      where: {
        id: params.id,
        organizationId: currentUser.organizationId,
      },
    });

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Remove role assignments
    const result = await db.userCustomRole.deleteMany({
      where: {
        userId: params.id,
        roleId: { in: validatedData.roleIds },
      },
    });

    logger.info('Roles revoked from user', {
      targetUserId: params.id,
      roleIds: validatedData.roleIds,
      revokedCount: result.count,
      revokedBy: session.user.id,
      organizationId: currentUser.organizationId,
    });

    return NextResponse.json({
      success: true,
      message: `${result.count} role(s) revoked successfully`,
      data: {
        revokedCount: result.count,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('Error revoking roles:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
