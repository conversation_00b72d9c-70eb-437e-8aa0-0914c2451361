import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { logger } from '@/lib/logger';
import { initializeRbacSystem, isRbacInitialized } from '@/lib/rbac/init-rbac';

/**
 * POST /api/rbac/init
 * Initialize the RBAC system (Admin only)
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin (only admins can initialize RBAC)
    if (session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Admin access required' }, { status: 403 });
    }

    // Check if already initialized
    const isInitialized = await isRbacInitialized();
    if (isInitialized) {
      return NextResponse.json({
        success: true,
        message: 'RBAC system is already initialized',
        alreadyInitialized: true,
      });
    }

    // Initialize the RBAC system
    await initializeRbacSystem();

    logger.info('RBAC system initialized successfully', {
      initializedBy: session.user.id,
    });

    return NextResponse.json({
      success: true,
      message: 'RBAC system initialized successfully',
      alreadyInitialized: false,
    });
  } catch (error) {
    logger.error('Error initializing RBAC system:', error);
    return NextResponse.json(
      { error: 'Failed to initialize RBAC system' },
      { status: 500 }
    );
  }
}

/**
 * GET /api/rbac/init
 * Check RBAC initialization status
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const isInitialized = await isRbacInitialized();

    return NextResponse.json({
      success: true,
      initialized: isInitialized,
    });
  } catch (error) {
    logger.error('Error checking RBAC initialization status:', error);
    return NextResponse.json(
      { error: 'Failed to check RBAC status' },
      { status: 500 }
    );
  }
}
