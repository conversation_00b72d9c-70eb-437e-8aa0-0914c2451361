import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

const updateRoleSchema = z.object({
  role: z.enum(["CLIENT", "BROKER", "USER", "ADMIN", "ORGANIZATION_ADMIN"]),
});

export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { role } = updateRoleSchema.parse(body);

    // Convert CLIENT to USER for database storage
    const dbRole = role === "CLIENT" ? "USER" : role;

    // Update user role in database
    const updatedUser = await db.user.update({
      where: { id: session.user.id },
      data: { role: dbRole },
    });

    logger.info("User role updated", {
      userId: session.user.id,
      email: session.user.email,
      newRole: dbRole,
    });

    return NextResponse.json({
      success: true,
      role: updatedUser.role,
    });
  } catch (error) {
    logger.error("Error updating user role", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid role specified" },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update user role" },
      { status: 500 }
    );
  }
}
