import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { AuditLogType } from '@prisma/client';
import { z } from 'zod';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';

// Enhanced validation schema for audit log filters
const auditLogFilterSchema = z.object({
  type: z.nativeEnum(AuditLogType).optional(),
  types: z.array(z.nativeEnum(AuditLogType)).optional(),
  excludeTypes: z.array(z.nativeEnum(AuditLogType)).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['createdAt', 'type', 'description', 'severity', 'category']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
  resourceType: z.string().optional(),
  resourceId: z.string().optional(),

  // Enhanced filtering options
  severity: z.enum(['INFO', 'WARN', 'ERROR', 'CRITICAL']).optional(),
  category: z.enum(['AUTHENTICATION', 'AUTHORIZATION', 'DATA', 'DATA_ENTRY', 'SYSTEM', 'SPV', 'DOCUMENT', 'ROLE', 'API', 'TRANSACTION', 'WALLET']).optional(),
  source: z.enum(['WEB', 'API', 'MOBILE', 'SYSTEM', 'CLI']).optional(),
  success: z.boolean().optional(),
  sessionId: z.string().optional(),
  requestId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  userId: z.string().optional(),
  organizationId: z.string().optional(),
  spvId: z.string().optional(),
  projectId: z.string().optional(),

  // Export and real-time options
  export: z.enum(['csv', 'json', 'pdf']).optional(),
  includeMetadata: z.boolean().default(false),
  realtime: z.boolean().default(false),
  lastTimestamp: z.string().datetime().optional(),
});

/**
 * GET /api/audit-logs
 * Get audit logs based on user role and permissions
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters with enhanced support
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Convert string parameters to appropriate types
    const processedParams: any = { ...queryParams };

    if (processedParams.page) processedParams.page = parseInt(processedParams.page);
    if (processedParams.limit) processedParams.limit = parseInt(processedParams.limit);
    if (processedParams.types) processedParams.types = processedParams.types.split(',');
    if (processedParams.excludeTypes) processedParams.excludeTypes = processedParams.excludeTypes.split(',');
    if (processedParams.tags) processedParams.tags = processedParams.tags.split(',');
    if (processedParams.success) processedParams.success = processedParams.success === 'true';
    if (processedParams.includeMetadata) processedParams.includeMetadata = processedParams.includeMetadata === 'true';
    if (processedParams.realtime) processedParams.realtime = processedParams.realtime === 'true';

    const validatedParams = auditLogFilterSchema.parse(processedParams);

    // Get user details with role information
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        organization: true,
        spvUser: {
          include: {
            spv: true,
            projectAssignments: {
              include: {
                project: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Build filter based on user role and permissions
    const filter = {
      ...validatedParams,
      startDate: validatedParams.startDate ? new Date(validatedParams.startDate) : undefined,
      endDate: validatedParams.endDate ? new Date(validatedParams.endDate) : undefined,
    };

    let auditLogsResponse;

    // Role-based filtering
    if (user.role === 'ADMIN') {
      // Super Admin - can see all logs
      auditLogsResponse = await AuditService.getAuditLogsForSuperAdmin(filter);
    } else if (user.role === 'ORGANIZATION_ADMIN') {
      // Organization Admin - can see all logs if no organizationId filter, otherwise org-specific logs
      if (validatedParams.organizationId) {
        // Specific organization requested
        auditLogsResponse = await AuditService.getAuditLogsForOrgAdmin(validatedParams.organizationId, filter);
      } else {
        // No organization filter - show all logs (like super admin)
        auditLogsResponse = await AuditService.getAuditLogsForSuperAdmin(filter);
      }
    } else if (user.spvUser) {
      // SPV Users - role-based access within SPV
      const spvUser = user.spvUser;
      
      if (spvUser.role === 'SPV_ADMIN') {
        // SPV Admin - can see all SPV logs
        auditLogsResponse = await AuditService.getAuditLogsForSPVAdmin(spvUser.spvId, filter);
      } else if (spvUser.role === 'PROJECT_MANAGER') {
        // Project Manager - can see assigned project logs
        const projectIds = spvUser.projectAssignments.map(assignment => assignment.projectId);
        auditLogsResponse = await AuditService.getAuditLogsForProjectManager(projectIds, filter);
      } else if (spvUser.role === 'SITE_WORKER') {
        // Site Worker - can only see own logs
        auditLogsResponse = await AuditService.getAuditLogsForSiteWorker(user.id, filter);
      } else {
        return NextResponse.json({ error: 'Invalid SPV role' }, { status: 403 });
      }
    } else {
      // Regular organization user - can only see own logs
      auditLogsResponse = await AuditService.getAuditLogsForSiteWorker(user.id, filter);
    }

    // Handle real-time updates
    if (validatedParams.realtime && validatedParams.lastTimestamp) {
      if (user.role === 'ORGANIZATION_ADMIN' && user.organizationId) {
        const realtimeData = await AuditService.getRealtimeAuditLogs(
          user.organizationId,
          new Date(validatedParams.lastTimestamp),
          validatedParams.limit
        );
        return NextResponse.json(realtimeData);
      } else {
        return NextResponse.json({ error: 'Real-time access requires organization admin role' }, { status: 403 });
      }
    }

    // Handle export functionality
    if (validatedParams.export) {
      let exportData: string | Buffer;
      let contentType: string;
      let filename: string;

      const exportFilter = { ...filter };

      switch (validatedParams.export) {
        case 'csv':
          exportData = await AuditService.exportAuditLogs(exportFilter, 'csv');
          contentType = 'text/csv';
          filename = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
          break;
        case 'json':
          exportData = await AuditService.exportAuditLogs(exportFilter, 'json');
          contentType = 'application/json';
          filename = `audit-logs-${new Date().toISOString().split('T')[0]}.json`;
          break;
        case 'pdf':
          // For now, return CSV data as PDF is not implemented
          exportData = await AuditService.exportAuditLogs(exportFilter, 'csv');
          contentType = 'text/csv';
          filename = `audit-logs-report-${new Date().toISOString().split('T')[0]}.csv`;
          break;
        default:
          return NextResponse.json({ error: 'Invalid export format' }, { status: 400 });
      }

      // Log the export action (only for exports, not regular API calls)
      if (validatedParams.export) {
        try {
          await AuditService.createAuditLog({
            type: AuditLogType.AUDIT_LOG_EXPORTED,
            description: `User exported audit logs in ${validatedParams.export} format`,
            userId: user.id,
            organizationId: user.organizationId || undefined,
            spvId: user.spvUser?.spvId,
            metadata: {
              exportFormat: validatedParams.export,
              filters: exportFilter,
              userRole: user.role,
              spvRole: user.spvUser?.role
            },
            ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
            userAgent: req.headers.get('user-agent') || 'unknown',
          });
        } catch (auditError) {
          // Don't fail the export if audit logging fails
          console.warn('Failed to log export action:', auditError);
        }
      }

      return new NextResponse(exportData, {
        headers: {
          'Content-Type': contentType,
          'Content-Disposition': `attachment; filename="${filename}"`,
        },
      });
    }

    // Note: Removed audit logging here to prevent self-logging loop

    return NextResponse.json(auditLogsResponse);

  } catch (error) {
    logger.error('Error fetching audit logs:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/audit-logs
 * Create a new audit log entry
 */
export async function POST(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    const auditLogSchema = z.object({
      type: z.nativeEnum(AuditLogType),
      description: z.string().min(1),
      resourceType: z.string().optional(),
      resourceId: z.string().optional(),
      oldValue: z.any().optional(),
      newValue: z.any().optional(),
      metadata: z.record(z.any()).optional(),
    });

    const validatedData = auditLogSchema.parse(body);

    // Get user details
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        spvUser: true
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const auditLog = await AuditService.createAuditLog({
      ...validatedData,
      userId: user.id,
      organizationId: user.organizationId || undefined,
      spvId: user.spvUser?.spvId,
      ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json(auditLog, { status: 201 });

  } catch (error) {
    logger.error('Error creating audit log:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
