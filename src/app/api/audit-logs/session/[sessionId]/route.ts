import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';

/**
 * GET /api/audit-logs/session/[sessionId]
 * Get audit logs for a specific session
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { sessionId: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { sessionId } = params;

    if (!sessionId) {
      return NextResponse.json({ error: 'Session ID is required' }, { status: 400 });
    }

    // Get user details
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        spvUser: true
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check permissions
    let organizationId: string | undefined;
    
    if (user.role === 'ADMIN') {
      // Super admin can access any session logs
      organizationId = undefined;
    } else if (user.role === 'ORGANIZATION_ADMIN') {
      // Organization admin can access sessions within their organization
      if (!user.organizationId) {
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
      }
      organizationId = user.organizationId;
    } else {
      // Regular users can only access their own session logs
      // First, check if this session belongs to them
      const sessionCheck = await db.auditLog.findFirst({
        where: {
          sessionId,
          userId: user.id
        }
      });
      
      if (!sessionCheck) {
        return NextResponse.json({ error: 'Session not found or access denied' }, { status: 404 });
      }
      
      organizationId = user.organizationId || undefined;
    }

    // Get session audit logs
    const sessionLogs = await AuditService.getAuditLogsBySession(sessionId, organizationId);

    // Note: Removed audit logging here to prevent self-logging loop

    return NextResponse.json({
      success: true,
      sessionId,
      ...sessionLogs
    });

  } catch (error) {
    logger.error('Error fetching session audit logs:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
