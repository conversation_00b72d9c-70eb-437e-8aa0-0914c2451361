import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { z } from 'zod';

// Validation schema for search parameters
const searchQuerySchema = z.object({
  q: z.string().min(1, 'Search query is required'),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  severity: z.enum(['INFO', 'WARN', 'ERROR', 'CRITICAL']).optional(),
  category: z.enum(['AUTHENTICATION', 'AUTHORIZATION', 'DATA', 'DATA_ENTRY', 'SYSTEM', 'SPV', 'DOCUMENT', 'ROLE', 'API', 'TRANSACTION', 'WALLET']).optional(),
  source: z.enum(['WEB', 'API', 'MOBILE', 'SYSTEM', 'CLI']).optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

/**
 * GET /api/audit-logs/search
 * Advanced search functionality for audit logs
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    // Convert string parameters to appropriate types
    const processedParams: any = { ...queryParams };
    if (processedParams.page) processedParams.page = parseInt(processedParams.page);
    if (processedParams.limit) processedParams.limit = parseInt(processedParams.limit);

    const validatedParams = searchQuerySchema.parse(processedParams);

    // Get user details
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        spvUser: {
          include: {
            projectAssignments: {
              where: { isActive: true },
              select: { projectId: true }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check permissions and determine organization scope
    let organizationId: string | undefined;
    
    if (user.role === 'ADMIN') {
      // Super admin can search across all organizations (but we'll limit to their org for security)
      organizationId = user.organizationId || undefined;
    } else if (user.role === 'ORGANIZATION_ADMIN') {
      // Organization admin can search within their organization
      if (!user.organizationId) {
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
      }
      organizationId = user.organizationId;
    } else {
      // Other users have limited search capabilities
      return NextResponse.json({ error: 'Insufficient permissions for advanced search' }, { status: 403 });
    }

    // Build search filters
    const searchFilters = {
      ...validatedParams,
      organizationId,
      startDate: validatedParams.startDate ? new Date(validatedParams.startDate) : undefined,
      endDate: validatedParams.endDate ? new Date(validatedParams.endDate) : undefined,
    };

    // Perform the search
    const searchResults = await AuditService.searchAuditLogs(
      organizationId!,
      validatedParams.q,
      searchFilters
    );

    // Note: Removed audit logging here to prevent self-logging loop

    return NextResponse.json({
      success: true,
      query: validatedParams.q,
      ...searchResults
    });

  } catch (error) {
    logger.error('Error searching audit logs:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
