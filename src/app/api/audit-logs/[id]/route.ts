import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { AuditLogType } from '@prisma/client';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';

/**
 * GET /api/audit-logs/[id]
 * Get a specific audit log by ID with role-based access control
 */
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;

    // Get user details with role information
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        organization: true,
        spvUser: {
          include: {
            spv: true,
            projectAssignments: {
              include: {
                project: true
              }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Get the audit log
    const auditLog = await AuditService.getAuditLogById(id);
    
    if (!auditLog) {
      return NextResponse.json({ error: 'Audit log not found' }, { status: 404 });
    }

    // Check access permissions based on user role
    let hasAccess = false;

    if (user.role === 'ADMIN') {
      // Super Admin - can access all logs
      hasAccess = true;
    } else if (user.role === 'ORGANIZATION_ADMIN') {
      // Organization Admin - can access logs from their organization
      hasAccess = auditLog.organizationId === user.organizationId;
    } else if (user.spvUser) {
      // SPV Users - role-based access within SPV
      const spvUser = user.spvUser;
      
      if (spvUser.role === 'SPV_ADMIN') {
        // SPV Admin - can access logs from their SPV or organization
        hasAccess = auditLog.spvId === spvUser.spvId || 
                   auditLog.organizationId === user.organizationId;
      } else if (spvUser.role === 'PROJECT_MANAGER') {
        // Project Manager - can access logs from assigned projects
        const projectIds = spvUser.projectAssignments.map(assignment => assignment.projectId);
        hasAccess = auditLog.projectId ? projectIds.includes(auditLog.projectId) : false ||
                   auditLog.userId === user.id; // Can also see own logs
      } else if (spvUser.role === 'SITE_WORKER') {
        // Site Worker - can only access own logs
        hasAccess = auditLog.userId === user.id;
      }
    } else {
      // Regular organization user - can only access own logs
      hasAccess = auditLog.userId === user.id;
    }

    if (!hasAccess) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Log the audit log access
    await AuditService.createAuditLog({
      type: AuditLogType.AUDIT_LOG_ACCESSED,
      description: `User accessed audit log details: ${id}`,
      userId: user.id,
      organizationId: user.organizationId || undefined,
      spvId: user.spvUser?.spvId,
      resourceType: 'audit_log',
      resourceId: id,
      metadata: {
        accessedLogType: auditLog.type,
        accessedLogDescription: auditLog.description,
        userRole: user.role,
        spvRole: user.spvUser?.role
      },
      ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
    });

    return NextResponse.json(auditLog);

  } catch (error) {
    logger.error(`Error fetching audit log ${params.id}:`, error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
