import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { z } from 'zod';

// Validation schema for security audit parameters
const securityQuerySchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  limit: z.number().min(1).max(500).default(100),
  severity: z.enum(['ERROR', 'CRITICAL']).optional(),
});

/**
 * GET /api/audit-logs/security
 * Get security-related audit logs for organization admins
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    
    // Convert string parameters to appropriate types
    const processedParams: any = { ...queryParams };
    if (processedParams.limit) processedParams.limit = parseInt(processedParams.limit);

    const validatedParams = securityQuerySchema.parse(processedParams);

    // Get user details
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        spvUser: true
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check permissions - only organization admins and super admins can access security logs
    if (user.role !== 'ORGANIZATION_ADMIN' && user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Determine organization ID
    let organizationId: string;
    if (user.role === 'ADMIN') {
      // Super admin - for now, limit to their organization for security
      if (!user.organizationId) {
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
      }
      organizationId = user.organizationId;
    } else if (user.role === 'ORGANIZATION_ADMIN') {
      if (!user.organizationId) {
        return NextResponse.json({ error: 'Organization not found' }, { status: 404 });
      }
      organizationId = user.organizationId;
    } else {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Build time range
    const timeRange = validatedParams.startDate && validatedParams.endDate ? {
      start: new Date(validatedParams.startDate),
      end: new Date(validatedParams.endDate)
    } : undefined;

    // Get security audit logs
    const securityLogs = await AuditService.getSecurityAuditLogs(
      organizationId,
      timeRange,
      validatedParams.limit
    );

    // Note: Removed audit logging here to prevent self-logging loop

    return NextResponse.json({
      success: true,
      ...securityLogs,
      timeRange,
      organizationId
    });

  } catch (error) {
    logger.error('Error fetching security audit logs:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
