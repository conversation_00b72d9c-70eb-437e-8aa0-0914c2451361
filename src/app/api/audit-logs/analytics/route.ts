import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';
import { z } from 'zod';

// Validation schema for analytics parameters
const analyticsQuerySchema = z.object({
  startDate: z.string().transform((val) => new Date(val)),
  endDate: z.string().transform((val) => new Date(val)),
  organizationId: z.string().optional(),
});

/**
 * GET /api/audit-logs/analytics
 * Get audit log analytics and insights for organization admins
 */
export async function GET(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse query parameters
    const url = new URL(req.url);
    const queryParams = Object.fromEntries(url.searchParams.entries());
    console.log('Analytics API received params:', queryParams);
    const validatedParams = analyticsQuerySchema.parse(queryParams);
    console.log('Analytics API validated params:', validatedParams);

    // Get user details
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        spvUser: {
          include: {
            projectAssignments: {
              where: { isActive: true },
              select: { projectId: true }
            }
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Check permissions - only organization admins and super admins can access analytics
    if (user.role !== 'ORGANIZATION_ADMIN' && user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    // Determine organization ID - allow null for system-wide analytics
    let organizationId: string | undefined;
    if (user.role === 'ADMIN') {
      organizationId = validatedParams.organizationId; // Can be undefined for system-wide
    } else if (user.role === 'ORGANIZATION_ADMIN') {
      organizationId = validatedParams.organizationId || undefined; // Allow system-wide for org admins too
    }

    // Get analytics data
    const timeRange = {
      start: validatedParams.startDate,
      end: validatedParams.endDate
    };

    let analytics;
    try {
      analytics = await AuditService.getAuditAnalytics(organizationId, timeRange);
    } catch (analyticsError) {
      console.error('Analytics service error:', analyticsError);
      // Fallback analytics data
      analytics = {
        totalEvents: 0,
        eventsByType: {},
        eventsBySeverity: { 'INFO': 0 },
        eventsByCategory: { 'SYSTEM': 0 },
        topUsers: [],
        errorRate: 0,
        averageResponseTime: 0,
        securityEvents: 0
      };
    }

    // Note: Removed audit logging here to prevent self-logging loop

    return NextResponse.json({
      success: true,
      data: analytics,
      timeRange,
      organizationId
    });

  } catch (error) {
    logger.error('Error fetching audit analytics:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid parameters', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
