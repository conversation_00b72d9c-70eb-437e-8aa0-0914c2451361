import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { AuditLogType } from '@prisma/client';

/**
 * POST /api/test/create-audit-logs
 * Create test audit logs for analytics testing (development only)
 */
export async function POST(req: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;
    if (!user.organizationId) {
      return NextResponse.json({ error: 'User must belong to an organization' }, { status: 400 });
    }

    // Create various test audit logs
    const testLogs = [
      {
        type: AuditLogType.LOGIN_SUCCESS,
        description: 'User logged in successfully',
        severity: 'INFO' as const,
        category: 'AUTHENTICATION' as const,
        success: true,
        duration: 150
      },
      {
        type: AuditLogType.PROJECT_CREATED,
        description: 'New renewable energy project created',
        severity: 'INFO' as const,
        category: 'DATA' as const,
        success: true,
        duration: 320
      },
      {
        type: AuditLogType.CARBON_CREDIT_LISTED,
        description: 'Carbon credits listed on marketplace',
        severity: 'INFO' as const,
        category: 'DATA' as const,
        success: true,
        duration: 280
      },
      {
        type: AuditLogType.TRANSACTION_CREATED,
        description: 'Wallet transaction completed successfully',
        severity: 'INFO' as const,
        category: 'SYSTEM' as const,
        success: true,
        duration: 450
      },
      {
        type: AuditLogType.DOCUMENT_UPLOADED,
        description: 'Project verification document uploaded',
        severity: 'INFO' as const,
        category: 'DOCUMENT' as const,
        success: true,
        duration: 1200
      },
      {
        type: AuditLogType.VERIFICATION_APPROVED,
        description: 'Project verification process completed',
        severity: 'INFO' as const,
        category: 'SPV' as const,
        success: true,
        duration: 2300
      },
      {
        type: AuditLogType.API_REQUEST,
        description: 'API request processed successfully',
        severity: 'INFO' as const,
        category: 'SYSTEM' as const,
        success: true,
        duration: 85
      },
      {
        type: AuditLogType.PERMISSION_REVOKED,
        description: 'Access denied to restricted resource',
        severity: 'WARN' as const,
        category: 'AUTHORIZATION' as const,
        success: false,
        duration: 45
      },
      {
        type: AuditLogType.LOGIN_FAILED,
        description: 'Failed login attempt - invalid credentials',
        severity: 'ERROR' as const,
        category: 'AUTHENTICATION' as const,
        success: false,
        duration: 120
      },
      {
        type: AuditLogType.API_ERROR,
        description: 'Database connection timeout occurred',
        severity: 'CRITICAL' as const,
        category: 'SYSTEM' as const,
        success: false,
        duration: 5000
      }
    ];

    const createdLogs = [];

    // Create logs with random timestamps in the last 24 hours
    for (let i = 0; i < 25; i++) {
      const logTemplate = testLogs[i % testLogs.length];
      const randomTime = new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000);
      
      const logData = {
        ...logTemplate,
        userId: user.id,
        organizationId: user.organizationId,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Test Browser/1.0',
        metadata: {
          testData: true,
          iteration: i,
          randomValue: Math.random()
        }
      };

      const auditLog = await AuditService.createAuditLog(logData);
      createdLogs.push(auditLog);
    }

    return NextResponse.json({
      success: true,
      message: `Created ${createdLogs.length} test audit logs`,
      logs: createdLogs.map(log => ({
        id: log.id,
        type: log.type,
        description: log.description,
        createdAt: log.createdAt
      }))
    });

  } catch (error) {
    console.error('Error creating test audit logs:', error);
    return NextResponse.json(
      { error: 'Failed to create test audit logs' },
      { status: 500 }
    );
  }
}
