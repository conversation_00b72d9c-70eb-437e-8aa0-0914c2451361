import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { AuditService } from '@/lib/audit/service';
import { AuditLogType } from '@prisma/client';

/**
 * GET /api/test/spv-audit-visibility
 * Test SPV audit log visibility for organization admins
 */
export async function GET(req: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;
    
    // Get user details with organization info
    const userWithOrg = await db.user.findUnique({
      where: { id: user.id },
      include: {
        organization: {
          include: {
            spvs: {
              include: {
                spvUsers: {
                  include: {
                    user: { select: { id: true, name: true, email: true } }
                  }
                }
              }
            }
          }
        },
        spvUser: {
          include: {
            spv: { select: { id: true, name: true, organizationId: true } }
          }
        }
      }
    });

    if (!userWithOrg) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const result: any = {
      currentUser: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        organizationId: user.organizationId
      },
      organizationInfo: null,
      spvInfo: null,
      auditLogCounts: {},
      recentAuditLogs: [],
      testResults: {}
    };

    // If user is organization admin
    if (user.role === 'ORGANIZATION_ADMIN' && userWithOrg.organization) {
      const org = userWithOrg.organization;
      
      result.organizationInfo = {
        id: org.id,
        name: org.name,
        spvCount: org.spvs.length,
        spvs: org.spvs.map(spv => ({
          id: spv.id,
          name: spv.name,
          userCount: spv.spvUsers.length,
          users: spv.spvUsers.map(su => ({
            id: su.user.id,
            name: su.user.name,
            email: su.user.email,
            role: su.role
          }))
        }))
      };

      // Get audit logs for the organization
      const orgAuditLogs = await db.auditLog.findMany({
        where: { organizationId: org.id },
        include: {
          user: { select: { name: true, email: true } },
          spv: { select: { name: true } }
        },
        orderBy: { createdAt: 'desc' },
        take: 20
      });

      result.auditLogCounts.total = orgAuditLogs.length;
      result.auditLogCounts.withSpv = orgAuditLogs.filter(log => log.spvId).length;
      result.auditLogCounts.withoutSpv = orgAuditLogs.filter(log => !log.spvId).length;

      result.recentAuditLogs = orgAuditLogs.map(log => ({
        id: log.id,
        type: log.type,
        description: log.description,
        createdAt: log.createdAt,
        user: log.user ? { name: log.user.name, email: log.user.email } : null,
        spv: log.spv ? { name: log.spv.name } : null,
        spvId: log.spvId,
        organizationId: log.organizationId
      }));

      // Test: Create a sample SPV audit log if there are SPV users
      const spvUsers = org.spvs.flatMap(spv => spv.spvUsers);
      if (spvUsers.length > 0) {
        const testSpvUser = spvUsers[0];
        
        try {
          const testAuditLog = await AuditService.createAuditLog({
            type: AuditLogType.DATA_ENTRY_CREATED,
            description: `Test SPV audit log created by ${testSpvUser.user.name}`,
            userId: testSpvUser.user.id,
            organizationId: org.id,
            spvId: testSpvUser.spvId,
            metadata: {
              testLog: true,
              spvRole: testSpvUser.role,
              timestamp: new Date().toISOString()
            },
            ipAddress: req.headers.get('x-forwarded-for') || 'test-ip',
            userAgent: req.headers.get('user-agent') || 'test-agent'
          });

          result.testResults.spvAuditLogCreated = {
            success: true,
            logId: testAuditLog.id,
            organizationId: testAuditLog.organizationId,
            spvId: testAuditLog.spvId
          };

          // Verify it's visible to org admin
          const verifyLog = await db.auditLog.findFirst({
            where: {
              id: testAuditLog.id,
              organizationId: org.id
            }
          });

          result.testResults.visibilityTest = {
            logExists: !!verifyLog,
            organizationMatches: verifyLog?.organizationId === org.id
          };

        } catch (error) {
          result.testResults.spvAuditLogCreated = {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
        }
      }
    }

    // If user is SPV user
    if (userWithOrg.spvUser) {
      const spvUser = userWithOrg.spvUser;
      
      result.spvInfo = {
        spvId: spvUser.spvId,
        spvName: spvUser.spv.name,
        role: spvUser.role,
        organizationId: spvUser.spv.organizationId
      };

      // Get audit logs for this SPV user
      const spvUserLogs = await db.auditLog.findMany({
        where: { 
          userId: user.id,
          organizationId: spvUser.spv.organizationId
        },
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      result.auditLogCounts.userLogs = spvUserLogs.length;
      result.recentAuditLogs = spvUserLogs.map(log => ({
        id: log.id,
        type: log.type,
        description: log.description,
        createdAt: log.createdAt,
        organizationId: log.organizationId,
        spvId: log.spvId
      }));
    }

    return NextResponse.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('Error testing SPV audit visibility:', error);
    return NextResponse.json(
      { error: 'Failed to test SPV audit visibility' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/test/spv-audit-visibility
 * Create test SPV audit logs
 */
export async function POST(req: NextRequest) {
  try {
    // Only allow in development
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = session.user;
    
    // Get SPV user info
    const spvUser = await db.sPVUser.findFirst({
      where: { userId: user.id },
      include: {
        spv: { select: { id: true, name: true, organizationId: true } }
      }
    });

    if (!spvUser) {
      return NextResponse.json({ error: 'User is not an SPV user' }, { status: 400 });
    }

    // Create multiple test audit logs
    const testLogs = [];
    const logTypes = [
      { type: AuditLogType.DATA_ENTRY_CREATED, description: 'Test data entry created' },
      { type: AuditLogType.DATA_ENTRY_UPDATED, description: 'Test data entry updated' },
      { type: AuditLogType.DOCUMENT_UPLOADED, description: 'Test document uploaded' },
      { type: AuditLogType.PROJECT_UPDATED, description: 'Test project updated' },
      { type: AuditLogType.VERIFICATION_COMPLETED, description: 'Test verification completed' }
    ];

    for (let i = 0; i < 5; i++) {
      const logType = logTypes[i % logTypes.length];
      
      const auditLog = await AuditService.createAuditLog({
        type: logType.type,
        description: `${logType.description} by SPV user ${user.name} (Test #${i + 1})`,
        userId: user.id,
        organizationId: spvUser.spv.organizationId,
        spvId: spvUser.spvId,
        metadata: {
          testLog: true,
          testNumber: i + 1,
          spvRole: spvUser.role,
          timestamp: new Date().toISOString()
        },
        ipAddress: req.headers.get('x-forwarded-for') || 'test-ip',
        userAgent: req.headers.get('user-agent') || 'test-agent'
      });

      testLogs.push({
        id: auditLog.id,
        type: auditLog.type,
        description: auditLog.description,
        organizationId: auditLog.organizationId,
        spvId: auditLog.spvId
      });
    }

    return NextResponse.json({
      success: true,
      message: `Created ${testLogs.length} test SPV audit logs`,
      data: {
        spvUser: {
          id: spvUser.id,
          spvId: spvUser.spvId,
          spvName: spvUser.spv.name,
          organizationId: spvUser.spv.organizationId
        },
        testLogs
      }
    });

  } catch (error) {
    console.error('Error creating test SPV audit logs:', error);
    return NextResponse.json(
      { error: 'Failed to create test SPV audit logs' },
      { status: 500 }
    );
  }
}
