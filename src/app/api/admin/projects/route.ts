import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for admin project creation
const adminProjectCreationSchema = z.object({
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  type: z.enum([
    "RENEWABLE_ENERGY",
    "FORESTRY",
    "METHANE_REDUCTION",
    "ENERGY_EFFICIENCY",
    "WASTE_MANAGEMENT",
    "AGRICULTURE",
    "TRANSPORTATION",
    "INDUSTRIAL",
    "OTHER"
  ]),
  status: z.enum([
    "PENDING",
    "ACTIVE",
    "COMPLETED",
    "SUSPENDED",
    "CANCELLED"
  ]).default("PENDING"),
  organizationId: z.string().min(1, "Organization is required"),
  spvId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  location: z.string().optional(),
  country: z.string().optional(),
  coordinates: z.string().optional(),
  area: z.number().positive().optional(),
  externalProjectId: z.string().optional(),
  registryId: z.string().optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  methodologyVersion: z.string().optional(),
  estimatedReductions: z.number().min(0).optional(),
  actualReductions: z.number().min(0).optional(),
  verifier: z.string().optional(),
  validator: z.string().optional(),
  budget: z.number().min(0).optional(),
  roi: z.number().optional(),
  sdgs: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  verificationStatus: z.enum(["PENDING", "IN_REVIEW", "VERIFIED", "REJECTED"]).default("PENDING"),
});

/**
 * GET /api/admin/projects
 * Get all projects with filtering and pagination (admin only)
 */
export async function GET(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access projects" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to access projects" },
        { status: 403 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const status = searchParams.get("status");
    const type = searchParams.get("type");
    const verificationStatus = searchParams.get("verificationStatus");
    const organizationId = searchParams.get("organizationId");
    const search = searchParams.get("search");

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build filter object
    const filter: any = {};

    if (status && status !== "all") {
      filter.status = status;
    }

    if (type && type !== "all") {
      filter.type = type;
    }

    if (verificationStatus && verificationStatus !== "all") {
      filter.verificationStatus = verificationStatus;
    }

    if (organizationId) {
      filter.organizationId = organizationId;
    }

    if (search) {
      filter.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
        { location: { contains: search, mode: "insensitive" } },
        { country: { contains: search, mode: "insensitive" } },
        { standard: { contains: search, mode: "insensitive" } },
        { methodology: { contains: search, mode: "insensitive" } },
      ];
    }

    // Get projects with pagination
    const projects = await db.project.findMany({
      where: filter,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            carbonCredits: true,
            unitLogs: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await db.project.count({
      where: filter,
    });

    return NextResponse.json({
      projects,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });
  } catch (error) {
    logger.error("Error fetching projects:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching projects" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/projects
 * Create a new project (admin only)
 */
export async function POST(req: Request) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to create projects" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to create projects" },
        { status: 403 }
      );
    }

    const body = await req.json();
    const validatedData = adminProjectCreationSchema.parse(body);

    // Verify organization exists
    const organization = await db.organization.findUnique({
      where: { id: validatedData.organizationId },
    });

    if (!organization) {
      return NextResponse.json(
        { error: "Organization not found" },
        { status: 400 }
      );
    }

    // If SPV is provided, verify it exists and belongs to the organization
    if (validatedData.spvId) {
      const spv = await db.sPV.findUnique({
        where: { 
          id: validatedData.spvId,
          organizationId: validatedData.organizationId,
        },
      });

      if (!spv) {
        return NextResponse.json(
          { error: "SPV not found or does not belong to the specified organization" },
          { status: 400 }
        );
      }
    }

    // Create project
    const project = await db.project.create({
      data: {
        name: validatedData.name,
        description: validatedData.description,
        type: validatedData.type,
        status: validatedData.status,
        organizationId: validatedData.organizationId,
        spvId: validatedData.spvId || null,
        startDate: validatedData.startDate ? new Date(validatedData.startDate) : null,
        endDate: validatedData.endDate ? new Date(validatedData.endDate) : null,
        location: validatedData.location,
        country: validatedData.country,
        coordinates: validatedData.coordinates,
        area: validatedData.area,
        externalProjectId: validatedData.externalProjectId,
        registryId: validatedData.registryId,
        standard: validatedData.standard,
        methodology: validatedData.methodology,
        methodologyVersion: validatedData.methodologyVersion,
        estimatedReductions: validatedData.estimatedReductions,
        actualReductions: validatedData.actualReductions,
        verifier: validatedData.verifier,
        validator: validatedData.validator,
        budget: validatedData.budget,
        roi: validatedData.roi,
        sdgs: validatedData.sdgs || [],
        tags: validatedData.tags || [],
        verificationStatus: validatedData.verificationStatus,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "PROJECT_CREATED",
        description: `Project ${project.name} created by admin`,
        user: {
          connect: { id: session.user.id },
        },
        organization: {
          connect: { id: validatedData.organizationId },
        },
        metadata: {
          createdProjectId: project.id,
          createdProjectName: project.name,
        },
      },
    });

    logger.info(`Admin ${session.user.id} created project ${project.id}`);

    return NextResponse.json({
      project,
      message: "Project created successfully",
    });
  } catch (error) {
    logger.error("Error creating project:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid project data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while creating the project" },
      { status: 500 }
    );
  }
}
