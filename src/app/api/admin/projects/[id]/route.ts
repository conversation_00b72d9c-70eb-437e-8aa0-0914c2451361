import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { auth } from "@/lib/auth";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for admin project updates
const adminProjectUpdateSchema = z.object({
  name: z.string().min(1, "Project name is required").optional(),
  description: z.string().optional(),
  type: z.enum([
    "RENEWABLE_ENERGY",
    "FORESTRY",
    "METHANE_REDUCTION",
    "ENERGY_EFFICIENCY",
    "WASTE_MANAGEMENT",
    "AGRICULTURE",
    "TRANSPORTATION",
    "INDUSTRIAL",
    "OTHER"
  ]).optional(),
  status: z.enum([
    "PENDING",
    "ACTIVE",
    "COMPLETED",
    "SUSPENDED",
    "CANCELLED"
  ]).optional(),
  organizationId: z.string().optional(),
  spvId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  location: z.string().optional(),
  country: z.string().optional(),
  coordinates: z.string().optional(),
  area: z.number().positive().optional(),
  externalProjectId: z.string().optional(),
  registryId: z.string().optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
  methodologyVersion: z.string().optional(),
  estimatedReductions: z.number().min(0).optional(),
  actualReductions: z.number().min(0).optional(),
  verifier: z.string().optional(),
  validator: z.string().optional(),
  budget: z.number().min(0).optional(),
  roi: z.number().optional(),
  sdgs: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  verificationStatus: z.enum(["PENDING", "IN_REVIEW", "VERIFIED", "REJECTED"]).optional(),
});

/**
 * GET /api/admin/projects/[id]
 * Get a specific project (admin only)
 */
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access project details" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to access project details" },
        { status: 403 }
      );
    }

    const projectId = params.id;

    const project = await db.project.findUnique({
      where: { id: projectId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
            status: true,
            verificationStatus: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
            purpose: true,
            status: true,
          },
        },
        carbonCredits: {
          select: {
            id: true,
            tokenId: true,
            amount: true,
            price: true,
            status: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 10,
        },
        unitLogs: {
          select: {
            id: true,
            unitType: true,
            amount: true,
            timestamp: true,
            source: true,
          },
          orderBy: {
            timestamp: "desc",
          },
          take: 10,
        },
        _count: {
          select: {
            carbonCredits: true,
            unitLogs: true,
          },
        },
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ project });
  } catch (error) {
    logger.error("Error fetching project:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching the project" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/projects/[id]
 * Update a project (admin only)
 */
export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update projects" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update projects" },
        { status: 403 }
      );
    }

    const projectId = params.id;
    const body = await req.json();
    const validatedData = adminProjectUpdateSchema.parse(body);

    // Check if project exists
    const existingProject = await db.project.findUnique({
      where: { id: projectId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!existingProject) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    // If organizationId is being changed, verify the new organization exists
    if (validatedData.organizationId && validatedData.organizationId !== existingProject.organizationId) {
      const organization = await db.organization.findUnique({
        where: { id: validatedData.organizationId },
      });

      if (!organization) {
        return NextResponse.json(
          { error: "Organization not found" },
          { status: 400 }
        );
      }
    }

    // If SPV is being changed, verify it exists and belongs to the organization
    if (validatedData.spvId) {
      const organizationId = validatedData.organizationId || existingProject.organizationId;
      const spv = await db.sPV.findUnique({
        where: { 
          id: validatedData.spvId,
          organizationId: organizationId,
        },
      });

      if (!spv) {
        return NextResponse.json(
          { error: "SPV not found or does not belong to the specified organization" },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};

    Object.keys(validatedData).forEach((key) => {
      const value = (validatedData as any)[key];
      if (value !== undefined) {
        if (key === "startDate" || key === "endDate") {
          updateData[key] = value ? new Date(value) : null;
        } else {
          updateData[key] = value;
        }
      }
    });

    // Update project
    const updatedProject = await db.project.update({
      where: { id: projectId },
      data: updateData,
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        spv: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "PROJECT_UPDATED",
        description: `Project ${existingProject.name} updated by admin`,
        user: {
          connect: { id: session.user.id },
        },
        organization: {
          connect: { id: updatedProject.organizationId },
        },
        metadata: {
          projectId: projectId,
          projectName: existingProject.name,
          updatedFields: Object.keys(updateData),
        },
      },
    });

    logger.info(`Admin ${session.user.id} updated project ${projectId}`);

    return NextResponse.json({
      project: updatedProject,
      message: "Project updated successfully",
    });
  } catch (error) {
    logger.error("Error updating project:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid project data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating the project" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/projects/[id]
 * Delete a project (admin only)
 */
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to delete projects" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to delete projects" },
        { status: 403 }
      );
    }

    const projectId = params.id;

    // Check if project exists
    const project = await db.project.findUnique({
      where: { id: projectId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            carbonCredits: true,
            unitLogs: true,
          },
        },
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    // Check if project has dependent records
    const hasCredits = project._count.carbonCredits > 0;
    const hasUnitLogs = project._count.unitLogs > 0;

    if (hasCredits || hasUnitLogs) {
      return NextResponse.json(
        { 
          error: "Cannot delete project with existing carbon credits or unit logs. Please remove or transfer these first.",
          details: {
            carbonCredits: project._count.carbonCredits,
            unitLogs: project._count.unitLogs,
          }
        },
        { status: 400 }
      );
    }

    // Delete project
    await db.project.delete({
      where: { id: projectId },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "PROJECT_DELETED",
        description: `Project ${project.name} deleted by admin`,
        user: {
          connect: { id: session.user.id },
        },
        organization: {
          connect: { id: project.organizationId },
        },
        metadata: {
          deletedProjectId: projectId,
          deletedProjectName: project.name,
        },
      },
    });

    logger.info(`Admin ${session.user.id} deleted project ${projectId}`);

    return NextResponse.json({
      message: "Project deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting project:", error);

    return NextResponse.json(
      { error: "An error occurred while deleting the project" },
      { status: 500 }
    );
  }
}
