import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { z } from "zod";
import { hashPassword } from "@/lib/auth/password";
import { emailService } from "@/lib/email";

// Validation schema for SPV user creation
const spvUserCreationSchema = z.object({
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required"),
  spvId: z.string().min(1, "SPV ID is required"),
  role: z.enum(["SITE_WORKER", "PROJECT_MANAGER", "SPV_ADMIN"]),
  jobTitle: z.string().optional(),
  phoneNumber: z.string().optional(),
  permissions: z.record(z.any()).optional(),
  password: z.string().min(8, "Password must be at least 8 characters"),
});

/**
 * GET /api/admin/spv-users
 * Get all SPV users (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const spvId = searchParams.get("spvId") || "";
    const role = searchParams.get("role") || "";
    const isActive = searchParams.get("isActive");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    // For organization admins, only show SPV users from their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      where.spv = {
        organizationId: session.user.organizationId,
      };
    }

    if (search) {
      where.OR = [
        { user: { name: { contains: search, mode: "insensitive" } } },
        { user: { email: { contains: search, mode: "insensitive" } } },
      ];
    }

    if (spvId) {
      where.spvId = spvId;
    }

    if (role) {
      where.role = role;
    }

    if (isActive !== null) {
      where.isActive = isActive === "true";
    }

    // Get SPV users with related data
    const [spvUsers, totalCount] = await Promise.all([
      db.sPVUser.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              jobTitle: true,
              phoneNumber: true,
              createdAt: true,
              updatedAt: true,
            },
          },
          spv: {
            select: {
              id: true,
              name: true,
              purpose: true,
              jurisdiction: true,
              status: true,
              organizationId: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                  legalName: true,
                },
              },
            },
          },
          projectAssignments: {
            where: { isActive: true },
            include: {
              project: {
                select: {
                  id: true,
                  name: true,
                  type: true,
                  status: true,
                },
              },
            },
          },
        },
        skip,
        take: limit,
        orderBy: { createdAt: "desc" },
      }),
      db.sPVUser.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("SPV users retrieved", {
      userId: session.user.id,
      count: spvUsers.length,
      totalCount,
      page,
    });

    return NextResponse.json({
      success: true,
      data: {
        spvUsers,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });

  } catch (error) {
    logger.error("Error retrieving SPV users", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/spv-users
 * Create a new SPV user (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    console.log("DEBUG: Received body for SPV user creation:", body);

    let validatedData;
    try {
      validatedData = spvUserCreationSchema.parse(body);
    } catch (error) {
      console.error("Validation error:", error);
      return NextResponse.json(
        { error: "Validation failed", details: error.errors || error.message },
        { status: 400 }
      );
    }

    // Check if SPV exists and user has access to it
    const spv = await db.sPV.findUnique({
      where: { id: validatedData.spvId },
      include: { organization: true },
    });

    if (!spv) {
      return NextResponse.json(
        { error: "SPV not found" },
        { status: 404 }
      );
    }

    // For organization admins, ensure they can only create users for their organization's SPVs
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      if (spv.organizationId !== session.user.organizationId) {
        return NextResponse.json(
          { error: "Access denied. You can only create SPV users for your organization." },
          { status: 403 }
        );
      }
    }

    // Check if user with this email already exists
    const existingUser = await db.user.findUnique({
      where: { email: validatedData.email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);

    // Create user and SPV user in a transaction
    const result = await db.$transaction(async (tx) => {
      // Create the user
      const user = await tx.user.create({
        data: {
          email: validatedData.email,
          name: validatedData.name,
          password: hashedPassword,
          role: "SPV_USER",
          jobTitle: validatedData.jobTitle,
          phoneNumber: validatedData.phoneNumber,
          organizationId: spv.organizationId,
        },
      });

      // Create the SPV user
      const spvUser = await tx.sPVUser.create({
        data: {
          userId: user.id,
          spvId: validatedData.spvId,
          role: validatedData.role,
          permissions: validatedData.permissions,
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              jobTitle: true,
              phoneNumber: true,
              createdAt: true,
            },
          },
          spv: {
            select: {
              id: true,
              name: true,
              purpose: true,
              jurisdiction: true,
              status: true,
              organization: {
                select: {
                  id: true,
                  name: true,
                  legalName: true,
                },
              },
            },
          },
        },
      });

      return spvUser;
    });

    logger.info("SPV user created", {
      createdBy: session.user.id,
      spvUserId: result.id,
      userId: result.userId,
      spvId: validatedData.spvId,
      role: validatedData.role,
    });

    // Send credentials email to the new SPV user
    try {
      await emailService.sendCredentials({
        email: validatedData.email,
        password: validatedData.password,
        name: validatedData.name,
        role: validatedData.role,
        organizationName: result.spv.organization.name || result.spv.organization.legalName,
        spvName: result.spv.name,
        loginUrl: `${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/spv/login`
      });
      logger.info(`SPV user credentials email sent to ${validatedData.email}`);
    } catch (emailError) {
      logger.error(`Failed to send SPV user credentials email to ${validatedData.email}:`, emailError);
      // Don't fail the entire operation if email fails
    }

    return NextResponse.json({
      success: true,
      data: result,
      message: "SPV user created successfully! Credentials have been sent via email.",
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error creating SPV user", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
