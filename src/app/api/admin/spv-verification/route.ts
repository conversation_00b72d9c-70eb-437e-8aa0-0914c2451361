import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";

/**
 * GET /api/admin/spv-verification
 * Get all SPVs pending verification for admin review
 */
async function getSPVsForVerificationHandler(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to access SPV verification queue",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    // Only platform admins can access verification queue
    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You don't have permission to access SPV verification queue",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {};
    
    if (status && ['PENDING_VERIFICATION', 'IN_REVIEW', 'NEEDS_MORE_INFO', 'VERIFIED', 'REJECTED'].includes(status)) {
      whereClause.verificationStatus = status;
    } else {
      // Default to showing SPVs that need attention
      whereClause.verificationStatus = {
        in: ['PENDING_VERIFICATION', 'IN_REVIEW', 'NEEDS_MORE_INFO']
      };
    }

    // Get SPVs with verification details
    const [spvs, totalCount] = await Promise.all([
      db.sPV.findMany({
        where: whereClause,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              legalName: true,
              status: true,
              verificationStatus: true,
            },
          },
          documents: {
            select: {
              id: true,
              documentType: true,
              fileName: true,
              verified: true,
              uploadedAt: true,
            },
          },
          spvUsers: {
            where: {
              role: "SPV_ADMIN",
              isActive: true,
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          _count: {
            select: {
              projects: true,
              spvUsers: true,
            },
          },
        },
        orderBy: [
          { verificationStatus: 'asc' },
          { updatedAt: 'desc' },
        ],
        skip,
        take: limit,
      }),
      db.sPV.count({ where: whereClause }),
    ]);

    // Calculate verification progress for each SPV
    const spvsWithProgress = spvs.map(spv => {
      const requiredDocumentTypes = [
        "CERTIFICATE_OF_INCORPORATION",
        "PAN_CARD",
        "GST_REGISTRATION",
        "BOARD_RESOLUTION",
        "BANK_PROOF",
        "AUTHORIZED_SIGNATORY_ID",
        "MOA_AOA",
      ];

      const uploadedDocs = spv.documents.length;
      const verifiedDocs = spv.documents.filter(doc => doc.verified).length;
      const requiredDocsUploaded = requiredDocumentTypes.filter(type => 
        spv.documents.some(doc => doc.documentType === type)
      ).length;

      const hasBasicInfo = !!(
        spv.name &&
        spv.country &&
        spv.legalStructure &&
        spv.registrationNumber &&
        spv.contactPersonName &&
        spv.contactPersonEmail &&
        spv.bankAccountNumber
      );

      return {
        ...spv,
        verificationProgress: {
          hasBasicInfo,
          documentsUploaded: uploadedDocs,
          documentsVerified: verifiedDocs,
          requiredDocumentsUploaded: requiredDocsUploaded,
          totalRequiredDocuments: requiredDocumentTypes.length,
          completionPercentage: Math.round(
            ((hasBasicInfo ? 1 : 0) + (requiredDocsUploaded / requiredDocumentTypes.length)) / 2 * 100
          ),
        },
      };
    });

    // Get verification statistics
    const verificationStats = await db.sPV.groupBy({
      by: ['verificationStatus'],
      _count: {
        id: true,
      },
    });

    const stats = verificationStats.reduce((acc, stat) => {
      acc[stat.verificationStatus] = stat._count.id;
      return acc;
    }, {} as Record<string, number>);

    return NextResponse.json({
      spvs: spvsWithProgress,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit),
      },
      statistics: {
        pending: stats.PENDING_VERIFICATION || 0,
        inReview: stats.IN_REVIEW || 0,
        needsMoreInfo: stats.NEEDS_MORE_INFO || 0,
        verified: stats.VERIFIED || 0,
        rejected: stats.REJECTED || 0,
        total: totalCount,
      },
    });
  } catch (error) {
    logger.error("Error fetching SPVs for verification:", error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while fetching SPVs for verification",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getSPVsForVerificationHandler);
