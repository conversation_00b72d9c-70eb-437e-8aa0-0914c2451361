/**
 * Admin Broker Verification API Endpoints
 * 
 * Handles broker verification status updates for admin users.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { z } from "zod";

// Schema for verification status update
const verificationUpdateSchema = z.object({
  verificationStatus: z.enum(["PENDING", "IN_REVIEW", "VERIFIED", "REJECTED"]),
  verificationNotes: z.string().optional(),
  verifiedBy: z.string().optional(),
  verifiedAt: z.string().optional(),
});

/**
 * PATCH /api/admin/brokers/[id]/verification
 * Update broker verification status (admin only)
 */
export async function PATCH(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to update broker verification" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to update broker verification" },
        { status: 403 }
      );
    }

    const brokerId = params.id;
    const body = await req.json();
    const { verificationStatus, verificationNotes } = verificationUpdateSchema.parse(body);

    // Check if broker exists
    const broker = await db.broker.findUnique({
      where: { id: brokerId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker not found" },
        { status: 404 }
      );
    }

    // Update broker verification status
    const updatedBroker = await db.broker.update({
      where: { id: brokerId },
      data: {
        verificationStatus,
        verificationNotes,
        verifiedBy: verificationStatus === "VERIFIED" ? session.user.id : null,
        verifiedAt: verificationStatus === "VERIFIED" ? new Date() : null,
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        type: "BROKER_VERIFICATION_UPDATED",
        description: `Broker ${broker.name} verification status updated to ${verificationStatus} by admin`,
        user: {
          connect: { id: session.user.id },
        },
        organization: broker.organizationId
          ? { connect: { id: broker.organizationId } }
          : undefined,
        metadata: {
          brokerId: broker.id,
          brokerName: broker.name,
          oldVerificationStatus: broker.verificationStatus,
          newVerificationStatus: verificationStatus,
          verificationNotes,
        },
      },
    });

    logger.info(`Admin ${session.user.id} updated broker ${brokerId} verification status to ${verificationStatus}`);

    return NextResponse.json({
      broker: updatedBroker,
      message: "Broker verification status updated successfully",
    });
  } catch (error) {
    logger.error("Error updating broker verification:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid verification data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "An error occurred while updating broker verification" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/admin/brokers/[id]/verification
 * Get broker verification details (admin only)
 */
export async function GET(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "You must be logged in to access broker verification details" },
        { status: 401 }
      );
    }

    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "You do not have permission to access broker verification details" },
        { status: 403 }
      );
    }

    const brokerId = params.id;

    // Get broker with verification details
    const broker = await db.broker.findUnique({
      where: { id: brokerId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        verifiedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!broker) {
      return NextResponse.json(
        { error: "Broker not found" },
        { status: 404 }
      );
    }

    // Get verification history from audit logs
    const verificationHistory = await db.auditLog.findMany({
      where: {
        type: "BROKER_VERIFICATION_UPDATED",
        metadata: {
          path: ["brokerId"],
          equals: brokerId,
        },
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
    });

    return NextResponse.json({
      broker,
      verificationHistory,
    });
  } catch (error) {
    logger.error("Error fetching broker verification details:", error);

    return NextResponse.json(
      { error: "An error occurred while fetching broker verification details" },
      { status: 500 }
    );
  }
}
