/**
 * Admin Broker Individual API Endpoints
 * 
 * Handles individual broker operations for admin users across all organizations.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { brokerUpdateSchema } from "@/lib/validation/schemas";
import { ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";

/**
 * GET /api/admin/brokers/[id]
 * Get a specific broker by ID (admin only)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "Admin access required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    const brokerId = params.id;

    // Get broker with full details
    const broker = await db.broker.findUnique({
      where: { id: brokerId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            organization: {
              select: {
                id: true,
                name: true,
                legalName: true,
              },
            },
          },
        },
        clients: {
          select: {
            id: true,
            clientName: true,
            clientType: true,
            status: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
          take: 20, // More for admin view
        },
        transactions: {
          select: {
            id: true,
            transactionType: true,
            amount: true,
            commissionAmount: true,
            status: true,
            transactionDate: true,
          },
          orderBy: {
            transactionDate: "desc",
          },
          take: 20, // More for admin view
        },
        _count: {
          select: {
            clients: true,
            transactions: true,
          },
        },
      },
    });

    if (!broker) {
      throw new ApiError(
        "Broker not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Transform broker to match expected type structure
    const transformedBroker = {
      ...broker,
      organization: (broker as any).user?.organization || null,
      user: {
        id: (broker as any).user?.id,
        name: (broker as any).user?.name,
        email: (broker as any).user?.email,
      },
    };

    return NextResponse.json({
      broker: transformedBroker,
    });
  } catch (error) {
    console.error("Error fetching admin broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/brokers/[id]
 * Update a specific broker (admin only)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "Admin access required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    const brokerId = params.id;

    // Check if broker exists
    const existingBroker = await db.broker.findUnique({
      where: { id: brokerId },
    });

    if (!existingBroker) {
      throw new ApiError(
        "Broker not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = brokerUpdateSchema.parse(body);

    // Check if email is being changed and if it conflicts with another broker
    if (validatedData.email && validatedData.email !== existingBroker.email) {
      const emailConflict = await db.broker.findFirst({
        where: {
          email: validatedData.email,
          id: { not: brokerId },
        },
      });

      if (emailConflict) {
        throw new ApiError(
          "A broker with this email already exists",
          ErrorType.VALIDATION_ERROR,
          400
        );
      }
    }

    // Update the broker
    const updatedBroker = await db.broker.update({
      where: { id: brokerId },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.legalName !== undefined && { legalName: validatedData.legalName }),
        ...(validatedData.description !== undefined && { description: validatedData.description }),
        ...(validatedData.website !== undefined && { website: validatedData.website }),
        ...(validatedData.email && { email: validatedData.email }),
        ...(validatedData.phoneNumber !== undefined && { phoneNumber: validatedData.phoneNumber }),
        ...(validatedData.address !== undefined && { address: validatedData.address }),
        ...(validatedData.city !== undefined && { city: validatedData.city }),
        ...(validatedData.state !== undefined && { state: validatedData.state }),
        ...(validatedData.postalCode !== undefined && { postalCode: validatedData.postalCode }),
        ...(validatedData.country !== undefined && { country: validatedData.country }),
        ...(validatedData.licenseNumber !== undefined && { licenseNumber: validatedData.licenseNumber }),
        ...(validatedData.licenseType !== undefined && { licenseType: validatedData.licenseType }),
        ...(validatedData.licenseIssuer !== undefined && { licenseIssuer: validatedData.licenseIssuer }),
        ...(validatedData.licenseExpiryDate !== undefined && { 
          licenseExpiryDate: validatedData.licenseExpiryDate ? new Date(validatedData.licenseExpiryDate) : null 
        }),
        ...(validatedData.registrationNumber !== undefined && { registrationNumber: validatedData.registrationNumber }),
        ...(validatedData.taxId !== undefined && { taxId: validatedData.taxId }),
        ...(validatedData.operatingModel && { operatingModel: validatedData.operatingModel }),
        ...(validatedData.commissionRate !== undefined && { commissionRate: validatedData.commissionRate }),
        ...(validatedData.minimumCommission !== undefined && { minimumCommission: validatedData.minimumCommission }),
        ...(validatedData.maximumCommission !== undefined && { maximumCommission: validatedData.maximumCommission }),
        ...(validatedData.specializations !== undefined && { specializations: validatedData.specializations }),
        ...(validatedData.servicesOffered !== undefined && { servicesOffered: validatedData.servicesOffered }),
        ...(validatedData.targetMarkets !== undefined && { targetMarkets: validatedData.targetMarkets }),
        ...(validatedData.bondAmount !== undefined && { bondAmount: validatedData.bondAmount }),
        ...(validatedData.insuranceAmount !== undefined && { insuranceAmount: validatedData.insuranceAmount }),
        ...(validatedData.creditRating !== undefined && { creditRating: validatedData.creditRating }),
        ...(validatedData.establishedDate !== undefined && { 
          establishedDate: validatedData.establishedDate ? new Date(validatedData.establishedDate) : null 
        }),
        ...(validatedData.status && { status: validatedData.status }),
        ...(validatedData.verificationStatus && { verificationStatus: validatedData.verificationStatus }),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      broker: updatedBroker,
      message: "Broker updated successfully",
    });
  } catch (error) {
    console.error("Error updating admin broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/brokers/[id]
 * Delete a specific broker (admin only)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "Admin access required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    const brokerId = params.id;

    // Check if broker exists
    const existingBroker = await db.broker.findUnique({
      where: { id: brokerId },
    });

    if (!existingBroker) {
      throw new ApiError(
        "Broker not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Delete the broker (cascade will handle related records)
    await db.broker.delete({
      where: { id: brokerId },
    });

    return NextResponse.json({
      message: "Broker deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting admin broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
