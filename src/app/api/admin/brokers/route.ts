/**
 * Admin Brokers API Endpoints
 * 
 * Handles CRUD operations for brokers across all organizations (admin only).
 * Follows the same patterns as admin SPV management.
 */

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { adminBrokerCreationSchema, brokerQuerySchema } from "@/lib/validation/schemas";
import { ApiError, ErrorType } from "@/lib/error-handler";
import { z } from "zod";

/**
 * GET /api/admin/brokers
 * Get all brokers across organizations (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "Admin access required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedParams = brokerQuerySchema.parse(queryParams);

    // Build where clause
    const where: any = {};

    if (validatedParams.search) {
      where.OR = [
        { name: { contains: validatedParams.search, mode: "insensitive" } },
        { legalName: { contains: validatedParams.search, mode: "insensitive" } },
        { email: { contains: validatedParams.search, mode: "insensitive" } },
        { user: { organization: { name: { contains: validatedParams.search, mode: "insensitive" } } } },
      ];
    }

    if (validatedParams.organizationId) {
      where.user = {
        organizationId: validatedParams.organizationId,
      };
    }

    if (validatedParams.status) {
      where.status = validatedParams.status;
    }

    if (validatedParams.operatingModel) {
      where.operatingModel = validatedParams.operatingModel;
    }

    if (validatedParams.verificationStatus) {
      where.verificationStatus = validatedParams.verificationStatus;
    }

    // Build order by clause
    const orderBy: any = {};
    if (validatedParams.sortBy === "organization") {
      orderBy.user = { organization: { name: validatedParams.sortOrder } };
    } else {
      orderBy[validatedParams.sortBy] = validatedParams.sortOrder;
    }

    // Calculate pagination
    const skip = (validatedParams.page - 1) * validatedParams.limit;

    // Get total count
    const totalCount = await db.broker.count({ where });

    // Get brokers with organization details
    const brokers = await db.broker.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            organization: {
              select: {
                id: true,
                name: true,
                legalName: true,
              },
            },
          },
        },
        _count: {
          select: {
            clients: true,
            transactions: true,
          },
        },
      },
      orderBy,
      skip,
      take: validatedParams.limit,
    });

    // Get all organizations for filtering
    const organizations = await db.organization.findMany({
      select: {
        id: true,
        name: true,
        legalName: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / validatedParams.limit);
    const hasNext = validatedParams.page < totalPages;
    const hasPrev = validatedParams.page > 1;

    // Transform brokers to match expected type structure
    const transformedBrokers = brokers.map((broker: any) => ({
      ...broker,
      organization: broker.user?.organization || null,
      user: {
        id: broker.user?.id,
        name: broker.user?.name,
        email: broker.user?.email,
      },
    }));

    return NextResponse.json({
      brokers: transformedBrokers,
      organizations,
      pagination: {
        page: validatedParams.page,
        limit: validatedParams.limit,
        totalCount,
        totalPages,
        hasNext,
        hasPrev,
      },
      filters: {
        search: validatedParams.search,
        organizationId: validatedParams.organizationId,
        status: validatedParams.status,
        operatingModel: validatedParams.operatingModel,
        verificationStatus: validatedParams.verificationStatus,
        sortBy: validatedParams.sortBy,
        sortOrder: validatedParams.sortOrder,
      },
    });
  } catch (error) {
    console.error("Error fetching admin brokers:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/brokers
 * Create a new broker (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      throw new ApiError(
        "Authentication required",
        ErrorType.AUTHENTICATION_ERROR,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "Admin access required",
        ErrorType.AUTHORIZATION_ERROR,
        403
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = adminBrokerCreationSchema.parse(body);

    // Check if broker with same email already exists
    const existingBroker = await db.broker.findUnique({
      where: { email: validatedData.email },
    });

    if (existingBroker) {
      throw new ApiError(
        "A broker with this email already exists",
        ErrorType.VALIDATION_ERROR,
        400
      );
    }

    // Validate organization exists if provided
    if (validatedData.organizationId) {
      const organization = await db.organization.findUnique({
        where: { id: validatedData.organizationId },
      });

      if (!organization) {
        throw new ApiError(
          "Organization not found",
          ErrorType.NOT_FOUND,
          404
        );
      }
    }

    // Validate user exists if provided
    if (validatedData.userId) {
      const user = await db.user.findUnique({
        where: { id: validatedData.userId },
      });

      if (!user) {
        throw new ApiError(
          "User not found",
          ErrorType.NOT_FOUND,
          404
        );
      }
    }

    // Create the broker
    const broker = await db.broker.create({
      data: {
        name: validatedData.name,
        legalName: validatedData.legalName || null,
        description: validatedData.description || null,
        website: validatedData.website || null,
        email: validatedData.email,
        phoneNumber: validatedData.phoneNumber || null,
        address: validatedData.address || null,
        city: validatedData.city || null,
        state: validatedData.state || null,
        postalCode: validatedData.postalCode || null,
        country: validatedData.country || null,
        licenseNumber: validatedData.licenseNumber || null,
        licenseType: validatedData.licenseType || null,
        licenseIssuer: validatedData.licenseIssuer || null,
        licenseExpiryDate: validatedData.licenseExpiryDate ? new Date(validatedData.licenseExpiryDate) : null,
        registrationNumber: validatedData.registrationNumber || null,
        taxId: validatedData.taxId || null,
        operatingModel: validatedData.operatingModel || "INDEPENDENT",
        commissionRate: validatedData.commissionRate || 0.02,
        minimumCommission: validatedData.minimumCommission || null,
        maximumCommission: validatedData.maximumCommission || null,
        specializations: validatedData.specializations || null,
        servicesOffered: validatedData.servicesOffered || null,
        targetMarkets: validatedData.targetMarkets || null,
        bondAmount: validatedData.bondAmount || null,
        insuranceAmount: validatedData.insuranceAmount || null,
        creditRating: validatedData.creditRating || null,
        establishedDate: validatedData.establishedDate ? new Date(validatedData.establishedDate) : null,
        organizationId: validatedData.organizationId || null,
        userId: validatedData.userId || null,
        status: "PENDING",
        verificationStatus: "PENDING",
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      broker,
      message: "Broker created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating admin broker:", error);
    
    if (error instanceof ApiError) {
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: error.statusCode }
      );
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
