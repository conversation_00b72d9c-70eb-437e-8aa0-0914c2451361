import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { spvCreationSchema } from "@/lib/validation/schemas";
import { z } from "zod";
import { hashPassword } from "@/lib/auth/password";
import { generatePassword } from "@/lib/utils/password-generator";
import { emailService } from "@/lib/email";

// Schema for admin SPV creation (includes organizationId)
const adminSPVCreationSchema = spvCreationSchema.extend({
  organizationId: z.string().min(1, "Organization ID is required"),
});

// Schema for query parameters
const spvQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? parseInt(val) : 10),
  search: z.string().optional(),
  organizationId: z.string().optional(),
  status: z.enum(["ACTIVE", "INACTIVE", "DISSOLVED"]).optional(),
  sortBy: z.enum(["name", "createdAt", "status", "organization"]).optional().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).optional().default("desc"),
});

/**
 * GET /api/admin/spv
 * Get all SPVs across organizations (admin only)
 */
async function getSPVsHandler(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to access SPVs",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN" && session.user.role !== "ORGANIZATION_ADMIN") {
      throw new ApiError(
        "You do not have permission to access SPVs",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const {
      page,
      limit,
      search,
      organizationId,
      status,
      sortBy,
      sortOrder,
    } = spvQuerySchema.parse(queryParams);

    // Build where clause
    const where: any = {};

    // For organization admins, only show SPVs from their organization
    if (session.user.role === "ORGANIZATION_ADMIN") {
      where.organizationId = session.user.organizationId;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { purpose: { contains: search, mode: "insensitive" } },
        { jurisdiction: { contains: search, mode: "insensitive" } },
        { organization: { name: { contains: search, mode: "insensitive" } } },
      ];
    }

    if (organizationId && session.user.role === "ADMIN") {
      // Only platform admins can filter by organizationId
      where.organizationId = organizationId;
    }

    if (status) {
      where.status = status;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build order by clause
    const orderBy: any = {};
    if (sortBy === "organization") {
      orderBy.organization = { name: sortOrder };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    // Get SPVs with pagination
    const [spvs, totalCount] = await Promise.all([
      db.sPV.findMany({
        where,
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              legalName: true,
              status: true,
              verificationStatus: true,
              country: true,
              industry: true,
            },
          },
          projects: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          _count: {
            select: {
              projects: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      db.sPV.count({ where }),
    ]);

    // Get organizations for filter dropdown
    const organizations = await db.organization.findMany({
      select: {
        id: true,
        name: true,
        legalName: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    const totalPages = Math.ceil(totalCount / limit);

    return NextResponse.json({
      spvs,
      organizations,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        search,
        organizationId,
        status,
        sortBy,
        sortOrder,
      },
    });
  } catch (error) {
    logger.error("Error fetching admin SPVs:", error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while fetching SPVs",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * POST /api/admin/spv
 * Create a new SPV for any organization (admin only)
 */
async function createSPVHandler(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to create an SPV",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to create SPVs for other organizations",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const body = await request.json();
    const validatedData = adminSPVCreationSchema.parse(body);

    // Verify organization exists
    const organization = await db.organization.findUnique({
      where: { id: validatedData.organizationId },
      select: { id: true, name: true },
    });

    if (!organization) {
      throw new ApiError(
        "Organization not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Generate SPV admin credentials
    const adminCredentials = generatePassword(12, true);
    const adminEmail = validatedData.adminEmail;
    const hashedPassword = await hashPassword(adminCredentials);

    // Check if user with this email already exists
    const existingUser = await db.user.findUnique({
      where: { email: adminEmail },
    });

    if (existingUser) {
      throw new ApiError(
        "User with this email already exists",
        ErrorType.VALIDATION,
        400
      );
    }

    // Create the SPV and SPV admin user in a transaction
    const result = await db.$transaction(async (tx) => {
      // Create the SPV
      const spv = await tx.sPV.create({
        data: {
          name: validatedData.name,
          purpose: validatedData.purpose || null,
          legalStructure: validatedData.legalStructure || null,
          registrationNumber: validatedData.registrationNumber || null,
          taxId: validatedData.taxId || null,
          address: validatedData.address || null,
          description: validatedData.description || null,
          establishedDate: validatedData.establishedDate ? new Date(validatedData.establishedDate) : null,
          // New required fields from PRD
          country: validatedData.country,
          legalEntityId: validatedData.legalEntityId,
          contact: validatedData.contact,
          projectCategories: [validatedData.projectCategories], // Convert string to array
          organizationId: validatedData.organizationId,
          status: "ACTIVE",
        },
        include: {
          organization: {
            select: {
              id: true,
              name: true,
              legalName: true,
            },
          },
        },
      });

      // Create the SPV admin user
      const adminUser = await tx.user.create({
        data: {
          email: adminEmail,
          name: `${validatedData.name} Administrator`,
          password: hashedPassword,
          role: "SPV_USER",
          jobTitle: "SPV Administrator",
          organizationId: validatedData.organizationId,
        },
      });

      // Create the SPV user record
      const spvUser = await tx.sPVUser.create({
        data: {
          userId: adminUser.id,
          spvId: spv.id,
          role: "SPV_ADMIN",
          isActive: true,
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              jobTitle: true,
            },
          },
        },
      });

      return { spv, spvUser, adminCredentials };
    });

    logger.info(`Admin ${session.user.id} created SPV ${result.spv.id} with admin user ${result.spvUser.id} for organization ${validatedData.organizationId}`);

    // Send credentials email to SPV admin
    try {
      await emailService.sendSPVAdminCredentialsEmail(
        adminEmail,
        result.spvUser.user.name,
        result.spv.name,
        result.spv.organization.name || result.spv.organization.legalName,
        adminEmail,
        result.adminCredentials
      );
      logger.info(`SPV admin credentials email sent to ${adminEmail}`);
    } catch (emailError) {
      logger.error(`Failed to send SPV admin credentials email to ${adminEmail}:`, emailError);
      // Don't fail the entire operation if email fails
    }

    return NextResponse.json({
      spv: result.spv,
      spvAdmin: {
        user: result.spvUser.user,
      },
      message: `SPV "${result.spv.name}" created successfully! Administrator credentials have been sent to ${adminEmail}.`,
    });
  } catch (error) {
    logger.error("Error creating admin SPV:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      throw new ApiError(
        "Invalid SPV data provided",
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while creating the SPV",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getSPVsHandler);
export const POST = withErrorHandling(createSPVHandler);
