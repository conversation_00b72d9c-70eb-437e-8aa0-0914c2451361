import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { hashPassword } from "@/lib/auth/password";
import { generatePassword } from "@/lib/utils/password-generator";

/**
 * POST /api/admin/spv/[id]/reset-password
 * Reset password for SPV admin user
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  return withErrorHandling(async () => {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError("Unauthorized", ErrorType.UNAUTHORIZED, 401);
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "Access denied. Admin role required.",
        ErrorType.FORBIDDEN,
        403
      );
    }

    const { id: spvId } = await params;

    // Find the SPV and its admin user
    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
        spvUsers: {
          where: {
            role: "SPV_ADMIN",
            isActive: true,
          },
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
          },
        },
      },
    });

    if (!spv) {
      throw new ApiError("SPV not found", ErrorType.NOT_FOUND, 404);
    }

    const spvAdmin = spv.spvUsers[0];
    if (!spvAdmin) {
      throw new ApiError("SPV admin not found", ErrorType.NOT_FOUND, 404);
    }

    // Generate new password
    const newPassword = generatePassword(12, true);
    const hashedPassword = await hashPassword(newPassword);

    // Update the user's password
    await db.user.update({
      where: { id: spvAdmin.user.id },
      data: { password: hashedPassword },
    });

    logger.info(
      `Admin ${session.user.id} reset password for SPV admin ${spvAdmin.user.id} of SPV ${spvId}`
    );

    return NextResponse.json({
      message: "Password reset successfully",
      credentials: {
        email: spvAdmin.user.email,
        password: newPassword,
        name: spvAdmin.user.name,
        spvName: spv.name,
      },
    });
  });
}
