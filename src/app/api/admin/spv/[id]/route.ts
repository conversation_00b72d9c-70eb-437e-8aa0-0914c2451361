import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { withErrorHandling, ApiError, ErrorType } from "@/lib/error-handler";
import { spvCreationSchema } from "@/lib/validation/schemas";
import { z } from "zod";

// Schema for SPV updates
const spvUpdateSchema = spvCreationSchema.partial().extend({
  status: z.enum(["ACTIVE", "INACTIVE", "PENDING", "DISSOLVED"]).optional(),
});

/**
 * GET /api/admin/spv/[id]
 * Get a specific SPV by ID (admin only)
 */
async function getSPVHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to access SPV details",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to access SPV details",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const spvId = params.id;

    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
            status: true,
            verificationStatus: true,
            country: true,
            industry: true,
            email: true,
            phoneNumber: true,
          },
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true,
            type: true,
            createdAt: true,
            estimatedReductions: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        _count: {
          select: {
            projects: true,
          },
        },
      },
    });

    if (!spv) {
      throw new ApiError(
        "SPV not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    return NextResponse.json({ spv });
  } catch (error) {
    logger.error("Error fetching SPV details:", error);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    throw new ApiError(
      "An error occurred while fetching SPV details",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * PUT /api/admin/spv/[id]
 * Update a specific SPV (admin only)
 */
async function updateSPVHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to update SPV",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to update SPVs",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const spvId = params.id;
    const body = await request.json();
    const validatedData = spvUpdateSchema.parse(body);

    // Check if SPV exists
    const existingSPV = await db.sPV.findUnique({
      where: { id: spvId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    if (!existingSPV) {
      throw new ApiError(
        "SPV not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Update the SPV
    const updatedSPV = await db.sPV.update({
      where: { id: spvId },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.purpose !== undefined && { purpose: validatedData.purpose || null }),
        ...(validatedData.jurisdiction && { jurisdiction: validatedData.jurisdiction }),
        ...(validatedData.legalStructure && { legalStructure: validatedData.legalStructure }),
        ...(validatedData.registrationNumber !== undefined && { registrationNumber: validatedData.registrationNumber || null }),
        ...(validatedData.taxId !== undefined && { taxId: validatedData.taxId || null }),
        ...(validatedData.address !== undefined && { address: validatedData.address || null }),
        ...(validatedData.description !== undefined && { description: validatedData.description || null }),
        ...(validatedData.establishedDate !== undefined && {
          establishedDate: validatedData.establishedDate ? new Date(validatedData.establishedDate) : null
        }),
        ...(validatedData.status && { status: validatedData.status }),
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
            legalName: true,
          },
        },
      },
    });

    logger.info(`Admin ${session.user.id} updated SPV ${spvId} for organization ${existingSPV.organizationId}`);

    return NextResponse.json({
      spv: updatedSPV,
      message: "SPV updated successfully",
    });
  } catch (error) {
    logger.error("Error updating SPV:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    // Handle validation errors
    if (error instanceof Error && error.name === 'ZodError') {
      throw new ApiError(
        "Invalid SPV data provided",
        ErrorType.VALIDATION,
        400
      );
    }

    throw new ApiError(
      "An error occurred while updating the SPV",
      ErrorType.INTERNAL,
      500
    );
  }
}

/**
 * DELETE /api/admin/spv/[id]
 * Delete a specific SPV (admin only)
 */
async function deleteSPVHandler(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      throw new ApiError(
        "You must be logged in to delete SPV",
        ErrorType.AUTHENTICATION,
        401
      );
    }

    if (session.user.role !== "ADMIN") {
      throw new ApiError(
        "You do not have permission to delete SPVs",
        ErrorType.AUTHORIZATION,
        403
      );
    }

    const spvId = params.id;

    // Check if SPV exists and has associated projects
    const spv = await db.sPV.findUnique({
      where: { id: spvId },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        _count: {
          select: {
            projects: true,
          },
        },
      },
    });

    if (!spv) {
      throw new ApiError(
        "SPV not found",
        ErrorType.NOT_FOUND,
        404
      );
    }

    // Check if SPV has associated projects
    if (spv._count.projects > 0) {
      throw new ApiError(
        "Cannot delete SPV with associated projects. Please reassign or delete projects first.",
        ErrorType.VALIDATION,
        400
      );
    }

    // Delete the SPV
    await db.sPV.delete({
      where: { id: spvId },
    });

    logger.info(`Admin ${session.user.id} deleted SPV ${spvId} from organization ${spv.organizationId}`);

    return NextResponse.json({
      message: "SPV deleted successfully",
    });
  } catch (error) {
    logger.error("Error deleting SPV:", error);

    if (error instanceof ApiError) {
      throw error;
    }

    throw new ApiError(
      "An error occurred while deleting the SPV",
      ErrorType.INTERNAL,
      500
    );
  }
}

export const GET = withErrorHandling(getSPVHandler);
export const PUT = withErrorHandling(updateSPVHandler);
export const DELETE = withErrorHandling(deleteSPVHandler);
