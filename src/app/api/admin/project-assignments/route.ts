import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";
import { z } from "zod";

// Validation schema for project assignment creation
const projectAssignmentSchema = z.object({
  projectId: z.string().min(1, "Project ID is required"),
  userId: z.string().min(1, "User ID is required"), // Changed from spvUserId to userId
  permissions: z.record(z.any()).optional(),
});

/**
 * GET /api/admin/project-assignments
 * Get all project assignments (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const spvUserId = searchParams.get("spvUserId") || "";
    const projectId = searchParams.get("projectId") || "";
    const isActive = searchParams.get("isActive");

    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};

    // For organization admins, only show assignments from their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      where.project = {
        organizationId: session.user.organizationId,
      };
    }

    if (search) {
      where.OR = [
        { project: { name: { contains: search, mode: "insensitive" } } },
        { spvUser: { user: { name: { contains: search, mode: "insensitive" } } } },
        { spvUser: { spv: { name: { contains: search, mode: "insensitive" } } } },
      ];
    }

    if (spvUserId) {
      where.spvUserId = spvUserId;
    }

    if (projectId) {
      where.projectId = projectId;
    }

    if (isActive !== null) {
      where.isActive = isActive === "true";
    }

    // Get project assignments with related data
    const [assignments, totalCount] = await Promise.all([
      db.projectAssignment.findMany({
        where,
        include: {
          project: {
            select: {
              id: true,
              name: true,
              description: true,
              type: true,
              status: true,
              location: true,
              country: true,
              startDate: true,
              endDate: true,
            },
          },
          spvUser: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true,
                  jobTitle: true,
                },
              },
              spv: {
                select: {
                  id: true,
                  name: true,
                  purpose: true,
                  jurisdiction: true,
                  status: true,
                },
              },
            },
          },
          assignedByUser: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        skip,
        take: limit,
        orderBy: { assignedAt: "desc" },
      }),
      db.projectAssignment.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    logger.info("Project assignments retrieved", {
      userId: session.user.id,
      count: assignments.length,
      totalCount,
      page,
    });

    return NextResponse.json({
      success: true,
      data: {
        assignments,
        pagination: {
          page,
          limit,
          totalCount,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
      },
    });

  } catch (error) {
    logger.error("Error retrieving project assignments", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/project-assignments
 * Create a new project assignment (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = projectAssignmentSchema.parse(body);

    // Check if project exists and user has access to it
    const project = await db.project.findUnique({
      where: { id: validatedData.projectId },
    });

    if (!project) {
      return NextResponse.json(
        { error: "Project not found" },
        { status: 404 }
      );
    }

    // For organization admins, ensure they can only assign their organization's projects
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      if (project.organizationId !== session.user.organizationId) {
        return NextResponse.json(
          { error: "Access denied. You can only assign projects from your organization." },
          { status: 403 }
        );
      }
    }

    // Check if user exists and user has access to it
    const user = await db.user.findUnique({
      where: { id: validatedData.userId },
      include: {
        organization: true,
        spvUser: {
          include: {
            spv: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // For organization admins, ensure they can only assign to users from their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      if (user.organizationId !== session.user.organizationId) {
        return NextResponse.json(
          { error: "Access denied. You can only assign to users from your organization." },
          { status: 403 }
        );
      }
    }

    // Check if assignment already exists
    const existingAssignment = await db.projectAssignment.findFirst({
      where: {
        projectId: validatedData.projectId,
        userId: validatedData.userId,
        isActive: true,
      },
    });

    if (existingAssignment) {
      return NextResponse.json(
        { error: "Project is already assigned to this user" },
        { status: 400 }
      );
    }

    // Create the assignment - use spvUserId if user has SPV relationship, otherwise show helpful error
    const assignment = await db.$transaction(async (tx) => {
      if (user.spvUser) {
        // User is an SPV user - create assignment through SPVUser relationship
        const newAssignment = await tx.projectAssignment.create({
          data: {
            projectId: validatedData.projectId,
            spvUserId: user.spvUser.id,
            assignedBy: session.user.id,
            permissions: validatedData.permissions,
          },
        });

        // Update the project's spvId to link it to the SPV
        await tx.project.update({
          where: { id: validatedData.projectId },
          data: { spvId: user.spvUser.spvId },
        });

        return newAssignment;
      } else {
        // User is a regular organization user - provide helpful error message
        throw new Error(
          `Cannot assign project to user "${user.name}" because they are not part of an SPV. ` +
          `Please create an SPV for the organization and add the user to it first, or select a user who is already part of an SPV.`
        );
      }
    });

    // Fetch the assignment with full details
    const assignmentWithDetails = await db.projectAssignment.findUnique({
      where: { id: assignment.id },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            description: true,
            type: true,
            status: true,
            location: true,
            country: true,
          },
        },
        spvUser: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
                jobTitle: true,
              },
            },
            spv: {
              select: {
                id: true,
                name: true,
                purpose: true,
                jurisdiction: true,
                status: true,
              },
            },
          },
        },
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    logger.info("Project assignment created", {
      assignedBy: session.user.id,
      assignmentId: assignment.id,
      projectId: validatedData.projectId,
      spvUserId: validatedData.spvUserId,
      spvId: spvUser.spvId,
    });

    return NextResponse.json({
      success: true,
      data: assignmentWithDetails,
      message: "Project assigned successfully",
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    logger.error("Error creating project assignment", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
