import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/authorization";

interface RouteParams {
  params: {
    id: string;
  };
}

/**
 * GET /api/admin/project-assignments/[id]
 * Get a specific project assignment (admin only)
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    const assignmentId = params.id;

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const assignment = await db.projectAssignment.findUnique({
      where: { id: assignmentId },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            description: true,
            type: true,
            status: true,
            location: true,
            country: true,
            startDate: true,
            endDate: true,
            organizationId: true,
          },
        },
        spvUser: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
                jobTitle: true,
              },
            },
            spv: {
              select: {
                id: true,
                name: true,
                purpose: true,
                jurisdiction: true,
                status: true,
                organizationId: true,
              },
            },
          },
        },
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "Project assignment not found" },
        { status: 404 }
      );
    }

    // For organization admins, ensure they can only view assignments from their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      if (assignment.project.organizationId !== session.user.organizationId) {
        return NextResponse.json(
          { error: "Access denied. You can only view assignments from your organization." },
          { status: 403 }
        );
      }
    }

    logger.info("Project assignment retrieved", {
      userId: session.user.id,
      assignmentId,
    });

    return NextResponse.json({
      success: true,
      data: assignment,
    });

  } catch (error) {
    logger.error("Error retrieving project assignment", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/project-assignments/[id]
 * Update a project assignment (admin only)
 */
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    const assignmentId = params.id;

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { isActive, permissions } = body;

    // Get the assignment first to check permissions
    const assignment = await db.projectAssignment.findUnique({
      where: { id: assignmentId },
      include: {
        project: { select: { organizationId: true } },
        spvUser: {
          include: {
            spv: { select: { organizationId: true } },
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "Project assignment not found" },
        { status: 404 }
      );
    }

    // For organization admins, ensure they can only update assignments from their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      if (assignment.project.organizationId !== session.user.organizationId) {
        return NextResponse.json(
          { error: "Access denied. You can only update assignments from your organization." },
          { status: 403 }
        );
      }
    }

    // Update the assignment
    const updatedAssignment = await db.projectAssignment.update({
      where: { id: assignmentId },
      data: {
        isActive: isActive !== undefined ? isActive : undefined,
        permissions: permissions !== undefined ? permissions : undefined,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            description: true,
            type: true,
            status: true,
            location: true,
            country: true,
          },
        },
        spvUser: {
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
                jobTitle: true,
              },
            },
            spv: {
              select: {
                id: true,
                name: true,
                purpose: true,
                jurisdiction: true,
                status: true,
              },
            },
          },
        },
        assignedByUser: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    logger.info("Project assignment updated", {
      updatedBy: session.user.id,
      assignmentId,
      changes: { isActive, permissions: !!permissions },
    });

    return NextResponse.json({
      success: true,
      data: updatedAssignment,
      message: "Project assignment updated successfully",
    });

  } catch (error) {
    logger.error("Error updating project assignment", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/project-assignments/[id]
 * Delete a project assignment (admin only)
 */
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    const assignmentId = params.id;

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
      return NextResponse.json(
        { error: "Access denied. Admin role required." },
        { status: 403 }
      );
    }

    // Get the assignment first to check permissions
    const assignment = await db.projectAssignment.findUnique({
      where: { id: assignmentId },
      include: {
        project: { select: { organizationId: true } },
        spvUser: {
          include: {
            spv: { select: { organizationId: true } },
          },
        },
      },
    });

    if (!assignment) {
      return NextResponse.json(
        { error: "Project assignment not found" },
        { status: 404 }
      );
    }

    // For organization admins, ensure they can only delete assignments from their organization
    if (session.user.role === UserRole.ORGANIZATION_ADMIN) {
      if (assignment.project.organizationId !== session.user.organizationId) {
        return NextResponse.json(
          { error: "Access denied. You can only delete assignments from your organization." },
          { status: 403 }
        );
      }
    }

    // Soft delete by setting isActive to false
    await db.projectAssignment.update({
      where: { id: assignmentId },
      data: { isActive: false },
    });

    logger.info("Project assignment deleted", {
      deletedBy: session.user.id,
      assignmentId,
    });

    return NextResponse.json({
      success: true,
      message: "Project assignment removed successfully",
    });

  } catch (error) {
    logger.error("Error deleting project assignment", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
