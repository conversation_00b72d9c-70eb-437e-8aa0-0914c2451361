"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Building2, 
  Handshake, 
  Users, 
  TrendingUp, 
  Shield, 
  Globe,
  ArrowRight,
  CheckCircle,
  Loader2
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "@/components/ui/use-toast";

export default function RoleSelectionPage() {
  const router = useRouter();
  const { data: session, update } = useSession();
  const [selectedRole, setSelectedRole] = useState<"CLIENT" | "BROKER" | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleRoleSelection = async (role: "CLIENT" | "BROKER") => {
    if (isLoading) return;
    
    setIsLoading(true);
    try {
      // Update user role in the database
      const response = await fetch("/api/user/role", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ role }),
      });

      if (!response.ok) {
        throw new Error("Failed to update user role");
      }

      // Update the session
      await update();

      // Show success message
      toast({
        title: "Role Selected",
        description: `You've successfully selected to proceed as a ${role.toLowerCase()}.`,
      });

      // Redirect based on role
      if (role === "BROKER") {
        router.push("/broker/onboarding");
      } else {
        router.push("/onboarding");
      }
    } catch (error) {
      console.error("Error updating role:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update your role. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const roleOptions = [
    {
      id: "CLIENT" as const,
      title: "Organization Client",
      description: "I represent an organization looking to buy, sell, or manage carbon credits",
      icon: Building2,
      features: [
        "Create and manage organization profile",
        "Purchase and retire carbon credits",
        "Track carbon footprint and offsets",
        "Team collaboration tools",
        "Compliance reporting"
      ],
      color: "from-blue-500 to-purple-600",
      badge: "Most Popular"
    },
    {
      id: "BROKER" as const,
      title: "Carbon Credit Broker",
      description: "I'm a licensed broker facilitating carbon credit transactions",
      icon: Handshake,
      features: [
        "Manage client portfolios",
        "Facilitate carbon credit transactions",
        "Commission tracking and reporting",
        "Client relationship management",
        "Market insights and analytics"
      ],
      color: "from-green-500 to-teal-600",
      badge: "Professional"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4">
      <div className="w-full max-w-6xl">
        {/* Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Welcome to CarbonX, {session?.user?.name}!
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            To provide you with the best experience, please select how you'd like to use our platform.
          </p>
        </motion.div>

        {/* Role Selection Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-8">
          {roleOptions.map((option, index) => (
            <motion.div
              key={option.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
            >
              <Card 
                className={`relative overflow-hidden cursor-pointer transition-all duration-300 hover:shadow-xl ${
                  selectedRole === option.id 
                    ? 'ring-2 ring-blue-500 shadow-lg transform scale-105' 
                    : 'hover:shadow-lg hover:transform hover:scale-102'
                }`}
                onClick={() => setSelectedRole(option.id)}
              >
                {/* Background Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${option.color} opacity-5`} />
                
                {/* Badge */}
                <div className="absolute top-4 right-4">
                  <Badge variant="secondary" className="bg-white/80 backdrop-blur-sm">
                    {option.badge}
                  </Badge>
                </div>

                <CardHeader className="relative">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className={`w-16 h-16 rounded-xl bg-gradient-to-br ${option.color} flex items-center justify-center shadow-lg`}>
                      <option.icon className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl font-bold text-gray-900">
                        {option.title}
                      </CardTitle>
                    </div>
                  </div>
                  <CardDescription className="text-gray-600 text-lg">
                    {option.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="relative">
                  <div className="space-y-3">
                    <h4 className="font-semibold text-gray-900 mb-3">What you'll get:</h4>
                    {option.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                        <span className="text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    className={`w-full mt-6 bg-gradient-to-r ${option.color} hover:opacity-90 text-white font-semibold py-3 transition-all duration-300 ${
                      selectedRole === option.id ? 'shadow-lg' : ''
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      handleRoleSelection(option.id);
                    }}
                    disabled={isLoading}
                  >
                    {isLoading && selectedRole === option.id ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Setting up...
                      </>
                    ) : (
                      <>
                        Continue as {option.title}
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </>
                    )}
                  </Button>
                </CardContent>

                {/* Selection Indicator */}
                {selectedRole === option.id && (
                  <motion.div
                    className="absolute inset-0 border-2 border-blue-500 rounded-lg pointer-events-none"
                    initial={{ scale: 0.95, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.2 }}
                  />
                )}
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Additional Info */}
        <motion.div
          className="text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <p className="text-gray-500 text-sm">
            Don't worry, you can always change your role later in your account settings.
          </p>
        </motion.div>
      </div>
    </div>
  );
}
