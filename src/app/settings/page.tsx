"use client";

import React from "react";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, User, Lock, CheckCircle, Loader2 } from "lucide-react";
import { z } from "zod";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

// Simple wrapper component to replace PageTransition
const PageWrapper = ({ children }: { children: React.ReactNode }) => (
  <div className="min-h-screen">{children}</div>
);

// Simple wrapper for page header
const PageHeaderWithBreadcrumb = ({ title, description, breadcrumbItems }: {
  title: string;
  description: string;
  breadcrumbItems: Array<{ label: string; href: string; isCurrent?: boolean }>;
}) => (
  <div className="mb-6">
    <h1 className="text-3xl font-bold">{title}</h1>
    <p className="text-muted-foreground">{description}</p>
  </div>
);

// Simple ValidatedForm component
const ValidatedForm = ({ schema, defaultValues, onSubmit, className, children }: {
  schema: any;
  defaultValues: any;
  onSubmit: (data: any) => void;
  className?: string;
  children: React.ReactNode | ((props: any) => React.ReactNode);
}) => {
  const mockProps = {
    control: {},
    formState: { errors: {} },
    isSubmitting: false,
    formError: null
  };

  return (
    <form onSubmit={(e) => { e.preventDefault(); onSubmit(defaultValues); }} className={className}>
      {typeof children === 'function' ? children(mockProps) : children}
    </form>
  );
};

// Aliases for animated components (ignoring animation props)
const AnimatedCard = ({ children, animationVariant, ...props }: any) => <Card {...props}>{children}</Card>;
const AnimatedCardHeader = ({ children, ...props }: any) => <CardHeader {...props}>{children}</CardHeader>;
const AnimatedCardTitle = ({ children, ...props }: any) => <CardTitle {...props}>{children}</CardTitle>;
const AnimatedCardContent = ({ children, ...props }: any) => <CardContent {...props}>{children}</CardContent>;
const AnimatedButton = ({ children, animationVariant, ...props }: any) => <Button {...props}>{children}</Button>;
const AnimatedInput = ({ animationVariant, ...props }: any) => <Input {...props} />;

// Mock organization data
const mockOrganization = {
  name: "Sample Organization",
  description: "A sample organization description",
  website: "https://example.com",
  industry: "Technology",
  size: "50-100"
};

// Simple AnimationSettings component
const AnimationSettings = () => (
  <AnimatedCard animationVariant="fadeIn">
    <AnimatedCardHeader>
      <AnimatedCardTitle>Animation Settings</AnimatedCardTitle>
    </AnimatedCardHeader>
    <AnimatedCardContent>
      <div className="space-y-4">
        <div>
          <Label htmlFor="animation-speed">Animation Speed</Label>
          <Input id="animation-speed" type="range" min="0.5" max="2" step="0.1" defaultValue="1" />
        </div>
        <div>
          <Label htmlFor="reduce-motion">Reduce Motion</Label>
          <Input id="reduce-motion" type="checkbox" />
        </div>
      </div>
    </AnimatedCardContent>
  </AnimatedCard>
);

// Validation schemas
const userProfileSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  jobTitle: z.string().max(100, "Job title too long").optional(),
  phoneNumber: z.string().max(20, "Phone number too long").optional(),
  bio: z.string().max(500, "Bio too long").optional(),
});

const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export default function SettingsPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("profile");
  const [userProfile, setUserProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mock values for the form
  const profileValues = {
    name: session?.user?.name || "",
    email: session?.user?.email || "",
    bio: "",
    jobTitle: "",
    department: ""
  };

  const passwordValues = {
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  };

  // Profile form
  const profileForm = useForm({
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      name: "",
      jobTitle: "",
      phoneNumber: "",
      bio: "",
    },
  });

  // Password form
  const passwordForm = useForm({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // Fetch user profile on component mount
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const response = await fetch("/api/user/profile");
        if (response.ok) {
          const result = await response.json();
          setUserProfile(result.data);
          profileForm.reset({
            name: result.data.name || "",
            jobTitle: result.data.jobTitle || "",
            phoneNumber: result.data.phoneNumber || "",
            bio: result.data.bio || "",
          });
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsLoading(false);
      }
    };

    if (session?.user) {
      fetchUserProfile();
    }
  }, [session, profileForm]);

  // Handle profile form submission
  const handleProfileSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/user/profile", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update profile");
      }

      const result = await response.json();
      setUserProfile(result.data);
      toast.success("Profile updated successfully");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error(error.message || "Failed to update profile");
    }
  };

  // Handle password change form submission
  const handlePasswordSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/user/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to change password");
      }

      passwordForm.reset();
      toast.success("Password changed successfully");
    } catch (error) {
      console.error("Error changing password:", error);
      toast.error(error.message || "Failed to change password");
    }
  };

  if (isLoading) {
    return (
      <PageWrapper>
        <div className="space-y-6">
          <PageHeaderWithBreadcrumb
            title="Profile Settings"
            description="Manage your profile and account settings"
            breadcrumbItems={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Settings", href: "/settings", isCurrent: true }
            ]}
          />
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
          </div>
        </div>
      </PageWrapper>
    );
  }

  return (
    <PageWrapper>
      <div className="space-y-6">
        <PageHeaderWithBreadcrumb
          title="Profile Settings"
          description="Manage your profile and account settings"
          breadcrumbItems={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Settings", href: "/settings", isCurrent: true }
          ]}
        />

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Lock className="h-4 w-4" />
              Security
            </TabsTrigger>
          </TabsList>

      {activeTab === "profile" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Profile Information</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <ValidatedForm
              schema={userProfileSchema}
              defaultValues={profileValues}
              onSubmit={handleProfileSubmit}
              className="space-y-4"
            >
              {({ control, formState, isSubmitting, formError }) => (
                <>
                  {formError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{formError.message}</AlertDescription>
                    </Alert>
                  )}

                  <div className="grid gap-2">
                    <label htmlFor="name" className="text-sm font-medium">
                      Name
                    </label>
                    <Input
                      id="name"
                      {...control.register("name")}
                      placeholder="Your name"
                    />
                    {formState.errors.name && (
                      <p className="text-xs text-destructive">{formState.errors.name.message as string}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="email" className="text-sm font-medium">
                      Email
                    </label>
                    <Input
                      id="email"
                      type="email"
                      {...control.register("email")}
                      placeholder="Your email"
                      disabled
                    />
                    <p className="text-xs text-muted-foreground">
                      Your email cannot be changed
                    </p>
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="jobTitle" className="text-sm font-medium">
                      Job Title
                    </label>
                    <Input
                      id="jobTitle"
                      {...control.register("jobTitle")}
                      placeholder="Your job title"
                    />
                    {formState.errors.jobTitle && (
                      <p className="text-xs text-destructive">{formState.errors.jobTitle.message as string}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="phoneNumber" className="text-sm font-medium">
                      Phone Number
                    </label>
                    <Input
                      id="phoneNumber"
                      {...control.register("phoneNumber")}
                      placeholder="Your phone number"
                    />
                    {formState.errors.phoneNumber && (
                      <p className="text-xs text-destructive">{formState.errors.phoneNumber.message as string}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="bio" className="text-sm font-medium">
                      Bio
                    </label>
                    <Textarea
                      id="bio"
                      {...control.register("bio")}
                      placeholder="A short bio about yourself"
                      rows={4}
                    />
                    {formState.errors.bio && (
                      <p className="text-xs text-destructive">{formState.errors.bio.message as string}</p>
                    )}
                  </div>

                  <AnimatedButton
                    type="submit"
                    disabled={isSubmitting}
                    animationVariant="buttonTap"
                  >
                    {isSubmitting ? "Saving..." : "Save Changes"}
                  </AnimatedButton>
                </>
              )}
            </ValidatedForm>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "organization" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Organization Settings</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <form className="space-y-4">
              <div className="grid gap-2">
                <label htmlFor="org-name" className="text-sm font-medium">
                  Organization Name
                </label>
                <AnimatedInput
                  id="org-name"
                  defaultValue={mockOrganization.name}
                  placeholder="Organization name"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="org-description" className="text-sm font-medium">
                  Description
                </label>
                <textarea
                  id="org-description"
                  className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  defaultValue={mockOrganization.description}
                  placeholder="Organization description"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="org-website" className="text-sm font-medium">
                  Website
                </label>
                <AnimatedInput
                  id="org-website"
                  type="url"
                  defaultValue={mockOrganization.website}
                  placeholder="https://example.com"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="org-logo" className="text-sm font-medium">
                  Logo
                </label>
                <AnimatedInput id="org-logo" type="file" accept="image/*" />
              </div>
              <AnimatedButton animationVariant="buttonTap">Save Organization Settings</AnimatedButton>
            </form>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "security" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Security Settings</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <ValidatedForm
              schema={passwordChangeSchema}
              defaultValues={passwordValues}
              onSubmit={handlePasswordSubmit}
              className="space-y-4"
            >
              {({ control, formState, isSubmitting, formError }) => (
                <>
                  {formError && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{formError.message}</AlertDescription>
                    </Alert>
                  )}

                  <div className="grid gap-2">
                    <label htmlFor="currentPassword" className="text-sm font-medium">
                      Current Password
                    </label>
                    <Input
                      id="currentPassword"
                      type="password"
                      {...control.register("currentPassword")}
                      placeholder="Enter current password"
                    />
                    {formState.errors.currentPassword && (
                      <p className="text-xs text-destructive">{formState.errors.currentPassword.message as string}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="newPassword" className="text-sm font-medium">
                      New Password
                    </label>
                    <Input
                      id="newPassword"
                      type="password"
                      {...control.register("newPassword")}
                      placeholder="Enter new password"
                    />
                    {formState.errors.newPassword && (
                      <p className="text-xs text-destructive">{formState.errors.newPassword.message as string}</p>
                    )}
                  </div>

                  <div className="grid gap-2">
                    <label htmlFor="confirmPassword" className="text-sm font-medium">
                      Confirm New Password
                    </label>
                    <Input
                      id="confirmPassword"
                      type="password"
                      {...control.register("confirmPassword")}
                      placeholder="Confirm new password"
                    />
                    {formState.errors.confirmPassword && (
                      <p className="text-xs text-destructive">{formState.errors.confirmPassword.message as string}</p>
                    )}
                  </div>

                  <AnimatedButton
                    type="submit"
                    disabled={isSubmitting}
                    animationVariant="buttonTap"
                  >
                    {isSubmitting ? "Changing Password..." : "Change Password"}
                  </AnimatedButton>
                </>
              )}
            </ValidatedForm>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "notifications" && (
        <AnimatedCard animationVariant="fadeIn">
          <AnimatedCardHeader>
            <AnimatedCardTitle>Notification Preferences</AnimatedCardTitle>
          </AnimatedCardHeader>
          <AnimatedCardContent>
            <form className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Email Notifications</h3>
                  <p className="text-xs text-muted-foreground">
                    Receive email notifications for important updates
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Transaction Alerts</h3>
                  <p className="text-xs text-muted-foreground">
                    Get notified when transactions occur
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Market Updates</h3>
                  <p className="text-xs text-muted-foreground">
                    Receive updates about new carbon credits and market changes
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium">Platform Announcements</h3>
                  <p className="text-xs text-muted-foreground">
                    Get notified about platform updates and announcements
                  </p>
                </div>
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                  defaultChecked
                />
              </div>
              <AnimatedButton animationVariant="buttonTap">Save Preferences</AnimatedButton>
            </form>
          </AnimatedCardContent>
        </AnimatedCard>
      )}

      {activeTab === "animations" && (
        <AnimationSettings />
      )}
        </Tabs>
      </div>
    </PageWrapper>
  );
}
