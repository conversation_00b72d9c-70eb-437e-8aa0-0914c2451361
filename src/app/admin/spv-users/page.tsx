import { Metada<PERSON> } from "next";
import { AdminGuard } from "@/components/auth/auth-guard";
import SPVUsersManagementClient from "./spv-users-management-client";

export const metadata: Metadata = {
  title: "SPV Users Management - Admin",
  description: "Manage SPV users, roles, and permissions",
};

export default async function SPVUsersManagementPage() {
  return (
    <AdminGuard>
      <div className="container mx-auto py-6">
        <SPVUsersManagementClient />
      </div>
    </AdminGuard>
  );
}
