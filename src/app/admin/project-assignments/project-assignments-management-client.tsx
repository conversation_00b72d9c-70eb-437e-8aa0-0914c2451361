"use client";

import { useState, useEffect } from "react";
import { Plus, Search, Filter, Edit, Trash2, Eye, UserPlus, FolderOpen } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ProjectAssignmentCreateForm } from "@/components/admin/project-assignment-create-form";
import { toast } from "sonner";

interface ProjectAssignment {
  id: string;
  assignedAt: string;
  isActive: boolean;
  permissions: Record<string, any> | null;
  project: {
    id: string;
    name: string;
    description: string | null;
    type: string;
    status: string;
    location: string | null;
    country: string | null;
  };
  spvUser: {
    id: string;
    role: string;
    user: {
      id: string;
      email: string;
      name: string;
      jobTitle?: string;
    };
    spv: {
      id: string;
      name: string;
      purpose: string | null;
      jurisdiction: string | null;
      status: string;
    };
  };
  assignedByUser: {
    id: string;
    name: string;
    email: string;
  };
}

export default function ProjectAssignmentsManagementClient() {
  const [assignments, setAssignments] = useState<ProjectAssignment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [projectFilter, setProjectFilter] = useState<string>("all");
  const [spvFilter, setSpvFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchAssignments();
  }, [currentPage, searchTerm, projectFilter, spvFilter, statusFilter]);

  const fetchAssignments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(projectFilter && projectFilter !== "all" && { projectId: projectFilter }),
        ...(spvFilter && spvFilter !== "all" && { spvId: spvFilter }),
        ...(statusFilter && statusFilter !== "all" && { isActive: statusFilter }),
      });

      const response = await fetch(`/api/admin/project-assignments?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAssignments(data.data.assignments || []);
        setTotalPages(data.data.pagination.totalPages || 1);
      } else {
        setError("Failed to fetch project assignments");
      }
    } catch (err) {
      setError("Error fetching project assignments");
      console.error("Project assignments fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAssignment = async (assignmentData: any) => {
    try {
      const response = await fetch("/api/admin/project-assignments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(assignmentData),
      });

      if (response.ok) {
        toast.success("Project assigned successfully");
        setIsCreateDialogOpen(false);
        fetchAssignments();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to assign project");
      }
    } catch (error) {
      toast.error("Error assigning project");
      console.error("Create assignment error:", error);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'SITE_WORKER':
        return 'bg-blue-100 text-blue-800';
      case 'PROJECT_MANAGER':
        return 'bg-green-100 text-green-800';
      case 'SPV_ADMIN':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'ON_HOLD':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading && assignments.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-10 w-10 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-48" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Project Assignments</h1>
          <p className="text-muted-foreground">
            Assign projects to SPV users and manage project access
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Assign Project
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search assignments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                autoComplete="off"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Assignments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Project Assignments</CardTitle>
          <CardDescription>
            {assignments.length} assignment{assignments.length !== 1 ? 's' : ''} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {assignments.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Project</TableHead>
                    <TableHead>SPV User</TableHead>
                    <TableHead>SPV</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Assigned</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {assignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{assignment.project.name}</div>
                          <div className="text-sm text-muted-foreground">{assignment.project.type}</div>
                          {assignment.project.location && (
                            <div className="text-xs text-muted-foreground">{assignment.project.location}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{assignment.spvUser.user.name}</div>
                          <div className="text-sm text-muted-foreground">{assignment.spvUser.user.email}</div>
                          {assignment.spvUser.user.jobTitle && (
                            <div className="text-xs text-muted-foreground">{assignment.spvUser.user.jobTitle}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{assignment.spvUser.spv.name}</div>
                          <div className="text-sm text-muted-foreground">{assignment.spvUser.spv.jurisdiction}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRoleColor(assignment.spvUser.role)}>
                          {assignment.spvUser.role.replace('_', ' ')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(assignment.project.status)}>
                          {assignment.project.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(assignment.assignedAt).toLocaleDateString()}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          by {assignment.assignedByUser.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <FolderOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No project assignments found</h3>
              <p className="text-muted-foreground mb-4">
                Assign projects to SPV users to get started
              </p>
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Assign Project
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Create Assignment Dialog */}
      <ProjectAssignmentCreateForm
        isOpen={isCreateDialogOpen}
        onClose={() => setIsCreateDialogOpen(false)}
        onSuccess={handleCreateAssignment}
      />
    </div>
  );
}
