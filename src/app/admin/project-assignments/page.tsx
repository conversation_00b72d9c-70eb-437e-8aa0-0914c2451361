import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { UserRole } from "@/lib/authorization";
import ProjectAssignmentsManagementClient from "./project-assignments-management-client";

export const metadata: Metadata = {
  title: "Project Assignments - Admin",
  description: "Manage project assignments to SPV users",
};

export default async function ProjectAssignmentsManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Only admins and organization admins can access this page
  if (session.user.role !== UserRole.ADMIN && session.user.role !== UserRole.ORGANIZATION_ADMIN) {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <ProjectAssignmentsManagementClient />
    </div>
  );
}
