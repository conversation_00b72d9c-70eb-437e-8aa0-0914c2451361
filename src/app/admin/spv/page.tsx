import { Metadata } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import AdminSPVManagementClient from "./admin-spv-management-client";

export const metadata: Metadata = {
  title: "Admin SPV Management",
  description: "Manage Special Purpose Vehicles across all organizations",
};

export default async function AdminSPVManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  if (session.user.role !== "ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <AdminSPVManagementClient />
    </div>
  );
}
