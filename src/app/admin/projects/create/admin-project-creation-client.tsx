"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import dynamic from "next/dynamic";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { ArrowLeft, Building2, Folder, MapPin, Calendar, DollarSign, Map, Search, X, Sun, Wind, Zap, Trees, Leaf, Recycle, Factory, Truck, Building, Droplets, Flame, Lightbulb, Wheat, Car } from "lucide-react";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { countries } from "@/lib/countries";

// Dynamically import the map component to avoid SSR issues
const InteractiveMap = dynamic(() => import("@/components/ui/interactive-map"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-100 rounded-md flex items-center justify-center">
      <div className="text-center">
        <Map className="h-8 w-8 text-gray-400 mx-auto mb-2" />
        <p className="text-sm text-gray-500">Loading map...</p>
      </div>
    </div>
  ),
});

// Schema for admin project creation
const adminProjectCreationSchema = z.object({
  organizationId: z.string().min(1, "Organization is required"),
  spvId: z.string().optional(),
  name: z.string().min(1, "Project name is required"),
  description: z.string().optional(),
  type: z.enum([
    "RENEWABLE_ENERGY",
    "FORESTRY",
    "METHANE_REDUCTION",
    "ENERGY_EFFICIENCY",
    "WASTE_MANAGEMENT",
    "AGRICULTURE",
    "TRANSPORTATION",
    "INDUSTRIAL",
    "OTHER"
  ]),
  subtype: z.string().min(1, "Project subtype is required"),
  startDate: z.string().optional(),
  commissioningDate: z.string().optional(),
  country: z.string().min(1, "Country is required"),
  region: z.string().optional(),
  coordinates: z.object({
    latitude: z.string().optional(),
    longitude: z.string().optional(),
  }),
  estimatedReductions: z.number().min(0).optional(),
  budget: z.number().min(0).optional(),
  standard: z.string().optional(),
  methodology: z.string().optional(),
});

type AdminProjectFormValues = z.infer<typeof adminProjectCreationSchema>;

interface Organization {
  id: string;
  name: string;
  status: string;
}

interface SPV {
  id: string;
  name: string;
  organizationId: string;
}

const projectTypes = [
  { value: "RENEWABLE_ENERGY", label: "Renewable Energy" },
  { value: "FORESTRY", label: "Forestry" },
  { value: "METHANE_REDUCTION", label: "Methane Reduction" },
  { value: "ENERGY_EFFICIENCY", label: "Energy Efficiency" },
  { value: "WASTE_MANAGEMENT", label: "Waste Management" },
  { value: "AGRICULTURE", label: "Agriculture" },
  { value: "TRANSPORTATION", label: "Transportation" },
  { value: "INDUSTRIAL", label: "Industrial" },
  { value: "OTHER", label: "Other" },
];

// Define project sub-types for each main type
const PROJECT_SUB_TYPES = {
  RENEWABLE_ENERGY: [
    { id: "SOLAR_PV", name: "Solar Energy", icon: <Sun className="h-5 w-5" /> },
    { id: "WIND_POWER", name: "Wind Energy", icon: <Wind className="h-5 w-5" /> },
    { id: "HYBRID_RENEWABLE", name: "Hybrid Energy", icon: <Zap className="h-5 w-5" /> },
  ],
  FORESTRY: [
    { id: "AFFORESTATION", name: "Afforestation", icon: <Trees className="h-5 w-5" /> },
    { id: "REFORESTATION", name: "Reforestation", icon: <Leaf className="h-5 w-5" /> },
    { id: "FOREST_CONSERVATION", name: "Forest Conservation", icon: <Trees className="h-5 w-5" /> },
  ],
  METHANE_REDUCTION: [
    { id: "LANDFILL_GAS", name: "Landfill Gas Capture", icon: <Recycle className="h-5 w-5" /> },
    { id: "LIVESTOCK_METHANE", name: "Livestock Methane", icon: <Wheat className="h-5 w-5" /> },
    { id: "BIOGAS_CAPTURE", name: "Biogas Capture", icon: <Flame className="h-5 w-5" /> },
  ],
  ENERGY_EFFICIENCY: [
    { id: "BUILDING_EFFICIENCY", name: "Building Efficiency", icon: <Building className="h-5 w-5" /> },
    { id: "INDUSTRIAL_EFFICIENCY", name: "Industrial Efficiency", icon: <Factory className="h-5 w-5" /> },
    { id: "SMART_GRID", name: "Smart Grid", icon: <Lightbulb className="h-5 w-5" /> },
  ],
  WASTE_MANAGEMENT: [
    { id: "WASTE_TO_ENERGY", name: "Waste-to-Energy", icon: <Recycle className="h-5 w-5" /> },
    { id: "RECYCLING", name: "Recycling Programs", icon: <Recycle className="h-5 w-5" /> },
    { id: "WASTE_REDUCTION", name: "Waste Reduction", icon: <Recycle className="h-5 w-5" /> },
  ],
  AGRICULTURE: [
    { id: "SUSTAINABLE_FARMING", name: "Sustainable Farming", icon: <Wheat className="h-5 w-5" /> },
    { id: "SOIL_CARBON", name: "Soil Carbon Sequestration", icon: <Leaf className="h-5 w-5" /> },
    { id: "AGROFORESTRY", name: "Agroforestry", icon: <Trees className="h-5 w-5" /> },
  ],
  TRANSPORTATION: [
    { id: "ELECTRIC_VEHICLES", name: "Electric Vehicles", icon: <Car className="h-5 w-5" /> },
    { id: "PUBLIC_TRANSPORT", name: "Public Transportation", icon: <Truck className="h-5 w-5" /> },
    { id: "FREIGHT_EFFICIENCY", name: "Freight Efficiency", icon: <Truck className="h-5 w-5" /> },
  ],
  INDUSTRIAL: [
    { id: "PROCESS_OPTIMIZATION", name: "Process Optimization", icon: <Factory className="h-5 w-5" /> },
    { id: "FUEL_SWITCHING", name: "Fuel Switching", icon: <Flame className="h-5 w-5" /> },
    { id: "CARBON_CAPTURE", name: "Carbon Capture", icon: <Factory className="h-5 w-5" /> },
  ],
  OTHER: [
    { id: "OTHER", name: "Other", icon: <Folder className="h-5 w-5" /> },
  ],
};

// Mock regions for demonstration
const REGIONS_BY_COUNTRY: Record<string, string[]> = {
  "US": [
    "Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia",
    "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland",
    "Massachusetts", "Michigan", "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey",
    "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island", "South Carolina",
    "South Dakota", "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia", "Wisconsin", "Wyoming"
  ],
  "BR": [
    "Acre", "Alagoas", "Amapá", "Amazonas", "Bahia", "Ceará", "Distrito Federal", "Espírito Santo", "Goiás", "Maranhão",
    "Mato Grosso", "Mato Grosso do Sul", "Minas Gerais", "Pará", "Paraíba", "Paraná", "Pernambuco", "Piauí", "Rio de Janeiro", "Rio Grande do Norte",
    "Rio Grande do Sul", "Rondônia", "Roraima", "Santa Catarina", "São Paulo", "Sergipe", "Tocantins"
  ],
  "IN": [
    "Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand",
    "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab",
    "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal"
  ],
  // Add more countries as needed
};

export default function AdminProjectCreationClient() {
  const router = useRouter();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [spvs, setSpvs] = useState<SPV[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedOrganizationId, setSelectedOrganizationId] = useState<string>("");
  const [mapDialogOpen, setMapDialogOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [tempCoordinates, setTempCoordinates] = useState<{latitude: string, longitude: string} | null>(null);

  const form = useForm<AdminProjectFormValues>({
    resolver: zodResolver(adminProjectCreationSchema),
    defaultValues: {
      organizationId: "",
      spvId: "",
      name: "",
      description: "",
      type: "RENEWABLE_ENERGY",
      subtype: "",
      startDate: "",
      commissioningDate: "",
      country: "",
      region: "",
      coordinates: {
        latitude: "",
        longitude: "",
      },
      estimatedReductions: undefined,
      budget: undefined,
      standard: "",
      methodology: "",
    },
  });

  // Watch form values for dynamic updates (must be declared before useEffect)
  const watchedProjectType = form.watch("type");
  const selectedCountry = form.watch("country");

  // Get subtypes for selected project type
  const availableSubtypes = watchedProjectType ? PROJECT_SUB_TYPES[watchedProjectType as keyof typeof PROJECT_SUB_TYPES] || [] : [];

  // Get regions for selected country
  const regions = selectedCountry ? REGIONS_BY_COUNTRY[selectedCountry] || [] : [];

  useEffect(() => {
    fetchOrganizations();
    fetchSPVs();
  }, []);

  // Clear subtype when project type changes
  useEffect(() => {
    if (watchedProjectType) {
      const currentSubtype = form.getValues("subtype");
      const validSubtypes = availableSubtypes.map(st => st.id);

      // If current subtype is not valid for the new project type, clear it
      if (currentSubtype && !validSubtypes.includes(currentSubtype)) {
        form.setValue("subtype", "");
      }
    }
  }, [watchedProjectType, availableSubtypes, form]);

  const fetchOrganizations = async () => {
    try {
      const response = await fetch("/api/admin/organizations");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch organizations");
      }

      setOrganizations(data.organizations || []);
    } catch (error) {
      console.error("Error fetching organizations:", error);
      toast({
        title: "Error",
        description: "Failed to fetch organizations",
        variant: "destructive",
      });
    }
  };

  const fetchSPVs = async () => {
    try {
      const response = await fetch("/api/admin/spv");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch SPVs");
      }

      setSpvs(data.spvs || []);
    } catch (error) {
      console.error("Error fetching SPVs:", error);
    }
  };

  // Filter SPVs based on selected organization
  const filteredSpvs = spvs.filter(spv => spv.organizationId === selectedOrganizationId);

  const handleOrganizationChange = (organizationId: string) => {
    setSelectedOrganizationId(organizationId);
    form.setValue("organizationId", organizationId);
    // Clear SPV selection when organization changes
    form.setValue("spvId", "");
  };

  const handleProjectTypeChange = (type: string) => {
    form.setValue("type", type);
  };

  // Handle map dialog open
  const handleOpenMapDialog = () => {
    const coordinates = form.watch("coordinates");
    if (coordinates?.latitude && coordinates?.longitude) {
      setTempCoordinates({
        latitude: coordinates.latitude.toString(),
        longitude: coordinates.longitude.toString()
      });
    } else {
      setTempCoordinates(null);
    }
    setMapDialogOpen(true);
  };

  // Handle map location selection
  const handleLocationSelect = (coordinates: { latitude: number; longitude: number }) => {
    setTempCoordinates({
      latitude: coordinates.latitude.toString(),
      longitude: coordinates.longitude.toString()
    });
  };

  // Handle search location using geocoding
  const handleSearchLocation = async () => {
    if (!searchQuery.trim()) return;

    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(searchQuery)}&limit=1`
      );
      const data = await response.json();

      if (data && data.length > 0) {
        const result = data[0];
        setTempCoordinates({
          latitude: parseFloat(result.lat).toFixed(6),
          longitude: parseFloat(result.lon).toFixed(6)
        });
      }
    } catch (error) {
      console.error("Geocoding error:", error);
    }

    setSearchQuery("");
  };

  // Handle save coordinates
  const handleSaveCoordinates = () => {
    if (tempCoordinates) {
      form.setValue("coordinates.latitude", tempCoordinates.latitude, { shouldValidate: true });
      form.setValue("coordinates.longitude", tempCoordinates.longitude, { shouldValidate: true });
      form.setValue("coordinates", tempCoordinates, { shouldValidate: true });
    }
    setMapDialogOpen(false);
  };

  const handleSubmit = async (values: AdminProjectFormValues) => {
    try {
      setIsSubmitting(true);

      // Convert "none" spvId back to undefined and prepare data for API
      let submitData: any = { ...values };
      if (submitData.spvId === "none") {
        submitData = { ...submitData, spvId: undefined };
      }

      // Convert coordinates to string format for API
      if (submitData.coordinates?.latitude && submitData.coordinates?.longitude) {
        submitData.coordinates = `${submitData.coordinates.latitude},${submitData.coordinates.longitude}`;
      } else {
        submitData.coordinates = undefined;
      }

      // Map region to location field for API compatibility
      submitData.location = submitData.region;
      delete submitData.region;

      const response = await fetch("/api/admin/projects", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submitData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to create project");
      }

      toast({
        title: "Success",
        description: "Project created successfully",
      });

      // Redirect back to admin projects list
      router.push("/admin/projects");
    } catch (error) {
      console.error("Error creating project:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create project",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    router.push("/admin/projects");
  };

  return (
    <PageTransition>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="icon" onClick={handleCancel}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Create Project</h1>
            <p className="text-muted-foreground">
              Create a new carbon credit project for any organization
            </p>
          </div>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Organization Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Building2 className="h-4 w-4" />
                  Organization Assignment
                </CardTitle>
                <CardDescription>
                  Select the organization this project will belong to
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="organizationId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organization *</FormLabel>
                        <Select onValueChange={handleOrganizationChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select organization" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {organizations.map((org) => (
                              <SelectItem key={org.id} value={org.id}>
                                {org.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="spvId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SPV (Optional)</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select SPV (optional)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="none">No SPV</SelectItem>
                            {filteredSpvs.map((spv) => (
                              <SelectItem key={spv.id} value={spv.id}>
                                {spv.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          {selectedOrganizationId 
                            ? `SPVs available for the selected organization`
                            : "Select an organization first to see available SPVs"
                          }
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Folder className="h-4 w-4" />
                  Basic Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter project name" autoComplete="off" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter project description"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="type"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Type *</FormLabel>
                      <Select onValueChange={handleProjectTypeChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select project type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {projectTypes.map((type) => (
                            <SelectItem key={type.value} value={type.value}>
                              {type.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="subtype"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Project Subtype *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={!watchedProjectType || availableSubtypes.length === 0}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder={
                              !watchedProjectType
                                ? "Select a project type first"
                                : availableSubtypes.length === 0
                                ? "No subtypes available"
                                : "Select project subtype"
                            } />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {availableSubtypes.map((subtype) => (
                            <SelectItem key={subtype.id} value={subtype.id}>
                              <div className="flex items-center space-x-2">
                                {subtype.icon}
                                <span>{subtype.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        {!watchedProjectType
                          ? "Select a project type first to see available subtypes"
                          : "Choose the specific subtype that best describes your project"
                        }
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Location Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location Information
                </CardTitle>
                <CardDescription>
                  Specify the geographic location of your project
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="country"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Country *</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select country" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {countries.map((country) => (
                              <SelectItem key={country.code} value={country.code}>
                                {country.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="region"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>State/Region</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          disabled={!selectedCountry || regions.length === 0}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder={
                                !selectedCountry
                                  ? "Select a country first"
                                  : regions.length === 0
                                  ? "No regions available"
                                  : "Select a region"
                              } />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {regions.map((region) => (
                              <SelectItem key={region} value={region}>
                                {region}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="coordinates.latitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Latitude</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="e.g., 40.7128"
                            autoComplete="off"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="coordinates.longitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Longitude</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="e.g., -74.0060"
                            autoComplete="off"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Map Section */}
                <div className="mt-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-sm font-medium">Map Location</h4>
                      <p className="text-sm text-muted-foreground">
                        {form.watch("coordinates")?.latitude && form.watch("coordinates")?.longitude
                          ? "Location has been pinned. Click to adjust."
                          : "Click to select your project location on the map."}
                      </p>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleOpenMapDialog}
                    >
                      <MapPin className="h-4 w-4 mr-2" />
                      {form.watch("coordinates")?.latitude ? "Edit Location" : "Pin on Map"}
                    </Button>
                  </div>

                  {form.watch("coordinates")?.latitude && form.watch("coordinates")?.longitude ? (
                    <div
                      className="bg-background rounded-md p-4 text-center relative h-40 flex items-center justify-center cursor-pointer border border-muted-foreground/20 hover:border-primary/50 transition-colors"
                      onClick={handleOpenMapDialog}
                    >
                      <div className="relative z-10 bg-background/80 p-4 rounded-lg backdrop-blur-sm">
                        <MapPin className="h-8 w-8 text-primary mx-auto mb-2" />
                        <p className="text-sm font-medium">Location Pinned</p>
                        <p className="text-xs text-muted-foreground">
                          Lat: {form.watch("coordinates")?.latitude}, Long: {form.watch("coordinates")?.longitude}
                        </p>
                        <p className="text-xs mt-2 text-primary">Click to adjust location</p>
                      </div>
                    </div>
                  ) : (
                    <div
                      className="bg-background rounded-md p-4 text-center h-40 flex items-center justify-center cursor-pointer border border-dashed border-muted-foreground/50 hover:border-primary/50 transition-colors"
                      onClick={handleOpenMapDialog}
                    >
                      <div>
                        <Map className="h-12 w-12 text-muted-foreground mx-auto mb-3" />
                        <p className="text-sm text-muted-foreground max-w-xs">
                          No location pinned yet. Click here to open the interactive map and select your project location.
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Timeline and Financial Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Timeline and Financial Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="startDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="commissioningDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Commissioning Date</FormLabel>
                        <FormControl>
                          <Input type="date" {...field} />
                        </FormControl>
                        <FormDescription>
                          Expected date when the project will be operational
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="estimatedReductions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Estimated CO2 Reductions (tonnes)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter estimated reductions"
                            {...field}
                            onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="budget"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget (USD)</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <DollarSign className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                            <Input
                              type="number"
                              placeholder="Enter budget"
                              className="pl-10"
                              {...field}
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : undefined)}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Standards and Methodology */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Folder className="h-4 w-4" />
                  Standards and Methodology
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="standard"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Standard</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., Verra VCS, Gold Standard" autoComplete="off" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="methodology"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Methodology</FormLabel>
                        <FormControl>
                          <Input placeholder="e.g., VM0025, ACM0002" autoComplete="off" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Creating..." : "Create Project"}
              </Button>
            </div>
          </form>
        </Form>

        {/* Map Dialog */}
        <Dialog open={mapDialogOpen} onOpenChange={setMapDialogOpen}>
          <DialogContent className="sm:max-w-[700px] h-[600px] flex flex-col">
            <DialogHeader>
              <DialogTitle>Select Project Location</DialogTitle>
              <DialogDescription>
                Click on the map to place a pin at your project's location.
              </DialogDescription>
            </DialogHeader>

            <div className="flex items-center space-x-2 mb-4">
              <div className="relative flex-1">
                <Input
                  placeholder="Search for a location..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pr-10"
                  onKeyDown={(e) => e.key === 'Enter' && handleSearchLocation()}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full"
                  onClick={handleSearchLocation}
                >
                  <Search className="h-4 w-4" />
                </Button>
              </div>
              <Button variant="outline" size="icon" onClick={() => setSearchQuery("")}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="relative flex-1 border rounded-md overflow-hidden">
              <InteractiveMap
                coordinates={tempCoordinates ? {
                  latitude: parseFloat(tempCoordinates.latitude),
                  longitude: parseFloat(tempCoordinates.longitude)
                } : null}
                onLocationSelect={handleLocationSelect}
                height="100%"
                className="w-full h-full"
              />

              {tempCoordinates && (
                <div className="absolute bottom-4 left-4 bg-background/80 backdrop-blur-sm p-2 rounded-md shadow-md z-[1001]">
                  <p className="text-xs font-medium">Selected Coordinates:</p>
                  <p className="text-xs">Lat: {tempCoordinates.latitude}, Long: {tempCoordinates.longitude}</p>
                </div>
              )}
            </div>

            <DialogFooter className="mt-4">
              <Button variant="outline" onClick={() => setMapDialogOpen(false)}>Cancel</Button>
              <Button onClick={handleSaveCoordinates} disabled={!tempCoordinates}>
                <MapPin className="h-4 w-4 mr-2" />
                Save Location
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </PageTransition>
  );
}
