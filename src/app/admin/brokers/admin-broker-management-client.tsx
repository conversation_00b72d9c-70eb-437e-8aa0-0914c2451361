"use client";

import { useState } from "react";
import { Plus, Building2, Download, Upload } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAdminBrokers, useAdminBrokerMutations } from "@/hooks/use-admin-brokers";
import { BrokerDataTable } from "@/components/broker/broker-data-table";
import { BrokerFilters, BrokerStats } from "@/components/broker/broker-filters";
import { AdminBrokerForm } from "@/components/broker/admin-broker-form";
import { BrokerDetailsView } from "@/components/broker/broker-details-view";
import { BrokerWithOrganization, AdminBrokerCreateData, BrokerUpdateData } from "@/types/broker";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

type ViewMode = "list" | "create" | "edit" | "view";

export function AdminBrokerManagementClient() {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedBroker, setSelectedBroker] = useState<BrokerWithOrganization | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [brokerToDelete, setBrokerToDelete] = useState<BrokerWithOrganization | null>(null);

  const {
    brokers,
    organizations,
    pagination,
    filters,
    isLoading,
    error,
    refetch,
    updateFilters,
  } = useAdminBrokers();

  const {
    createBroker,
    updateBroker,
    deleteBroker,
    bulkOperation,
    isCreating,
    isUpdating,
    isDeleting,
    isBulkOperating,
    error: mutationError,
  } = useAdminBrokerMutations();

  const handleCreateBroker = async (data: AdminBrokerCreateData) => {
    await createBroker(data);
    setViewMode("list");
    refetch();
  };

  const handleUpdateBroker = async (data: BrokerUpdateData) => {
    if (!selectedBroker) return;
    await updateBroker(selectedBroker.id, data);
    setViewMode("list");
    setSelectedBroker(null);
    refetch();
  };

  const handleDeleteBroker = async () => {
    if (!brokerToDelete) return;
    try {
      await deleteBroker(brokerToDelete.id);
      toast.success("Broker deleted successfully");
      setDeleteDialogOpen(false);
      setBrokerToDelete(null);
      refetch();
    } catch (error) {
      toast.error("Failed to delete broker");
    }
  };

  const handleEdit = (broker: BrokerWithOrganization) => {
    setSelectedBroker(broker);
    setViewMode("edit");
  };

  const handleView = (broker: BrokerWithOrganization) => {
    setSelectedBroker(broker);
    setViewMode("view");
  };

  const handleDelete = (broker: BrokerWithOrganization) => {
    setBrokerToDelete(broker);
    setDeleteDialogOpen(true);
  };

  const handleVerificationChange = async (broker: BrokerWithOrganization, status: string) => {
    try {
      const response = await fetch(`/api/admin/brokers/${broker.id}/verification`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          verificationStatus: status,
          verificationNotes: `Verification status updated to ${status} by admin`
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to update verification status");
      }

      toast.success(`Broker verification status updated to ${status}`);
      refetch();
    } catch (error) {
      toast.error("Failed to update verification status");
      console.error("Error updating verification status:", error);
    }
  };

  const handleSort = (sortBy: string) => {
    const newSortOrder = filters?.sortBy === sortBy && filters?.sortOrder === "asc" ? "desc" : "asc";
    updateFilters({ sortBy, sortOrder: newSortOrder });
  };

  const handlePageChange = (page: number) => {
    updateFilters({ page });
  };

  // Calculate stats from brokers data
  const stats = brokers ? {
    total: pagination?.totalCount || 0,
    active: brokers.filter(b => b.status === "ACTIVE").length,
    inactive: brokers.filter(b => b.status === "INACTIVE").length,
    suspended: brokers.filter(b => b.status === "SUSPENDED").length,
  } : undefined;

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (viewMode === "create") {
    return (
      <div className="container mx-auto py-6">
        <AdminBrokerForm
          mode="create"
          organizations={organizations}
          onSubmit={handleCreateBroker}
          onCancel={() => setViewMode("list")}
          isLoading={isCreating}
        />
      </div>
    );
  }

  if (viewMode === "edit" && selectedBroker) {
    return (
      <div className="container mx-auto py-6">
        <AdminBrokerForm
          mode="edit"
          organizations={organizations}
          initialData={selectedBroker}
          onSubmit={handleUpdateBroker}
          onCancel={() => {
            setViewMode("list");
            setSelectedBroker(null);
          }}
          isLoading={isUpdating}
          showStatusFields={true}
        />
      </div>
    );
  }

  if (viewMode === "view" && selectedBroker) {
    return (
      <div className="container mx-auto py-6">
        <BrokerDetailsView
          broker={selectedBroker as any} // Type assertion for now
          onEdit={() => setViewMode("edit")}
          showEditButton={true}
        />
        <div className="mt-6">
          <Button variant="outline" onClick={() => {
            setViewMode("list");
            setSelectedBroker(null);
          }}>
            Back to List
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="admin-content space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Admin - Broker Management</h1>
          <p className="text-muted-foreground">
            Manage brokers across all organizations
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button onClick={() => setViewMode("create")}>
            <Plus className="mr-2 h-4 w-4" />
            Add Broker
          </Button>
        </div>
      </div>

      {/* Stats */}
      <BrokerStats stats={stats} />

      {/* Filters */}
      {filters && (
        <BrokerFilters
          filters={filters}
          organizations={organizations}
          onFiltersChange={updateFilters}
          showOrganizationFilter={true}
        />
      )}

      {/* Brokers Table */}
      <BrokerDataTable
        brokers={brokers}
        pagination={pagination}
        filters={filters}
        onSort={handleSort}
        onPageChange={handlePageChange}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        onVerificationChange={handleVerificationChange}
        showOrganization={true}
        showActions={true}
        showVerificationActions={true}
        isLoading={isLoading}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the broker
              "{brokerToDelete?.name}" and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteBroker}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Error Display */}
      {mutationError && (
        <Alert variant="destructive">
          <AlertDescription>{mutationError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
