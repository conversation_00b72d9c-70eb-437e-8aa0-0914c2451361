"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { 
  Settings, 
  Plus, 
  ArrowRight,
  CheckCircle,
  Info,
  Lightbulb,
  MousePointer
} from "lucide-react";
import { motion } from "framer-motion";

const steps = [
  {
    id: 1,
    title: "Access Custom Fields Manager",
    description: "Navigate to the Custom Fields section in your dashboard",
    action: "Click on 'Custom Fields' in the sidebar navigation",
    icon: Settings,
  },
  {
    id: 2,
    title: "Select Project Type",
    description: "Choose the project type and subtype you want to customize",
    action: "Select 'Renewable Energy' → 'Solar Energy' for Solar projects",
    icon: MousePointer,
  },
  {
    id: 3,
    title: "Add Custom Fields",
    description: "Click 'Add Field' to create new custom fields",
    action: "Configure field type, label, validation, and which step to show it in",
    icon: Plus,
  },
  {
    id: 4,
    title: "Save Configuration",
    description: "Save your custom fields configuration",
    action: "Click 'Save Custom Fields' to apply changes",
    icon: CheckCircle,
  },
];

const fieldTypes = [
  { name: "Text", description: "Single line text input", example: "Project Manager Name" },
  { name: "Number", description: "Numeric input with validation", example: "Additional Capacity (MW)" },
  { name: "Date", description: "Date picker", example: "Environmental Clearance Date" },
  { name: "Select", description: "Dropdown with options", example: "Financing Type: [Debt, Equity, Mixed]" },
  { name: "Textarea", description: "Multi-line text", example: "Environmental Impact Assessment" },
  { name: "File", description: "File upload", example: "Environmental Clearance Certificate" },
  { name: "Checkbox", description: "Yes/No option", example: "Grid Connection Available" },
];

export default function CustomFieldsGuidePage() {
  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center space-y-4"
      >
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-blue-100 rounded-lg">
            <Lightbulb className="h-8 w-8 text-blue-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Custom Fields Quick Guide</h1>
            <p className="text-gray-600">Learn how to add custom fields to your project forms</p>
          </div>
        </div>
      </motion.div>

      {/* Quick Start */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ArrowRight className="h-5 w-5 text-blue-600" />
              Quick Start
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-700 mb-2">
                  Ready to add custom fields to your Solar Energy project forms?
                </p>
                <p className="text-sm text-gray-600">
                  Start by accessing the Custom Fields Manager from your dashboard.
                </p>
              </div>
              <Link href="/dashboard/custom-fields">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Settings className="mr-2 h-4 w-4" />
                  Open Custom Fields Manager
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Step-by-Step Guide */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Step-by-Step Guide</CardTitle>
            <p className="text-sm text-muted-foreground">
              Follow these steps to add custom fields to your project forms
            </p>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {steps.map((step, index) => (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.1 * index }}
                  className="flex items-start gap-4 p-4 rounded-lg border bg-gray-50"
                >
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <step.icon className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="outline">Step {step.id}</Badge>
                      <h3 className="font-medium">{step.title}</h3>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{step.description}</p>
                    <p className="text-sm font-medium text-blue-700">{step.action}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Field Types */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <Card>
          <CardHeader>
            <CardTitle>Available Field Types</CardTitle>
            <p className="text-sm text-muted-foreground">
              Choose from these field types to collect the information you need
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {fieldTypes.map((fieldType, index) => (
                <motion.div
                  key={fieldType.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.05 * index }}
                  className="p-4 border rounded-lg"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <Badge variant="secondary">{fieldType.name}</Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{fieldType.description}</p>
                  <p className="text-xs text-blue-600 font-medium">
                    Example: {fieldType.example}
                  </p>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Best Practices
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3">Field Design Tips:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Use clear, descriptive field labels</li>
                  <li>• Add helpful placeholder text</li>
                  <li>• Include help text for complex fields</li>
                  <li>• Only mark fields as required if truly necessary</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-3">Organization Tips:</h4>
                <ul className="space-y-2 text-sm text-gray-600">
                  <li>• Group related fields in the same step</li>
                  <li>• Test the form after adding new fields</li>
                  <li>• Consider the user experience</li>
                  <li>• Review fields periodically for relevance</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Call to Action */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="text-center"
      >
        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
          <CardContent className="pt-6">
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-green-900">Ready to Get Started?</h3>
              <p className="text-green-700">
                Start customizing your project forms to collect exactly the information your organization needs.
              </p>
              <div className="flex items-center justify-center gap-4">
                <Link href="/dashboard/custom-fields">
                  <Button className="bg-green-600 hover:bg-green-700">
                    <Settings className="mr-2 h-4 w-4" />
                    Manage Custom Fields
                  </Button>
                </Link>
                <Link href="/dashboard/projects/create">
                  <Button variant="outline">
                    <Plus className="mr-2 h-4 w-4" />
                    Create Project
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
