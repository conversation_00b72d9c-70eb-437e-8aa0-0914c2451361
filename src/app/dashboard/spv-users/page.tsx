import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { UserRole } from "@/lib/authorization";
import SPVUsersManagementClient from "./spv-users-management-client";

export const metadata: Metadata = {
  title: "SPV Users Management",
  description: "Manage SPV users, roles, and permissions for your organization",
};

export default async function SPVUsersManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  if (!session.user.organizationId) {
    redirect("/onboarding");
  }

  // Only organization admins can access this page
  if (session.user.role !== UserRole.ORGANIZATION_ADMIN) {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <SPVUsersManagementClient />
    </div>
  );
}
