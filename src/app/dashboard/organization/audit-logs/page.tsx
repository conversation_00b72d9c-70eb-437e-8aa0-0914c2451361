"use client";

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { AlertTriangle } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedAuditTrail } from '@/components/audit/enhanced-audit-trail';
import { AuditAnalytics } from '@/components/audit/audit-analytics';



export default function OrganizationAuditLogsPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("audit-trail");

  if (!session?.user) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Please log in to view audit logs.</p>
      </div>
    );
  }

  if (session.user.role !== 'ORGANIZATION_ADMIN' && session.user.role !== 'ADMIN') {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            You don't have permission to view organization audit logs.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Audit Trail</h1>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="audit-trail">Audit Trail</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="audit-trail" className="space-y-6">
          <EnhancedAuditTrail
            title="Audit Trail"
            showFilters={true}
            showExport={true}
            autoRefresh={false}
            refreshInterval={30000}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AuditAnalytics
            timeRange="all"
            autoRefresh={false}
          />
        </TabsContent>


      </Tabs>
    </div>
  );
}
