"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import {
  Animated<PERSON><PERSON>,
  AnimatedCardHeader,
  AnimatedCardContent,
  AnimatedCardTitle,
  AnimatedButton,
  AnimatedInput,
  PageTransition
} from "@/components/ui/animated";
import { AnimationSettings } from "@/components/settings/animation-settings";
import { ProtectedPage } from "@/components/auth/protected-page";
import { User, Lock, CheckCircle, Loader2, Eye, EyeOff } from "lucide-react";
import { z } from "zod";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";


// Validation schemas
const userProfileSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  jobTitle: z.string().max(100, "Job title too long").optional(),
  phoneNumber: z.string().max(20, "Phone number too long").optional(),
  bio: z.string().max(500, "Bio too long").optional(),
});

const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export default function SettingsPage() {
  const { data: session, update } = useSession();
  const [activeTab, setActiveTab] = useState("profile");
  const [userProfile, setUserProfile] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Password visibility states
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Profile form
  const profileForm = useForm({
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      name: "",
      jobTitle: "",
      phoneNumber: "",
      bio: "",
    },
  });

  // Password form
  const passwordForm = useForm({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  // Mock organization data
  const mockOrganization = {
    name: "Green Energy Co.",
    description: "Renewable energy solutions provider",
    website: "https://greenenergy.example.com",
    logo: "",
    transactionFeeRate: 1.0,
    listingFeeRate: 0.5,
  };

  // Fetch user profile on component mount
  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        const response = await fetch("/api/user/profile");
        if (response.ok) {
          const result = await response.json();
          setUserProfile(result.data);
          profileForm.reset({
            name: result.data.name || "",
            jobTitle: result.data.jobTitle || "",
            phoneNumber: result.data.phoneNumber || "",
            bio: result.data.bio || "",
          });
        }
      } catch (error) {
        console.error("Error fetching user profile:", error);
        toast.error("Failed to load profile data");
      } finally {
        setIsLoading(false);
      }
    };

    if (session?.user) {
      fetchUserProfile();
    }
  }, [session, profileForm]);

  // Handle profile form submission
  const handleProfileSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/user/profile", {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        toast.error(result.error || "Failed to update profile. Please try again.");
        return;
      }

      setUserProfile(result.data);

      // Update the session to reflect the new name in the navbar
      await update({
        ...session,
        user: {
          ...session?.user,
          name: result.data.name,
        },
      });

      toast.success("Profile updated successfully! Your changes have been saved.");
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  // Handle password change form submission
  const handlePasswordSubmit = async (data: any) => {
    try {
      const response = await fetch("/api/user/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 400 && result.error === "Current password is incorrect") {
          toast.error("Current password is incorrect. Please try again.");
          passwordForm.setError("currentPassword", {
            type: "manual",
            message: "Current password is incorrect"
          });
        } else if (response.status === 400 && result.error === "New password must be different from current password") {
          toast.error("New password must be different from your current password.");
          passwordForm.setError("newPassword", {
            type: "manual",
            message: "New password must be different from current password"
          });
        } else {
          toast.error(result.error || "Failed to change password. Please try again.");
        }
        return;
      }

      // Success case
      passwordForm.reset();
      setShowCurrentPassword(false);
      setShowNewPassword(false);
      setShowConfirmPassword(false);
      toast.success("Password changed successfully! Please use your new password for future logins.");
    } catch (error) {
      console.error("Error changing password:", error);
      toast.error("An unexpected error occurred. Please try again.");
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <PageTransition>
          <div className="space-y-6">
            <div className="page-header-spacing">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
                <p className="text-muted-foreground">Manage your profile and account settings</p>
              </div>
            </div>
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          </div>
        </PageTransition>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="space-y-6">
          <div className="page-header-spacing">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
              <p className="text-muted-foreground">Manage your profile and account settings</p>
            </div>
          </div>
          <div className="space-y-6">
            <div className="button-group flex-wrap">
              <AnimatedButton
                variant={activeTab === "profile" ? "default" : "outline"}
                onClick={() => setActiveTab("profile")}
                animationVariant="buttonTap"
              >
                Profile
              </AnimatedButton>
            {session?.user?.role === "ORGANIZATION_ADMIN" && (
              <AnimatedButton
                variant={activeTab === "organization" ? "default" : "outline"}
                onClick={() => setActiveTab("organization")}
                animationVariant="buttonTap"
              >
                Organization
              </AnimatedButton>
            )}
            <AnimatedButton
              variant={activeTab === "security" ? "default" : "outline"}
              onClick={() => setActiveTab("security")}
              animationVariant="buttonTap"
            >
              Security
            </AnimatedButton>
            <AnimatedButton
              variant={activeTab === "notifications" ? "default" : "outline"}
              onClick={() => setActiveTab("notifications")}
              animationVariant="buttonTap"
            >
              Notifications
            </AnimatedButton>
            {/* <AnimatedButton
              variant={activeTab === "animations" ? "default" : "outline"}
              onClick={() => setActiveTab("animations")}
              animationVariant="buttonTap"
            >
              Animations
            </AnimatedButton> */}
          </div>

          {activeTab === "profile" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profile Information
                </AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <Form {...profileForm}>
                  <form onSubmit={profileForm.handleSubmit(handleProfileSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <FormField
                        control={profileForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name *</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter your full name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={profileForm.control}
                        name="jobTitle"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Job Title</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter your job title" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <Label htmlFor="email">Email Address</Label>
                        <Input
                          id="email"
                          type="email"
                          value={userProfile?.email || session?.user?.email || ""}
                          disabled
                          className="bg-muted"
                        />
                        <p className="text-xs text-muted-foreground">
                          Your email cannot be changed
                        </p>
                      </div>

                      <FormField
                        control={profileForm.control}
                        name="phoneNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Phone Number</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter your phone number" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={profileForm.control}
                      name="bio"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Bio</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Tell us about yourself"
                              rows={4}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <AnimatedButton
                      type="submit"
                      disabled={profileForm.formState.isSubmitting}
                      animationVariant="buttonTap"
                      className="w-full md:w-auto"
                    >
                      {profileForm.formState.isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Update Profile
                        </>
                      )}
                    </AnimatedButton>
                  </form>
                </Form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "organization" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Organization Settings</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <form className="space-y-4">
                  <div className="grid gap-2">
                    <label htmlFor="org-name" className="text-sm font-medium">
                      Organization Name
                    </label>
                    <AnimatedInput
                      id="org-name"
                      defaultValue={mockOrganization.name}
                      placeholder="Organization name"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="org-description" className="text-sm font-medium">
                      Description
                    </label>
                    <textarea
                      id="org-description"
                      className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      defaultValue={mockOrganization.description}
                      placeholder="Organization description"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="org-website" className="text-sm font-medium">
                      Website
                    </label>
                    <AnimatedInput
                      id="org-website"
                      type="url"
                      defaultValue={mockOrganization.website}
                      placeholder="https://example.com"
                    />
                  </div>
                  <div className="grid gap-2">
                    <label htmlFor="org-logo" className="text-sm font-medium">
                      Logo
                    </label>
                    <AnimatedInput id="org-logo" type="file" accept="image/*" />
                  </div>
                  <AnimatedButton animationVariant="buttonTap">Save Organization Settings</AnimatedButton>
                </form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "security" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle className="flex items-center gap-2">
                  <Lock className="h-5 w-5" />
                  Security Settings
                </AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <Form {...passwordForm}>
                  <form onSubmit={passwordForm.handleSubmit(handlePasswordSubmit)} className="space-y-6">
                    <div className="space-y-4">
                      <FormField
                        control={passwordForm.control}
                        name="currentPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Current Password *</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  type={showCurrentPassword ? "text" : "password"}
                                  placeholder="Enter your current password"
                                  {...field}
                                  className="pr-10"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                >
                                  {showCurrentPassword ? (
                                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                                  ) : (
                                    <Eye className="h-4 w-4 text-muted-foreground" />
                                  )}
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={passwordForm.control}
                        name="newPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>New Password *</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  type={showNewPassword ? "text" : "password"}
                                  placeholder="Enter your new password"
                                  {...field}
                                  className="pr-10"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                  onClick={() => setShowNewPassword(!showNewPassword)}
                                >
                                  {showNewPassword ? (
                                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                                  ) : (
                                    <Eye className="h-4 w-4 text-muted-foreground" />
                                  )}
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={passwordForm.control}
                        name="confirmPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Confirm New Password *</FormLabel>
                            <FormControl>
                              <div className="relative">
                                <Input
                                  type={showConfirmPassword ? "text" : "password"}
                                  placeholder="Confirm your new password"
                                  {...field}
                                  className="pr-10"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                >
                                  {showConfirmPassword ? (
                                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                                  ) : (
                                    <Eye className="h-4 w-4 text-muted-foreground" />
                                  )}
                                </Button>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="bg-muted p-4 rounded-lg">
                      <h4 className="text-sm font-medium mb-2">Password Requirements:</h4>
                      <ul className="text-xs text-muted-foreground space-y-1">
                        <li>• At least 8 characters long</li>
                        <li>• Must be different from your current password</li>
                        <li>• Consider using a mix of letters, numbers, and symbols</li>
                      </ul>
                    </div>

                    <AnimatedButton
                      type="submit"
                      disabled={passwordForm.formState.isSubmitting}
                      animationVariant="buttonTap"
                      className="w-full md:w-auto"
                    >
                      {passwordForm.formState.isSubmitting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Changing Password...
                        </>
                      ) : (
                        <>
                          <Lock className="mr-2 h-4 w-4" />
                          Change Password
                        </>
                      )}
                    </AnimatedButton>
                  </form>
                </Form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "notifications" && (
            <AnimatedCard animationVariant="fadeIn">
              <AnimatedCardHeader>
                <AnimatedCardTitle>Notification Preferences</AnimatedCardTitle>
              </AnimatedCardHeader>
              <AnimatedCardContent>
                <form className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Email Notifications</h3>
                      <p className="text-xs text-muted-foreground">
                        Receive email notifications for important updates
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Transaction Alerts</h3>
                      <p className="text-xs text-muted-foreground">
                        Get notified when transactions occur
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Market Updates</h3>
                      <p className="text-xs text-muted-foreground">
                        Receive updates about new carbon credits and market changes
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-sm font-medium">Platform Announcements</h3>
                      <p className="text-xs text-muted-foreground">
                        Get notified about platform updates and announcements
                      </p>
                    </div>
                    <input
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                      defaultChecked
                    />
                  </div>
                  <AnimatedButton animationVariant="buttonTap">Save Preferences</AnimatedButton>
                </form>
              </AnimatedCardContent>
            </AnimatedCard>
          )}

          {activeTab === "animations" && (
            <AnimationSettings />
          )}
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
