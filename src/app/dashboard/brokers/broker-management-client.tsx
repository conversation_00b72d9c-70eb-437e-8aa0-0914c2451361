"use client";

import { useState } from "react";
import { Plus, Building2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useOrganizationBrokers, useOrganizationBrokerMutations } from "@/hooks/use-organization-brokers";
import { BrokerDataTable } from "@/components/broker/broker-data-table";
import { BrokerFilters, BrokerStats } from "@/components/broker/broker-filters";
import { BrokerForm } from "@/components/broker/broker-form";
import { BrokerDetailsView } from "@/components/broker/broker-details-view";
import { BrokerWithOrganization, BrokerCreateData, BrokerUpdateData } from "@/types/broker";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

type ViewMode = "list" | "create" | "edit" | "view";

export function BrokerManagementClient() {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedBroker, setSelectedBroker] = useState<BrokerWithOrganization | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [brokerToDelete, setBrokerToDelete] = useState<BrokerWithOrganization | null>(null);

  const {
    brokers,
    pagination,
    filters,
    isLoading,
    error,
    refetch,
    updateFilters,
  } = useOrganizationBrokers();

  const {
    createBroker,
    updateBroker,
    deleteBroker,
    isCreating,
    isUpdating,
    isDeleting,
    error: mutationError,
  } = useOrganizationBrokerMutations();

  const handleCreateBroker = async (data: BrokerCreateData) => {
    await createBroker(data);
    setViewMode("list");
    refetch();
  };

  const handleUpdateBroker = async (data: BrokerUpdateData) => {
    if (!selectedBroker) return;
    await updateBroker(selectedBroker.id, data);
    setViewMode("list");
    setSelectedBroker(null);
    refetch();
  };

  const handleDeleteBroker = async () => {
    if (!brokerToDelete) return;
    try {
      await deleteBroker(brokerToDelete.id);
      toast.success("Broker deleted successfully");
      setDeleteDialogOpen(false);
      setBrokerToDelete(null);
      refetch();
    } catch (error) {
      toast.error("Failed to delete broker");
    }
  };

  const handleEdit = (broker: BrokerWithOrganization) => {
    setSelectedBroker(broker);
    setViewMode("edit");
  };

  const handleView = (broker: BrokerWithOrganization) => {
    setSelectedBroker(broker);
    setViewMode("view");
  };

  const handleDelete = (broker: BrokerWithOrganization) => {
    setBrokerToDelete(broker);
    setDeleteDialogOpen(true);
  };

  const handleSort = (sortBy: string) => {
    const newSortOrder = filters?.sortBy === sortBy && filters?.sortOrder === "asc" ? "desc" : "asc";
    updateFilters({ sortBy, sortOrder: newSortOrder });
  };

  const handlePageChange = (page: number) => {
    updateFilters({ page });
  };

  // Calculate stats from brokers data
  const stats = brokers ? {
    total: pagination?.totalCount || 0,
    active: brokers.filter(b => b.status === "ACTIVE").length,
    pending: brokers.filter(b => b.status === "PENDING").length,
    suspended: brokers.filter(b => b.status === "SUSPENDED").length,
  } : undefined;

  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (viewMode === "create") {
    return (
      <div className="container mx-auto py-6">
        <BrokerForm
          mode="create"
          onSubmit={handleCreateBroker}
          onCancel={() => setViewMode("list")}
          isLoading={isCreating}
        />
      </div>
    );
  }

  if (viewMode === "edit" && selectedBroker) {
    return (
      <div className="container mx-auto py-6">
        <BrokerForm
          mode="edit"
          initialData={selectedBroker}
          onSubmit={handleUpdateBroker}
          onCancel={() => {
            setViewMode("list");
            setSelectedBroker(null);
          }}
          isLoading={isUpdating}
        />
      </div>
    );
  }

  if (viewMode === "view" && selectedBroker) {
    return (
      <div className="container mx-auto py-6">
        <BrokerDetailsView
          broker={selectedBroker as any} // Type assertion for now
          onEdit={() => setViewMode("edit")}
          showEditButton={true}
        />
        <div className="mt-6">
          <Button variant="outline" onClick={() => {
            setViewMode("list");
            setSelectedBroker(null);
          }}>
            Back to List
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Broker Management</h1>
          <p className="text-muted-foreground">
            Manage your organization's brokers and their relationships
          </p>
        </div>
        <Button onClick={() => setViewMode("create")}>
          <Plus className="mr-2 h-4 w-4" />
          Add Broker
        </Button>
      </div>

      {/* Stats */}
      <BrokerStats stats={stats} />

      {/* Filters */}
      {filters && (
        <BrokerFilters
          filters={filters}
          onFiltersChange={updateFilters}
          showOrganizationFilter={false}
        />
      )}

      {/* Brokers Table */}
      <BrokerDataTable
        brokers={brokers}
        pagination={pagination}
        filters={filters}
        onSort={handleSort}
        onPageChange={handlePageChange}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        showOrganization={false}
        showActions={true}
        isLoading={isLoading}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the broker
              "{brokerToDelete?.name}" and remove all associated data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteBroker}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Error Display */}
      {mutationError && (
        <Alert variant="destructive">
          <AlertDescription>{mutationError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
