import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import ProjectManagementClient from "./project-management-client";

export const metadata: Metadata = {
  title: "Project Management",
  description: "Manage project assignments for your organization's SPV users",
};

export default async function ProjectManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  if (session.user.role !== "ORGANIZATION_ADMIN") {
    redirect("/dashboard");
  }

  return (
    <div className="container mx-auto py-6">
      <ProjectManagementClient />
    </div>
  );
}
