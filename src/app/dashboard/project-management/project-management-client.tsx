"use client";

import { useState } from "react";
import Link from "next/link";
import { Plus, FolderO<PERSON>, Users, BarChart3 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { OrgProjectAssignmentModal } from "@/components/organization/org-project-assignment-modal";
import { OrgProjectAssignmentsTable } from "@/components/organization/org-project-assignments-table";

export default function ProjectManagementClient() {
  const [showAssignmentModal, setShowAssignmentModal] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  const handleAssignmentSuccess = () => {
    setRefreshKey(prev => prev + 1);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Project Management</h1>
          <p className="text-muted-foreground">
            Manage project assignments for your organization's SPV users
          </p>
        </div>
        <Button onClick={() => setShowAssignmentModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Assign Projects
        </Button>
      </div>

      {/* Overview Cards */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <FolderOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-</div>
            <p className="text-xs text-muted-foreground">
              Projects in your organization
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-</div>
            <p className="text-xs text-muted-foreground">
              Current project assignments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">SPV Users</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">-</div>
            <p className="text-xs text-muted-foreground">
              Users across all SPVs
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="assignments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="assignments" className="flex items-center gap-2">
            <FolderOpen className="h-4 w-4" />
            Project Assignments
          </TabsTrigger>
          <TabsTrigger value="audit" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Audit Trail
          </TabsTrigger>
        </TabsList>

        <TabsContent value="assignments" className="space-y-4">
          <OrgProjectAssignmentsTable
            key={refreshKey}
            onRefresh={handleAssignmentSuccess}
          />
        </TabsContent>

        <TabsContent value="audit" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Organization Audit Trail</CardTitle>
              <CardDescription>
                Comprehensive audit logs for all projects in your organization
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 mb-6">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Organization Audit Logs</CardTitle>
                    <BarChart3 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">-</div>
                    <p className="text-xs text-muted-foreground">
                      All organization activities
                    </p>
                    <Button variant="outline" size="sm" className="mt-2" asChild>
                      <Link href="/dashboard/organization/audit-logs">
                        View All Logs
                      </Link>
                    </Button>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Project Audit Trails</CardTitle>
                    <FolderOpen className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">-</div>
                    <p className="text-xs text-muted-foreground">
                      Individual project audits
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Use "View Audit Trail" in assignments table
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Data Entry Flow</CardTitle>
                    <Users className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">-</div>
                    <p className="text-xs text-muted-foreground">
                      Verification workflows
                    </p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Track data entry progression
                    </p>
                  </CardContent>
                </Card>
              </div>

              <div className="text-center py-4">
                <h3 className="text-lg font-semibold mb-2">Audit Trail Features</h3>
                <div className="grid gap-4 md:grid-cols-2 text-left">
                  <div className="space-y-2">
                    <h4 className="font-medium">Organization Level</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• User management activities</li>
                      <li>• SPV creation and updates</li>
                      <li>• Project assignments</li>
                      <li>• Role changes and permissions</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-medium">Project Level</h4>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Data entry submissions</li>
                      <li>• Verification workflows</li>
                      <li>• Document uploads</li>
                      <li>• Status changes and approvals</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Assignment Modal */}
      <OrgProjectAssignmentModal
        isOpen={showAssignmentModal}
        onClose={() => setShowAssignmentModal(false)}
        onSuccess={handleAssignmentSuccess}
      />
    </div>
  );
}
