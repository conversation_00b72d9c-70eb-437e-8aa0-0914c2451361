"use client";

import { useState } from "react";
import { Plus, FileTex<PERSON>, CheckCircle, Building2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { useOrganizationSPVs } from "@/hooks/use-organization-spvs";
import { ComprehensiveSPVCreationForm } from "@/components/spv/comprehensive-spv-creation-form";
// Verification flow commented out for now
// import { SPVVerificationForm } from "@/components/spv/spv-verification-form";
import { SPVDocumentUpload } from "@/components/spv/spv-document-upload";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";

// import { SPVCreationStepper } from "@/components/spv/spv-creation-stepper";

import { SPV } from "@/types/spv";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { SPVCardGrid } from "@/components/spv/spv-card-grid";
import { SPVStats } from "@/components/spv/spv-filters";
import { toast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { OrganizationVerificationDashboard } from "@/components/verification/organization-verification-dashboard";

export default function SPVManagementClient() {
  const { organization, spvs, isLoading, error, refetch } = useOrganizationSPVs();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("spvs");
  const [isCreateFormOpen, setIsCreateFormOpen] = useState(false);
  const [selectedSPV, setSelectedSPV] = useState<SPV | null>(null);
  // Verification flow commented out for now
  // const [isVerificationFormOpen, setIsVerificationFormOpen] = useState(false);
  const [isDocumentUploadOpen, setIsDocumentUploadOpen] = useState(false);

  const handleSPVCreated = async (data: any) => {
    try {
      const response = await fetch('/api/organizations/spvs', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create SPV: ${response.status} ${errorText}`);
      }

      const result = await response.json();

      // Log admin credentials in development for testing
      if (process.env.NODE_ENV === 'development' && result.adminCredentials) {
        console.log('🔑 SPV Admin Credentials (Development Only):');
        console.log('Email:', result.adminCredentials.email);
        console.log('Password:', result.adminCredentials.password);
        console.log('Login URL: /spv/login');
      }

      // Show success message with admin user info
      toast({
        title: "SPV Created Successfully!",
        description: result.message || `${result.spv.name} has been created with admin user. Credentials have been sent via email.`,
        duration: 8000,
      });

      refetch(); // Refresh the list
      setIsCreateFormOpen(false);
    } catch (error) {
      console.error('Error creating SPV:', error);
      toast({
        title: "Error",
        description: "Failed to create SPV. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleViewSPV = (spv: SPV) => {
    router.push(`/dashboard/spv/${spv.id}`);
  };

  // Verification flow commented out for now
  // const handleEditVerification = (spv: SPV) => {
  //   setSelectedSPV(spv);
  //   setIsVerificationFormOpen(true);
  // };

  // const handleCompleteVerification = (spv: SPV) => {
  //   setSelectedSPV(spv);
  //   setIsVerificationFormOpen(true);
  // };



  const handleCompleteVerification = (spv: SPV) => {
    // Redirect to edit SPV instead of verification
    router.push(`/dashboard/spv/${spv.id}/edit`);
  };

  const handleUploadDocuments = (spv: SPV) => {
    setSelectedSPV(spv);
    setIsDocumentUploadOpen(true);
  };

  const handleDocumentUpload = (document: any) => {
    toast({
      title: "Document Uploaded",
      description: `${document.fileName} has been uploaded successfully.`,
      duration: 3000,
    });
  };

  // Verification flow commented out for now
  // const handleVerificationSubmit = async (data: any) => {
  //   if (!selectedSPV) return;

  //   try {
  //     const response = await fetch(`/api/organizations/spvs/${selectedSPV.id}/verification`, {
  //       method: 'PUT',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify(data),
  //     });

  //     if (!response.ok) {
  //       throw new Error('Failed to update verification details');
  //     }

  //     const result = await response.json();

  //     toast({
  //       title: "Verification Details Updated",
  //       description: result.message,
  //       duration: 5000,
  //     });

  //     refetch(); // Refresh the list
  //     setIsVerificationFormOpen(false);
  //     setSelectedSPV(null);
  //   } catch (error) {
  //     console.error('Error updating verification details:', error);
  //     toast({
  //       title: "Error",
  //       description: "Failed to update verification details. Please try again.",
  //       variant: "destructive",
  //     });
  //   }
  // };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-10 w-32" />
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          Error loading SPVs: {error}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">SPV Management</h1>
          <p className="text-muted-foreground">
            Manage Special Purpose Vehicles for {organization?.name}
          </p>
        </div>
        <div className="flex gap-3">
          <Button onClick={() => setIsCreateFormOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create SPV
          </Button>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="spvs" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            SPV Management
          </TabsTrigger>
          <TabsTrigger value="verification" className="flex items-center gap-2">
            <CheckCircle className="h-4 w-4" />
            Verification
          </TabsTrigger>
        </TabsList>

        <TabsContent value="spvs" className="space-y-6">
          {/* SPV Stats */}
          <SPVStats
            totalSPVs={spvs.length}
            activeSPVs={spvs.filter(spv => spv.status === "ACTIVE").length}
            pendingSPVs={spvs.filter(spv => spv.status === "PENDING").length}
            inactiveSPVs={spvs.filter(spv => spv.status === "INACTIVE").length}
            // Verification flow commented out for now
            // verifiedSPVs={spvs.filter(spv => spv.verificationStatus === "VERIFIED").length}
            // pendingVerificationSPVs={spvs.filter(spv => spv.verificationStatus === "PENDING_VERIFICATION").length}
            // rejectedSPVs={spvs.filter(spv => spv.verificationStatus === "REJECTED").length}
            // inReviewSPVs={spvs.filter(spv => spv.verificationStatus === "IN_REVIEW").length}
            verifiedSPVs={spvs.length} // All SPVs are considered verified for now
            pendingVerificationSPVs={0}
            rejectedSPVs={0}
            inReviewSPVs={0}
          />

          {/* SPV List */}
          <SPVCardGrid
            spvs={spvs}
            isLoading={isLoading}
            showActions={true}
            showEditOption={false}
            onView={handleViewSPV}
            onCompleteVerification={handleCompleteVerification}
            onUploadDocuments={handleUploadDocuments}
            emptyState={{
              title: "No SPVs created yet",
              description: "Create your first Special Purpose Vehicle to get started",
              action: (
                <Button onClick={() => setIsCreateFormOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Your First SPV
                </Button>
              ),
            }}
          />
        </TabsContent>

        <TabsContent value="verification" className="space-y-6">
          <OrganizationVerificationDashboard />
        </TabsContent>
      </Tabs>

      {/* SPV Creation Form */}
      <ComprehensiveSPVCreationForm
        isOpen={isCreateFormOpen}
        onClose={() => setIsCreateFormOpen(false)}
        onSubmit={handleSPVCreated}
        mode="user"
      />

      {/* SPV Verification Form - Commented out for now */}
      {/* <SPVVerificationForm
        isOpen={isVerificationFormOpen}
        onClose={() => {
          setIsVerificationFormOpen(false);
          setSelectedSPV(null);
        }}
        onSubmit={handleVerificationSubmit}
        initialData={selectedSPV ? {
          name: selectedSPV.name,
          purpose: selectedSPV.purpose || "",
          legalStructure: selectedSPV.legalStructure || "",
          registrationNumber: selectedSPV.registrationNumber || "",
          jurisdiction: selectedSPV.jurisdiction || "",
          country: selectedSPV.country || "",
          gstNumber: selectedSPV.gstNumber || "",
          cinNumber: selectedSPV.cinNumber || "",
          panNumber: selectedSPV.panNumber || "",
          incorporationDate: selectedSPV.incorporationDate || "",
          establishedDate: selectedSPV.establishedDate || "",
          registeredAddress: selectedSPV.registeredAddress || "",
          address: selectedSPV.address || "",
          contactPersonName: selectedSPV.contactPersonName || "",
          contactPersonEmail: selectedSPV.contactPersonEmail || "",
          contactPersonMobile: selectedSPV.contactPersonMobile || "",
          contact: selectedSPV.contact || "",
          bankAccountNumber: selectedSPV.bankAccountNumber || "",
          ifscCode: selectedSPV.ifscCode || "",
          projectCategories: selectedSPV.projectCategories?.[0] || "",
          description: selectedSPV.description || "",
          taxId: selectedSPV.taxId || "",
          legalEntityId: selectedSPV.legalEntityId || "",
        } : undefined}
        spvId={selectedSPV?.id}
      /> */}

      {/* SPV Document Upload Dialog */}
      <Dialog open={isDocumentUploadOpen} onOpenChange={(open) => {
        if (!open) {
          setIsDocumentUploadOpen(false);
          setSelectedSPV(null);
        }
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Upload Documents - {selectedSPV?.name}
            </DialogTitle>
            <DialogDescription>
              Upload the required verification documents for your SPV
            </DialogDescription>
          </DialogHeader>

          {selectedSPV && (
            <SPVDocumentUpload
              spvId={selectedSPV.id}
              onUploadComplete={handleDocumentUpload}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
