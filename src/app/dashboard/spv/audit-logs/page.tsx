"use client";

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { AlertTriangle } from 'lucide-react';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { EnhancedAuditTrail } from '@/components/audit/enhanced-audit-trail';
import { AuditAnalytics } from '@/components/audit/audit-analytics';
import { AuditSearch } from '@/components/audit/audit-search';

export default function SPVAuditLogsPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("audit-trail");

  if (!session?.user) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Please log in to view audit logs.</p>
      </div>
    );
  }

  // Check if user has SPV access
  const hasSpvAccess = session.user.role === 'ADMIN' || 
                      session.user.role === 'ORGANIZATION_ADMIN' ||
                      (session.user as any).spvUser?.role === 'SPV_ADMIN';

  if (!hasSpvAccess) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            You don't have permission to view SPV audit logs.
          </p>
        </div>
      </div>
    );
  }

  const spvId = (session.user as any).spvUser?.spvId;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">SPV Audit Trail</h1>
        <p className="text-muted-foreground">
          Audit trail and analytics for SPV activities and operations
        </p>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="audit-trail">Audit Trail</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="search">Advanced Search</TabsTrigger>
        </TabsList>

        <TabsContent value="audit-trail" className="space-y-6">
          <EnhancedAuditTrail
            title="SPV Audit Trail"
            description="Real-time audit trail for SPV activities and project operations"
            organizationId={session.user.organizationId || undefined}
            spvId={spvId}
            showFilters={true}
            showExport={true}
            showRealtime={true}
            autoRefresh={false}
            refreshInterval={30000}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AuditAnalytics
            organizationId={session.user.organizationId || undefined}
            timeRange="24h"
            autoRefresh={true}
          />
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
          <AuditSearch
            organizationId={session.user.organizationId || undefined}
            placeholder="Search SPV audit logs..."
            onResultSelect={(result) => {
              console.log('Selected SPV audit log:', result);
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
