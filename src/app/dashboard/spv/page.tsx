import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import SPVManagementClient from "./spv-management-client";

export const metadata: Metadata = {
  title: "SPV Management",
  description: "Manage your organization's Special Purpose Vehicles",
};

export default async function SPVManagementPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  if (!session.user.organizationId) {
    redirect("/onboarding");
  }

  return (
    <div className="container mx-auto py-6">
      <SPVManagementClient />
    </div>
  );
}
