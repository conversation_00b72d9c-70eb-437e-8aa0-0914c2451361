"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  ArrowLeft,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Zap,
  Leaf,
  DollarSign,
  Calendar,
  Target,
  LineChart,
  ToggleLeft,
  ToggleRight,
  Filter,
  Table,
  Eye,
  BarChart,
  RefreshCw
} from "lucide-react";
import { format, subDays, subMonths, subYears, startOfDay, endOfDay, startOfMonth, endOfMonth, startOfYear, endOfYear } from "date-fns";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Spinner } from "@/components/ui/spinner";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { BarChart as RechartsBarChart, LineChart as RechartsLineChart, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Bar, Line, Legend } from "recharts";
import { Table as UITable, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

interface ProjectAnalyticsPageProps {
  params: Promise<{ projectId: string }>;
}

interface UnitLog {
  id: string;
  logDate: string;
  frequency: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  verificationStatus: string;
  metadata?: {
    generationType?: string;
    fileName?: string;
    [key: string]: any;
  };
  logger?: {
    id: string;
    name: string;
    email: string;
  };
}

interface AnalyticsData {
  date: string;
  actualGeneration: number;
  estimatedGeneration: number;
  actualReduction: number;
  estimatedReduction: number;
  efficiency: number;
}

interface CarbonCreditEstimate {
  projectId: string;
  projectName: string;
  projectType: string;
  methodology?: string;
  standard?: string;
  calculation: {
    totalGeneration: number;
    totalGenerationInBaseUnits: number;
    calculationFactor: number;
    factorDescription: string;
    estimatedCredits: number;
    marketRate: number;
    estimatedValue: number;
    verificationRate: number;
    dataPoints: number;
    lastUpdated: string;
  };
}

type TimeFilter = "day" | "month" | "year";
type ViewMode = "analysis" | "data";
type ChartType = "bar" | "line";

function ProjectAnalyticsContent({ params }: ProjectAnalyticsPageProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [project, setProject] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [projectId, setProjectId] = useState<string>("");

  // Analytics state
  const [unitLogs, setUnitLogs] = useState<UnitLog[]>([]);
  const [carbonEstimate, setCarbonEstimate] = useState<CarbonCreditEstimate | null>(null);
  const [isAnalyticsLoading, setIsAnalyticsLoading] = useState(false);

  // UI state
  const [timeFilter, setTimeFilter] = useState<TimeFilter>("month");
  const [viewMode, setViewMode] = useState<ViewMode>("analysis");
  const [chartType, setChartType] = useState<ChartType>("bar");

  // Unwrap params Promise
  const resolvedParams = use(params);

  useEffect(() => {
    setProjectId(resolvedParams.projectId);
  }, [resolvedParams.projectId]);

  useEffect(() => {
    if (projectId && session?.user?.id) {
      fetchProject();
      fetchAnalyticsData();
    }
  }, [projectId, session?.user?.id]);

  useEffect(() => {
    if (projectId) {
      fetchAnalyticsData();
    }
  }, [timeFilter]);

  const fetchProject = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/projects/${projectId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch project");
      }

      const data = await response.json();
      setProject(data);
    } catch (error) {
      console.error("Error fetching project:", error);
      setError("Failed to load project");
      toast({
        title: "Error",
        description: "Failed to load project details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAnalyticsData = async () => {
    try {
      setIsAnalyticsLoading(true);

      // Fetch unit logs
      const unitLogsResponse = await fetch(`/api/projects/${projectId}/unit-logs?limit=10000`);
      if (unitLogsResponse.ok) {
        const unitLogsData = await unitLogsResponse.json();
        setUnitLogs(unitLogsData.unitLogs || []);
      }

      // Fetch carbon credit estimates
      const estimateResponse = await fetch(`/api/projects/${projectId}/carbon-credits/estimate`);
      if (estimateResponse.ok) {
        const estimateData = await estimateResponse.json();
        setCarbonEstimate(estimateData);
      }

    } catch (error) {
      console.error("Error fetching analytics data:", error);
      toast({
        title: "Error",
        description: "Failed to load analytics data",
        variant: "destructive",
      });
    } finally {
      setIsAnalyticsLoading(false);
    }
  };

  // Process analytics data based on time filter
  const processAnalyticsData = (): AnalyticsData[] => {
    if (!unitLogs.length || !project) return [];

    const isHybridProject = project.type === "RENEWABLE_ENERGY" &&
                           project.metadata?.projectSubType === "HYBRID_RENEWABLE";

    // Group data by time period
    const groupedData: Record<string, UnitLog[]> = {};

    unitLogs.forEach(log => {
      let dateKey: string;
      const logDate = new Date(log.logDate);

      switch (timeFilter) {
        case "day":
          dateKey = format(logDate, "yyyy-MM-dd");
          break;
        case "month":
          dateKey = format(logDate, "yyyy-MM");
          break;
        case "year":
          dateKey = format(logDate, "yyyy");
          break;
        default:
          dateKey = format(logDate, "yyyy-MM");
      }

      if (!groupedData[dateKey]) {
        groupedData[dateKey] = [];
      }
      groupedData[dateKey].push(log);
    });

    // Calculate analytics for each time period
    const analyticsData: AnalyticsData[] = Object.entries(groupedData).map(([dateKey, logs]) => {
      let actualGeneration: number;

      if (isHybridProject) {
        // For hybrid projects, only use outgoing generation
        const outgoingLogs = logs.filter(log => log.metadata?.generationType === 'OUTGOING');
        actualGeneration = outgoingLogs.reduce((sum, log) => sum + (Number(log.quantity) || 0), 0);
      } else {
        // For standard projects, sum all logs
        actualGeneration = logs.reduce((sum, log) => sum + (Number(log.quantity) || 0), 0);
      }

      // Convert to MWh
      const actualGenerationMWh = actualGeneration / 1000;

      // Calculate estimated generation (based on project's estimated annual reductions)
      const estimatedAnnualMWh = project.estimatedReductions || 1000;
      let estimatedGeneration: number;

      switch (timeFilter) {
        case "day":
          estimatedGeneration = estimatedAnnualMWh / 365;
          break;
        case "month":
          estimatedGeneration = estimatedAnnualMWh / 12;
          break;
        case "year":
          estimatedGeneration = estimatedAnnualMWh;
          break;
        default:
          estimatedGeneration = estimatedAnnualMWh / 12;
      }

      // Calculate carbon reductions (0.5 tCO2e per MWh)
      const emissionFactor = 0.5;
      const actualReduction = actualGenerationMWh * emissionFactor;
      const estimatedReduction = estimatedGeneration * emissionFactor;

      // Calculate efficiency
      const efficiency = estimatedGeneration > 0 ? (actualGenerationMWh / estimatedGeneration) * 100 : 0;

      return {
        date: dateKey,
        actualGeneration: Math.round(actualGenerationMWh * 100) / 100,
        estimatedGeneration: Math.round(estimatedGeneration * 100) / 100,
        actualReduction: Math.round(actualReduction * 100) / 100,
        estimatedReduction: Math.round(estimatedReduction * 100) / 100,
        efficiency: Math.round(efficiency * 100) / 100
      };
    });

    // Sort by date
    return analyticsData.sort((a, b) => a.date.localeCompare(b.date));
  };

  // Calculate summary metrics
  const calculateSummaryMetrics = () => {
    const data = processAnalyticsData();
    if (!data.length) {
      return {
        totalActualGeneration: 0,
        totalEstimatedGeneration: 0,
        totalActualReduction: 0,
        totalEstimatedReduction: 0,
        averageEfficiency: 0,
        generationVariance: 0
      };
    }

    const totalActualGeneration = data.reduce((sum, d) => sum + d.actualGeneration, 0);
    const totalEstimatedGeneration = data.reduce((sum, d) => sum + d.estimatedGeneration, 0);
    const totalActualReduction = data.reduce((sum, d) => sum + d.actualReduction, 0);
    const totalEstimatedReduction = data.reduce((sum, d) => sum + d.estimatedReduction, 0);
    const averageEfficiency = data.reduce((sum, d) => sum + d.efficiency, 0) / data.length;
    const generationVariance = totalEstimatedGeneration > 0 ?
      ((totalActualGeneration - totalEstimatedGeneration) / totalEstimatedGeneration) * 100 : 0;

    return {
      totalActualGeneration: Math.round(totalActualGeneration * 100) / 100,
      totalEstimatedGeneration: Math.round(totalEstimatedGeneration * 100) / 100,
      totalActualReduction: Math.round(totalActualReduction * 100) / 100,
      totalEstimatedReduction: Math.round(totalEstimatedReduction * 100) / 100,
      averageEfficiency: Math.round(averageEfficiency * 100) / 100,
      generationVariance: Math.round(generationVariance * 100) / 100
    };
  };

  // Get processed data and metrics
  const processedAnalyticsData = processAnalyticsData();
  const summaryMetrics = calculateSummaryMetrics();

  if (isLoading) {
    return (
      <ProtectedPage>
        <div className="flex items-center justify-center min-h-screen">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error || !project) {
    return (
      <ProtectedPage>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Project Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The project you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button onClick={() => router.push("/dashboard/projects")}>
              Back to Projects
            </Button>
          </div>
        </div>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/dashboard/projects/${projectId}`)}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Project
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div>
                <h1 className="text-2xl font-bold">{project.name}</h1>
                <p className="text-muted-foreground">Project Analytics</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={project.status === "ACTIVE" ? "default" : "secondary"}>
                {project.status}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchAnalyticsData}
                disabled={isAnalyticsLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isAnalyticsLoading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </div>

          {/* Controls */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              {/* Time Filter */}
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <Select value={timeFilter} onValueChange={(value: TimeFilter) => setTimeFilter(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="day">Day</SelectItem>
                    <SelectItem value="month">Month</SelectItem>
                    <SelectItem value="year">Year</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Chart Type Toggle */}
              <div className="flex items-center gap-2">
                <Button
                  variant={chartType === "bar" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setChartType("bar")}
                >
                  <BarChart className="h-4 w-4 mr-2" />
                  Bar Chart
                </Button>
                <Button
                  variant={chartType === "line" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setChartType("line")}
                >
                  <LineChart className="h-4 w-4 mr-2" />
                  Line Chart
                </Button>
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === "analysis" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("analysis")}
              >
                <Eye className="h-4 w-4 mr-2" />
                Analysis
              </Button>
              <Button
                variant={viewMode === "data" ? "default" : "outline"}
                size="sm"
                onClick={() => setViewMode("data")}
              >
                <Table className="h-4 w-4 mr-2" />
                Data View
              </Button>
            </div>
          </div>

          {/* Analytics Content */}
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Actual Generation</CardTitle>
                  <Zap className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {summaryMetrics.totalActualGeneration.toLocaleString()} MWh
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {summaryMetrics.generationVariance >= 0 ? (
                      <TrendingUp className="inline h-3 w-3 mr-1 text-green-600" />
                    ) : (
                      <TrendingDown className="inline h-3 w-3 mr-1 text-red-600" />
                    )}
                    <span className={summaryMetrics.generationVariance >= 0 ? "text-green-600" : "text-red-600"}>
                      {summaryMetrics.generationVariance >= 0 ? "+" : ""}{summaryMetrics.generationVariance.toFixed(1)}%
                    </span> vs estimated
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Carbon Reduction</CardTitle>
                  <Leaf className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {summaryMetrics.totalActualReduction.toLocaleString()} tCO2e
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Estimated: {summaryMetrics.totalEstimatedReduction.toLocaleString()} tCO2e
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Estimated Value</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${carbonEstimate?.calculation.estimatedValue.toLocaleString() || "0"}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {carbonEstimate?.calculation.estimatedCredits.toLocaleString() || "0"} credits estimated
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Avg Efficiency</CardTitle>
                  <Target className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {summaryMetrics.averageEfficiency.toFixed(1)}%
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Performance vs target
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            {viewMode === "analysis" ? (
              <div className="space-y-6">
                {/* Main Charts */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Generation Comparison Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Generation: Actual vs Estimated</CardTitle>
                      <CardDescription>
                        Comparison of actual power generation against estimated targets
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {isAnalyticsLoading ? (
                        <div className="h-80 flex items-center justify-center">
                          <Spinner />
                        </div>
                      ) : processedAnalyticsData.length > 0 ? (
                        <ResponsiveContainer width="100%" height={320}>
                          {chartType === "bar" ? (
                            <RechartsBarChart data={processedAnalyticsData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="date" />
                              <YAxis />
                              <Tooltip
                                formatter={(value, name) => [
                                  `${Number(value).toLocaleString()} MWh`,
                                  name === "Actual Generation" ? "Actual" : "Estimated"
                                ]}
                              />
                              <Legend />
                              <Bar dataKey="actualGeneration" fill="#3b82f6" name="Actual Generation" />
                              <Bar dataKey="estimatedGeneration" fill="#10b981" name="Estimated Generation" />
                            </RechartsBarChart>
                          ) : (
                            <RechartsLineChart data={processedAnalyticsData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="date" />
                              <YAxis />
                              <Tooltip
                                formatter={(value, name) => [
                                  `${Number(value).toLocaleString()} MWh`,
                                  name === "Actual Generation" ? "Actual" : "Estimated"
                                ]}
                              />
                              <Legend />
                              <Line type="monotone" dataKey="actualGeneration" stroke="#3b82f6" name="Actual Generation" strokeWidth={2} />
                              <Line type="monotone" dataKey="estimatedGeneration" stroke="#10b981" name="Estimated Generation" strokeWidth={2} />
                            </RechartsLineChart>
                          )}
                        </ResponsiveContainer>
                      ) : (
                        <div className="h-80 flex items-center justify-center">
                          <div className="text-center">
                            <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-medium mb-2">No Data Available</h3>
                            <p className="text-muted-foreground">
                              Add monitoring data to see generation analytics
                            </p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>

                  {/* Carbon Reduction Chart */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Carbon Reduction: Actual vs Estimated</CardTitle>
                      <CardDescription>
                        Comparison of actual carbon reduction against estimated targets
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {isAnalyticsLoading ? (
                        <div className="h-80 flex items-center justify-center">
                          <Spinner />
                        </div>
                      ) : processedAnalyticsData.length > 0 ? (
                        <ResponsiveContainer width="100%" height={320}>
                          {chartType === "bar" ? (
                            <RechartsBarChart data={processedAnalyticsData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="date" />
                              <YAxis />
                              <Tooltip
                                formatter={(value, name) => [
                                  `${Number(value).toLocaleString()} tCO2e`,
                                  name === "Actual Reduction" ? "Actual" : "Estimated"
                                ]}
                              />
                              <Legend />
                              <Bar dataKey="actualReduction" fill="#16a34a" name="Actual Reduction" />
                              <Bar dataKey="estimatedReduction" fill="#84cc16" name="Estimated Reduction" />
                            </RechartsBarChart>
                          ) : (
                            <RechartsLineChart data={processedAnalyticsData}>
                              <CartesianGrid strokeDasharray="3 3" />
                              <XAxis dataKey="date" />
                              <YAxis />
                              <Tooltip
                                formatter={(value, name) => [
                                  `${Number(value).toLocaleString()} tCO2e`,
                                  name === "Actual Reduction" ? "Actual" : "Estimated"
                                ]}
                              />
                              <Legend />
                              <Line type="monotone" dataKey="actualReduction" stroke="#16a34a" name="Actual Reduction" strokeWidth={2} />
                              <Line type="monotone" dataKey="estimatedReduction" stroke="#84cc16" name="Estimated Reduction" strokeWidth={2} />
                            </RechartsLineChart>
                          )}
                        </ResponsiveContainer>
                      ) : (
                        <div className="h-80 flex items-center justify-center">
                          <div className="text-center">
                            <Leaf className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                            <h3 className="text-lg font-medium mb-2">No Data Available</h3>
                            <p className="text-muted-foreground">
                              Add monitoring data to see carbon reduction analytics
                            </p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </div>

                {/* Efficiency Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle>Performance Efficiency</CardTitle>
                    <CardDescription>
                      Efficiency percentage (Actual / Estimated * 100)
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    {isAnalyticsLoading ? (
                      <div className="h-80 flex items-center justify-center">
                        <Spinner />
                      </div>
                    ) : processedAnalyticsData.length > 0 ? (
                      <ResponsiveContainer width="100%" height={320}>
                        {chartType === "bar" ? (
                          <RechartsBarChart data={processedAnalyticsData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip
                              formatter={(value) => [`${Number(value).toFixed(1)}%`, "Efficiency"]}
                            />
                            <Bar dataKey="efficiency" fill="#f59e0b" name="Efficiency %" />
                          </RechartsBarChart>
                        ) : (
                          <RechartsLineChart data={processedAnalyticsData}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis />
                            <Tooltip
                              formatter={(value) => [`${Number(value).toFixed(1)}%`, "Efficiency"]}
                            />
                            <Line type="monotone" dataKey="efficiency" stroke="#f59e0b" name="Efficiency %" strokeWidth={2} />
                          </RechartsLineChart>
                        )}
                      </ResponsiveContainer>
                    ) : (
                      <div className="h-80 flex items-center justify-center">
                        <div className="text-center">
                          <Target className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                          <h3 className="text-lg font-medium mb-2">No Data Available</h3>
                          <p className="text-muted-foreground">
                            Add monitoring data to see efficiency analytics
                          </p>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            ) : (
              /* Data View Mode */
              <Card>
                <CardHeader>
                  <CardTitle>Analytics Data Table</CardTitle>
                  <CardDescription>
                    Detailed breakdown of analytics data by {timeFilter}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {isAnalyticsLoading ? (
                    <div className="space-y-4">
                      {[...Array(5)].map((_, i) => (
                        <Skeleton key={i} className="h-12 w-full" />
                      ))}
                    </div>
                  ) : processedAnalyticsData.length > 0 ? (
                    <div className="overflow-x-auto">
                      <UITable>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Period</TableHead>
                            <TableHead className="text-right">Actual Generation (MWh)</TableHead>
                            <TableHead className="text-right">Estimated Generation (MWh)</TableHead>
                            <TableHead className="text-right">Actual Reduction (tCO2e)</TableHead>
                            <TableHead className="text-right">Estimated Reduction (tCO2e)</TableHead>
                            <TableHead className="text-right">Efficiency (%)</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {processedAnalyticsData.map((row, index) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">
                                {timeFilter === "day"
                                  ? format(new Date(row.date), "MMM d, yyyy")
                                  : timeFilter === "month"
                                  ? format(new Date(row.date + "-01"), "MMM yyyy")
                                  : row.date
                                }
                              </TableCell>
                              <TableCell className="text-right">
                                {row.actualGeneration.toLocaleString()}
                              </TableCell>
                              <TableCell className="text-right">
                                {row.estimatedGeneration.toLocaleString()}
                              </TableCell>
                              <TableCell className="text-right">
                                {row.actualReduction.toLocaleString()}
                              </TableCell>
                              <TableCell className="text-right">
                                {row.estimatedReduction.toLocaleString()}
                              </TableCell>
                              <TableCell className="text-right">
                                <span className={
                                  row.efficiency >= 100 ? "text-green-600" :
                                  row.efficiency >= 80 ? "text-yellow-600" : "text-red-600"
                                }>
                                  {row.efficiency.toFixed(1)}%
                                </span>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </UITable>
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Table className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                      <h3 className="text-lg font-medium mb-2">No Data Available</h3>
                      <p className="text-muted-foreground">
                        Add monitoring data to see detailed analytics
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}

export default function ProjectAnalyticsPage({ params }: ProjectAnalyticsPageProps) {
  return <ProjectAnalyticsContent params={params} />;
}
