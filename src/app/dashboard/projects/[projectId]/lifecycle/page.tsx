"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  ArrowLeft,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  Calendar,
  User,
  Plus,
  Edit,
  Eye,
  BarChart3
} from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { AnimatedButton } from "@/components/ui/animated-button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface LifecycleStage {
  id: string;
  name: string;
  status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "REJECTED";
  startDate?: string;
  endDate?: string;
  description?: string;
  documents?: Array<{
    id: string;
    name: string;
    url: string;
    type: string;
  }>;
  assignedTo?: {
    id: string;
    name: string;
    email: string;
  };
  notes?: string;
}

export default function ProjectLifecyclePage({ params }: { params: Promise<{ projectId: string }> }) {
  const router = useRouter();
  const { data: session } = useSession();
  const [project, setProject] = useState<any>(null);
  const [lifecycleStages, setLifecycleStages] = useState<LifecycleStage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Unwrap params Promise
  const resolvedParams = use(params);

  useEffect(() => {
    fetchProjectLifecycle();
  }, [resolvedParams.projectId]);

  const fetchProjectLifecycle = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Fetch project details
      const projectResponse = await fetch(`/api/projects/${resolvedParams.projectId}`);
      if (!projectResponse.ok) {
        throw new Error("Failed to fetch project details");
      }
      const projectData = await projectResponse.json();
      setProject(projectData.project);

      // Fetch lifecycle stages (mock data for now)
      const mockLifecycleStages: LifecycleStage[] = [
        {
          id: "1",
          name: "Project Design Document (PDD)",
          status: "COMPLETED",
          startDate: "2024-01-15",
          endDate: "2024-02-15",
          description: "Development of the Project Design Document outlining project methodology and baseline",
          assignedTo: {
            id: "user1",
            name: "John Smith",
            email: "<EMAIL>"
          }
        },
        {
          id: "2",
          name: "Validation",
          status: "COMPLETED",
          startDate: "2024-02-16",
          endDate: "2024-03-30",
          description: "Third-party validation of the project design and methodology",
          assignedTo: {
            id: "user2",
            name: "Validation Body Inc.",
            email: "<EMAIL>"
          }
        },
        {
          id: "3",
          name: "Registration",
          status: "IN_PROGRESS",
          startDate: "2024-04-01",
          description: "Registration of the project with the carbon standard registry",
          assignedTo: {
            id: "user3",
            name: "Registry Team",
            email: "<EMAIL>"
          }
        },
        {
          id: "4",
          name: "Implementation",
          status: "PENDING",
          description: "Implementation of the carbon reduction activities",
        },
        {
          id: "5",
          name: "Monitoring",
          status: "PENDING",
          description: "Ongoing monitoring and data collection for verification",
        },
        {
          id: "6",
          name: "Verification",
          status: "PENDING",
          description: "Third-party verification of emission reductions",
        },
        {
          id: "7",
          name: "Issuance",
          status: "PENDING",
          description: "Issuance of carbon credits by the registry",
        }
      ];

      setLifecycleStages(mockLifecycleStages);
    } catch (error) {
      console.error("Error fetching project lifecycle:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch project lifecycle");
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "IN_PROGRESS":
        return <Clock className="h-5 w-5 text-blue-600" />;
      case "REJECTED":
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return "bg-green-100 text-green-800";
      case "IN_PROGRESS":
        return "bg-blue-100 text-blue-800";
      case "REJECTED":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (isLoading) {
    return (
      <PageTransition>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-32" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>
          <div className="space-y-4">
            {Array(5).fill(0).map((_, i) => (
              <Skeleton key={i} className="h-32" />
            ))}
          </div>
        </div>
      </PageTransition>
    );
  }

  if (error || !project) {
    return (
      <PageTransition>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.push(`/dashboard/projects/${params.id}`)}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
          </div>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || "Project not found"}
            </AlertDescription>
          </Alert>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" onClick={() => router.push(`/dashboard/projects/${resolvedParams.projectId}`)}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Project
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Project Lifecycle</h1>
              <p className="text-muted-foreground">{project.name}</p>
            </div>
          </div>
          <AnimatedButton
            onClick={() => toast({ title: "Feature Coming Soon", description: "Lifecycle management features are being developed" })}
            animationVariant="buttonTap"
          >
            <Plus className="mr-2 h-4 w-4" />
            Add Stage
          </AnimatedButton>
        </div>

        <Tabs defaultValue="timeline" className="w-full">
          <TabsList>
            <TabsTrigger value="timeline">Timeline</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="reports">Reports</TabsTrigger>
          </TabsList>

          <TabsContent value="timeline" className="space-y-4">
            <div className="relative">
              {/* Timeline line */}
              <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-border"></div>
              
              {lifecycleStages.map((stage, index) => (
                <div key={stage.id} className="relative flex items-start space-x-4 pb-8">
                  {/* Timeline dot */}
                  <div className="relative z-10 flex items-center justify-center w-12 h-12 rounded-full bg-background border-2 border-border">
                    {getStatusIcon(stage.status)}
                  </div>
                  
                  {/* Stage content */}
                  <div className="flex-1 min-w-0">
                    <Card>
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{stage.name}</CardTitle>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className={getStatusColor(stage.status)}>
                              {stage.status.replace("_", " ")}
                            </Badge>
                            <AnimatedButton variant="ghost" size="sm" animationVariant="buttonTap">
                              <Edit className="h-4 w-4" />
                            </AnimatedButton>
                          </div>
                        </div>
                        {stage.description && (
                          <CardDescription>{stage.description}</CardDescription>
                        )}
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          {stage.startDate && (
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="font-medium">Start Date</p>
                                <p className="text-muted-foreground">
                                  {format(new Date(stage.startDate), "MMM d, yyyy")}
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {stage.endDate && (
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="font-medium">End Date</p>
                                <p className="text-muted-foreground">
                                  {format(new Date(stage.endDate), "MMM d, yyyy")}
                                </p>
                              </div>
                            </div>
                          )}
                          
                          {stage.assignedTo && (
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="font-medium">Assigned To</p>
                                <p className="text-muted-foreground">{stage.assignedTo.name}</p>
                              </div>
                            </div>
                          )}
                        </div>
                        
                        {stage.documents && stage.documents.length > 0 && (
                          <div className="mt-4">
                            <p className="font-medium mb-2">Documents</p>
                            <div className="flex flex-wrap gap-2">
                              {stage.documents.map((doc) => (
                                <Button key={doc.id} variant="outline" size="sm">
                                  <FileText className="mr-2 h-4 w-4" />
                                  {doc.name}
                                </Button>
                              ))}
                            </div>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <FileText className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Documents</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  Lifecycle documents will appear here as stages progress.
                </p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reports" className="space-y-4">
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <BarChart3 className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">No Reports</h3>
                <p className="text-muted-foreground text-center max-w-md">
                  Progress reports and analytics will be available here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </PageTransition>
  );
}
