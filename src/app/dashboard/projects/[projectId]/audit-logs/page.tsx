"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useParams } from 'next/navigation';
import { AlertTriangle } from 'lucide-react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { EnhancedAuditTrail } from '@/components/audit/enhanced-audit-trail';
import { AuditAnalytics } from '@/components/audit/audit-analytics';
import { AuditSearch } from '@/components/audit/audit-search';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface Project {
  id: string;
  name: string;
  description?: string;
  organizationId: string;
  spvId?: string;
}

export default function ProjectAuditLogsPage() {
  const { data: session } = useSession();
  const params = useParams();
  const projectId = params.projectId as string;
  const [activeTab, setActiveTab] = useState("audit-trail");
  const [project, setProject] = useState<Project | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch project details
  useEffect(() => {
    const fetchProject = async () => {
      if (!projectId || !session?.user) return;

      try {
        const response = await fetch(`/api/projects/${projectId}`);
        if (response.ok) {
          const projectData = await response.json();
          setProject(projectData);
        }
      } catch (error) {
        console.error('Error fetching project:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [projectId, session]);

  if (!session?.user) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Please log in to view audit logs.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-muted-foreground">Loading project details...</p>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Project Not Found</h3>
          <p className="text-muted-foreground">
            The requested project could not be found or you don't have access to it.
          </p>
        </div>
      </div>
    );
  }

  // Check access permissions
  const hasAccess = session.user.role === 'ADMIN' || 
                   session.user.role === 'ORGANIZATION_ADMIN' ||
                   (session.user as any).spvUser?.role === 'SPV_ADMIN' ||
                   (session.user as any).spvUser?.role === 'PROJECT_MANAGER';

  if (!hasAccess) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            You don't have permission to view project audit logs.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Project Audit Trail</h1>
        <p className="text-muted-foreground">
          Audit trail and analytics for project: {project.name}
        </p>
      </div>

      {/* Project Info Card */}
      <Card>
        <CardHeader>
          <CardTitle>Project Information</CardTitle>
          <CardDescription>
            Details about the project being audited
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <div className="text-sm font-medium text-muted-foreground">Project Name</div>
              <div className="text-lg font-semibold">{project.name}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Project ID</div>
              <div className="text-sm font-mono">{project.id}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-muted-foreground">Organization</div>
              <div className="text-sm">{project.organizationId}</div>
            </div>
          </div>
          {project.description && (
            <div className="mt-4">
              <div className="text-sm font-medium text-muted-foreground">Description</div>
              <div className="text-sm mt-1">{project.description}</div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="audit-trail">Audit Trail</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="search">Advanced Search</TabsTrigger>
        </TabsList>

        <TabsContent value="audit-trail" className="space-y-6">
          <EnhancedAuditTrail
            title="Project Audit Trail"
            description={`Real-time audit trail for project: ${project.name}`}
            organizationId={project.organizationId}
            spvId={project.spvId}
            projectId={project.id}
            showFilters={true}
            showExport={true}
            showRealtime={true}
            autoRefresh={false}
            refreshInterval={30000}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <AuditAnalytics
            organizationId={project.organizationId}
            timeRange="24h"
            autoRefresh={true}
          />
        </TabsContent>

        <TabsContent value="search" className="space-y-6">
          <AuditSearch
            organizationId={project.organizationId}
            placeholder={`Search audit logs for project: ${project.name}...`}
            onResultSelect={(result) => {
              console.log('Selected project audit log:', result);
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
