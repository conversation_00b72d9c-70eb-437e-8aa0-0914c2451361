import { Suspense } from "react";
import { notFound } from "next/navigation";
import { auth } from "@/lib/auth";
import { db } from "@/lib/db";
import { ProjectMonitoringDashboard } from "@/components/projects/monitoring/project-monitoring-dashboard";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

interface ProjectMonitoringPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getProject(projectId: string, userId: string) {
  try {
    const project = await db.project.findUnique({
      where: {
        id: projectId,
        organization: {
          members: {
            some: {
              userId: userId,
            },
          },
        },
      },
      include: {
        organization: {
          select: {
            id: true,
            name: true,
          },
        },
        unitLogs: {
          take: 10,
          orderBy: {
            logDate: "desc",
          },
          include: {
            logger: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
        baselineConfig: true,
        emissionCalculations: {
          take: 5,
          orderBy: {
            createdAt: "desc",
          },
        },
        apiIntegrations: {
          select: {
            id: true,
            name: true,
            apiType: true,
            status: true,
            lastSync: true,
            errorCount: true,
          },
        },
      },
    });

    return project;
  } catch (error) {
    console.error("Error fetching project:", error);
    return null;
  }
}

function ProjectMonitoringLoading() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="flex items-center gap-2">
          <Skeleton className="h-9 w-20" />
          <Skeleton className="h-9 w-24" />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-8 w-16 mb-2" />
              <Skeleton className="h-3 w-24" />
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-24" />
            <Skeleton className="h-4 w-40" />
          </CardHeader>
          <CardContent>
            <Skeleton className="h-64 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

async function ProjectMonitoringContent({ params }: ProjectMonitoringPageProps) {
  const session = await auth();

  if (!session?.user?.id) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          You must be logged in to view project monitoring data.
        </AlertDescription>
      </Alert>
    );
  }

  const resolvedParams = await params;
  const project = await getProject(resolvedParams.id, session.user.id);

  if (!project) {
    notFound();
  }

  // All project types now support monitoring
  return (
    <ProjectMonitoringDashboard
      projectId={project.id}
      project={{
        id: project.id,
        name: project.name,
        type: project.type,
        status: project.status,
        startDate: project.startDate?.toISOString() || "",
        estimatedReductions: project.estimatedReductions || 0,
        metadata: project.metadata as any,
      }}
    />
  );
}

export default function ProjectMonitoringPage({ params }: ProjectMonitoringPageProps) {
  return (
    <Suspense fallback={<ProjectMonitoringLoading />}>
      <ProjectMonitoringContent params={params} />
    </Suspense>
  );
}

export async function generateMetadata({ params }: ProjectMonitoringPageProps) {
  const session = await auth();

  if (!session?.user?.id) {
    return {
      title: "Project Monitoring - CarbonX",
      description: "Monitor project performance and data collection",
    };
  }

  const resolvedParams = await params;
  const project = await getProject(resolvedParams.id, session.user.id);

  if (!project) {
    return {
      title: "Project Not Found - CarbonX",
      description: "The requested project could not be found",
    };
  }

  return {
    title: `${project.name} - Monitoring - CarbonX`,
    description: `Monitor power generation and emission reductions for ${project.name}`,
  };
}
