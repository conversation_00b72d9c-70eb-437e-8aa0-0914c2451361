"use client";

import { use } from "react";
import { useRouter } from "next/navigation";
import UnifiedDataEntry from "@/app/spv/data-entry/unified-data-entry";

interface DataEntryPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function DataEntryPage({ params }: DataEntryPageProps) {
  const router = useRouter();
  const resolvedParams = use(params);

  const handleBack = () => {
    router.push(`/dashboard/projects/${resolvedParams.id}?tab=monitoring`);
  };

  return (
    <div className="container mx-auto py-6">
      <UnifiedDataEntry
        context="dashboard"
        projectId={resolvedParams.id}
        onBack={handleBack}
      />
    </div>
  );
}
