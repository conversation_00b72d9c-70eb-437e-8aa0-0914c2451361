"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  ArrowLeft,
  FileText,
  Shield,
  CheckCircle,
  AlertCircle,
  Clock,
  User,
  Calendar,
  Filter,
  Download
} from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Spinner } from "@/components/ui/spinner";
import { Input } from "@/components/ui/input";
import { AuditLogTable, AuditLogFilters, AuditLog } from "@/components/projects/audit/audit-log-table";

interface ProjectAuditPageProps {
  params: Promise<{ projectId: string }>;
}

function ProjectAuditContent({ params }: ProjectAuditPageProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const [project, setProject] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [projectId, setProjectId] = useState<string>("");
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [auditPagination, setAuditPagination] = useState({
    total: 0,
    page: 1,
    limit: 50,
    totalPages: 1,
    hasMore: false,
  });
  const [auditSummary, setAuditSummary] = useState<Record<string, number>>({});
  const [auditLoading, setAuditLoading] = useState(false);
  const [auditFilters, setAuditFilters] = useState<AuditLogFilters>({});

  // Unwrap params Promise
  const resolvedParams = use(params);

  useEffect(() => {
    setProjectId(resolvedParams.projectId);
  }, [resolvedParams.projectId]);

  useEffect(() => {
    if (projectId && session?.user?.id) {
      fetchProject();
      fetchAuditLogs();
    }
  }, [projectId, session?.user?.id]);

  const fetchProject = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/projects/${projectId}`);

      if (!response.ok) {
        throw new Error("Failed to fetch project");
      }

      const data = await response.json();
      setProject(data);
    } catch (error) {
      console.error("Error fetching project:", error);
      setError("Failed to load project");
      toast({
        title: "Error",
        description: "Failed to load project details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAuditLogs = async (filters: AuditLogFilters = {}, page: number = 1) => {
    try {
      setAuditLoading(true);

      const params = new URLSearchParams({
        page: page.toString(),
        limit: auditPagination.limit.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined && value !== "")
        ),
      });

      const response = await fetch(`/api/projects/${projectId}/audit-logs?${params}`);

      if (!response.ok) {
        throw new Error("Failed to fetch audit logs");
      }

      const data = await response.json();
      setAuditLogs(data.auditLogs || []);
      setAuditPagination(data.pagination || auditPagination);
      setAuditSummary(data.summary || {});
    } catch (error) {
      console.error("Error fetching audit logs:", error);
      toast({
        title: "Error",
        description: "Failed to load audit logs",
        variant: "destructive",
      });
    } finally {
      setAuditLoading(false);
    }
  };

  const handleFilterChange = (filters: AuditLogFilters) => {
    setAuditFilters(filters);
    fetchAuditLogs(filters, 1);
  };

  const handlePageChange = (page: number) => {
    fetchAuditLogs(auditFilters, page);
  };

  const handleExport = async () => {
    try {
      const params = new URLSearchParams({
        ...Object.fromEntries(
          Object.entries(auditFilters).filter(([_, value]) => value !== undefined && value !== "")
        ),
        export: "true",
      });

      const response = await fetch(`/api/projects/${projectId}/audit-logs/export?${params}`);

      if (!response.ok) {
        throw new Error("Failed to export audit logs");
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `project-${projectId}-audit-logs.csv`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "Success",
        description: "Audit logs exported successfully",
      });
    } catch (error) {
      console.error("Error exporting audit logs:", error);
      toast({
        title: "Error",
        description: "Failed to export audit logs",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <div className="flex items-center justify-center min-h-screen">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error || !project) {
    return (
      <ProtectedPage>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-2xl font-bold mb-2">Project Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The project you're looking for doesn't exist or you don't have access to it.
            </p>
            <Button onClick={() => router.push("/dashboard/projects")}>
              Back to Projects
            </Button>
          </div>
        </div>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="container mx-auto px-4 py-6 max-w-7xl">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/dashboard/projects/${projectId}`)}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Project
              </Button>
              <Separator orientation="vertical" className="h-6" />
              <div>
                <h1 className="text-2xl font-bold">{project.name}</h1>
                <p className="text-muted-foreground">Audit Trail & Compliance</p>
              </div>
            </div>
            <Badge variant={project.status === "ACTIVE" ? "default" : "secondary"}>
              {project.status}
            </Badge>
          </div>

          {/* Audit Content */}
          <div className="space-y-6">
            {/* Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Audit Events</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{auditPagination.total}</div>
                  <p className="text-xs text-muted-foreground">
                    Total audit entries
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Data Entries</CardTitle>
                  <Shield className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Object.entries(auditSummary)
                      .filter(([key]) => key.startsWith("DATA_ENTRY_"))
                      .reduce((sum, [, count]) => sum + count, 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Data entry events
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Approvals</CardTitle>
                  <CheckCircle className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Object.entries(auditSummary)
                      .filter(([key]) => key.includes("APPROVED"))
                      .reduce((sum, [, count]) => sum + count, 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Approved actions
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">API Events</CardTitle>
                  <Clock className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {Object.entries(auditSummary)
                      .filter(([key]) => key.startsWith("API_"))
                      .reduce((sum, [, count]) => sum + count, 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    API integration events
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Audit Log Table */}
            <AuditLogTable
              auditLogs={auditLogs}
              pagination={auditPagination}
              summary={auditSummary}
              onPageChange={handlePageChange}
              onFilterChange={handleFilterChange}
              onExport={handleExport}
              isLoading={auditLoading}
            />


          </div>
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}

export default function ProjectAuditPage({ params }: ProjectAuditPageProps) {
  return <ProjectAuditContent params={params} />;
}
