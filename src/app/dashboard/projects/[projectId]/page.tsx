"use client";

import { useState, useEffect, use } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSession } from "next-auth/react";
import {
  ArrowLeft,
  FileText,
  BarChart3,
  Edit,
  Plus,
  ExternalLink,
  AlertCircle
} from "lucide-react";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import { PageTransition } from "@/components/ui/page-transition";
import { ProtectedPage } from "@/components/auth/protected-page";
import { Spinner } from "@/components/ui/spinner";
import { ProjectCreditLinking } from "@/components/projects/project-credit-linking";
import { MonitoringDataTable } from "@/components/projects/monitoring/monitoring-data-table";
import { EstimatedCarbonCredits } from "@/components/projects/estimated-carbon-credits";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function ProjectDetailPage({ params }: { params: Promise<{ projectId: string }> }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { data: session } = useSession();
  const [project, setProject] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [showCreditLinking, setShowCreditLinking] = useState(false);
  const [availableCredits, setAvailableCredits] = useState([]);
  const [projectId, setProjectId] = useState<string>("");
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Unwrap params Promise
  const resolvedParams = use(params);

  useEffect(() => {
    // Set project ID from resolved params
    setProjectId(resolvedParams.projectId);
  }, [resolvedParams.projectId]);

  // Handle tab parameter from URL
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['overview', 'monitoring', 'analytics', 'documents', 'audit'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  // Handle tab change and update URL
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);

    // Update URL with new tab parameter using Next.js router
    const newSearchParams = new URLSearchParams(searchParams);
    if (newTab === 'overview') {
      // Remove tab parameter for overview (default tab)
      newSearchParams.delete('tab');
    } else {
      newSearchParams.set('tab', newTab);
    }

    const queryString = newSearchParams.toString();
    const newUrl = queryString ? `?${queryString}` : '';

    // Update URL without triggering a page reload
    router.replace(`/dashboard/projects/${projectId}${newUrl}`, { scroll: false });
  };

  useEffect(() => {
    if (projectId && session) {
      fetchProjectDetails();
    }
  }, [projectId, session]);

  const fetchProjectDetails = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use organization-specific API for organization admins, with fallback
      let apiUrl = session?.user?.role === "ORGANIZATION_ADMIN"
        ? `/api/organizations/projects/${projectId}`
        : `/api/projects/${projectId}`;

      console.log("Fetching project from:", apiUrl);
      console.log("Session info:", {
        userId: session?.user?.id,
        userRole: session?.user?.role,
        organizationId: session?.user?.organizationId,
      });

      let response = await fetch(apiUrl);
      console.log("Initial response:", {
        url: apiUrl,
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
      });

      // If organization API fails, try the general API as fallback
      if (!response.ok && session?.user?.role === "ORGANIZATION_ADMIN") {
        console.log("Organization API failed, trying general API");
        apiUrl = `/api/projects/${projectId}`;
        response = await fetch(apiUrl);
        console.log("Fallback response:", {
          url: apiUrl,
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
        });
      }

      // Check if response is empty or not JSON
      const contentType = response.headers.get("content-type");
      const responseText = await response.text();

      console.log("API Response Debug:", {
        finalUrl: apiUrl,
        userRole: session?.user?.role,
        status: response.status,
        statusText: response.statusText,
        contentType,
        responseLength: responseText.length,
        responsePreview: responseText.substring(0, 200),
      });

      if (!response.ok) {
        let errorMessage = "Failed to fetch project details";
        try {
          if (responseText && contentType?.includes("application/json")) {
            const errorData = JSON.parse(responseText);
            errorMessage = errorData.error || errorMessage;
          } else {
            errorMessage = `Server error: ${response.status} ${response.statusText}`;
          }
        } catch (parseError) {
          errorMessage = `Server error: ${response.status} - ${responseText.substring(0, 100)}`;
        }
        throw new Error(errorMessage);
      }

      if (!responseText) {
        throw new Error("Empty response from server");
      }

      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error("JSON Parse Error:", parseError);
        throw new Error("Invalid JSON response from server");
      }

      // Handle different response formats
      const projectData = data.project || data.data?.project;

      if (!projectData) {
        throw new Error("Project data not found in response");
      }

      setProject(projectData);
    } catch (error) {
      console.error("Error fetching project details:", error);
      setError(error instanceof Error ? error.message : "An error occurred");
      toast({
        title: "Error",
        description: "Failed to fetch project details",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const fetchAvailableCredits = async () => {
    try {
      const response = await fetch(`/api/carbon-credits?status=VERIFIED&verificationStatus=VERIFIED`);

      if (!response.ok) {
        throw new Error("Failed to fetch available carbon credits");
      }

      const data = await response.json();
      setAvailableCredits(data.carbonCredits || []);
    } catch (error) {
      console.error("Error fetching available credits:", error);
      toast({
        title: "Error",
        description: "Failed to fetch available carbon credits",
        variant: "destructive",
      });
    }
  };

  const handleLinkCredits = () => {
    fetchAvailableCredits();
    setShowCreditLinking(true);
  };

  const handleSaveCreditLinks = async (toLink, toUnlink) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/carbon-credits/link`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ toLink, toUnlink }),
      });

      if (!response.ok) {
        throw new Error("Failed to update credit links");
      }

      toast({
        title: "Success",
        description: "Carbon credit links updated successfully",
      });

      setShowCreditLinking(false);
      fetchProjectDetails();
    } catch (error) {
      console.error("Error updating credit links:", error);
      toast({
        title: "Error",
        description: "Failed to update credit links",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return "Not specified";
    return format(new Date(dateString), "MMM d, yyyy");
  };

  const getStatusBadgeVariant = (status) => {
    const variants = {
      PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
      ACTIVE: "bg-green-100 text-green-800 border-green-200",
      COMPLETED: "bg-blue-100 text-blue-800 border-blue-200",
      SUSPENDED: "bg-red-100 text-red-800 border-red-200",
      CANCELLED: "bg-gray-100 text-gray-800 border-gray-200",
    };

    return variants[status] || "";
  };

  const getVerificationStatusBadgeVariant = (status) => {
    const variants = {
      PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
      VERIFIED: "bg-green-100 text-green-800 border-green-200",
      REJECTED: "bg-red-100 text-red-800 border-red-200",
      EXPIRED: "bg-gray-100 text-gray-800 border-gray-200",
    };

    return variants[status] || "";
  };

  if (isLoading) {
    return (
      <ProtectedPage>
        <div className="flex items-center justify-center h-full">
          <Spinner size="lg" />
        </div>
      </ProtectedPage>
    );
  }

  if (error || !project) {
    return (
      <ProtectedPage>
        <div className="container mx-auto py-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || "Project not found"}
            </AlertDescription>
          </Alert>
          <div className="mt-4">
            <Button variant="outline" onClick={() => router.push("/dashboard/projects")}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Projects
            </Button>
          </div>
        </div>
      </ProtectedPage>
    );
  }

  return (
    <ProtectedPage>
      <PageTransition>
        <div className="space-y-6">
          {/* Header with project name and back button */}
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1 min-w-0">
              <h1 className="text-3xl font-bold tracking-tight truncate">{project.name}</h1>
               <p className="text-muted-foreground mt-1">
                {project.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())} Project
              </p>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline" className={getVerificationStatusBadgeVariant(project.verificationStatus)}>
                  {project.verificationStatus}
                </Badge>
                <Badge variant="outline" className={getStatusBadgeVariant(project.status)}>
                  {project.status}
                </Badge>
              </div>
            </div>

            <div className="ml-4 flex-shrink-0">
              <Button variant="outline" onClick={() => router.push("/dashboard/projects")}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Projects
              </Button>
            </div>
          </div>



          {showCreditLinking ? (
            <ProjectCreditLinking
              project={project}
              availableCredits={availableCredits}
              onSave={handleSaveCreditLinks}
              onCancel={() => setShowCreditLinking(false)}
            />
          ) : (
            <Tabs value={activeTab} onValueChange={handleTabChange}>
              <TabsList className="grid w-full grid-cols-5 md:w-auto md:grid-cols-none md:flex">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
                <TabsTrigger value="analytics">Analytics</TabsTrigger>
                <TabsTrigger value="documents">Documents</TabsTrigger>
                {/* <TabsTrigger value="audit">Audit</TabsTrigger> */}
                {/* <TabsTrigger value="credits">Carbon Credits</TabsTrigger> */}
                {/* <TabsTrigger value="financials">Financials</TabsTrigger> */}
              </TabsList>

              <div className="mt-6">
                <TabsContent value="overview" className="space-y-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle>Project Details</CardTitle>
                      <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/projects/${projectId}/edit`)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Project
                      </Button>
                    </CardHeader>
                    <CardContent className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                          <p className="font-normal">{project.description || "No description provided"}</p>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Project Type</h3>
                            <p>{project.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Organization</h3>
                            <p>{project.organization?.name || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Start Date</h3>
                            <p>{formatDate(project.startDate)}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">End Date</h3>
                            <p>{formatDate(project.endDate)}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Location</h3>
                            <p>{project.location || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Country</h3>
                            <p>{project.country || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Standard</h3>
                            <p>{project.standard || "Not specified"}</p>
                          </div>

                          <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-1">Methodology</h3>
                            <p>{project.methodology || "Not specified"}</p>
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-2">Emissions Reductions</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-muted-foreground">Estimated Reductions</p>
                            <p className="text-lg font-medium">{project.estimatedReductions?.toLocaleString() || "Not specified"} tCO2e</p>
                          </div>

                          <div>
                            <p className="text-sm text-muted-foreground">Actual Reductions</p>
                            <p className="text-lg font-medium">{project.actualReductions?.toLocaleString() || "Not specified"} tCO2e</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* <TabsContent value="credits" className="space-y-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle>Carbon Credits</CardTitle>
                      <Button variant="outline" size="sm" onClick={handleLinkCredits}>
                        <Plus className="mr-2 h-4 w-4" />
                        Manage Credits
                      </Button>
                    </CardHeader>
                    <CardContent>
                      {project.carbonCredits && project.carbonCredits.length > 0 ? (
                        <div className="space-y-4">
                          {project.carbonCredits.map((credit) => (
                            <div key={credit.id} className="flex items-center justify-between p-4 border rounded-md">
                              <div>
                                <p className="font-medium">{credit.name}</p>
                                <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                                  <span>Vintage: {credit.vintage}</span>
                                  <span>Quantity: {credit.quantity.toLocaleString()} tCO2e</span>
                                  <span>Available: {credit.availableQuantity.toLocaleString()} tCO2e</span>
                                </div>
                              </div>
                              <Button variant="ghost" size="sm" onClick={() => router.push(`/dashboard/carbon-credits/${credit.id}`)}>
                                View Details
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <p className="text-muted-foreground mb-4">No carbon credits linked to this project yet</p>
                          <Button variant="outline" onClick={handleLinkCredits}>
                            <Plus className="mr-2 h-4 w-4" />
                            Link Carbon Credits
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent> */}

                <TabsContent value="documents" className="space-y-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle>Project Documents</CardTitle>
                      <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/projects/${projectId}/documents/upload`)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Upload Document
                      </Button>
                    </CardHeader>
                    <CardContent>
                      {project.documents && project.documents.length > 0 ? (
                        <div className="space-y-4">
                          {project.documents.map((document) => (
                            <div key={document.id} className="flex items-center justify-between p-4 border rounded-md">
                              <div className="flex items-center gap-3">
                                <FileText className="h-5 w-5 text-muted-foreground" />
                                <div>
                                  <p className="font-medium">{document.name}</p>
                                  <p className="text-sm text-muted-foreground">
                                    {document.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                                  </p>
                                </div>
                              </div>
                              <Button variant="ghost" size="sm" asChild>
                                <a href={document.url} target="_blank" rel="noopener noreferrer">
                                  <ExternalLink className="mr-2 h-4 w-4" />
                                  View
                                </a>
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-6">
                          <p className="text-muted-foreground mb-4">No documents uploaded for this project yet</p>
                          <Button variant="outline" onClick={() => router.push(`/dashboard/projects/${projectId}/documents/upload`)}>
                            <Plus className="mr-2 h-4 w-4" />
                            Upload Document
                          </Button>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                {/* <TabsContent value="financials" className="space-y-6">
                  <Card>
                    <CardHeader className="flex flex-row items-center justify-between">
                      <CardTitle>Financial Metrics</CardTitle>
                      <Button variant="outline" size="sm" onClick={() => router.push(`/dashboard/projects/${projectId}/lifecycle`)}>
                        <BarChart3 className="mr-2 h-4 w-4" />
                        Project Lifecycle
                      </Button>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="p-4 border rounded-md">
                          <p className="text-sm text-muted-foreground">Budget</p>
                          <p className="text-xl font-medium">${project.budget?.toLocaleString() || "Not specified"}</p>
                        </div>

                        <div className="p-4 border rounded-md">
                          <p className="text-sm text-muted-foreground">ROI</p>
                          <p className="text-xl font-medium">{project.roi ? `${project.roi}%` : "Not specified"}</p>
                        </div>

                        <div className="p-4 border rounded-md">
                          <p className="text-sm text-muted-foreground">Carbon Value</p>
                          <p className="text-xl font-medium">
                            {project.carbonCredits && project.carbonCredits.length > 0
                              ? `$${project.carbonCredits.reduce((sum, credit) => sum + (credit.price * credit.quantity), 0).toLocaleString()}`
                              : "Not available"}
                          </p>
                        </div>
                      </div>

                      {project.financialMetrics && project.financialMetrics.length > 0 ? (
                        <div className="mt-6">
                          <h3 className="text-sm font-medium mb-3">Financial History</h3>
                          <div className="space-y-4">
                            {project.financialMetrics.map((metric) => (
                              <div key={metric.id} className="flex items-center justify-between p-4 border rounded-md">
                                <div>
                                  <p className="font-medium">{metric.name}</p>
                                  <div className="flex items-center gap-4 mt-1 text-sm text-muted-foreground">
                                    <span>{metric.period}</span>
                                    <span>{formatDate(metric.startDate)} - {formatDate(metric.endDate)}</span>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="font-medium">{metric.currency} {metric.value.toLocaleString()}</p>
                                  {metric.changePercent && (
                                    <p className={`text-sm ${metric.changePercent > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                      {metric.changePercent > 0 ? '+' : ''}{metric.changePercent}%
                                    </p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      ) : (
                        <div className="text-center py-6 mt-4">
                          <p className="text-muted-foreground">No financial metrics available for this project</p>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent> */}

                <TabsContent value="analytics" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Project Analytics</CardTitle>
                      <CardDescription>
                        Comprehensive analytics and insights for this project
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-12">
                        <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                          <BarChart3 className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium mb-2">Advanced Analytics Available</h3>
                        <p className="text-muted-foreground max-w-md mx-auto mb-6">
                          View detailed analytics including generation vs estimates, carbon reduction tracking,
                          and performance efficiency metrics.
                        </p>
                        <Button onClick={() => router.push(`/dashboard/projects/${projectId}/analytics`)}>
                          <BarChart3 className="mr-2 h-4 w-4" />
                          Open Analytics Dashboard
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="audit" className="space-y-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Audit Trail</CardTitle>
                      <CardDescription>
                        Complete audit history and compliance tracking for this project
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="text-center py-12">
                        <div className="mx-auto w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mb-4">
                          <FileText className="h-8 w-8 text-primary" />
                        </div>
                        <h3 className="text-lg font-medium mb-2">View Full Audit Trail</h3>
                        <p className="text-muted-foreground max-w-md mx-auto mb-6">
                          Access comprehensive audit trails, compliance tracking, verification records,
                          and regulatory reporting for this project.
                        </p>
                        <Button
                          onClick={() => router.push(`/dashboard/projects/${projectId}/audit`)}
                          className="flex items-center gap-2"
                        >
                          <FileText className="h-4 w-4" />
                          Open Audit Page
                          <ExternalLink className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="monitoring" className="space-y-6">
                  {/* Add Data Button */}
                  <div className="flex justify-between items-center">
                    <div>
                      <h2 className="text-2xl font-bold tracking-tight">Monitoring Data</h2>
                      <p className="text-muted-foreground">
                        View and manage all monitoring data entries for this project
                      </p>
                    </div>
                    <Button onClick={() => router.push(`/dashboard/projects/${projectId}/monitoring/data-entry`)}>
                      <Plus className="mr-2 h-4 w-4" />
                      Add Data
                    </Button>
                  </div>

                  {/* Estimated Carbon Credits Card */}
                  <EstimatedCarbonCredits
                    projectId={projectId}
                    refreshTrigger={refreshTrigger}
                  />

                  {/* Monitoring Data Table */}
                  <MonitoringDataTable
                    projectId={projectId}
                    projectType={project.type}
                    projectSubType={project.metadata?.projectSubType}
                    refreshTrigger={refreshTrigger}
                  />
                </TabsContent>
              </div>
            </Tabs>
          )}
        </div>
      </PageTransition>
    </ProtectedPage>
  );
}
