import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { AuditLogClient } from "@/components/rbac/audit-log-client";

export const metadata: Metadata = {
  title: "Audit Log | RBAC",
  description: "View access and permission changes audit trail",
};

export default async function AuditLogPage() {
  const session = await auth();

  if (!session?.user) {
    redirect("/login");
  }

  // Check if user is organization admin
  if (session.user.role !== "ORGANIZATION_ADMIN") {
    redirect("/dashboard");
  }

  return <AuditLogClient />;
}
