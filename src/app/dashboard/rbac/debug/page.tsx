"use client";

import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  User, 
  Shield, 
  Key, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  AlertTriangle,
} from "lucide-react";
import { toast } from "sonner";

interface DebugInfo {
  session: any;
  rbacStatus: any;
  testResults: any;
}

export default function RBACDebugPage() {
  const { data: session, status } = useSession();
  const [debugInfo, setDebugInfo] = useState<DebugInfo>({
    session: null,
    rbacStatus: null,
    testResults: null,
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setDebugInfo(prev => ({
      ...prev,
      session: {
        status,
        user: session?.user,
        expires: session?.expires,
      },
    }));
  }, [session, status]);

  const checkRBACStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/rbac/init');
      const data = await response.json();
      setDebugInfo(prev => ({
        ...prev,
        rbacStatus: data,
      }));
    } catch (error) {
      console.error('Error checking RBAC status:', error);
      toast.error('Failed to check RBAC status');
    } finally {
      setLoading(false);
    }
  };

  const runTests = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/rbac/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ cleanup: true }),
      });
      const data = await response.json();
      setDebugInfo(prev => ({
        ...prev,
        testResults: data,
      }));
      
      if (data.success) {
        toast.success(`Tests completed: ${data.summary.passed}/${data.summary.total} passed`);
      } else {
        toast.error('Tests failed');
      }
    } catch (error) {
      console.error('Error running tests:', error);
      toast.error('Failed to run tests');
    } finally {
      setLoading(false);
    }
  };

  const initializeRBAC = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/rbac/init', {
        method: 'POST',
      });
      const data = await response.json();
      
      if (data.success) {
        toast.success('RBAC system initialized successfully');
        checkRBACStatus();
      } else {
        toast.error('Failed to initialize RBAC system');
      }
    } catch (error) {
      console.error('Error initializing RBAC:', error);
      toast.error('Failed to initialize RBAC system');
    } finally {
      setLoading(false);
    }
  };

  const isOrgAdmin = session?.user?.role === "ORGANIZATION_ADMIN";
  const isAdmin = session?.user?.role === "ADMIN";

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">RBAC Debug Console</h1>
          <p className="text-muted-foreground">
            Debug and test the RBAC system implementation
          </p>
        </div>
        <div className="flex gap-2">
          <Button onClick={checkRBACStatus} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Check Status
          </Button>
          {isAdmin && (
            <>
              <Button onClick={initializeRBAC} disabled={loading} variant="outline">
                <Shield className="h-4 w-4 mr-2" />
                Initialize RBAC
              </Button>
              <Button onClick={runTests} disabled={loading} variant="outline">
                <CheckCircle className="h-4 w-4 mr-2" />
                Run Tests
              </Button>
            </>
          )}
        </div>
      </div>

      {/* Session Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Session Information
          </CardTitle>
          <CardDescription>
            Current user session and authentication status
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium">Status</label>
              <div className="flex items-center gap-2 mt-1">
                {status === 'authenticated' ? (
                  <CheckCircle className="h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500" />
                )}
                <Badge variant={status === 'authenticated' ? 'default' : 'destructive'}>
                  {status}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium">User Role</label>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={isOrgAdmin || isAdmin ? 'default' : 'secondary'}>
                  {session?.user?.role || 'None'}
                </Badge>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium">RBAC Access</label>
              <div className="flex items-center gap-2 mt-1">
                {isOrgAdmin ? (
                  <Badge variant="default" className="bg-green-500">
                    Organization Admin
                  </Badge>
                ) : isAdmin ? (
                  <Badge variant="default" className="bg-blue-500">
                    Platform Admin
                  </Badge>
                ) : (
                  <Badge variant="outline">
                    No RBAC Access
                  </Badge>
                )}
              </div>
            </div>
          </div>

          {session?.user && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">User Details</h4>
              <pre className="text-xs overflow-auto">
                {JSON.stringify(session.user, null, 2)}
              </pre>
            </div>
          )}

          {!isOrgAdmin && !isAdmin && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-600" />
                <span className="font-medium text-yellow-800">Access Denied</span>
              </div>
              <p className="text-sm text-yellow-700 mt-1">
                You need ORGANIZATION_ADMIN role to access RBAC features. 
                Contact your administrator to get the appropriate role assigned.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
