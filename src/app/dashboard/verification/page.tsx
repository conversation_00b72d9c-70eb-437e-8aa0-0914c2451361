/**
 * Organization Admin Verification Page
 * 
 * Provides verification interface for organization admins to review and approve
 * data entries verified by SPV admins before submitting to VVB
 */

"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDes<PERSON>,
  Di<PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  CheckCircle,
  XCircle,
  Clock,
  Send,
  FileText,
  Calendar,
  Building,
  User,
  Search,
  Filter,
  Eye,
  Award,
  AlertCircle,
} from "lucide-react";
import { toast } from "sonner";
import { VerificationAuditTrail } from "@/components/verification/verification-audit-trail";

interface UnitLogEntry {
  id: string;
  logDate: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  verificationStatus: string;
  spvApprovedBy?: string;
  spvApprovedAt?: string;
  spvApprovalNotes?: string;
  orgApprovedBy?: string;
  orgApprovedAt?: string;
  orgApprovalNotes?: string;
  notes?: string;
  createdAt: string;
  project: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  logger: {
    id: string;
    name: string;
    email: string;
    role: string;
  };
  verificationLogs: Array<{
    id: string;
    fromStatus: string;
    toStatus: string;
    verificationNotes?: string;
    createdAt: string;
    verifier: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
  }>;
}

interface VerificationStats {
  SUBMITTED_TO_ORG_ADMIN: number;
  ORG_APPROVED: number;
  ORG_REJECTED: number;
  SUBMITTED_TO_VVB: number;
  VVB_VERIFIED: number;
  VVB_REJECTED: number;
}

export default function OrganizationVerificationPage() {
  const { data: session } = useSession();
  const [activeTab, setActiveTab] = useState("pending");
  const [unitLogs, setUnitLogs] = useState<UnitLogEntry[]>([]);
  const [stats, setStats] = useState<VerificationStats>({
    SUBMITTED_TO_ORG_ADMIN: 0,
    ORG_APPROVED: 0,
    ORG_REJECTED: 0,
    SUBMITTED_TO_VVB: 0,
    VVB_VERIFIED: 0,
    VVB_REJECTED: 0,
  });
  const [loading, setLoading] = useState(true);
  const [selectedEntries, setSelectedEntries] = useState<string[]>([]);
  const [actionDialog, setActionDialog] = useState<{
    isOpen: boolean;
    action: string | null;
  }>({ isOpen: false, action: null });
  const [actionNotes, setActionNotes] = useState("");
  const [rejectionReason, setRejectionReason] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [projectFilter, setProjectFilter] = useState("all");

  // Check if user is organization admin
  if (session?.user?.role !== "ORGANIZATION_ADMIN") {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
          <p className="text-muted-foreground">
            Only organization administrators can access the verification dashboard.
          </p>
        </div>
      </div>
    );
  }

  const getStatusForTab = (tab: string) => {
    switch (tab) {
      case "pending":
        return "SUBMITTED_TO_ORG_ADMIN";
      case "approved":
        return "ORG_APPROVED";
      case "rejected":
        return "ORG_REJECTED";
      case "submitted":
        return "SUBMITTED_TO_VVB";
      case "vvb-verified":
        return "VVB_VERIFIED";
      case "vvb-rejected":
        return "VVB_REJECTED";
      default:
        return "SUBMITTED_TO_ORG_ADMIN";
    }
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      const status = getStatusForTab(activeTab);
      const response = await fetch(`/api/organizations/verification/data?status=${status}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch verification data");
      }

      const data = await response.json();
      setUnitLogs(data.data.unitLogs || []);
      setStats(data.data.statistics || {});
    } catch (error) {
      console.error("Error fetching verification data:", error);
      toast.error("Failed to load verification data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [activeTab]);

  const handleSelectEntry = (entryId: string, checked: boolean) => {
    if (checked) {
      setSelectedEntries([...selectedEntries, entryId]);
    } else {
      setSelectedEntries(selectedEntries.filter((id) => id !== entryId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEntries(filteredLogs.map((log) => log.id));
    } else {
      setSelectedEntries([]);
    }
  };

  const handleAction = (action: string) => {
    if (selectedEntries.length === 0) {
      toast.error("Please select at least one entry");
      return;
    }
    setActionDialog({ isOpen: true, action });
    setActionNotes("");
    setRejectionReason("");
  };

  const performAction = async () => {
    if (!actionDialog.action) return;

    // Validate rejection reason for rejection actions
    if (actionDialog.action === "ORG_ADMIN_REJECT" && !rejectionReason?.trim()) {
      toast.error("Rejection reason is required for rejection actions");
      return;
    }

    try {
      setIsSubmitting(true);
      const response = await fetch("/api/organizations/verification/data", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          unitLogIds: selectedEntries,
          action: actionDialog.action,
          notes: actionNotes,
          rejectionReason: rejectionReason,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to perform verification action");
      }

      const result = await response.json();
      toast.success(result.message);
      
      setActionDialog({ isOpen: false, action: null });
      setSelectedEntries([]);
      await fetchData();
    } catch (error) {
      console.error("Error performing action:", error);
      toast.error("Failed to perform verification action");
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "SUBMITTED_TO_ORG_ADMIN":
        return <Badge variant="secondary">Submitted for Review</Badge>;
      case "SPV_ADMIN_VERIFIED":
        return <Badge variant="secondary">SPV Admin Verified</Badge>;
      case "ORG_APPROVED":
        return <Badge variant="default" className="bg-green-100 text-green-800">Org Approved</Badge>;
      case "ORG_REJECTED":
        return <Badge variant="destructive" className="bg-red-100 text-red-800">Org Rejected</Badge>;
      case "SUBMITTED_TO_VVB":
        return <Badge variant="outline" className="border-blue-500 text-blue-700">Submitted to VVB</Badge>;
      case "VVB_VERIFIED":
        return <Badge variant="default" className="bg-green-200 text-green-900">VVB Verified</Badge>;
      case "VVB_REJECTED":
        return <Badge variant="destructive">VVB Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getActionTitle = (action: string) => {
    switch (action) {
      case "ORG_ADMIN_VERIFY":
        return "Approve Selected Entries";
      case "ORG_ADMIN_REJECT":
        return "Reject Selected Entries";
      case "SUBMIT_TO_VVB":
        return "Submit to VVB for Final Verification";
      default:
        return "Perform Action";
    }
  };

  const filteredLogs = unitLogs.filter((log) => {
    const matchesSearch = searchTerm === "" ||
      log.project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.unitType.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesProject = projectFilter === "all" || log.project.id === projectFilter;
    
    return matchesSearch && matchesProject;
  });

  const uniqueProjects = Array.from(
    new Set(unitLogs.map((log) => log.project.id))
  ).map((projectId) => {
    const project = unitLogs.find((log) => log.project.id === projectId)?.project;
    return { id: projectId, name: project?.name || "" };
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Verification Dashboard</h1>
          <p className="text-muted-foreground">
            Review and approve data entries verified by SPV admins
          </p>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.SUBMITTED_TO_ORG_ADMIN || 0}</div>
            <p className="text-xs text-muted-foreground">Submitted for Review</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Org Approved</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.ORG_APPROVED || 0}</div>
            <p className="text-xs text-muted-foreground">Ready for VVB</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Org Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.ORG_REJECTED || 0}</div>
            <p className="text-xs text-muted-foreground">Needs Revision</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Submitted to VVB</CardTitle>
            <Send className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.SUBMITTED_TO_VVB || 0}</div>
            <p className="text-xs text-muted-foreground">Under VVB Review</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VVB Verified</CardTitle>
            <Award className="h-4 w-4 text-green-700" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.VVB_VERIFIED || 0}</div>
            <p className="text-xs text-muted-foreground">Fully Certified</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">VVB Rejected</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.VVB_REJECTED || 0}</div>
            <p className="text-xs text-muted-foreground">Needs Review</p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-7">
          <TabsTrigger value="pending">Pending Review</TabsTrigger>
          <TabsTrigger value="approved">Org Approved</TabsTrigger>
          <TabsTrigger value="rejected">Org Rejected</TabsTrigger>
          <TabsTrigger value="submitted">Submitted to VVB</TabsTrigger>
          <TabsTrigger value="vvb-verified">VVB Verified</TabsTrigger>
          <TabsTrigger value="vvb-rejected">VVB Rejected</TabsTrigger>
          <TabsTrigger value="audit">Audit Trail</TabsTrigger>
        </TabsList>

        <TabsContent value="audit">
          <VerificationAuditTrail organizationId={session?.user?.organizationId} />
        </TabsContent>

        {["pending", "approved", "submitted", "vvb-verified", "vvb-rejected"].map((tab) => (
          <TabsContent key={tab} value={tab}>
            <Card>
              <CardHeader>
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle>Data Entries</CardTitle>
                    <CardDescription>
                      {tab === "pending" && "Review and approve data entries verified by SPV admins"}
                      {tab === "approved" && "Data entries approved by organization admin"}
                      {tab === "submitted" && "Data entries submitted to VVB for verification"}
                      {tab === "vvb-verified" && "Data entries verified and certified by VVB"}
                      {tab === "vvb-rejected" && "Data entries rejected by VVB"}
                    </CardDescription>
                  </div>
                  <Badge variant="outline">
                    {selectedEntries.length} selected
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                {/* Filters */}
                <div className="flex justify-between items-center mb-4">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                      <Input
                        placeholder="Search projects, data..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10 w-64"
                      />
                    </div>
                    <Select value={projectFilter} onValueChange={setProjectFilter}>
                      <SelectTrigger className="w-fit min-w-[160px]">
                        <SelectValue placeholder="Filter by project" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Projects</SelectItem>
                        {uniqueProjects.map((project) => (
                          <SelectItem key={project.id} value={project.id}>
                            {project.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex space-x-2">
                    {tab === "pending" && (
                      <>
                        <Button
                          onClick={() => handleAction("ORG_ADMIN_VERIFY")}
                          disabled={selectedEntries.length === 0}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </Button>
                        <Button
                          onClick={() => handleAction("ORG_ADMIN_REJECT")}
                          disabled={selectedEntries.length === 0}
                          variant="destructive"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Reject
                        </Button>
                      </>
                    )}
                    {tab === "approved" && (
                      <Button
                        onClick={() => handleAction("SUBMIT_TO_VVB")}
                        disabled={selectedEntries.length === 0}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <Send className="h-4 w-4 mr-2" />
                        Submit to VVB
                      </Button>
                    )}
                  </div>
                </div>

                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : filteredLogs.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No Data Entries Found</h3>
                    <p className="text-muted-foreground">
                      No data entries match the current filter criteria.
                    </p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-12">
                          <Checkbox
                            checked={selectedEntries.length === filteredLogs.length}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead>Project & Data</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>SPV Approval</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredLogs.map((log) => (
                        <TableRow key={log.id}>
                          <TableCell>
                            <Checkbox
                              checked={selectedEntries.includes(log.id)}
                              onCheckedChange={(checked) => handleSelectEntry(log.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell>
                            <div>
                              <p className="font-medium">{log.project.name}</p>
                              <p className="text-sm text-muted-foreground">
                                {log.quantity} {log.unitType} • {log.dataSource}
                              </p>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(log.verificationStatus)}
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <p className="font-medium">
                                {log.spvApprovedAt ? new Date(log.spvApprovedAt).toLocaleDateString() : "N/A"}
                              </p>
                              {log.spvApprovalNotes && (
                                <p className="text-muted-foreground truncate max-w-32">
                                  {log.spvApprovalNotes}
                                </p>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">
                                {new Date(log.logDate).toLocaleDateString()}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <Eye className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Action Dialog */}
      <Dialog open={actionDialog.isOpen} onOpenChange={(open) => setActionDialog({ isOpen: open, action: null })}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{getActionTitle(actionDialog.action || "")}</DialogTitle>
            <DialogDescription>
              You are about to {actionDialog.action?.toLowerCase().replace("_", " ")} {selectedEntries.length} data entries.
              {actionDialog.action === "ORG_ADMIN_REJECT" && " Please provide a reason for rejection."}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Notes (Optional)</label>
              <Textarea
                value={actionNotes}
                onChange={(e) => setActionNotes(e.target.value)}
                placeholder="Add any additional notes..."
                className="mt-1"
              />
            </div>
            {actionDialog.action === "ORG_ADMIN_REJECT" && (
              <div>
                <label className="text-sm font-medium">Rejection Reason *</label>
                <Textarea
                  value={rejectionReason}
                  onChange={(e) => setRejectionReason(e.target.value)}
                  placeholder="Please provide a reason for rejection..."
                  className="mt-1"
                  required
                />
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setActionDialog({ isOpen: false, action: null })}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              onClick={performAction}
              disabled={isSubmitting || (actionDialog.action === "ORG_ADMIN_REJECT" && !rejectionReason?.trim())}
            >
              {isSubmitting ? "Processing..." : "Confirm"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
