"use client";

import { useState } from "react";
import { Plus, Users, Search, Filter, MoreHorizontal, Edit, Trash2, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useBrokerClients } from "@/hooks/use-broker-clients";
import { Broker<PERSON>lientForm } from "@/components/broker/broker-client-form";
import { BrokerClientDetails } from "@/components/broker/broker-client-details";

type ViewMode = "list" | "create" | "edit" | "view";

export default function BrokerClientsPage() {
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedClient, setSelectedClient] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");

  const {
    clients,
    pagination,
    isLoading,
    error,
    refetch,
    updateFilters,
  } = useBrokerClients({
    search: searchTerm,
    status: statusFilter === "all" ? undefined : statusFilter,
    clientType: typeFilter === "all" ? undefined : typeFilter,
  });

  const handleCreateClient = () => {
    setSelectedClient(null);
    setViewMode("create");
  };

  const handleEditClient = (client: any) => {
    setSelectedClient(client);
    setViewMode("edit");
  };

  const handleViewClient = (client: any) => {
    setSelectedClient(client);
    setViewMode("view");
  };

  const handleDeleteClient = async (client: any) => {
    if (confirm(`Are you sure you want to delete client "${client.clientName}"?`)) {
      try {
        // TODO: Implement delete functionality
        console.log("Delete client:", client.id);
        refetch();
      } catch (error) {
        console.error("Error deleting client:", error);
      }
    }
  };

  const handleFormSuccess = () => {
    setViewMode("list");
    refetch();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "inactive":
        return "bg-gray-100 text-gray-800";
      case "suspended":
        return "bg-yellow-100 text-yellow-800";
      case "terminated":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "organization":
        return "bg-blue-100 text-blue-800";
      case "individual":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (viewMode === "create") {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Add New Client</h1>
            <p className="text-gray-600 mt-1">Create a new client relationship.</p>
          </div>
          <Button variant="outline" onClick={() => setViewMode("list")}>
            Back to Clients
          </Button>
        </div>
        <BrokerClientForm onSuccess={handleFormSuccess} onCancel={() => setViewMode("list")} />
      </div>
    );
  }

  if (viewMode === "edit" && selectedClient) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Edit Client</h1>
            <p className="text-gray-600 mt-1">Update client information.</p>
          </div>
          <Button variant="outline" onClick={() => setViewMode("list")}>
            Back to Clients
          </Button>
        </div>
        <BrokerClientForm 
          client={selectedClient}
          onSuccess={handleFormSuccess} 
          onCancel={() => setViewMode("list")} 
        />
      </div>
    );
  }

  if (viewMode === "view" && selectedClient) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Client Details</h1>
            <p className="text-gray-600 mt-1">View client information and history.</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={() => handleEditClient(selectedClient)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Client
            </Button>
            <Button variant="outline" onClick={() => setViewMode("list")}>
              Back to Clients
            </Button>
          </div>
        </div>
        <BrokerClientDetails client={selectedClient} />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Client Management</h1>
          <p className="text-gray-600 mt-1">
            Manage your client relationships and track their activities.
          </p>
        </div>
        <Button onClick={handleCreateClient}>
          <Plus className="h-4 w-4 mr-2" />
          Add Client
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search clients..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="INACTIVE">Inactive</SelectItem>
                <SelectItem value="SUSPENDED">Suspended</SelectItem>
                <SelectItem value="TERMINATED">Terminated</SelectItem>
              </SelectContent>
            </Select>
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="ORGANIZATION">Organization</SelectItem>
                <SelectItem value="INDIVIDUAL">Individual</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Clients Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Clients</CardTitle>
              <CardDescription>
                {pagination?.totalCount || 0} total clients
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Spinner size="lg" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-600">
              <p>Error loading clients: {error}</p>
              <Button onClick={refetch} className="mt-4">
                Try Again
              </Button>
            </div>
          ) : clients && clients.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Client</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Commission Rate</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {clients.map((client) => (
                  <TableRow key={client.id}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{client.clientName}</p>
                        <p className="text-sm text-gray-600">{client.clientEmail}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={getTypeColor(client.clientType)}>
                        {client.clientType}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(client.status)}>
                        {client.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {client.commissionRate ? `${(client.commissionRate * 100).toFixed(1)}%` : "Default"}
                    </TableCell>
                    <TableCell>
                      {new Date(client.createdAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewClient(client)}>
                            <Eye className="h-4 w-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEditClient(client)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDeleteClient(client)}
                            className="text-red-600"
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No clients found</h3>
              <p className="text-gray-600 mb-4">Get started by adding your first client.</p>
              <Button onClick={handleCreateClient}>
                <Plus className="h-4 w-4 mr-2" />
                Add Client
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
