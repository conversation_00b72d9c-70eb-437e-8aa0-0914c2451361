"use client";

import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building, 
  FileText,
  Edit,
  Save,
  X
} from "lucide-react";
import { useState } from "react";

export default function BrokerProfilePage() {
  const { data: session } = useSession();
  const [isEditing, setIsEditing] = useState(false);

  // Mock broker data - in real app, this would come from API
  const [brokerData, setBrokerData] = useState({
    name: session?.user?.name || "<PERSON>roke<PERSON>",
    email: session?.user?.email || "<EMAIL>",
    phone: "+****************",
    address: "123 Business St, Suite 100",
    city: "New York",
    state: "NY",
    postalCode: "10001",
    country: "United States",
    licenseNumber: "BRK-2024-001",
    licenseType: "Securities Broker",
    licenseIssuer: "SEC",
    licenseExpiryDate: "2025-12-31",
    operatingModel: "Independent",
    commissionRate: "2.5%",
    specializations: ["Carbon Credits", "Environmental Securities", "ESG Investments"],
    description: "Experienced broker specializing in carbon credit trading and environmental securities with over 10 years in the industry.",
    status: "Active",
    verificationStatus: "Verified"
  });

  const handleSave = () => {
    // In real app, this would make an API call to update the broker profile
    setIsEditing(false);
    // Show success message
  };

  const handleCancel = () => {
    setIsEditing(false);
    // Reset form data if needed
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active": return "bg-green-100 text-green-800";
      case "inactive": return "bg-gray-100 text-gray-800";
      case "suspended": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getVerificationColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "verified": return "bg-green-100 text-green-800";
      case "pending": return "bg-yellow-100 text-yellow-800";
      case "rejected": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
          <p className="text-gray-600 mt-1">
            Manage your broker profile and business information.
          </p>
        </div>
        <div className="flex space-x-3">
          {isEditing ? (
            <>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
              <Button variant="outline" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Overview */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle>Profile Overview</CardTitle>
            <CardDescription>
              Your current status and verification information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="font-medium">{brokerData.name}</p>
                <p className="text-sm text-gray-600">{brokerData.email}</p>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Status</span>
                <Badge className={getStatusColor(brokerData.status)}>
                  {brokerData.status}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Verification</span>
                <Badge className={getVerificationColor(brokerData.verificationStatus)}>
                  {brokerData.verificationStatus}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Operating Model</span>
                <span className="text-sm font-medium">{brokerData.operatingModel}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Commission Rate</span>
                <span className="text-sm font-medium">{brokerData.commissionRate}</span>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium">Specializations</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {brokerData.specializations.map((spec, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {spec}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Details */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Profile Details</CardTitle>
            <CardDescription>
              Your personal and business information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Basic Information */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={brokerData.name}
                    onChange={(e) => setBrokerData({...brokerData, name: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={brokerData.email}
                    onChange={(e) => setBrokerData({...brokerData, email: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={brokerData.phone}
                    onChange={(e) => setBrokerData({...brokerData, phone: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div>
              <h3 className="text-lg font-medium mb-4">Address Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <Label htmlFor="address">Street Address</Label>
                  <Input
                    id="address"
                    value={brokerData.address}
                    onChange={(e) => setBrokerData({...brokerData, address: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={brokerData.city}
                    onChange={(e) => setBrokerData({...brokerData, city: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="state">State/Province</Label>
                  <Input
                    id="state"
                    value={brokerData.state}
                    onChange={(e) => setBrokerData({...brokerData, state: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="postalCode">Postal Code</Label>
                  <Input
                    id="postalCode"
                    value={brokerData.postalCode}
                    onChange={(e) => setBrokerData({...brokerData, postalCode: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={brokerData.country}
                    onChange={(e) => setBrokerData({...brokerData, country: e.target.value})}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* License Information */}
            <div>
              <h3 className="text-lg font-medium mb-4">License Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="licenseNumber">License Number</Label>
                  <Input
                    id="licenseNumber"
                    value={brokerData.licenseNumber}
                    disabled
                  />
                </div>
                <div>
                  <Label htmlFor="licenseType">License Type</Label>
                  <Input
                    id="licenseType"
                    value={brokerData.licenseType}
                    disabled
                  />
                </div>
                <div>
                  <Label htmlFor="licenseIssuer">License Issuer</Label>
                  <Input
                    id="licenseIssuer"
                    value={brokerData.licenseIssuer}
                    disabled
                  />
                </div>
                <div>
                  <Label htmlFor="licenseExpiryDate">License Expiry Date</Label>
                  <Input
                    id="licenseExpiryDate"
                    value={brokerData.licenseExpiryDate}
                    disabled
                  />
                </div>
              </div>
            </div>

            {/* Description */}
            <div>
              <Label htmlFor="description">Professional Description</Label>
              <Textarea
                id="description"
                value={brokerData.description}
                onChange={(e) => setBrokerData({...brokerData, description: e.target.value})}
                disabled={!isEditing}
                rows={4}
                className="mt-1"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
