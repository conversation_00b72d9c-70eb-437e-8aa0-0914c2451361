"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Handshake, 
  FileText, 
  Shield, 
  Building,
  ArrowRight,
  CheckCircle,
  Loader2,
  User,
  MapPin,
  Phone,
  Mail
} from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "@/components/ui/use-toast";
import { ValidatedForm } from "@/components/forms/validated-form";
import { z } from "zod";

const brokerOnboardingSchema = z.object({
  // Business Information
  businessName: z.string().min(2, "Business name must be at least 2 characters"),
  legalName: z.string().min(2, "Legal name must be at least 2 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  
  // Contact Information
  phoneNumber: z.string().min(10, "Please enter a valid phone number"),
  address: z.string().min(5, "Please enter a complete address"),
  city: z.string().min(2, "City is required"),
  state: z.string().min(2, "State/Province is required"),
  postalCode: z.string().min(3, "Postal code is required"),
  country: z.string().min(2, "Country is required"),
  
  // License Information
  licenseNumber: z.string().min(3, "License number is required"),
  licenseType: z.string().min(3, "License type is required"),
  licenseIssuer: z.string().min(2, "License issuer is required"),
  licenseExpiryDate: z.string().min(1, "License expiry date is required"),
  
  // Business Model
  operatingModel: z.enum(["INDEPENDENT", "ONIX_MANAGED"]),
  commissionRate: z.number().min(0.001).max(0.5),
  specializations: z.array(z.string()).min(1, "Please select at least one specialization"),
});

type BrokerOnboardingData = z.infer<typeof brokerOnboardingSchema>;

export default function BrokerOnboardingPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);

  const specializationOptions = [
    "Carbon Credits",
    "Environmental Securities", 
    "ESG Investments",
    "Renewable Energy Credits",
    "Biodiversity Credits",
    "Water Credits",
    "Sustainable Finance",
    "Green Bonds"
  ];

  const handleSubmit = async (data: BrokerOnboardingData) => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/broker/onboarding", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to complete broker onboarding");
      }

      toast({
        title: "Broker Profile Created",
        description: "Your broker profile has been successfully created!",
      });

      router.push("/broker/dashboard");
    } catch (error) {
      console.error("Error completing broker onboarding:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to complete onboarding. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-teal-50 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <motion.div
          className="text-center mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 rounded-xl bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center shadow-lg">
              <Handshake className="h-8 w-8 text-white" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Set Up Your Broker Profile
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Complete your broker profile to start facilitating carbon credit transactions and managing client portfolios.
          </p>
        </motion.div>

        {/* Progress Indicator */}
        <motion.div
          className="mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <span className="ml-2 text-sm font-medium text-green-600">Role Selected</span>
            </div>
            <div className="w-16 h-0.5 bg-green-500" />
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center">
                <span className="text-white text-sm font-bold">2</span>
              </div>
              <span className="ml-2 text-sm font-medium text-blue-600">Broker Profile</span>
            </div>
            <div className="w-16 h-0.5 bg-gray-300" />
            <div className="flex items-center">
              <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
                <span className="text-gray-600 text-sm font-bold">3</span>
              </div>
              <span className="ml-2 text-sm font-medium text-gray-500">Complete</span>
            </div>
          </div>
        </motion.div>

        {/* Onboarding Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <Card className="shadow-xl">
            <CardHeader>
              <CardTitle className="text-2xl">Broker Information</CardTitle>
              <CardDescription>
                Provide your business and licensing information to get started.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ValidatedForm
                schema={brokerOnboardingSchema}
                onSubmit={handleSubmit}
                defaultValues={{
                  businessName: "",
                  legalName: "",
                  description: "",
                  phoneNumber: "",
                  address: "",
                  city: "",
                  state: "",
                  postalCode: "",
                  country: "United States",
                  licenseNumber: "",
                  licenseType: "",
                  licenseIssuer: "",
                  licenseExpiryDate: "",
                  operatingModel: "INDEPENDENT" as const,
                  commissionRate: 0.025,
                  specializations: [],
                }}
              >
                {({ register, formState: { errors }, setValue, watch }) => {
                  const specializations = watch("specializations") || [];
                  
                  return (
                    <div className="space-y-8">
                      {/* Business Information */}
                      <div>
                        <h3 className="text-lg font-semibold mb-4 flex items-center">
                          <Building className="h-5 w-5 mr-2 text-blue-600" />
                          Business Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="businessName">Business Name</Label>
                            <Input
                              id="businessName"
                              {...register("businessName")}
                              placeholder="Your brokerage name"
                            />
                            {errors.businessName && (
                              <p className="text-sm text-red-600 mt-1">{errors.businessName.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="legalName">Legal Name</Label>
                            <Input
                              id="legalName"
                              {...register("legalName")}
                              placeholder="Legal business name"
                            />
                            {errors.legalName && (
                              <p className="text-sm text-red-600 mt-1">{errors.legalName.message}</p>
                            )}
                          </div>
                        </div>
                        <div className="mt-4">
                          <Label htmlFor="description">Business Description</Label>
                          <Textarea
                            id="description"
                            {...register("description")}
                            placeholder="Describe your brokerage services and expertise..."
                            rows={3}
                          />
                          {errors.description && (
                            <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                          )}
                        </div>
                      </div>

                      {/* Contact Information */}
                      <div>
                        <h3 className="text-lg font-semibold mb-4 flex items-center">
                          <MapPin className="h-5 w-5 mr-2 text-blue-600" />
                          Contact Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="phoneNumber">Phone Number</Label>
                            <Input
                              id="phoneNumber"
                              {...register("phoneNumber")}
                              placeholder="+****************"
                            />
                            {errors.phoneNumber && (
                              <p className="text-sm text-red-600 mt-1">{errors.phoneNumber.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="address">Address</Label>
                            <Input
                              id="address"
                              {...register("address")}
                              placeholder="Street address"
                            />
                            {errors.address && (
                              <p className="text-sm text-red-600 mt-1">{errors.address.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="city">City</Label>
                            <Input
                              id="city"
                              {...register("city")}
                              placeholder="City"
                            />
                            {errors.city && (
                              <p className="text-sm text-red-600 mt-1">{errors.city.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="state">State/Province</Label>
                            <Input
                              id="state"
                              {...register("state")}
                              placeholder="State or Province"
                            />
                            {errors.state && (
                              <p className="text-sm text-red-600 mt-1">{errors.state.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="postalCode">Postal Code</Label>
                            <Input
                              id="postalCode"
                              {...register("postalCode")}
                              placeholder="Postal code"
                            />
                            {errors.postalCode && (
                              <p className="text-sm text-red-600 mt-1">{errors.postalCode.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="country">Country</Label>
                            <Input
                              id="country"
                              {...register("country")}
                              placeholder="Country"
                            />
                            {errors.country && (
                              <p className="text-sm text-red-600 mt-1">{errors.country.message}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* License Information */}
                      <div>
                        <h3 className="text-lg font-semibold mb-4 flex items-center">
                          <Shield className="h-5 w-5 mr-2 text-blue-600" />
                          License Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="licenseNumber">License Number</Label>
                            <Input
                              id="licenseNumber"
                              {...register("licenseNumber")}
                              placeholder="License number"
                            />
                            {errors.licenseNumber && (
                              <p className="text-sm text-red-600 mt-1">{errors.licenseNumber.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="licenseType">License Type</Label>
                            <Input
                              id="licenseType"
                              {...register("licenseType")}
                              placeholder="e.g., Securities Broker, Carbon Credit Broker"
                            />
                            {errors.licenseType && (
                              <p className="text-sm text-red-600 mt-1">{errors.licenseType.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="licenseIssuer">License Issuer</Label>
                            <Input
                              id="licenseIssuer"
                              {...register("licenseIssuer")}
                              placeholder="e.g., SEC, CFTC, State Authority"
                            />
                            {errors.licenseIssuer && (
                              <p className="text-sm text-red-600 mt-1">{errors.licenseIssuer.message}</p>
                            )}
                          </div>
                          <div>
                            <Label htmlFor="licenseExpiryDate">License Expiry Date</Label>
                            <Input
                              id="licenseExpiryDate"
                              type="date"
                              {...register("licenseExpiryDate")}
                            />
                            {errors.licenseExpiryDate && (
                              <p className="text-sm text-red-600 mt-1">{errors.licenseExpiryDate.message}</p>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Business Model */}
                      <div>
                        <h3 className="text-lg font-semibold mb-4 flex items-center">
                          <FileText className="h-5 w-5 mr-2 text-blue-600" />
                          Business Model
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="operatingModel">Operating Model</Label>
                            <Select onValueChange={(value) => setValue("operatingModel", value as "INDEPENDENT" | "ONIX_MANAGED")}>
                              <SelectTrigger>
                                <SelectValue placeholder="Select operating model" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="INDEPENDENT">Independent Broker</SelectItem>
                                <SelectItem value="ONIX_MANAGED">ONIX Managed</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          <div>
                            <Label htmlFor="commissionRate">Commission Rate (%)</Label>
                            <Input
                              id="commissionRate"
                              type="number"
                              step="0.001"
                              min="0.001"
                              max="50"
                              {...register("commissionRate", { valueAsNumber: true })}
                              placeholder="2.5"
                            />
                            {errors.commissionRate && (
                              <p className="text-sm text-red-600 mt-1">{errors.commissionRate.message}</p>
                            )}
                          </div>
                        </div>
                        
                        <div className="mt-4">
                          <Label>Specializations</Label>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                            {specializationOptions.map((spec) => (
                              <Badge
                                key={spec}
                                variant={specializations.includes(spec) ? "default" : "outline"}
                                className="cursor-pointer justify-center py-2"
                                onClick={() => {
                                  const newSpecs = specializations.includes(spec)
                                    ? specializations.filter(s => s !== spec)
                                    : [...specializations, spec];
                                  setValue("specializations", newSpecs);
                                }}
                              >
                                {spec}
                              </Badge>
                            ))}
                          </div>
                          {errors.specializations && (
                            <p className="text-sm text-red-600 mt-1">{errors.specializations.message}</p>
                          )}
                        </div>
                      </div>

                      {/* Submit Button */}
                      <div className="flex justify-end pt-6">
                        <Button
                          type="submit"
                          size="lg"
                          disabled={isLoading}
                          className="bg-gradient-to-r from-green-500 to-teal-600 hover:opacity-90 text-white font-semibold px-8"
                        >
                          {isLoading ? (
                            <>
                              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                              Creating Profile...
                            </>
                          ) : (
                            <>
                              Complete Setup
                              <ArrowRight className="h-4 w-4 ml-2" />
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  );
                }}
              </ValidatedForm>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
