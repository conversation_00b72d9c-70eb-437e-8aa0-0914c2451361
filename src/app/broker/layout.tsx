"use client";

import { ReactNode, useState, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { cn } from "@/lib/utils";

import { Button } from "@/components/ui/button";
import { Navbar } from "@/components/layout/navbar";
import { ClientBrokerGuard } from "@/components/auth/client-auth-guard";
import {
  LayoutDashboard,
  Users,
  Building2,
  Settings,
  FileText,
  ChevronLeft,
  ChevronRight,
  TrendingUp,
  Handshake,
  User
} from "lucide-react";
import { PageTransition } from "@/components/ui/animated";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { motion, AnimatePresence } from "framer-motion";

interface BrokerLayoutProps {
  children: ReactNode;
}

export default function BrokerLayout({ children }: BrokerLayoutProps) {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Persist sidebar state in localStorage
  useEffect(() => {
    const savedState = localStorage.getItem('broker-sidebar-collapsed');
    if (savedState !== null) {
      setIsCollapsed(JSON.parse(savedState));
    }
  }, []);

  useEffect(() => {
    localStorage.setItem('broker-sidebar-collapsed', JSON.stringify(isCollapsed));
  }, [isCollapsed]);

  const navigation = [
    {
      title: "Overview",
      items: [
        { title: "Dashboard", href: "/broker/dashboard", icon: LayoutDashboard },
      ]
    },
    {
      title: "Client Management",
      items: [
        { title: "Clients", href: "/broker/clients", icon: Users },
        { title: "Transactions", href: "/broker/transactions", icon: TrendingUp },
      ]
    },
    {
      title: "Profile",
      items: [
        { title: "My Profile", href: "/broker/profile", icon: User },
        { title: "Documents", href: "/broker/documents", icon: FileText },
        { title: "Settings", href: "/broker/settings", icon: Settings },
      ]
    }
  ];

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Get page title based on pathname
  const getPageTitle = (pathname: string) => {
    if (pathname === "/broker/dashboard") return "Broker Dashboard";
    if (pathname === "/broker/clients") return "Client Management";
    if (pathname === "/broker/transactions") return "Transaction History";
    if (pathname === "/broker/profile") return "My Profile";
    if (pathname === "/broker/documents") return "Documents";
    if (pathname === "/broker/settings") return "Settings";
    return "Broker Dashboard";
  };

  return (
    <ClientBrokerGuard>
      <TooltipProvider>
        <div className="flex h-screen">
          {/* Broker Sidebar */}
          <motion.aside
            className="fixed left-0 top-0 z-40 h-full bg-white border-r border-gray-200 shadow-sm hidden md:flex flex-col"
            animate={{
              width: isCollapsed ? '4rem' : '15rem'
            }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
          >
            <div className="flex flex-col h-full">
              {/* Header with Logo and Toggle */}
              <div className="flex items-center justify-between h-14 px-4 border-b border-gray-200">
                <motion.div
                  className="flex items-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {!isCollapsed ? (
                    <div className="flex items-center space-x-1.5">
                      <div className="w-5 h-5 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center shadow-sm">
                        <Handshake className="h-3 w-3 text-white" />
                      </div>
                      <motion.h1
                        className="text-sm font-bold text-gray-900"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                      >
                        Broker Portal
                      </motion.h1>
                    </div>
                  ) : (
                    <Tooltip delayDuration={300}>
                      <TooltipTrigger asChild>
                        <div className="w-5 h-5 bg-gradient-to-br from-blue-500 to-purple-600 rounded flex items-center justify-center cursor-pointer shadow-sm">
                          <Handshake className="h-3 w-3 text-white" />
                        </div>
                      </TooltipTrigger>
                      <TooltipContent
                        side="right"
                        className="bg-gray-900 text-white text-sm px-3 py-2 rounded-md shadow-lg border-0"
                        sideOffset={8}
                      >
                        Broker Portal
                      </TooltipContent>
                    </Tooltip>
                  )}
                </motion.div>

                {/* Toggle Button */}
                <motion.button
                  onClick={toggleCollapse}
                  className="p-0.5 rounded hover:bg-gray-100 transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isCollapsed ? (
                    <ChevronRight className="h-3 w-3 text-gray-600" />
                  ) : (
                    <ChevronLeft className="h-3 w-3 text-gray-600" />
                  )}
                </motion.button>
              </div>
              
              {/* Broker Navigation Menu */}
              <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
                {navigation.map((section, sectionIndex) => (
                  <div key={section.title} className="py-2">
                    {/* Section Title with smooth animation */}
                    <motion.h4
                      className="mb-1 rounded-md px-2 py-1 text-sm font-semibold"
                      animate={{
                        opacity: isCollapsed ? 0 : 1,
                        height: isCollapsed ? 0 : 'auto',
                        marginBottom: isCollapsed ? 0 : '0.25rem',
                      }}
                      transition={{ duration: 0.3 }}
                      style={{
                        overflow: 'hidden',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {section.title}
                    </motion.h4>

                    {section.items?.map((item, itemIndex) => {
                      // Check if current path matches or starts with the navigation item's path
                      const isActive = pathname === item.href ||
                        (item.href !== '/broker/dashboard' && pathname.startsWith(item.href + '/'));

                      // Create the navigation item with motion
                      const NavItem = (
                        <motion.div
                          key={item.href}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: 0.1 * (sectionIndex * 3 + itemIndex), duration: 0.3 }}
                        >
                          <Link
                            href={item.href}
                            className={cn(
                              "group flex items-center px-3 py-2 text-sm font-medium rounded-md transition-all duration-200",
                              isActive
                                ? "bg-blue-50 text-blue-700 border-l-3 border-blue-700"
                                : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                              isCollapsed && "justify-center px-2"
                            )}
                          >
                            <motion.div
                              className="flex items-center justify-center"
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              {item.icon && (
                                <item.icon
                                  className={cn(
                                    "h-4 w-4 transition-colors",
                                    isActive ? "text-blue-700" : "text-gray-500 group-hover:text-gray-700"
                                  )}
                                />
                              )}
                            </motion.div>

                            <motion.span
                              className="ml-3 truncate"
                              animate={{
                                opacity: isCollapsed ? 0 : 1,
                                width: isCollapsed ? 0 : 'auto',
                                marginLeft: isCollapsed ? 0 : '0.75rem',
                              }}
                              transition={{ duration: 0.3 }}
                              style={{
                                overflow: 'hidden',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {item.title}
                            </motion.span>
                          </Link>
                        </motion.div>
                      );

                      // Wrap with tooltip when collapsed
                      if (isCollapsed) {
                        return (
                          <Tooltip key={item.href} delayDuration={300}>
                            <TooltipTrigger asChild>
                              {NavItem}
                            </TooltipTrigger>
                            <TooltipContent
                              side="right"
                              className="bg-gray-900 text-white text-sm px-3 py-2 rounded-md shadow-lg border-0"
                              sideOffset={8}
                            >
                              {item.title}
                            </TooltipContent>
                          </Tooltip>
                        );
                      }

                      return NavItem;
                    })}
                  </div>
                ))}
              </nav>
            </div>
          </motion.aside>

          {/* Mobile Sidebar Overlay */}
          <AnimatePresence>
            {mobileMenuOpen && (
              <>
                {/* Backdrop */}
                <motion.div
                  className="fixed inset-0 bg-black bg-opacity-50 z-50 md:hidden"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  onClick={toggleMobileMenu}
                />

                {/* Mobile Sidebar */}
                <motion.div
                  className="fixed left-0 top-0 z-50 h-full w-64 bg-white border-r border-gray-200 shadow-lg md:hidden"
                  initial={{ x: '-100%' }}
                  animate={{ x: 0 }}
                  exit={{ x: '-100%' }}
                  transition={{ duration: 0.3, ease: 'easeInOut' }}
                >
                  <div className="flex flex-col h-full">
                    {/* Mobile Header */}
                    <div className="flex items-center justify-between h-10 px-3 border-b border-gray-200">
                      <h1 className="text-base font-bold text-gray-900">Broker Portal</h1>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={toggleMobileMenu}
                      >
                        <ChevronLeft className="h-3 w-3" />
                      </Button>
                    </div>

                    {/* Mobile Navigation */}
                    <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
                      {navigation.map((section) => (
                        <div key={section.title} className="py-2">
                          <h4 className="mb-1 rounded-md px-2 py-1 text-sm font-semibold">
                            {section.title}
                          </h4>
                          {section.items?.map((item) => {
                            const isActive = pathname === item.href ||
                              (item.href !== '/broker/dashboard' && pathname.startsWith(item.href + '/'));

                            return (
                              <Link
                                key={item.href}
                                href={item.href}
                                className={cn(
                                  "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium hover:bg-muted",
                                  isActive ? "bg-muted" : "transparent"
                                )}
                                onClick={toggleMobileMenu}
                              >
                                {item.icon && (
                                  <item.icon className={cn("h-5 w-5", isActive ? "text-primary" : "text-muted-foreground")} />
                                )}
                                <span>{item.title}</span>
                              </Link>
                            );
                          })}
                        </div>
                      ))}
                    </nav>
                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>

          {/* Broker Main content area */}
          <div
            className="flex flex-col flex-1 transition-all duration-300 ml-0 md:ml-[var(--sidebar-width)]"
            style={{
              '--sidebar-width': isCollapsed ? '4rem' : '15rem'
            } as React.CSSProperties}
          >
            {/* Broker Navbar */}
            <div className="relative">
              <Navbar
                variant="broker"
                title={getPageTitle(pathname)}
                className="relative"
                onMobileMenuClick={toggleMobileMenu}
              />
            </div>

            {/* Broker Main content */}
            <main className="flex-1 overflow-y-auto bg-blue-50">
              <div className="page-container page-content p-6">
                <PageTransition>
                  {children}
                </PageTransition>
              </div>
            </main>
          </div>
        </div>
      </TooltipProvider>
    </ClientBrokerGuard>
  );
}
