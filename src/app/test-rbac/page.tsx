"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';

interface TestResult {
  name: string;
  url: string;
  method: string;
  status: number;
  success: boolean;
  data?: any;
  error?: string;
}

export default function TestRBACPage() {
  const { data: session, status } = useSession();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [summary, setSummary] = useState({ total: 0, passed: 0, failed: 0 });

  const runAPITest = async (name: string, url: string, method: string = 'GET'): Promise<TestResult> => {
    try {
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json().catch(() => ({}));

      return {
        name,
        url,
        method,
        status: response.status,
        success: response.ok,
        data,
      };
    } catch (error) {
      return {
        name,
        url,
        method,
        status: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  };

  const runAllTests = async () => {
    if (!session?.user) {
      toast.error('Please log in to run tests');
      return;
    }

    setIsRunning(true);
    setTestResults([]);
    
    const tests = [
      // RBAC System Tests
      ['RBAC Init Status', '/api/rbac/init', 'GET'],
      ['RBAC Initialize', '/api/rbac/init', 'POST'],
      
      // User Management Tests
      ['List Users', '/api/rbac/users?limit=5', 'GET'],
      ['User Permissions', '/api/rbac/user-permissions', 'GET'],
      
      // Role Management Tests
      ['List Roles', '/api/rbac/roles', 'GET'],
      
      // Permission Tests
      ['List Permissions', '/api/rbac/permissions', 'GET'],
      ['Check Permission (read:user)', '/api/rbac/check-permission?permission=read:user', 'GET'],
      ['Check Permission (create:user)', '/api/rbac/check-permission?permission=create:user', 'GET'],
      
      // Audit Tests
      ['Audit Log', '/api/rbac/audit?limit=5', 'GET'],
      
      // Test Suite
      ['Run RBAC Tests', '/api/rbac/test', 'POST'],
    ];

    const results: TestResult[] = [];
    let passed = 0;
    let failed = 0;

    for (const [name, url, method] of tests) {
      const result = await runAPITest(name, url, method);
      results.push(result);
      
      if (result.success) {
        passed++;
      } else {
        failed++;
      }
      
      // Update results in real-time
      setTestResults([...results]);
      setSummary({ total: results.length, passed, failed });
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    setIsRunning(false);
    
    if (failed === 0) {
      toast.success(`All ${passed} tests passed! 🎉`);
    } else {
      toast.warning(`${passed} passed, ${failed} failed`);
    }
  };

  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'bg-green-500';
    if (status >= 400 && status < 500) return 'bg-yellow-500';
    if (status >= 500) return 'bg-red-500';
    return 'bg-gray-500';
  };

  const getStatusText = (status: number) => {
    if (status === 0) return 'Network Error';
    if (status >= 200 && status < 300) return 'Success';
    if (status === 401) return 'Unauthorized';
    if (status === 403) return 'Forbidden';
    if (status >= 400 && status < 500) return 'Client Error';
    if (status >= 500) return 'Server Error';
    return 'Unknown';
  };

  if (status === 'loading') {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading...</div>
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please log in to test the RBAC system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={() => window.location.href = '/login'}>
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">RBAC System Test</h1>
          <p className="text-muted-foreground">
            Test all RBAC API endpoints and functionality
          </p>
        </div>
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          size="lg"
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </Button>
      </div>

      {/* User Info */}
      <Card>
        <CardHeader>
          <CardTitle>Current User</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <strong>Name:</strong> {session.user.name}
            </div>
            <div>
              <strong>Email:</strong> {session.user.email}
            </div>
            <div>
              <strong>Role:</strong> 
              <Badge variant="outline" className="ml-2">
                {session.user.role}
              </Badge>
            </div>
            <div>
              <strong>Organization:</strong> {session.user.organizationId || 'None'}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Test Summary */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold">{summary.total}</div>
                <div className="text-sm text-muted-foreground">Total</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">{summary.passed}</div>
                <div className="text-sm text-muted-foreground">Passed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">{summary.failed}</div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </div>
              <div>
                <div className="text-2xl font-bold">
                  {summary.total > 0 ? Math.round((summary.passed / summary.total) * 100) : 0}%
                </div>
                <div className="text-sm text-muted-foreground">Success Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
            <CardDescription>
              Detailed results for each API endpoint test
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <h3 className="font-semibold">{result.name}</h3>
                      <Badge variant="outline">{result.method}</Badge>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${getStatusColor(result.status)}`} />
                      <span className="text-sm font-mono">{result.status}</span>
                      <span className="text-sm text-muted-foreground">
                        {getStatusText(result.status)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="text-sm text-muted-foreground mb-2">
                    {result.url}
                  </div>
                  
                  {result.error && (
                    <div className="text-sm text-red-600 bg-red-50 p-2 rounded">
                      Error: {result.error}
                    </div>
                  )}
                  
                  {result.data && (
                    <details className="text-sm">
                      <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                        Response Data
                      </summary>
                      <pre className="mt-2 p-2 bg-gray-50 rounded text-xs overflow-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>Testing Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold mb-2">Expected Results:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>200 Success:</strong> API endpoint is working correctly</li>
              <li><strong>401 Unauthorized:</strong> User is not authenticated (should not happen here)</li>
              <li><strong>403 Forbidden:</strong> User lacks required permissions</li>
              <li><strong>500 Server Error:</strong> Internal server error (needs investigation)</li>
            </ul>
          </div>
          
          <Separator />
          
          <div>
            <h4 className="font-semibold mb-2">Test Different User Roles:</h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="p-3 border rounded">
                <strong>Admin User:</strong><br />
                <EMAIL> / admin123<br />
                <span className="text-muted-foreground">Should have access to all endpoints</span>
              </div>
              <div className="p-3 border rounded">
                <strong>Org Admin:</strong><br />
                <EMAIL> / orgadmin123<br />
                <span className="text-muted-foreground">Should have organization-level access</span>
              </div>
              <div className="p-3 border rounded">
                <strong>Regular User:</strong><br />
                <EMAIL> / user123<br />
                <span className="text-muted-foreground">Should have limited access</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
