"use client";

import { useState, useEffect, use } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ManualDataEntry } from "@/components/projects/monitoring/manual-data-entry";
import { CsvUpload } from "@/components/projects/monitoring/csv-upload";
import { ApiIntegrationConfig } from "@/components/projects/monitoring/api-integration-config";
import { useSPVUser, useSPVPermissions } from "@/components/auth/client-spv-auth-guard";
import {
  ArrowLeft,
  Edit,
  FileSpreadsheet,
  Wifi,
  Info,
  CheckCircle,
  Clock,
  AlertCircle,
  Lock
} from "lucide-react";

interface UnifiedDataEntryProps {
  projectId?: string; // Optional for SPV context where user selects project
  context: "dashboard" | "spv"; // Determines which APIs and auth to use
  onBack?: () => void; // Custom back handler
}

interface ProjectData {
  id: string;
  name: string;
  type: string;
  metadata?: {
    projectSubType?: string;
  };
}

export default function UnifiedDataEntry({ 
  projectId: initialProjectId, 
  context,
  onBack 
}: UnifiedDataEntryProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState("manual");
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [projectId, setProjectId] = useState<string>(initialProjectId || "");
  const [projectData, setProjectData] = useState<ProjectData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [availableProjects, setAvailableProjects] = useState<ProjectData[]>([]);

  // SPV-specific hooks (only used when context is "spv")
  const { spvUser, loading: spvLoading, error: spvError } = context === "spv" ? useSPVUser() : { spvUser: null, loading: false, error: null };
  const { hasPermission } = context === "spv" ? useSPVPermissions() : { hasPermission: () => true };

  useEffect(() => {
    if (context === "spv" && spvUser) {
      fetchSPVProjects();
    } else if (context === "dashboard" && projectId) {
      fetchProjectData(projectId);
    }
  }, [context, spvUser, projectId]);

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['manual', 'csv', 'api'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const fetchSPVProjects = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/spv/projects");
      if (response.ok) {
        const data = await response.json();
        const projects = data.data.projects; // Projects are returned directly, not as nested assignment.project
        setAvailableProjects(projects);
        
        // If no project selected and we have projects, select the first one
        if (!projectId && projects.length > 0) {
          setProjectId(projects[0].id);
          setProjectData(projects[0]);
        } else if (projectId) {
          const selectedProject = projects.find((p: any) => p.id === projectId);
          if (selectedProject) {
            setProjectData(selectedProject);
          }
        }
      } else {
        console.error('Failed to fetch SPV projects');
      }
    } catch (error) {
      console.error('Error fetching SPV projects:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProjectData = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/projects/${id}`);
      if (response.ok) {
        const data = await response.json();
        setProjectData(data.project);
      } else {
        console.error('Failed to fetch project data');
      }
    } catch (error) {
      console.error('Error fetching project data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuccess = (message: string) => {
    setSuccessMessage(message);
    setRefreshTrigger(prev => prev + 1);
    setTimeout(() => {
      setSuccessMessage(null);
    }, 5000);
  };

  const handleCancel = () => {
    if (onBack) {
      onBack();
    } else if (context === "spv") {
      router.push('/spv/dashboard');
    } else if (projectId) {
      router.push(`/dashboard/projects/${projectId}/monitoring`);
    } else {
      router.push('/dashboard/projects');
    }
  };

  const handleBackToMonitoring = () => {
    if (onBack) {
      onBack();
    } else if (context === "spv") {
      router.push('/spv/dashboard');
    } else if (projectId) {
      router.push(`/dashboard/projects/${projectId}?tab=monitoring`);
    } else {
      router.push('/dashboard/projects');
    }
  };

  // Check permissions for SPV users
  const canCreateUnitLogs = context === "dashboard" || hasPermission("create:unit_log");
  const canUploadCSV = context === "dashboard" || hasPermission("upload:data_files");
  const canConfigureAPI = context === "dashboard" || hasPermission("manage:project_data");

  if (context === "spv" && spvLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Data Entry</h1>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (context === "spv" && spvError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{spvError}</AlertDescription>
      </Alert>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Data Entry</h1>
            <p className="text-muted-foreground">Loading project data...</p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              disabled
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Data Entry</h1>
          <p className="text-muted-foreground">
            {context === "spv" 
              ? "Enter monitoring data for your assigned projects"
              : "Add power generation and monitoring data to your project"
            }
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={handleBackToMonitoring}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {context === "spv" ? "Back to Dashboard" : "Back to Monitoring"}
          </Button>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-800">
            {successMessage}
          </AlertDescription>
        </Alert>
      )}

      {/* Project Selection for SPV users */}
      {context === "spv" && availableProjects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Select Project</CardTitle>
            <CardDescription>Choose the project you want to enter data for</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2 lg:grid-cols-3">
              {availableProjects.filter(project => project && project.id).map((project) => (
                <Button
                  key={project.id}
                  variant={projectId === project.id ? "default" : "outline"}
                  className="justify-start h-auto p-4"
                  onClick={() => {
                    setProjectId(project.id);
                    setProjectData(project);
                  }}
                >
                  <div className="text-left">
                    <div className="font-medium">{project.name}</div>
                    <div className="text-sm text-muted-foreground">{project.type}</div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Show message if no project selected */}
      {!projectId && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {context === "spv" 
              ? "Please select a project to start entering data."
              : "No project specified."
            }
          </AlertDescription>
        </Alert>
      )}

      {/* Data Entry Methods - only show if project is selected */}
      {projectId && projectData && (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger 
              value="manual" 
              className="flex items-center gap-2"
              disabled={!canCreateUnitLogs}
            >
              <Edit className="h-4 w-4" />
              Manual Entry
              {!canCreateUnitLogs && <Lock className="h-3 w-3" />}
            </TabsTrigger>
            <TabsTrigger 
              value="csv" 
              className="flex items-center gap-2"
              disabled={!canUploadCSV}
            >
              <FileSpreadsheet className="h-4 w-4" />
              CSV Upload
              {!canUploadCSV && <Lock className="h-3 w-3" />}
            </TabsTrigger>
            <TabsTrigger 
              value="api" 
              className="flex items-center gap-2"
              disabled={!canConfigureAPI}
            >
              <Wifi className="h-4 w-4" />
              API Integration
              {!canConfigureAPI && <Lock className="h-3 w-3" />}
            </TabsTrigger>
          </TabsList>

          {/* Tab Content */}
          <TabsContent value="manual" className="space-y-4">
            {canCreateUnitLogs ? (
              <div className="max-w-4xl mx-auto">
                <ManualDataEntry
                  projectId={projectId}
                  projectType={projectData?.type}
                  projectSubType={projectData?.metadata?.projectSubType}
                  onSuccess={() => handleSuccess("Data entry saved successfully!")}
                  onCancel={handleCancel}
                  apiContext={context} // Pass context to component
                />
              </div>
            ) : (
              <Alert variant="destructive">
                <Lock className="h-4 w-4" />
                <AlertDescription>
                  You don't have permission to create unit logs. Contact your administrator.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="csv" className="space-y-4">
            {canUploadCSV ? (
              <div className="max-w-6xl mx-auto">
                <CsvUpload
                  projectId={projectId}
                  projectType={projectData?.type}
                  projectSubType={projectData?.metadata?.projectSubType}
                  onSuccess={() => handleSuccess("CSV data uploaded successfully!")}
                  onCancel={handleCancel}
                  apiContext={context} // Pass context to component
                />
              </div>
            ) : (
              <Alert variant="destructive">
                <Lock className="h-4 w-4" />
                <AlertDescription>
                  You don't have permission to upload CSV files. Contact your administrator.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>

          <TabsContent value="api" className="space-y-4">
            {canConfigureAPI ? (
              <div className="max-w-6xl mx-auto">
                <ApiIntegrationConfig
                  projectId={projectId}
                  onSuccess={() => handleSuccess("API integration configured successfully!")}
                  onCancel={handleCancel}
                />
              </div>
            ) : (
              <Alert variant="destructive">
                <Lock className="h-4 w-4" />
                <AlertDescription>
                  You don't have permission to configure API integrations. Contact your administrator.
                </AlertDescription>
              </Alert>
            )}
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
