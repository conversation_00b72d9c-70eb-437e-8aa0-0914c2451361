import { Metadata } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import UnifiedDataEntry from "./unified-data-entry";

export const metadata: Metadata = {
  title: "Data Entry - SPV Portal",
  description: "Enter and manage unit log data for assigned projects",
};

export default async function DataEntryPage() {
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <UnifiedDataEntry context="spv" />
      </div>
    </SPVAuthGuard>
  );
}
