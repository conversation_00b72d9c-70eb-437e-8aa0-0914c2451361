"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { 
  AlertTriangle, 
  RefreshCw, 
  Calendar, 
  User, 
  FileText,
  ArrowLeft,
  Edit
} from "lucide-react";
import { ManualDataEntry } from "@/components/projects/monitoring/manual-data-entry";
import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import { useRouter } from "next/navigation";
import { toast } from "@/components/ui/use-toast";

interface RejectedEntry {
  id: string;
  project: {
    id: string;
    name: string;
    type: string;
    subtype?: string;
  };
  rejectionFeedback: {
    unitLogId: string;
    rejectionReason: string;
    rejectedBy: string;
    rejectedAt: string;
    originalData: {
      logDate: string;
      unitType: string;
      quantity: number;
      frequency: string;
      metadata?: any;
    };
  };
  verificationStatus: string;
  createdAt: string;
  updatedAt: string;
}

export default function RejectedEntriesClient() {
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const router = useRouter();
  const [rejectedEntries, setRejectedEntries] = useState<RejectedEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedEntry, setSelectedEntry] = useState<RejectedEntry | null>(null);
  const [isResubmissionModalOpen, setIsResubmissionModalOpen] = useState(false);

  useEffect(() => {
    if (spvUser) {
      fetchRejectedEntries();
    }
  }, [spvUser]);

  const fetchRejectedEntries = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/spv/unit-logs/rejected");
      
      if (response.ok) {
        const data = await response.json();
        setRejectedEntries(data.data || []);
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Failed to fetch rejected entries");
      }
    } catch (err) {
      setError("Error fetching rejected entries");
      console.error("Fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleResubmit = (entry: RejectedEntry) => {
    setSelectedEntry(entry);
    setIsResubmissionModalOpen(true);
  };

  const handleResubmissionSuccess = () => {
    setIsResubmissionModalOpen(false);
    setSelectedEntry(null);
    fetchRejectedEntries(); // Refresh the list
    toast({
      title: "Success",
      description: "Entry resubmitted successfully",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-orange-100 text-orange-800';
      case 'PM_VERIFIED':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (userLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-8 w-8" />
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        
        <div className="grid gap-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-32" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-4 w-full mb-2" />
                <Skeleton className="h-4 w-3/4" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (userError || error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertDescription>
            {userError || error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <AlertTriangle className="h-8 w-8 text-orange-500" />
            Rejected Entries
          </h1>
          <p className="text-muted-foreground">
            Review and resubmit your rejected data entries
          </p>
        </div>
      </div>

      {/* Rejected Entries List */}
      {rejectedEntries.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <RefreshCw className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">No rejected entries</h3>
            <p className="text-muted-foreground">
              You don't have any rejected entries that need resubmission.
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {rejectedEntries.map((entry) => (
            <Card key={entry.id} className="border-l-4 border-l-orange-500">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      {entry.project.name}
                    </CardTitle>
                    <CardDescription>
                      {entry.rejectionFeedback.originalData.unitType} • {entry.rejectionFeedback.originalData.quantity} units
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(entry.verificationStatus)}>
                    {entry.verificationStatus.replace('_', ' ')}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Rejection Details */}
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">Rejection Reason:</p>
                      <p className="text-sm">{entry.rejectionFeedback.rejectionReason}</p>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          Rejected by {entry.rejectionFeedback.rejectedBy}
                        </span>
                        <span className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {new Date(entry.rejectionFeedback.rejectedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>

                {/* Original Data Summary */}
                <div className="bg-muted/50 p-3 rounded-lg">
                  <h4 className="font-medium mb-2">Original Entry Details:</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Date:</span>
                      <p>{new Date(entry.rejectionFeedback.originalData.logDate).toLocaleDateString()}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Type:</span>
                      <p>{entry.rejectionFeedback.originalData.unitType}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Quantity:</span>
                      <p>{entry.rejectionFeedback.originalData.quantity}</p>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Frequency:</span>
                      <p>{entry.rejectionFeedback.originalData.frequency}</p>
                    </div>
                  </div>
                </div>

                {/* Action Button */}
                <div className="flex justify-end">
                  <Button onClick={() => handleResubmit(entry)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit & Resubmit
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Resubmission Modal */}
      <Dialog open={isResubmissionModalOpen} onOpenChange={setIsResubmissionModalOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit and Resubmit Entry</DialogTitle>
          </DialogHeader>
          {selectedEntry && (
            <ManualDataEntry
              projectId={selectedEntry.project.id}
              projectType={selectedEntry.project.type}
              projectSubType={selectedEntry.project.subtype}
              apiContext="spv"
              rejectionFeedback={selectedEntry.rejectionFeedback}
              onSuccess={handleResubmissionSuccess}
              onCancel={() => setIsResubmissionModalOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
