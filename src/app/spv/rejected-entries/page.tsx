import { Metadata } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import RejectedEntriesClient from "./rejected-entries-client";

export const metadata: Metadata = {
  title: "Rejected Entries",
  description: "View and resubmit rejected data entries",
};

export default async function RejectedEntriesPage() {
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <RejectedEntriesClient />
      </div>
    </SPVAuthGuard>
  );
}
