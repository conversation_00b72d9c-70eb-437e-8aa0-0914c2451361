"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import { VerificationActionModal } from "@/components/spv/verification-action-modal";
import {
  ClipboardCheck,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Eye,
  FileText,
} from "lucide-react";
import { toast } from "sonner";

interface UnitLogWithVerification {
  id: string;
  logDate: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  verificationStatus: string;
  verificationNotes?: string;
  createdAt: string;
  availableActions?: string[];
  logger: {
    id: string;
    name: string;
    email: string;
  };
  verifier?: {
    id: string;
    name: string;
    email: string;
  };
  project: {
    id: string;
    name: string;
    type: string;
    status: string;
  };
  verificationLogs: {
    id: string;
    fromStatus: string;
    toStatus: string;
    verificationNotes?: string;
    createdAt: string;
    verifier: {
      id: string;
      name: string;
      email: string;
      role: string;
    };
  }[];
}

export default function VerificationQueueClient() {
  // All hooks must be called before any conditional returns
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const [unitLogs, setUnitLogs] = useState<UnitLogWithVerification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("ALL");
  const [projectFilter, setProjectFilter] = useState<string>("");
  const [selectedUnitLog, setSelectedUnitLog] = useState<UnitLogWithVerification | null>(null);
  const [isActionModalOpen, setIsActionModalOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [userRole, setUserRole] = useState<string>("");

  useEffect(() => {
    if (spvUser) {
      fetchVerificationQueue();
    }
  }, [spvUser, currentPage, statusFilter, projectFilter]);

  const fetchVerificationQueue = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(statusFilter && statusFilter !== "ALL" && { status: statusFilter }),
        ...(projectFilter && { projectId: projectFilter }),
      });

      const response = await fetch(`/api/spv/verification?${params}`);
      if (response.ok) {
        const data = await response.json();
        setUnitLogs(data.data.unitLogs);
        setTotalPages(data.data.pagination.totalPages);
        setUserRole(data.data.userRole);
      } else {
        setError("Failed to fetch verification queue");
      }
    } catch (err) {
      setError("Error fetching verification queue");
      console.error("Verification queue fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleVerificationAction = async (action: string, notes?: string, rejectionReason?: string) => {
    if (!selectedUnitLog) return;



    try {
      const response = await fetch("/api/spv/verification", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          unitLogId: selectedUnitLog.id,
          action,
          notes,
          rejectionReason,
        }),
      });

      if (response.ok) {
        toast.success("Verification action completed successfully");
        setIsActionModalOpen(false);
        setSelectedUnitLog(null);
        fetchVerificationQueue();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to perform verification action");
        // Don't close modal on error so user can see the error and retry
        throw new Error(errorData.error || "Failed to perform verification action");
      }
    } catch (error) {
      toast.error("Error performing verification action");
      console.error("Verification action error:", error);
      // Re-throw error so modal knows the action failed
      throw error;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'SUBMITTED_FOR_VERIFICATION':
        return 'bg-yellow-100 text-yellow-800';
      case 'PM_VERIFIED':
        return 'bg-blue-100 text-blue-800';
      case 'SPV_ADMIN_VERIFIED':
        return 'bg-purple-100 text-purple-800';
      case 'SUBMITTED_TO_ORG_ADMIN':
        return 'bg-indigo-100 text-indigo-800';
      case 'ORG_APPROVED':
        return 'bg-emerald-100 text-emerald-800';
      case 'SUBMITTED_TO_VVB':
        return 'bg-cyan-100 text-cyan-800';
      case 'VVB_VERIFIED':
        return 'bg-green-200 text-green-900';
      case 'VVB_REJECTED':
        return 'bg-red-200 text-red-900';
      case 'VERIFIED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'ORG_REJECTED':
        return 'bg-red-200 text-red-900';
      case 'SPV_APPROVED':
        return 'bg-blue-100 text-blue-800';
      case 'SPV_REJECTED':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'VERIFIED':
      case 'SPV_APPROVED':
      case 'SPV_ADMIN_VERIFIED':
      case 'ORG_APPROVED':
      case 'VVB_VERIFIED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REJECTED':
      case 'SPV_REJECTED':
      case 'ORG_REJECTED':
      case 'VVB_REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'SUBMITTED_FOR_VERIFICATION':
      case 'PM_VERIFIED':
      case 'SUBMITTED_TO_ORG_ADMIN':
      case 'SUBMITTED_TO_VVB':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };



  // Render loading state
  if (userLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Render error state
  if (userError || error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {userError || error}
        </AlertDescription>
      </Alert>
    );
  }

  // Render main content
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Verification Queue</h1>
          <p className="text-muted-foreground">
            Review and verify data entries • Role: {userRole?.replace('_', ' ')}
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search entries..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                autoComplete="off"
              />
            </div>
            
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">All Status</SelectItem>
                <SelectItem value="DRAFT">Draft</SelectItem>
                <SelectItem value="SUBMITTED_FOR_VERIFICATION">Submitted</SelectItem>
                <SelectItem value="PM_VERIFIED">PM Verified</SelectItem>
                <SelectItem value="SPV_ADMIN_VERIFIED">SPV Admin Verified</SelectItem>
                <SelectItem value="SUBMITTED_TO_ORG_ADMIN">Submitted to Org Admin</SelectItem>
                <SelectItem value="ORG_APPROVED">Org Approved</SelectItem>
                <SelectItem value="SUBMITTED_TO_VVB">Submitted to VVB</SelectItem>
                <SelectItem value="VVB_VERIFIED">VVB Verified</SelectItem>
                <SelectItem value="VERIFIED">Verified</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
                <SelectItem value="SPV_APPROVED">SPV Approved</SelectItem>
                <SelectItem value="SPV_REJECTED">SPV Rejected</SelectItem>
                <SelectItem value="ORG_REJECTED">Org Rejected</SelectItem>
                <SelectItem value="VVB_REJECTED">VVB Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Verification Queue Table */}
      <Card>
        <CardHeader>
          <CardTitle>Data Entries</CardTitle>
          <CardDescription>
            {unitLogs.length} entr{unitLogs.length !== 1 ? 'ies' : 'y'} in queue
          </CardDescription>
        </CardHeader>
        <CardContent>
          {unitLogs.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Project</TableHead>
                    <TableHead>Unit Type</TableHead>
                    <TableHead>Quantity</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Logger</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {unitLogs.map((unitLog) => {
                    const isOwnEntry = unitLog.logger.id === spvUser?.userId;
                    return (
                      <TableRow key={unitLog.id} className={isOwnEntry ? "bg-blue-50/30" : ""}>
                      <TableCell>
                        <div className="text-sm">
                          {new Date(unitLog.logDate).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{unitLog.project.name}</div>
                          <div className="text-sm text-muted-foreground">{unitLog.project.type}</div>
                        </div>
                      </TableCell>
                      <TableCell>{unitLog.unitType}</TableCell>
                      <TableCell>{unitLog.quantity}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(unitLog.verificationStatus)}
                          <Badge className={getStatusColor(unitLog.verificationStatus)}>
                            {unitLog.verificationStatus.replace('_', ' ')}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center gap-2">
                            <span>{unitLog.logger.name}</span>
                            {unitLog.logger.id === spvUser?.userId && (
                              <Badge variant="outline" className="text-xs">
                                You
                              </Badge>
                            )}
                          </div>
                          <div className="text-muted-foreground">{unitLog.logger.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          {unitLog.logger.id === spvUser?.userId ? (
                            // Own entry - can only view, cannot verify
                            <div className="flex items-center gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setSelectedUnitLog(unitLog);
                                  setIsActionModalOpen(true);
                                }}
                              >
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </Button>
                              <Badge variant="secondary" className="text-xs">
                                Your Entry
                              </Badge>
                            </div>
                          ) : (
                            // Other's entry - can verify if actions available
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setSelectedUnitLog(unitLog);
                                setIsActionModalOpen(true);
                              }}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              {unitLog.availableActions && unitLog.availableActions.length > 0 ? "Review & Verify" : "View Details"}
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <ClipboardCheck className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No entries in queue</h3>
              <p className="text-muted-foreground">
                There are no data entries requiring verification at this time.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center gap-2">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>
          <span className="flex items-center px-4">
            Page {currentPage} of {totalPages}
          </span>
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}

      {/* Verification Action Modal */}
      <VerificationActionModal
        isOpen={isActionModalOpen}
        onClose={() => {
          setIsActionModalOpen(false);
          setSelectedUnitLog(null);
        }}
        unitLog={selectedUnitLog}
        userRole={userRole}
        availableActions={selectedUnitLog?.availableActions || []}
        onAction={handleVerificationAction}
      />
    </div>
  );
}
