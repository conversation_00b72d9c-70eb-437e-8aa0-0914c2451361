import { Metadata } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import VerificationQueueClient from "./verification-queue-client";

export const metadata: Metadata = {
  title: "Verification Queue - SPV Portal",
  description: "Review and verify data entries in the SPV portal",
};

export default async function VerificationQueuePage() {
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <VerificationQueueClient />
      </div>
    </SPVAuthGuard>
  );
}
