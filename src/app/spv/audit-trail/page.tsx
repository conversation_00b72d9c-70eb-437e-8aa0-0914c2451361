"use client";

import { AuditTrail } from '@/components/audit/audit-trail';
import { useSPVUser } from '@/components/auth/client-spv-auth-guard';
import { AlertTriangle } from 'lucide-react';

export default function SPVAuditTrailPage() {
  const { spvUser, loading } = useSPVUser();

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!spvUser) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
          <p className="text-muted-foreground">
            You don't have permission to view SPV audit logs.
          </p>
        </div>
      </div>
    );
  }

  // Role-specific content and permissions
  const getRoleInfo = () => {
    switch (spvUser.role) {
      case 'SPV_ADMIN':
        return {
          title: 'SPV Administrator Audit Trail',
          description: 'Complete audit trail for all SPV activities, user actions, and project operations'
        };
      case 'PROJECT_MANAGER':
        return {
          title: 'Project Manager Audit Trail',
          description: 'Audit trail for assigned projects and related activities'
        };
      case 'SITE_WORKER':
        return {
          title: 'Site Worker Audit Trail',
          description: 'Personal audit trail for your data entries and activities'
        };
      default:
        return {
          title: 'Audit Trail',
          description: 'System audit trail'
        };
    }
  };

  const roleInfo = getRoleInfo();

  return (
    <div className="space-y-6">
      {/* Audit Trail Component */}
      <AuditTrail
        title={roleInfo.title}
        description={roleInfo.description}
        showFilters={true}
        showHeaderButtons={true}
        defaultFilters={{
          // Add role-specific default filters if needed
          resourceType: spvUser.role === 'SITE_WORKER' ? 'data_entry' : undefined,
        }}
      />
    </div>
  );
}
