import { Metada<PERSON> } from "next";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { UserRole } from "@/lib/authorization";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { LoginFormContent } from "@/app/(auth)/login/login-form-content";

export const metadata: Metadata = {
  title: "SPV Portal Login",
  description: "Login to the SPV Portal to access your assigned projects and data entry workflows",
};

export default async function SPVLoginPage() {
  const session = await auth();

  // If user is already authenticated, redirect based on role
  if (session?.user) {
    if (session.user.role === UserRole.SPV_USER) {
      redirect("/spv/dashboard");
    } else {
      redirect("/dashboard");
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">SPV Portal</h1>
          <p className="mt-2 text-sm text-gray-600">
            Access your assigned projects and manage data entry workflows
          </p>
        </div>

        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">Sign in</CardTitle>
            <CardDescription className="text-center">
              Enter your credentials to access the SPV portal
            </CardDescription>
          </CardHeader>
          <CardContent>
            <LoginFormContent />
          </CardContent>
        </Card>

        <div className="text-center">
          <p className="text-xs text-gray-500">
            Need access? Contact your organization administrator to get SPV portal credentials.
          </p>
        </div>
      </div>
    </div>
  );
}
