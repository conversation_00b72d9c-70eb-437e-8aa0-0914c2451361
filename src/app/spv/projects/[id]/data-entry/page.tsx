import { Metadata } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import UnifiedDataEntry from "../../../data-entry/unified-data-entry";

interface SPVProjectDataEntryPageProps {
  params: Promise<{
    id: string;
  }>;
}

export const metadata: Metadata = {
  title: "Data Entry - SPV Portal",
  description: "Enter monitoring data for assigned project",
};

export default async function SPVProjectDataEntryPage({ params }: SPVProjectDataEntryPageProps) {
  const resolvedParams = await params;
  
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <UnifiedDataEntry 
          context="spv" 
          projectId={resolvedParams.id}
          onBack={() => {
            // This will be handled by the component's router navigation
          }}
        />
      </div>
    </SPVAuthGuard>
  );
}
