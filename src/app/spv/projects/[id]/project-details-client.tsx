"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { toast } from "@/components/ui/use-toast";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSPVUser, useSPVPermissions } from "@/components/auth/client-spv-auth-guard";
import {
  ArrowLeft,
  MapPin,
  Calendar,
  TrendingUp,
  Building,
  FileText,
  Upload,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  AlertCircle,
  BarChart3,
  Users,
  Activity,
  Target,
  Zap,
  Plus,
  ExternalLink,
  Download,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { MonitoringDataTable } from "@/components/projects/monitoring/monitoring-data-table";
import { EstimatedCarbonCredits } from "@/components/projects/estimated-carbon-credits";
import { ProjectMonitoringDashboard } from "@/components/projects/monitoring/project-monitoring-dashboard";
import { ProjectAuditInline } from "@/components/projects/audit/project-audit-inline";

interface ProjectDetails {
  id: string;
  name: string;
  description?: string;
  type: string;
  status: string;
  verificationStatus?: string;
  location?: string;
  country?: string;
  coordinates?: string;
  area?: number;
  startDate?: string;
  endDate?: string;
  estimatedReductions?: number;
  actualReductions?: number;
  standard?: string;
  methodology?: string;
  methodologyVersion?: string;
  verifier?: string;
  validator?: string;
  budget?: number;
  roi?: number;
  sdgs?: string[];
  tags?: string[];
  metadata?: {
    projectSubType?: string;
    [key: string]: any;
  };
  createdAt: string;
  updatedAt?: string;
  organization: {
    id: string;
    name: string;
    legalName: string;
  };
  spv?: {
    id: string;
    name: string;
    purpose?: string;
    jurisdiction?: string;
  };
  documents: Array<{
    id: string;
    name: string;
    type: string;
    url: string;
    status: string;
    notes?: string;
    createdAt: string;
  }>;
  assignment?: {
    id: string;
    assignedAt: string;
    assignedBy: string;
    isActive: boolean;
  };
  statistics: {
    totalUnitLogs: number;
    draftCount: number;
    submittedCount: number;
    verifiedCount: number;
    approvedCount: number;
    rejectedCount: number;
    userUnitLogs: number;
    completionRate: number;
    rejectionRate: number;
  };
  recentUnitLogs: Array<{
    id: string;
    logDate: string;
    unitType: string;
    quantity: number;
    verificationStatus: string;
    createdAt: string;
    logger: {
      id: string;
      name: string;
      email: string;
    };
  }>;
  carbonCredits?: Array<{
    id: string;
    tokenId: string;
    amount: number;
    price: number;
    status: string;
    createdAt: string;
  }>;
  financialMetrics?: Array<{
    id: string;
    metricType: string;
    name: string;
    value: number;
    previousValue?: number;
    changePercent?: number;
    currency: string;
    period: string;
    startDate: string;
    endDate: string;
    target?: number;
    status?: string;
    notes?: string;
  }>;
}

interface AnalyticsData {
  overview: {
    totalProjects: number;
    totalUnitLogs: number;
    verificationRate: number;
    averageProcessingTime: number;
    monthlyGrowth: number;
  };
  verificationMetrics: {
    submitted: number;
    verified: number;
    approved: number;
    rejected: number;
    pending: number;
  };
  userPerformance: {
    activeUsers: number;
    topPerformers: Array<{
      id: string;
      name: string;
      entriesCount: number;
      approvalRate: number;
    }>;
  };
  projectProgress: Array<{
    id: string;
    name: string;
    type: string;
    completionRate: number;
    totalEntries: number;
    verifiedEntries: number;
  }>;
}

interface ProjectDetailsClientProps {
  projectId: string;
}

export default function ProjectDetailsClient({ projectId }: ProjectDetailsClientProps) {
  const router = useRouter();
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const { hasPermission } = useSPVPermissions();
  const [project, setProject] = useState<ProjectDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [analyticsLoading, setAnalyticsLoading] = useState(false);
  const [analyticsError, setAnalyticsError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [showDocumentUpload, setShowDocumentUpload] = useState(false);

  useEffect(() => {
    if (spvUser) {
      fetchProjectDetails();
    }
  }, [spvUser, projectId]);

  const fetchProjectDetails = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/spv/projects/${projectId}`);
      if (response.ok) {
        const data = await response.json();
        setProject(data.data.project);
      } else if (response.status === 403) {
        setError("Access denied. This project is not assigned to you.");
      } else if (response.status === 404) {
        setError("Project not found.");
      } else {
        setError("Failed to fetch project details");
      }
    } catch (err) {
      setError("Error fetching project details");
      console.error("Project details fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAnalyticsData = async () => {
    try {
      setAnalyticsLoading(true);
      setAnalyticsError(null);

      const params = new URLSearchParams({
        timeRange: "30d",
        projectId: projectId,
      });

      const response = await fetch(`/api/spv/analytics?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data.data);
      } else {
        setAnalyticsError("Failed to fetch analytics data");
      }
    } catch (err) {
      setAnalyticsError("Error fetching analytics data");
      console.error("Analytics fetch error:", err);
    } finally {
      setAnalyticsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'SUSPENDED':
        return 'bg-orange-100 text-orange-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getVerificationStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT':
        return 'bg-gray-100 text-gray-800';
      case 'SUBMITTED_FOR_VERIFICATION':
        return 'bg-yellow-100 text-yellow-800';
      case 'VERIFIED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'SPV_APPROVED':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'VERIFIED':
      case 'SPV_APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'REJECTED':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'SUBMITTED_FOR_VERIFICATION':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not specified";
    return format(new Date(dateString), "MMM d, yyyy");
  };

  const getStatusBadgeVariant = (status: string) => {
    const variants = {
      PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
      ACTIVE: "bg-green-100 text-green-800 border-green-200",
      COMPLETED: "bg-blue-100 text-blue-800 border-blue-200",
      SUSPENDED: "bg-red-100 text-red-800 border-red-200",
      CANCELLED: "bg-gray-100 text-gray-800 border-gray-200",
    };
    return variants[status] || "";
  };

  const getVerificationStatusBadgeVariant = (status: string) => {
    const variants = {
      PENDING: "bg-yellow-100 text-yellow-800 border-yellow-200",
      VERIFIED: "bg-green-100 text-green-800 border-green-200",
      REJECTED: "bg-red-100 text-red-800 border-red-200",
      EXPIRED: "bg-gray-100 text-gray-800 border-gray-200",
    };
    return variants[status] || "";
  };

  const handleRefreshData = () => {
    setRefreshTrigger(prev => prev + 1);
    fetchProjectDetails();
    toast({
      title: "Data Refreshed",
      description: "Project data has been updated successfully.",
    });
  };

  if (userLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-4">
          <Skeleton className="h-10 w-10" />
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[...Array(4)].map((_, i) => (
                    <Skeleton key={i} className="h-4 w-full" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          <div>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[...Array(3)].map((_, i) => (
                    <Skeleton key={i} className="h-12 w-full" />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  if (userError || error) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <Alert variant="destructive">
          <AlertDescription>
            {userError || error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!project) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
        </div>
        <Alert>
          <AlertDescription>
            Project not found or you don't have access to it.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const canCreateUnitLogs = hasPermission("create:unit_log");
  const canManageDocuments = hasPermission("manage:project_data") || spvUser?.role === "SPV_ADMIN" || spvUser?.role === "PROJECT_MANAGER";
  const canViewAnalytics = hasPermission("view:spv_analytics") || spvUser?.role === "SPV_ADMIN" || spvUser?.role === "PROJECT_MANAGER";
  const canEditProject = spvUser?.role === "SPV_ADMIN" || spvUser?.role === "PROJECT_MANAGER";

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-start justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/spv/projects">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Projects
            </Link>
          </Button>
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold tracking-tight">{project.name}</h1>
              <Badge variant="outline" className={getVerificationStatusBadgeVariant(project.verificationStatus || 'PENDING')}>
                {project.verificationStatus || 'PENDING'}
              </Badge>
              <Badge variant="outline" className={getStatusBadgeVariant(project.status)}>
                {project.status}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              {project.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())} Project • {project.organization.name}
            </p>
          </div>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" size="sm" onClick={handleRefreshData}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          {canEditProject && (
            <Button variant="outline" size="sm" asChild>
              <Link href={`/dashboard/projects/${project.id}/edit`}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Project
              </Link>
            </Button>
          )}
          {canCreateUnitLogs && (
            <Button asChild>
              <Link href={`/spv/data-entry?project=${project.id}`}>
                <Upload className="h-4 w-4 mr-2" />
                Add Data
              </Link>
            </Button>
          )}
        </div>
      </div>

      {/* Main Content with Full Width Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5 md:w-auto md:grid-cols-none md:flex">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="audit">Audit</TabsTrigger>
        </TabsList>

        <div className="mt-6">
          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Project Details</CardTitle>
                <CardDescription>
                  Comprehensive information about this project
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Description</h3>
                    <p className="font-normal">{project.description || "No description provided"}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Project Type</h3>
                      <p>{project.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Organization</h3>
                      <p>{project.organization?.name || "Not specified"}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Start Date</h3>
                      <p>{formatDate(project.startDate)}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">End Date</h3>
                      <p>{formatDate(project.endDate)}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Location</h3>
                      <p>{project.location || "Not specified"}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Country</h3>
                      <p>{project.country || "Not specified"}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Standard</h3>
                      <p>{project.standard || "Not specified"}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Methodology</h3>
                      <p>{project.methodology || "Not specified"}</p>
                    </div>
                  </div>
                </div>

                <Separator />

                <div>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">Emissions Reductions</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-muted-foreground">Estimated Reductions</p>
                      <p className="text-lg font-medium">{project.estimatedReductions?.toLocaleString() || "Not specified"} tCO2e</p>
                    </div>

                    <div>
                      <p className="text-sm text-muted-foreground">Actual Reductions</p>
                      <p className="text-lg font-medium">{project.actualReductions?.toLocaleString() || "Not specified"} tCO2e</p>
                    </div>
                  </div>
                </div>

                {/* Organization & SPV Info */}
                <div className="grid gap-4 md:grid-cols-2 pt-4 border-t">
                  <div>
                    <h4 className="font-medium mb-2">Organization</h4>
                    <div className="flex items-center text-muted-foreground">
                      <Building className="h-4 w-4 mr-2" />
                      <div>
                        <div>{project.organization.name}</div>
                        {project.organization.legalName !== project.organization.name && (
                          <div className="text-xs">{project.organization.legalName}</div>
                        )}
                      </div>
                    </div>
                  </div>

                  {project.spv && (
                    <div>
                      <h4 className="font-medium mb-2">SPV</h4>
                      <div className="flex items-center text-muted-foreground">
                        <Users className="h-4 w-4 mr-2" />
                        <div>
                          <div>{project.spv.name}</div>
                          {project.spv.jurisdiction && (
                            <div className="text-xs">{project.spv.jurisdiction}</div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Assignment Info */}
                {project.assignment && (
                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-2">Assignment Details</h4>
                    <div className="text-sm text-muted-foreground">
                      Assigned on {formatDate(project.assignment.assignedAt)}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="monitoring" className="space-y-6">
            {/* Add Data Button */}
            <div className="flex justify-between items-center">
              <div>
                <h2 className="text-2xl font-bold tracking-tight">Monitoring Data</h2>
                <p className="text-muted-foreground">
                  View and manage all monitoring data entries for this project
                </p>
              </div>
              {canCreateUnitLogs && (
                <Button onClick={() => router.push(`/spv/data-entry?project=${projectId}`)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Data
                </Button>
              )}
            </div>

            {/* Estimated Carbon Credits Card */}
            <EstimatedCarbonCredits
              projectId={projectId}
              refreshTrigger={refreshTrigger}
            />

            {/* Monitoring Data Table */}
            <MonitoringDataTable
              projectId={projectId}
              projectType={project.type}
              projectSubType={project.metadata?.projectSubType}
              refreshTrigger={refreshTrigger}
              apiContext="spv"
            />
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            {canViewAnalytics ? (
              <>
                <div className="flex justify-between items-center">
                  <div>
                    <h2 className="text-2xl font-bold tracking-tight">Project Analytics</h2>
                    <p className="text-muted-foreground">
                      Comprehensive analytics and insights for this project
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    onClick={fetchAnalyticsData}
                    disabled={analyticsLoading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${analyticsLoading ? 'animate-spin' : ''}`} />
                    {analyticsLoading ? "Loading..." : "Refresh Analytics"}
                  </Button>
                </div>

                {analyticsLoading ? (
                  <div className="grid gap-6 md:grid-cols-2">
                    {[...Array(4)].map((_, i) => (
                      <Card key={i}>
                        <CardHeader>
                          <Skeleton className="h-6 w-32" />
                        </CardHeader>
                        <CardContent>
                          <Skeleton className="h-20 w-full" />
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : analyticsError ? (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      {analyticsError}
                    </AlertDescription>
                  </Alert>
                ) : analyticsData ? (
                  <div className="space-y-6">
                    {/* Overview Metrics */}
                    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Total Entries</CardTitle>
                          <Activity className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{analyticsData.overview.totalUnitLogs}</div>
                          <p className="text-xs text-muted-foreground">
                            {analyticsData.overview.monthlyGrowth > 0 ? '+' : ''}{analyticsData.overview.monthlyGrowth}% from last month
                          </p>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Verification Rate</CardTitle>
                          <Target className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{analyticsData.overview.verificationRate}%</div>
                          <p className="text-xs text-muted-foreground">
                            Data quality score
                          </p>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Approved</CardTitle>
                          <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{analyticsData.verificationMetrics.approved}</div>
                          <p className="text-xs text-muted-foreground">
                            Approved entries
                          </p>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                          <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
                          <Zap className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                          <div className="text-2xl font-bold">{analyticsData.overview.averageProcessingTime}h</div>
                          <p className="text-xs text-muted-foreground">
                            Average processing
                          </p>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Verification Status Breakdown */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Verification Status</CardTitle>
                        <CardDescription>
                          Breakdown of data entry verification statuses
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
                          <div className="text-center p-4 bg-blue-50 rounded-lg">
                            <div className="text-2xl font-bold text-blue-600">{analyticsData.verificationMetrics.submitted}</div>
                            <div className="text-sm text-muted-foreground">Submitted</div>
                          </div>
                          <div className="text-center p-4 bg-yellow-50 rounded-lg">
                            <div className="text-2xl font-bold text-yellow-600">{analyticsData.verificationMetrics.pending}</div>
                            <div className="text-sm text-muted-foreground">Pending</div>
                          </div>
                          <div className="text-center p-4 bg-green-50 rounded-lg">
                            <div className="text-2xl font-bold text-green-600">{analyticsData.verificationMetrics.verified}</div>
                            <div className="text-sm text-muted-foreground">Verified</div>
                          </div>
                          <div className="text-center p-4 bg-emerald-50 rounded-lg">
                            <div className="text-2xl font-bold text-emerald-600">{analyticsData.verificationMetrics.approved}</div>
                            <div className="text-sm text-muted-foreground">Approved</div>
                          </div>
                          <div className="text-center p-4 bg-red-50 rounded-lg">
                            <div className="text-2xl font-bold text-red-600">{analyticsData.verificationMetrics.rejected}</div>
                            <div className="text-sm text-muted-foreground">Rejected</div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Top Performers */}
                    {analyticsData.userPerformance.topPerformers.length > 0 && (
                      <Card>
                        <CardHeader>
                          <CardTitle>Top Performers</CardTitle>
                          <CardDescription>
                            Users with highest approval rates
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-3">
                            {analyticsData.userPerformance.topPerformers.slice(0, 5).map((performer, index) => (
                              <div key={performer.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div className="flex items-center gap-3">
                                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium">
                                    {index + 1}
                                  </div>
                                  <div>
                                    <div className="font-medium">{performer.name}</div>
                                    <div className="text-sm text-muted-foreground">{performer.entriesCount} entries</div>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <div className="font-medium">{performer.approvalRate}%</div>
                                  <div className="text-sm text-muted-foreground">approval rate</div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )}
                  </div>
                ) : (
                  <Card>
                    <CardContent className="text-center py-12">
                      <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No analytics data</h3>
                      <p className="text-muted-foreground mb-4">
                        Analytics data will appear here once there are data entries for this project.
                      </p>
                      <Button onClick={fetchAnalyticsData}>
                        Load Analytics
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </>
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">Access Restricted</h3>
                  <p className="text-muted-foreground">
                    You don't have permission to view analytics for this project.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="documents" className="space-y-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Project Documents</CardTitle>
                  <CardDescription>
                    Supporting documents and files for this project
                  </CardDescription>
                </div>
                {canManageDocuments && (
                  <Button variant="outline" size="sm" onClick={() => router.push(`/spv/projects/${projectId}/documents/upload`)}>
                    <Plus className="mr-2 h-4 w-4" />
                    Upload Document
                  </Button>
                )}
              </CardHeader>
              <CardContent>
                {project.documents && project.documents.length > 0 ? (
                  <div className="space-y-4">
                    {project.documents.map((document) => (
                      <div key={document.id} className="flex items-center justify-between p-4 border rounded-md">
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{document.name}</p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>{document.type.replace(/_/g, " ").toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}</span>
                              <span>•</span>
                              <span>{formatDate(document.createdAt)}</span>
                              {document.notes && (
                                <>
                                  <span>•</span>
                                  <span>{document.notes}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className={
                            document.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                            document.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                          }>
                            {document.status}
                          </Badge>
                          <Button variant="ghost" size="sm" asChild>
                            <a href={document.url} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="mr-2 h-4 w-4" />
                              View
                            </a>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No documents</h3>
                    <p className="text-muted-foreground mb-4">
                      No documents have been uploaded for this project yet.
                    </p>
                    {canManageDocuments && (
                      <Button variant="outline" onClick={() => router.push(`/spv/projects/${projectId}/documents/upload`)}>
                        <Plus className="mr-2 h-4 w-4" />
                        Upload Document
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="audit" className="space-y-6">
            <ProjectAuditInline projectId={projectId} />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}
