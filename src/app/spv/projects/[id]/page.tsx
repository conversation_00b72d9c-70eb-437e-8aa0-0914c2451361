import { Metadata } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import ProjectDetailsClient from "./project-details-client";

export const metadata: Metadata = {
  title: "Project Details - SPV Portal",
  description: "View project details and manage unit log data",
};

interface ProjectDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function ProjectDetailsPage({ params }: ProjectDetailsPageProps) {
  const resolvedParams = await params;

  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <ProjectDetailsClient projectId={resolvedParams.id} />
      </div>
    </SPVAuthGuard>
  );
}
