"use client";

import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import {
  ArrowLeft,
  FileText,
  Clock,
  User,
  Activity,
  Shield,
  CheckCircle,
  AlertCircle,
  Eye,
  Download,
  GitBranch,
  RefreshCw,
} from "lucide-react";
import Link from "next/link";
import { format } from "date-fns";
import { DataEntryFlowTimeline } from "@/components/projects/audit/data-entry-flow-timeline";
// import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";

interface AuditLog {
  id: string;
  type: string;
  description: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

interface VerificationHistory {
  id: string;
  status: string;
  verifier?: string;
  verifierEmail?: string;
  notes?: string;
  timestamp: string;
  metadata?: any;
}

interface ComplianceDocument {
  id: string;
  type: string;
  name: string;
  url: string;
  status: string;
  notes?: string;
  metadata?: any;
  expiresAt?: string;
  verifier?: string;
  verificationDate?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

interface SPVProjectAuditPageProps {
  params: Promise<{ id: string }>;
}

export default function SPVProjectAuditPage({ params }: SPVProjectAuditPageProps) {
  const router = useRouter();
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([]);
  const [verificationHistory, setVerificationHistory] = useState<VerificationHistory[]>([]);
  const [complianceDocuments, setComplianceDocuments] = useState<ComplianceDocument[]>([]);
  const [activeTab, setActiveTab] = useState("audit-logs");
  
  // Unwrap params Promise
  const resolvedParams = use(params);
  const projectId = resolvedParams.id;

  useEffect(() => {
    if (spvUser) {
      fetchAuditData();
    }
  }, [spvUser, projectId]);

  const fetchAuditData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch audit logs
      const auditResponse = await fetch(`/api/spv/projects/${projectId}/audit`);
      if (auditResponse.ok) {
        const auditData = await auditResponse.json();
        setAuditLogs(auditData.auditLogs || []);
        setVerificationHistory(auditData.verificationHistory || []);
        setComplianceDocuments(auditData.complianceDocuments || []);
      } else {
        throw new Error("Failed to fetch audit data");
      }
    } catch (err) {
      setError("Error fetching audit data");
      console.error("Audit data fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toUpperCase()) {
      case 'APPROVED':
      case 'VERIFIED':
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
      case 'IN_REVIEW':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      case 'EXPIRED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-blue-100 text-blue-800';
    }
  };

  const getAuditTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'verification':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'document_upload':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'data_entry':
        return <Activity className="h-4 w-4 text-purple-500" />;
      case 'access':
        return <Shield className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  if (userLoading || loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-10" />
            <div>
              <Skeleton className="h-8 w-64 mb-2" />
              <Skeleton className="h-4 w-96" />
            </div>
          </div>

          <div className="grid gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <CardHeader>
                  <Skeleton className="h-6 w-32" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[...Array(4)].map((_, j) => (
                      <Skeleton key={j} className="h-4 w-full" />
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (userError || error) {
    return (
      <div className="container mx-auto py-6">
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          <Alert variant="destructive">
            <AlertDescription>
              {userError || error}
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" asChild>
                <Link href={`/spv/projects/${projectId}`}>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Project
                </Link>
              </Button>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Project Audit Trail</h1>
                <p className="text-muted-foreground">
                  Complete audit history and compliance tracking
                </p>
              </div>
            </div>
            <Button variant="outline" onClick={fetchAuditData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList>
              <TabsTrigger value="audit-logs">Audit Logs</TabsTrigger>
              <TabsTrigger value="data-entry-flow">
                <GitBranch className="h-4 w-4 mr-2" />
                Data Entry Flow
              </TabsTrigger>
              <TabsTrigger value="verification">Verification History</TabsTrigger>
              <TabsTrigger value="compliance">Compliance Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="audit-logs" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Audit Logs</CardTitle>
                  <CardDescription>
                    Complete activity log for this project
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {auditLogs.length > 0 ? (
                    <div className="space-y-4">
                      {auditLogs.map((log) => (
                        <div key={log.id} className="flex items-start gap-4 p-4 border rounded-lg">
                          <div className="flex-shrink-0 mt-1">
                            {getAuditTypeIcon(log.type)}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <p className="font-medium">{log.description}</p>
                              <span className="text-sm text-muted-foreground">
                                {format(new Date(log.createdAt), "MMM d, yyyy 'at' h:mm a")}
                              </span>
                            </div>
                            {log.user && (
                              <div className="flex items-center gap-2 mt-1 text-sm text-muted-foreground">
                                <User className="h-3 w-3" />
                                <span>{log.user.name} ({log.user.email})</span>
                              </div>
                            )}
                            {log.metadata && (
                              <div className="mt-2 text-xs text-muted-foreground">
                                <pre className="whitespace-pre-wrap">
                                  {JSON.stringify(log.metadata, null, 2)}
                                </pre>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No audit logs</h3>
                      <p className="text-muted-foreground">
                        No audit activities have been recorded for this project yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="data-entry-flow" className="space-y-6">
              <DataEntryFlowTimeline projectId={projectId} />
            </TabsContent>

            <TabsContent value="verification" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Verification History</CardTitle>
                  <CardDescription>
                    Project verification and approval history
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {verificationHistory.length > 0 ? (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Status</TableHead>
                            <TableHead>Verifier</TableHead>
                            <TableHead>Notes</TableHead>
                            <TableHead>Date</TableHead>
                            <TableHead>Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {verificationHistory.map((verification) => (
                            <TableRow key={verification.id}>
                              <TableCell>
                                <Badge className={getStatusColor(verification.status)}>
                                  {verification.status}
                                </Badge>
                              </TableCell>
                              <TableCell>
                                {verification.verifier || verification.verifierEmail || "System"}
                              </TableCell>
                              <TableCell>
                                <div className="max-w-xs truncate">
                                  {verification.notes || "No notes"}
                                </div>
                              </TableCell>
                              <TableCell>
                                {format(new Date(verification.timestamp), "MMM d, yyyy")}
                              </TableCell>
                              <TableCell>
                                <Button size="sm" variant="outline">
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <CheckCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No verification history</h3>
                      <p className="text-muted-foreground">
                        No verification activities have been recorded for this project yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="compliance" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Compliance Documents</CardTitle>
                  <CardDescription>
                    Regulatory compliance and certification documents
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {complianceDocuments.length > 0 ? (
                    <div className="space-y-4">
                      {complianceDocuments.map((document) => (
                        <div key={document.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <FileText className="h-5 w-5 text-muted-foreground" />
                            <div>
                              <p className="font-medium">{document.name}</p>
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <span>{document.type.replace(/_/g, " ")}</span>
                                <span>•</span>
                                <span>{format(new Date(document.createdAt), "MMM d, yyyy")}</span>
                                {document.expiresAt && (
                                  <>
                                    <span>•</span>
                                    <span>Expires: {format(new Date(document.expiresAt), "MMM d, yyyy")}</span>
                                  </>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge className={getStatusColor(document.status)}>
                              {document.status}
                            </Badge>
                            <Button size="sm" variant="outline" asChild>
                              <a href={document.url} target="_blank" rel="noopener noreferrer">
                                <Eye className="h-4 w-4" />
                              </a>
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No compliance documents</h3>
                      <p className="text-muted-foreground">
                        No compliance documents have been uploaded for this project yet.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
  );
}
