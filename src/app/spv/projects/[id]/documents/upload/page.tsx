"use client";

import { useState, use } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { ArrowLeft, FileText, Upload } from "lucide-react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
// import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";

// Define form schema
const documentSchema = z.object({
  name: z.string().min(1, "Document name is required"),
  type: z.string().min(1, "Document type is required"),
  url: z.string().url("Please enter a valid URL"),
  notes: z.string().optional(),
});

type DocumentFormValues = z.infer<typeof documentSchema>;

const documentTypes = [
  { value: "PROJECT_DESIGN_DOCUMENT", label: "Project Design Document" },
  { value: "MONITORING_PLAN", label: "Monitoring Plan" },
  { value: "VALIDATION_REPORT", label: "Validation Report" },
  { value: "VERIFICATION_REPORT", label: "Verification Report" },
  { value: "ENVIRONMENTAL_IMPACT_ASSESSMENT", label: "Environmental Impact Assessment" },
  { value: "STAKEHOLDER_CONSULTATION", label: "Stakeholder Consultation" },
  { value: "TECHNICAL_SPECIFICATION", label: "Technical Specification" },
  { value: "FINANCIAL_ANALYSIS", label: "Financial Analysis" },
  { value: "RISK_ASSESSMENT", label: "Risk Assessment" },
  { value: "COMPLIANCE_CERTIFICATE", label: "Compliance Certificate" },
  { value: "OTHER", label: "Other Document" },
];

interface SPVProjectDocumentUploadPageProps {
  params: Promise<{ id: string }>;
}

export default function SPVProjectDocumentUploadPage({ params }: SPVProjectDocumentUploadPageProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Unwrap params Promise
  const resolvedParams = use(params);
  const projectId = resolvedParams.id;
  
  // Initialize form
  const form = useForm<DocumentFormValues>({
    resolver: zodResolver(documentSchema),
    defaultValues: {
      name: "",
      type: "",
      url: "",
      notes: "",
    },
  });
  
  // Handle form submission
  const onSubmit = async (data: DocumentFormValues) => {
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/spv/projects/${projectId}/documents`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to upload document");
      }
      
      toast({
        title: "Document Uploaded",
        description: "Your document has been uploaded successfully.",
      });
      
      // Redirect back to project details
      router.push(`/spv/projects/${projectId}?tab=documents`);
    } catch (error) {
      console.error("Error uploading document:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to upload document",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-6">
        <div className="max-w-2xl mx-auto space-y-6">
          {/* Header */}
          <div className="flex items-center gap-4">
            <Button variant="outline" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Upload Document</h1>
              <p className="text-muted-foreground">
                Add a new document to this project
              </p>
            </div>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Document Details</CardTitle>
              <CardDescription>
                Enter the details of the document you want to upload
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter document name" {...field} />
                        </FormControl>
                        <FormDescription>
                          A descriptive name for the document
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select document type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {documentTypes.map((type) => (
                              <SelectItem key={type.value} value={type.value}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Choose the type of document you're uploading
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="url"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Document URL</FormLabel>
                        <FormControl>
                          <Input placeholder="https://example.com/document.pdf" {...field} />
                        </FormControl>
                        <FormDescription>
                          URL to the document (e.g., from Google Drive, Dropbox, etc.)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea placeholder="Additional notes about this document" {...field} />
                        </FormControl>
                        <FormDescription>
                          Any additional information about this document
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Alert>
                    <FileText className="h-4 w-4" />
                    <AlertTitle>Document Security</AlertTitle>
                    <AlertDescription>
                      Make sure the document URL is accessible to authorized users. For sensitive documents, consider using a secure sharing service.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="flex justify-end space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.push(`/spv/projects/${projectId}?tab=documents`)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" disabled={isSubmitting}>
                      <Upload className="h-4 w-4 mr-2" />
                      {isSubmitting ? "Uploading..." : "Upload Document"}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
  );
}
