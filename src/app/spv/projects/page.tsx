import { Metada<PERSON> } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import SPVProjectsClient from "./spv-projects-client";

export const metadata: Metadata = {
  title: "Assigned Projects - SPV Portal",
  description: "View and manage your assigned projects in the SPV portal",
};

export default async function SPVProjectsPage() {
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <SPVProjectsClient />
      </div>
    </SPVAuthGuard>
  );
}
