import { Metada<PERSON> } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import ReportsClient from "./reports-client";

export const metadata: Metadata = {
  title: "Reports - SPV Portal",
  description: "Generate and download reports for SPV operations",
};

export default async function ReportsPage() {
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <ReportsClient />
      </div>
    </SPVAuthGuard>
  );
}
