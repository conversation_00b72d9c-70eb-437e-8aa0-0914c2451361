"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useSPVUser, useSPVPermissions } from "@/components/auth/client-spv-auth-guard";
import {
  Download,
  FileText,
  Calendar,
  Filter,
  BarChart3,
  Users,
  CheckCircle,
  Clock,
  AlertCircle,
  FileSpreadsheet,
  File,
} from "lucide-react";
import { toast } from "sonner";

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  type: 'VERIFICATION_SUMMARY' | 'PROJECT_PROGRESS' | 'USER_PERFORMANCE' | 'COMPLIANCE_REPORT';
  icon: React.ReactNode;
  requiredRole?: string[];
}

interface Project {
  id: string;
  name: string;
  type: string;
  status: string;
}

export default function ReportsClient() {
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const { hasPermission } = useSPVPermissions();
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [generating, setGenerating] = useState(false);

  // Report configuration
  const [selectedTemplate, setSelectedTemplate] = useState<string>("");
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const [selectedProjects, setSelectedProjects] = useState<string[]>([]);
  const [reportFormat, setReportFormat] = useState<'PDF' | 'EXCEL'>('PDF');
  const [includeCharts, setIncludeCharts] = useState(true);
  const [includeRawData, setIncludeRawData] = useState(false);

  const reportTemplates: ReportTemplate[] = [
    {
      id: 'verification_summary',
      name: 'Verification Summary',
      description: 'Overview of verification status and metrics across projects',
      type: 'VERIFICATION_SUMMARY',
      icon: <CheckCircle className="h-5 w-5 text-green-500" />,
    },
    {
      id: 'project_progress',
      name: 'Project Progress Report',
      description: 'Detailed progress tracking for all assigned projects',
      type: 'PROJECT_PROGRESS',
      icon: <BarChart3 className="h-5 w-5 text-blue-500" />,
    },
    {
      id: 'user_performance',
      name: 'User Performance Report',
      description: 'Team productivity and individual performance metrics',
      type: 'USER_PERFORMANCE',
      icon: <Users className="h-5 w-5 text-purple-500" />,
      requiredRole: ['PROJECT_MANAGER', 'SPV_ADMIN'],
    },
    {
      id: 'compliance_report',
      name: 'Compliance Report',
      description: 'Regulatory compliance and audit trail documentation',
      type: 'COMPLIANCE_REPORT',
      icon: <FileText className="h-5 w-5 text-orange-500" />,
      requiredRole: ['SPV_ADMIN'],
    },
  ];

  useEffect(() => {
    if (spvUser) {
      fetchProjects();
    }
  }, [spvUser]);

  const fetchProjects = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/spv/projects");
      if (response.ok) {
        const data = await response.json();
        setProjects(data.data.projects || []);
      } else {
        setError("Failed to fetch projects");
      }
    } catch (err) {
      setError("Error fetching projects");
      console.error("Projects fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectToggle = (projectId: string) => {
    setSelectedProjects(prev => 
      prev.includes(projectId) 
        ? prev.filter(id => id !== projectId)
        : [...prev, projectId]
    );
  };

  const handleSelectAllProjects = () => {
    if (selectedProjects.length === projects.length) {
      setSelectedProjects([]);
    } else {
      setSelectedProjects(projects.map(p => p.id));
    }
  };

  const generateReport = async () => {
    if (!selectedTemplate) {
      toast.error("Please select a report template");
      return;
    }

    try {
      setGenerating(true);

      const reportConfig = {
        templateId: selectedTemplate,
        dateRange,
        projectIds: selectedProjects.length > 0 ? selectedProjects : undefined,
        format: reportFormat,
        options: {
          includeCharts,
          includeRawData,
        },
      };

      const response = await fetch("/api/spv/reports/generate", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(reportConfig),
      });

      if (response.ok) {
        // Handle file download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        
        const template = reportTemplates.find(t => t.id === selectedTemplate);
        const fileName = `${template?.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.${reportFormat.toLowerCase()}`;
        a.download = fileName;
        
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        toast.success("Report generated and downloaded successfully");
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || "Failed to generate report");
      }
    } catch (error) {
      toast.error("Error generating report");
      console.error("Report generation error:", error);
    } finally {
      setGenerating(false);
    }
  };

  const canAccessTemplate = (template: ReportTemplate) => {
    if (!template.requiredRole) return true;
    return template.requiredRole.includes(spvUser?.role);
  };

  if (userLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (userError || error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {userError || error}
        </AlertDescription>
      </Alert>
    );
  }

  // Check permissions for reports access
  const canGenerateReports = hasPermission("generate:reports") || spvUser?.role !== "SITE_WORKER";

  if (!canGenerateReports) {
    return (
      <Alert>
        <AlertDescription>
          You don't have permission to generate reports. Contact your SPV administrator.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
          <p className="text-muted-foreground">
            Generate and download reports for {spvUser?.spv?.name}
          </p>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Report Templates */}
        <Card>
          <CardHeader>
            <CardTitle>Report Templates</CardTitle>
            <CardDescription>
              Choose a report template to generate
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {reportTemplates.map((template) => (
                <div
                  key={template.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedTemplate === template.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  } ${
                    !canAccessTemplate(template) ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  onClick={() => canAccessTemplate(template) && setSelectedTemplate(template.id)}
                >
                  <div className="flex items-start gap-3">
                    {template.icon}
                    <div className="flex-1">
                      <h4 className="font-medium">{template.name}</h4>
                      <p className="text-sm text-muted-foreground">{template.description}</p>
                      {template.requiredRole && (
                        <div className="mt-2">
                          <Badge variant="outline" className="text-xs">
                            {template.requiredRole.join(', ')} only
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Report Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>Report Configuration</CardTitle>
            <CardDescription>
              Configure report parameters and options
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Date Range */}
            <div className="space-y-2">
              <Label>Date Range</Label>
              <div className="grid grid-cols-2 gap-2">
                <div>
                  <Label htmlFor="startDate" className="text-xs">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                    value={dateRange.startDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="endDate" className="text-xs">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                    value={dateRange.endDate}
                    onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
                  />
                </div>
              </div>
            </div>

            {/* Project Selection */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label>Projects</Label>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAllProjects}
                >
                  {selectedProjects.length === projects.length ? 'Deselect All' : 'Select All'}
                </Button>
              </div>
              <div className="max-h-32 overflow-y-auto space-y-2 border rounded p-2">
                {projects.map((project) => (
                  <div key={project.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={project.id}
                      checked={selectedProjects.includes(project.id)}
                      onCheckedChange={() => handleProjectToggle(project.id)}
                    />
                    <Label htmlFor={project.id} className="text-sm">
                      {project.name} ({project.type})
                    </Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-muted-foreground">
                {selectedProjects.length === 0 ? 'All projects will be included' : `${selectedProjects.length} project(s) selected`}
              </p>
            </div>

            {/* Format Selection */}
            <div className="space-y-2">
              <Label>Report Format</Label>
              <Select value={reportFormat} onValueChange={(value: 'PDF' | 'EXCEL') => setReportFormat(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select format" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="PDF">
                    <div className="flex items-center gap-2">
                      <File className="h-4 w-4 text-red-500" />
                      PDF Document
                    </div>
                  </SelectItem>
                  <SelectItem value="EXCEL">
                    <div className="flex items-center gap-2">
                      <FileSpreadsheet className="h-4 w-4 text-green-500" />
                      Excel Spreadsheet
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Options */}
            <div className="space-y-3">
              <Label>Report Options</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeCharts"
                    checked={includeCharts}
                    onCheckedChange={(checked) => setIncludeCharts(checked as boolean)}
                  />
                  <Label htmlFor="includeCharts" className="text-sm">
                    Include charts and visualizations
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeRawData"
                    checked={includeRawData}
                    onCheckedChange={(checked) => setIncludeRawData(checked as boolean)}
                  />
                  <Label htmlFor="includeRawData" className="text-sm">
                    Include raw data tables
                  </Label>
                </div>
              </div>
            </div>

            {/* Generate Button */}
            <Button
              className="w-full"
              onClick={generateReport}
              disabled={!selectedTemplate || generating}
            >
              {generating ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  Generating Report...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Generate Report
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
