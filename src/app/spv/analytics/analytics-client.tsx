"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useSPVUser, useSPVPermissions } from "@/components/auth/client-spv-auth-guard";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Calendar,
  Download,
  Filter,
} from "lucide-react";

interface AnalyticsData {
  overview: {
    totalProjects: number;
    totalUnitLogs: number;
    verificationRate: number;
    averageProcessingTime: number;
    monthlyGrowth: number;
  };
  verificationMetrics: {
    submitted: number;
    verified: number;
    approved: number;
    rejected: number;
    pending: number;
  };
  userPerformance: {
    activeUsers: number;
    topPerformers: Array<{
      id: string;
      name: string;
      entriesCount: number;
      approvalRate: number;
    }>;
  };
  projectProgress: Array<{
    id: string;
    name: string;
    type: string;
    completionRate: number;
    totalEntries: number;
    verifiedEntries: number;
  }>;
  timeSeriesData: Array<{
    date: string;
    entries: number;
    verifications: number;
    approvals: number;
  }>;
}

export default function AnalyticsClient() {
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();
  const { hasPermission } = useSPVPermissions();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("30d");
  const [selectedProject, setSelectedProject] = useState<string>("all");

  useEffect(() => {
    if (spvUser) {
      fetchAnalyticsData();
    }
  }, [spvUser, timeRange, selectedProject]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        timeRange,
        ...(selectedProject !== "all" && { projectId: selectedProject }),
      });

      const response = await fetch(`/api/spv/analytics?${params}`);
      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data.data);
      } else {
        setError("Failed to fetch analytics data");
      }
    } catch (err) {
      setError("Error fetching analytics data");
      console.error("Analytics fetch error:", err);
    } finally {
      setLoading(false);
    }
  };

  const getMetricIcon = (metric: string) => {
    switch (metric) {
      case 'verified':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'rejected':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-blue-500" />;
    }
  };

  const getTrendIcon = (value: number) => {
    if (value > 0) {
      return <TrendingUp className="h-4 w-4 text-green-500" />;
    } else if (value < 0) {
      return <TrendingDown className="h-4 w-4 text-red-500" />;
    }
    return <TrendingUp className="h-4 w-4 text-gray-500" />;
  };

  if (userLoading || loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-48 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-24" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (userError || error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {userError || error}
        </AlertDescription>
      </Alert>
    );
  }

  // Check permissions for analytics access
  const canViewAnalytics = hasPermission("view:spv_analytics") || spvUser?.role === "SPV_ADMIN";

  if (!canViewAnalytics) {
    return (
      <Alert>
        <AlertDescription>
          You don't have permission to view analytics. Contact your SPV administrator.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Performance metrics and insights for {spvUser?.spv?.name}
          </p>
        </div>
        <div className="flex gap-3">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 3 months</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Projects</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData?.overview.totalProjects || 0}</div>
            <div className="flex items-center text-xs text-muted-foreground">
              {getTrendIcon(analyticsData?.overview.monthlyGrowth || 0)}
              <span className="ml-1">
                {analyticsData?.overview.monthlyGrowth || 0}% from last month
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Entries</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData?.overview.totalUnitLogs || 0}</div>
            <p className="text-xs text-muted-foreground">
              Data entries across all projects
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Verification Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData?.overview.verificationRate || 0}%</div>
            <p className="text-xs text-muted-foreground">
              Successfully verified entries
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Processing Time</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analyticsData?.overview.averageProcessingTime || 0}h</div>
            <p className="text-xs text-muted-foreground">
              From submission to approval
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Verification Status Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Verification Status</CardTitle>
            <CardDescription>
              Current status of all data entries
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsData?.verificationMetrics ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getMetricIcon('verified')}
                    <span className="text-sm">Verified</span>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{analyticsData.verificationMetrics.verified}</div>
                    <div className="text-xs text-muted-foreground">
                      {Math.round((analyticsData.verificationMetrics.verified / analyticsData.overview.totalUnitLogs) * 100)}%
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getMetricIcon('pending')}
                    <span className="text-sm">Pending</span>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{analyticsData.verificationMetrics.pending}</div>
                    <div className="text-xs text-muted-foreground">
                      {Math.round((analyticsData.verificationMetrics.pending / analyticsData.overview.totalUnitLogs) * 100)}%
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    {getMetricIcon('rejected')}
                    <span className="text-sm">Rejected</span>
                  </div>
                  <div className="text-right">
                    <div className="font-medium">{analyticsData.verificationMetrics.rejected}</div>
                    <div className="text-xs text-muted-foreground">
                      {Math.round((analyticsData.verificationMetrics.rejected / analyticsData.overview.totalUnitLogs) * 100)}%
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-6">
                <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No verification data available</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Top Performers */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performers</CardTitle>
            <CardDescription>
              Users with highest approval rates
            </CardDescription>
          </CardHeader>
          <CardContent>
            {analyticsData?.userPerformance.topPerformers?.length ? (
              <div className="space-y-4">
                {analyticsData.userPerformance.topPerformers.slice(0, 5).map((user, index) => (
                  <div key={user.id} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-6 h-6 rounded-full bg-blue-100 text-blue-600 text-xs font-medium flex items-center justify-center">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{user.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {user.entriesCount} entries
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-sm">{user.approvalRate}%</div>
                      <div className="text-xs text-muted-foreground">approval rate</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground">No performance data available</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Project Progress */}
      <Card>
        <CardHeader>
          <CardTitle>Project Progress</CardTitle>
          <CardDescription>
            Completion status across all assigned projects
          </CardDescription>
        </CardHeader>
        <CardContent>
          {analyticsData?.projectProgress?.length ? (
            <div className="space-y-4">
              {analyticsData.projectProgress.map((project) => (
                <div key={project.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-sm">{project.name}</h4>
                      <p className="text-xs text-muted-foreground">{project.type}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">{project.completionRate}%</div>
                      <div className="text-xs text-muted-foreground">
                        {project.verifiedEntries}/{project.totalEntries} verified
                      </div>
                    </div>
                  </div>
                  <Progress value={project.completionRate} className="h-2" />
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No project data available</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
