import { Metadata } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import AnalyticsClient from "./analytics-client";

export const metadata: Metadata = {
  title: "Analytics - SPV Portal",
  description: "View analytics and performance metrics for SPV operations",
};

export default async function AnalyticsPage() {
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <AnalyticsClient />
      </div>
    </SPVAuthGuard>
  );
}
