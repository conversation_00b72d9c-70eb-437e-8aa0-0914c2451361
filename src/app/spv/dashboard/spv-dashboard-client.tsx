"use client";

import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { Skeleton } from "@/components/ui/skeleton";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { SiteWorkerDashboard } from "@/components/spv/dashboards/site-worker-dashboard";
import { ProjectManagerDashboard } from "@/components/spv/dashboards/project-manager-dashboard";
import { SPVAdminDashboard } from "@/components/spv/dashboards/spv-admin-dashboard";

export default function SPVDashboardClient() {
  const { spvUser, loading: userLoading, error: userError } = useSPVUser();

  if (userLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Skeleton className="h-8 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
        </div>
        
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-8 w-16 mb-2" />
                <Skeleton className="h-3 w-32" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (userError) {
    return (
      <Alert variant="destructive">
        <AlertDescription>
          {userError}
        </AlertDescription>
      </Alert>
    );
  }

  if (!spvUser) {
    return (
      <Alert>
        <AlertDescription>
          SPV user information not found. Please contact your administrator.
        </AlertDescription>
      </Alert>
    );
  }

  // Render role-specific dashboard
  switch (spvUser.role) {
    case "SITE_WORKER":
      return <SiteWorkerDashboard spvUser={spvUser} />;
    case "PROJECT_MANAGER":
      return <ProjectManagerDashboard spvUser={spvUser} />;
    case "SPV_ADMIN":
      return <SPVAdminDashboard spvUser={spvUser} />;
    default:
      return (
        <Alert variant="destructive">
          <AlertDescription>
            Unknown SPV role: {spvUser.role}. Please contact your administrator.
          </AlertDescription>
        </Alert>
      );
  }
}
