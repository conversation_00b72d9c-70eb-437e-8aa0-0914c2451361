import { <PERSON>ada<PERSON> } from "next";
import { SPVAuthGuard } from "@/components/auth/spv-auth-guard";
import SPVDashboardClient from "./spv-dashboard-client";

export const metadata: Metadata = {
  title: "SPV Dashboard",
  description: "SPV portal dashboard showing assigned projects and verification workflow status",
};

export default async function SPVDashboardPage() {
  return (
    <SPVAuthGuard>
      <div className="container mx-auto py-6">
        <SPVDashboardClient />
      </div>
    </SPVAuthGuard>
  );
}
