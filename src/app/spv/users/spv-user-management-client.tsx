"use client";

import { useState, useEffect } from "react";
import { Search, Filter, Edit, Trash2, Eye, UserPlus, FolderOpen, Users } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useSPVUser } from "@/components/auth/client-spv-auth-guard";
import { SPVUserCreateForm } from "@/components/spv/spv-user-create-form";
import { SPVProjectAssignmentForm } from "@/components/spv/spv-project-assignment-form";
import { SPVProjectAssignmentsTable } from "@/components/spv/spv-project-assignments-table";
import { EditSPVUserForm } from "@/components/spv/edit-spv-user-form";
import { toast } from "sonner";

interface SPVUser {
  id: string;
  role: string;
  isActive: boolean;
  createdAt: string;
  user: {
    id: string;
    email: string;
    name: string;
    jobTitle?: string;
  };
  spv: {
    id: string;
    name: string;
    status: string;
    organization: {
      id: string;
      name: string;
    };
  };
  projectAssignments: {
    id: string;
    project: {
      id: string;
      name: string;
    };
  }[];
}

interface SPVUsersResponse {
  users: SPVUser[];
  totalCount: number;
  page: number;
  totalPages: number;
}

export function SPVUserManagementClient() {
  const { spvUser } = useSPVUser();
  const [users, setUsers] = useState<SPVUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("ALL");
  const [statusFilter, setStatusFilter] = useState<string>("ALL");
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [showAddUserModal, setShowAddUserModal] = useState(false);
  const [showProjectAssignmentModal, setShowProjectAssignmentModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<SPVUser | null>(null);
  const [selectedUserForAssignment, setSelectedUserForAssignment] = useState<string | undefined>(undefined);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [activeTab, setActiveTab] = useState("users");

  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: "10",
        ...(searchTerm && { search: searchTerm }),
        ...(roleFilter && roleFilter !== "ALL" && { role: roleFilter }),
        ...(statusFilter && statusFilter !== "ALL" && { status: statusFilter }),
      });

      const response = await fetch(`/api/spv/users?${params}`);
      
      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.status}`);
      }

      const data: SPVUsersResponse = await response.json();
      setUsers(data.users);
      setTotalPages(data.totalPages);
      setTotalCount(data.totalCount);
    } catch (error) {
      console.error("Error fetching SPV users:", error);
      setError(error instanceof Error ? error.message : "Failed to fetch users");
      toast.error("Failed to fetch users");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [currentPage, searchTerm, roleFilter, statusFilter]);

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "SITE_WORKER":
        return "bg-blue-100 text-blue-800";
      case "PROJECT_MANAGER":
        return "bg-green-100 text-green-800";
      case "SPV_ADMIN":
        return "bg-purple-100 text-purple-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusBadgeColor = (isActive: boolean) => {
    return isActive
      ? "bg-green-100 text-green-800"
      : "bg-red-100 text-red-800";
  };

  const handleAddUser = () => {
    setShowAddUserModal(true);
  };

  const handleAssignProjects = (user?: SPVUser) => {
    if (user) {
      setSelectedUserForAssignment(user.id);
    } else {
      setSelectedUserForAssignment(undefined);
    }
    setShowProjectAssignmentModal(true);
  };

  const handleViewUser = (user: SPVUser) => {
    setSelectedUser(user);
    setShowViewModal(true);
  };

  const handleEditUser = (user: SPVUser) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  const handleDeleteUser = (user: SPVUser) => {
    setSelectedUser(user);
    setShowDeleteModal(true);
  };

  const handleUserCreated = (userData: any) => {
    fetchUsers(); // Refresh the users list
  };

  const handleProjectAssignmentSuccess = () => {
    fetchUsers(); // Refresh the users list
    setSelectedUserForAssignment(undefined);
  };

  const confirmDeleteUser = async () => {
    if (!selectedUser) return;

    try {
      const response = await fetch(`/api/spv/users?userId=${selectedUser.userId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete user");
      }

      toast.success("User deleted successfully");
      setShowDeleteModal(false);
      setSelectedUser(null);
      fetchUsers(); // Refresh the list
    } catch (error) {
      console.error("Error deleting user:", error);
      toast.error("Failed to delete user");
    }
  };

  if (loading && users.length === 0) {
    return (
      <div className="p-6">
        <div className="space-y-6">
          <Skeleton className="h-8 w-64" />
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-96" />
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="h-16 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
            <p className="text-gray-600">Manage SPV team members, roles, and project assignments</p>
          </div>
          {spvUser?.role === "SPV_ADMIN" && (
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => handleAssignProjects()}>
                <FolderOpen className="h-4 w-4 mr-2" />
                Assign Projects
              </Button>
              <Button onClick={handleAddUser}>
                <UserPlus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </div>
          )}
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Main Content with Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Team Members
            </TabsTrigger>
            <TabsTrigger value="assignments" className="flex items-center gap-2">
              <FolderOpen className="h-4 w-4" />
              Project Assignments
            </TabsTrigger>
          </TabsList>

          {/* Users Tab */}
          <TabsContent value="users" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Team Members</CardTitle>
                <CardDescription>
                  {totalCount} total users in your SPV
                </CardDescription>
              </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Roles</SelectItem>
                  <SelectItem value="SITE_WORKER">Site Worker</SelectItem>
                  <SelectItem value="PROJECT_MANAGER">Project Manager</SelectItem>
                  <SelectItem value="SPV_ADMIN">SPV Admin</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ALL">All Status</SelectItem>
                  <SelectItem value="ACTIVE">Active</SelectItem>
                  <SelectItem value="INACTIVE">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Users Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Projects</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="text-gray-500">
                          {searchTerm || roleFilter !== "ALL" || statusFilter !== "ALL"
                            ? "No users found matching your filters"
                            : "No users found"}
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    users.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{user.user.name}</div>
                            <div className="text-sm text-gray-500">{user.user.email}</div>
                            {user.user.jobTitle && (
                              <div className="text-xs text-gray-400">{user.user.jobTitle}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={getRoleBadgeColor(user.role)}>
                            {user.role.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusBadgeColor(user.isActive)}>
                            {user.isActive ? 'Active' : 'Inactive'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {user.projectAssignments.length} project{user.projectAssignments.length !== 1 ? 's' : ''}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm text-gray-500">
                            {new Date(user.createdAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewUser(user)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {spvUser?.role === "SPV_ADMIN" && (
                              <>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="sm">
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem onClick={() => handleEditUser(user)}>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit User
                                    </DropdownMenuItem>
                                    <DropdownMenuItem onClick={() => handleAssignProjects(user)}>
                                      <FolderOpen className="mr-2 h-4 w-4" />
                                      Assign Projects
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => handleDeleteUser(user)}
                                      className="text-red-600"
                                    >
                                      <Trash2 className="mr-2 h-4 w-4" />
                                      Delete User
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </>
                            )}
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, totalCount)} of {totalCount} users
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    Next
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>

      {/* Project Assignments Tab */}
      <TabsContent value="assignments" className="space-y-6">
        <SPVProjectAssignmentsTable onRefresh={fetchUsers} />
      </TabsContent>
    </Tabs>

    {/* Add User Modal */}
    <SPVUserCreateForm
      isOpen={showAddUserModal}
      onClose={() => setShowAddUserModal(false)}
      onSuccess={handleUserCreated}
    />

    {/* Project Assignment Modal */}
    <SPVProjectAssignmentForm
      isOpen={showProjectAssignmentModal}
      onClose={() => {
        setShowProjectAssignmentModal(false);
        setSelectedUserForAssignment(undefined);
      }}
      onSuccess={handleProjectAssignmentSuccess}
      selectedUserId={selectedUserForAssignment}
    />

    {/* View User Modal */}
    <Dialog open={showViewModal} onOpenChange={setShowViewModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>User Details</DialogTitle>
          <DialogDescription>
            View detailed information about this team member.
          </DialogDescription>
        </DialogHeader>
        {selectedUser && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Name</label>
                <p className="text-sm">{selectedUser.user.name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Email</label>
                <p className="text-sm">{selectedUser.user.email}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Role</label>
                <p className="text-sm">
                  <Badge className={getRoleBadgeColor(selectedUser.role)}>
                    {selectedUser.role.replace('_', ' ')}
                  </Badge>
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <p className="text-sm">
                  <Badge className={getStatusBadgeColor(selectedUser.isActive)}>
                    {selectedUser.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Job Title</label>
                <p className="text-sm">{selectedUser.user.jobTitle || 'Not specified'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Projects</label>
                <p className="text-sm">{selectedUser.projectAssignments.length} assigned</p>
              </div>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-500">Joined</label>
              <p className="text-sm">{new Date(selectedUser.createdAt).toLocaleDateString()}</p>
            </div>
            {selectedUser.projectAssignments.length > 0 && (
              <div>
                <label className="text-sm font-medium text-gray-500">Assigned Projects</label>
                <div className="mt-2 space-y-1">
                  {selectedUser.projectAssignments.map((assignment) => (
                    <Badge key={assignment.id} variant="outline" className="mr-2">
                      {assignment.project.name}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowViewModal(false)}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    {/* Edit User Modal */}
    <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Edit User</DialogTitle>
          <DialogDescription>
            Update user information and permissions.
          </DialogDescription>
        </DialogHeader>
        {selectedUser && spvUser?.spv && (
          <EditSPVUserForm
            spvId={spvUser.spv.id}
            user={selectedUser}
            onSuccess={() => {
              setShowEditModal(false);
              setSelectedUser(null);
              fetchUsers();
              toast.success("User updated successfully");
            }}
            onCancel={() => {
              setShowEditModal(false);
              setSelectedUser(null);
            }}
          />
        )}
      </DialogContent>
    </Dialog>

    {/* Delete User Modal */}
    <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete User</DialogTitle>
          <DialogDescription>
            Are you sure you want to remove this user from your SPV? This action cannot be undone.
          </DialogDescription>
        </DialogHeader>
        {selectedUser && (
          <div className="p-4 bg-red-50 rounded-lg">
            <p className="text-sm">
              <strong>{selectedUser.user.name}</strong> ({selectedUser.user.email}) will be removed from the SPV and lose access to all assigned projects.
            </p>
          </div>
        )}
        <DialogFooter>
          <Button variant="outline" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="destructive" onClick={confirmDeleteUser}>
            Delete User
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
      </div>
    </div>
  );
}
