"use client";

import { use<PERSON><PERSON>, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  WindBasicInfoStep,
  WindLocationTechnicalStep,
  WindEquipmentStep,
  WindInfrastructureStep,
  WindLandResourcesStep
} from "@/components/projects/wizard-steps/wind-energy";

// Wind Energy specific schema for testing
const windEnergySchema = z.object({
  windEnergyData: z.object({
    windProjectName: z.string().min(1, "Project name is required"),
    windProjectLocation: z.string().min(1, "Project location is required"),
    windClientName: z.string().min(1, "Client name is required"),
    windProjectType: z.enum(["EPC", "IPP"]),
    windLatitude: z.string().min(1, "Latitude is required"),
    windLongitude: z.string().min(1, "Longitude is required"),
    windGoogleMapLocation: z.object({
      state: z.string().min(1, "State is required"),
      district: z.string().min(1, "District is required"),
      taluka: z.string().min(1, "Taluka is required"),
      village: z.string().min(1, "Village is required"),
    }),
    windProjectCapacity: z.number().positive("Project capacity must be positive"),
    windCapacityMW: z.number().positive("Wind capacity must be positive"),
    windVPName: z.string().min(1, "VP name is required"),
    windVPContactNo: z.string().min(1, "VP contact number is required"),
    windVPMailId: z.string().email("Valid VP email is required"),
    windSiteEngineerName: z.string().min(1, "Site engineer name is required"),
    windSiteEngineerContactNo: z.string().min(1, "Site engineer contact number is required"),
    windSiteEngineerMailId: z.string().email("Valid site engineer email is required"),
    windWTGMake: z.string().min(1, "WTG make is required"),
    windWTGCapacity: z.number().positive("WTG capacity must be positive"),
    windNoOfWTG: z.number().positive("Number of WTG must be positive"),
    windModuleType: z.string().min(1, "Module type is required"),
    windModuleSupplier: z.string().min(1, "Module supplier is required"),
    windNoOfModules: z.number().positive("Number of modules must be positive"),
    windInverter: z.string().min(1, "Inverter is required"),
    windNoOfInverters: z.number().positive("Number of inverters must be positive"),
    windInverterCapacityKW: z.number().positive("Inverter capacity must be positive"),
    windCOD: z.string().min(1, "COD is required"),
    windCommissioningCertificate: z.any().optional(),
    windTransformerCapacity: z.number().positive("Transformer capacity must be positive"),
    windNoOfTransformers: z.number().positive("Number of transformers must be positive"),
    windNoOfSubcontractors: z.number().min(0, "Number of subcontractors cannot be negative"),
    windGenerationDetails: z.string().min(1, "Generation details are required"),
    windSubstationName: z.string().min(1, "Substation name is required"),
    windEvacuationClass: z.string().min(1, "Evacuation class is required"),
    windConnectivityData: z.string().min(1, "Connectivity data is required"),
    windAvailableLandWTGNos: z.number().positive("Available land (WTG) numbers must be positive"),
  }),
  customFields: z.record(z.any()).optional(),
});

type FormValues = z.infer<typeof windEnergySchema>;

export default function WindFormTestPage() {
  const form = useForm<FormValues>({
    resolver: zodResolver(windEnergySchema),
    defaultValues: {
      windEnergyData: {
        windProjectName: "",
        windProjectLocation: "",
        windClientName: "",
        windProjectType: undefined,
        windLatitude: "",
        windLongitude: "",
        windGoogleMapLocation: {
          state: "",
          district: "",
          taluka: "",
          village: "",
        },
        windProjectCapacity: 0,
        windCapacityMW: 0,
        windVPName: "",
        windVPContactNo: "",
        windVPMailId: "",
        windSiteEngineerName: "",
        windSiteEngineerContactNo: "",
        windSiteEngineerMailId: "",
        windWTGMake: "",
        windWTGCapacity: 0,
        windNoOfWTG: 0,
        windModuleType: "",
        windModuleSupplier: "",
        windNoOfModules: 0,
        windInverter: "",
        windNoOfInverters: 0,
        windInverterCapacityKW: 0,
        windCOD: "",
        windCommissioningCertificate: null,
        windTransformerCapacity: 0,
        windNoOfTransformers: 0,
        windNoOfSubcontractors: 0,
        windGenerationDetails: "",
        windSubstationName: "",
        windEvacuationClass: "",
        windConnectivityData: "",
        windAvailableLandWTGNos: 0,
      },
      customFields: {},
    },
  });

  const onSubmit = (data: FormValues) => {
    console.log("Wind Energy Form Data:", data);
    alert("Form submitted successfully! Check console for data.");
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Wind Energy Form Test</CardTitle>
        </CardHeader>
        <CardContent>
          <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <WindBasicInfoStep />
              <WindLocationTechnicalStep />
              <WindEquipmentStep />
              <WindInfrastructureStep />
              <WindLandResourcesStep />
              
              <div className="flex justify-end">
                <Button type="submit" size="lg">
                  Submit Wind Energy Form
                </Button>
              </div>
            </form>
          </FormProvider>
        </CardContent>
      </Card>
    </div>
  );
}
