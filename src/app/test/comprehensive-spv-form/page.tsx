"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ComprehensiveSPVCreationForm } from "@/components/spv/comprehensive-spv-creation-form";
import { SPVCustomFieldsManager } from "@/components/spv/spv-custom-fields-manager";
import { ComprehensiveSPVCreateData } from "@/types/spv";
import { toast } from "sonner";
import { Building2, Settings, TestTube } from "lucide-react";

export default function ComprehensiveSPVFormTestPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isCustomFieldsOpen, setIsCustomFieldsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [customFields, setCustomFields] = useState<any[]>([]);

  const handleSPVSubmit = async (data: ComprehensiveSPVCreateData) => {
    setIsSubmitting(true);
    try {
      console.log("SPV Form Data:", data);
      
      // Simulate API call
      const response = await fetch("/api/organizations/spvs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create SPV");
      }

      const result = await response.json();
      console.log("SPV Created:", result);
      
      toast.success("SPV created successfully!");
      setIsFormOpen(false);
    } catch (error) {
      console.error("Error creating SPV:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create SPV");
      throw error; // Re-throw to let the form handle it
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCustomFieldsSave = async (fields: any[]) => {
    try {
      console.log("Custom Fields:", fields);
      setCustomFields(fields);
      toast.success("Custom fields saved successfully!");
    } catch (error) {
      console.error("Error saving custom fields:", error);
      toast.error("Failed to save custom fields");
      throw error;
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="space-y-2">
        <h1 className="text-3xl font-bold tracking-tight">Comprehensive SPV Form Test</h1>
        <p className="text-muted-foreground">
          Test page for the new comprehensive SPV creation form with all fields and custom field support.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* SPV Form Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              SPV Creation Form
            </CardTitle>
            <CardDescription>
              Test the comprehensive SPV creation form with all new fields including Basic Information, 
              Ownership & Stakeholders, and Financial Details.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Features to Test:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Basic Information (SPV Name, Parent Company, SPV Type, etc.)</li>
                <li>• Legal Details (CIN, PAN, GST with document upload)</li>
                <li>• Ownership & Stakeholders (Promoter, Equity Holders, Board Members)</li>
                <li>• Financial Details (Capital, Bank Details, Funding Source)</li>
                <li>• Document Upload for required fields</li>
                <li>• Form validation and error handling</li>
              </ul>
            </div>
            <Button onClick={() => setIsFormOpen(true)} className="w-full">
              <TestTube className="h-4 w-4 mr-2" />
              Test SPV Creation Form
            </Button>
          </CardContent>
        </Card>

        {/* Custom Fields Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Custom Fields Manager
            </CardTitle>
            <CardDescription>
              Test the custom fields manager to add dynamic fields to the SPV form.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Features to Test:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Add custom fields with different types</li>
                <li>• Assign fields to different sections</li>
                <li>• Set field validation rules</li>
                <li>• Save and load custom field configurations</li>
              </ul>
            </div>
            {customFields.length > 0 && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-800">
                  {customFields.length} custom field(s) configured
                </p>
              </div>
            )}
            <Button onClick={() => setIsCustomFieldsOpen(true)} className="w-full" variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Manage Custom Fields
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
          <CardDescription>
            Follow these steps to thoroughly test the comprehensive SPV form implementation.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div>
              <h4 className="font-medium">1. Test Basic Form Functionality</h4>
              <p className="text-sm text-muted-foreground">
                Open the SPV creation form and test all three tabs: Basic Information, Ownership & Stakeholders, and Financial Details.
              </p>
            </div>
            <div>
              <h4 className="font-medium">2. Test Field Validation</h4>
              <p className="text-sm text-muted-foreground">
                Try submitting the form with invalid data (e.g., invalid CIN format, invalid email) to test validation.
              </p>
            </div>
            <div>
              <h4 className="font-medium">3. Test Document Upload</h4>
              <p className="text-sm text-muted-foreground">
                Click the upload buttons next to CIN, PAN, GST, and other document fields to test file upload functionality.
              </p>
            </div>
            <div>
              <h4 className="font-medium">4. Test Dynamic Arrays</h4>
              <p className="text-sm text-muted-foreground">
                Add multiple equity holders and board members to test the dynamic array functionality.
              </p>
            </div>
            <div>
              <h4 className="font-medium">5. Test Custom Fields</h4>
              <p className="text-sm text-muted-foreground">
                Use the custom fields manager to add new fields and verify they appear in the form.
              </p>
            </div>
            <div>
              <h4 className="font-medium">6. Test API Integration</h4>
              <p className="text-sm text-muted-foreground">
                Submit a complete form to test the API integration and database storage.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* SPV Creation Form */}
      <ComprehensiveSPVCreationForm
        isOpen={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        onSubmit={handleSPVSubmit}
        isSubmitting={isSubmitting}
        mode="user"
      />

      {/* Custom Fields Manager */}
      <SPVCustomFieldsManager
        isOpen={isCustomFieldsOpen}
        onClose={() => setIsCustomFieldsOpen(false)}
        onSave={handleCustomFieldsSave}
        existingFields={customFields}
      />
    </div>
  );
}
