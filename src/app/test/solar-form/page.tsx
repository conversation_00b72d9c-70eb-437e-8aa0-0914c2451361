"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FormProvider, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  SolarBasicInfoStep,
  SolarLocationTechnicalStep,
  SolarEquipmentStep,
  SolarInfrastructureStep,
  SolarLandResourcesStep
} from "@/components/projects/wizard-steps/solar-energy";

// Solar Energy specific schema for testing
const solarEnergySchema = z.object({
  solarEnergyData: z.object({
    solarProjectName: z.string().min(1, "Project name is required"),
    solarProjectLocation: z.string().min(1, "Project location is required"),
    solarClientName: z.string().min(1, "Client name is required"),
    solarProjectType: z.enum(["EPC", "IPP"]),
    solarLatitude: z.string().min(1, "Latitude is required"),
    solarLongitude: z.string().min(1, "Longitude is required"),
    solarGoogleMapLocation: z.object({
      state: z.string().min(1, "State is required"),
      district: z.string().min(1, "District is required"),
      taluka: z.string().min(1, "Taluka is required"),
      village: z.string().min(1, "Village is required"),
    }),
    solarMwCapacityAC: z.number().positive("MW Capacity (AC) must be positive"),
    solarMwCapacityDC: z.number().positive("MW Capacity (DC) must be positive"),
    solarSubstationName: z.string().min(1, "Substation name is required"),
    solarModuleType: z.string().min(1, "Module type is required"),
    solarModuleSupplier: z.string().min(1, "Module supplier is required"),
    solarNoOfModules: z.number().positive("Number of modules must be positive"),
    solarInverter: z.string().min(1, "Inverter is required"),
    solarNoOfInverters: z.number().positive("Number of inverters must be positive"),
    solarInverterCapacity: z.number().positive("Inverter capacity must be positive"),
    solarCOD: z.string().min(1, "COD is required"),
    solarCommissioningCertificate: z.any().optional(),
    solarTransformerCapacity: z.number().positive("Transformer capacity must be positive"),
    solarNoOfTransformers: z.number().positive("Number of transformers must be positive"),
    solarNoOfSubcontractors: z.number().min(0, "Number of subcontractors cannot be negative"),
    solarAvailableLand: z.enum(["Lease", "Rent"]),
    solarTotalLandAvailability: z.number().positive("Total land availability must be positive"),
  }),
  customFields: z.record(z.any()).optional(),
});

type FormValues = z.infer<typeof solarEnergySchema>;

const steps = [
  { id: 1, name: "Basic Info", component: SolarBasicInfoStep },
  { id: 2, name: "Location & Technical", component: SolarLocationTechnicalStep },
  { id: 3, name: "Equipment", component: SolarEquipmentStep },
  { id: 4, name: "Infrastructure", component: SolarInfrastructureStep },
  { id: 5, name: "Land & Resources", component: SolarLandResourcesStep },
];

export default function SolarFormTestPage() {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(solarEnergySchema),
    defaultValues: {
      solarEnergyData: {
        solarProjectName: "",
        solarProjectLocation: "",
        solarClientName: "",
        solarProjectType: undefined,
        solarLatitude: "",
        solarLongitude: "",
        solarGoogleMapLocation: {
          state: "",
          district: "",
          taluka: "",
          village: "",
        },
        solarMwCapacityAC: 0,
        solarMwCapacityDC: 0,
        solarSubstationName: "",
        solarModuleType: "",
        solarModuleSupplier: "",
        solarNoOfModules: 0,
        solarInverter: "",
        solarNoOfInverters: 0,
        solarInverterCapacity: 0,
        solarCOD: "",
        solarCommissioningCertificate: null,
        solarTransformerCapacity: 0,
        solarNoOfTransformers: 0,
        solarNoOfSubcontractors: 0,
        solarAvailableLand: undefined,
        solarTotalLandAvailability: 0,
      },
      customFields: {},
    },
    mode: "onChange",
  });

  const handleNext = async () => {
    const isValid = await form.trigger();
    if (isValid && currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    try {
      console.log("Solar Energy Form Data:", JSON.stringify(data, null, 2));
      alert("Form submitted successfully! Check console for data.");
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Error submitting form. Check console for details.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const CurrentStepComponent = steps[currentStep - 1]?.component;

  return (
    <div className="container mx-auto py-8 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Solar Energy Project Form Test</CardTitle>
          <div className="flex items-center space-x-2">
            {steps.map((step) => (
              <div
                key={step.id}
                className={`px-3 py-1 rounded-full text-sm ${
                  step.id === currentStep
                    ? "bg-primary text-primary-foreground"
                    : step.id < currentStep
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-600"
                }`}
              >
                {step.id}. {step.name}
              </div>
            ))}
          </div>
        </CardHeader>
        <CardContent>
          <FormProvider {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
              {CurrentStepComponent && <CurrentStepComponent />}
              
              <div className="flex justify-between mt-8">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handlePrevious}
                  disabled={currentStep === 1}
                >
                  Previous
                </Button>
                
                {currentStep < steps.length ? (
                  <Button type="button" onClick={handleNext}>
                    Next
                  </Button>
                ) : (
                  <Button type="submit" disabled={isSubmitting}>
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </Button>
                )}
              </div>
            </form>
          </FormProvider>
        </CardContent>
      </Card>
      
      {/* Debug Panel */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle>Debug - Form Values</CardTitle>
        </CardHeader>
        <CardContent>
          <pre className="text-xs bg-gray-100 p-4 rounded overflow-auto max-h-96">
            {JSON.stringify(form.watch(), null, 2)}
          </pre>
        </CardContent>
      </Card>
    </div>
  );
}
