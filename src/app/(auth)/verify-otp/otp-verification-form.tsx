"use client";

import { useState, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { Mail, RefreshCw, Edit2 } from "lucide-react";

export function OtpVerificationForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  const [error, setError] = useState("");
  const [countdown, setCountdown] = useState(0);
  const COOLDOWN_DURATION = 60; // 1 minute cooldown period
  
  const email = searchParams.get("email");
  const name = searchParams.get("name");
  const inputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const handleEditEmail = () => {
    // Preserve user data when going back to registration
    const params = new URLSearchParams();
    if (name) params.set("name", name);
    if (email) params.set("email", email);
    params.set("edit", "true");

    router.push(`/register?${params.toString()}`);
  };

  // Countdown timer for resend button
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  // Redirect if no email provided and start initial cooldown
  useEffect(() => {
    if (!email) {
      router.push("/register");
    } else {
      // Start initial cooldown since OTP was just sent during registration
      setCountdown(COOLDOWN_DURATION);
    }
  }, [email, router]);

  const handleOtpChange = (index: number, value: string) => {
    if (value.length > 1) return; // Prevent multiple characters
    
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);
    setError("");

    // Auto-focus next input
    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePaste = (e: React.ClipboardEvent) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text").replace(/\D/g, "").slice(0, 6);
    
    if (pastedData.length === 6) {
      const newOtp = pastedData.split("");
      setOtp(newOtp);
      setError("");
      inputRefs.current[5]?.focus();
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const otpCode = otp.join("");
    if (otpCode.length !== 6) {
      setError("Please enter the complete 6-digit verification code");
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      const response = await fetch("/api/verify-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          otp: otpCode,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || "Verification failed");
        return;
      }

      toast({
        title: "Email Verified!",
        description: "Your email has been successfully verified.",
      });

      // Redirect to login page
      router.push("/login?verified=true");
    } catch (error) {
      console.error("OTP verification error:", error);
      setError("An error occurred during verification. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (countdown > 0) return;

    setIsResending(true);
    setError("");

    try {
      const response = await fetch("/api/resend-otp", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (!response.ok) {
        setError(result.error || "Failed to resend verification code");
        return;
      }

      toast({
        title: "New Code Sent!",
        description: `A fresh verification code has been sent to ${email}`,
      });

      setCountdown(COOLDOWN_DURATION); // Start cooldown period
      setOtp(["", "", "", "", "", ""]);
      setError(""); // Clear any existing errors
      inputRefs.current[0]?.focus();
    } catch (error) {
      console.error("Resend OTP error:", error);
      setError("An error occurred while resending the code. Please try again.");
    } finally {
      setIsResending(false);
    }
  };

  if (!email) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <div className="flex justify-center">
          <div className="p-3 bg-primary/10 rounded-full">
            <Mail className="h-6 w-6 text-primary" />
          </div>
        </div>
        <p className="text-sm text-muted-foreground">
          We've sent a 6-digit verification code to
        </p>
        <div className="flex items-center justify-center gap-2">
          <p className="font-medium">{email}</p>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleEditEmail}
            className="h-auto p-1 text-muted-foreground hover:text-foreground transition-none"
            title="Change email address"
          >
            <Edit2 className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="otp">Enter verification code</Label>
          <div className="flex gap-2 justify-center">
            {otp.map((digit, index) => (
              <Input
                key={index}
                ref={(el) => { inputRefs.current[index] = el; }}
                type="text"
                inputMode="numeric"
                pattern="[0-9]*"
                maxLength={1}
                value={digit}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                onPaste={handlePaste}
                className="w-12 h-12 text-center text-lg font-semibold"
                disabled={isLoading}
              />
            ))}
          </div>
        </div>

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Button
          type="submit"
          className="w-full"
          disabled={isLoading || otp.join("").length !== 6}
        >
          {isLoading ? "Verifying..." : "Verify Email"}
        </Button>
      </form>

      <div className="text-center space-y-3">
        <p className="text-sm text-muted-foreground">
          Didn't receive the code?
        </p>
        <div className="space-y-2">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleResendOtp}
            disabled={countdown > 0 || isResending}
            className={`transition-none ${
              countdown > 0
                ? "text-muted-foreground cursor-not-allowed"
                : "text-primary hover:text-primary/80"
            }`}
          >
            {isResending ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Sending new code...
              </>
            ) : countdown > 0 ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Resend available in {countdown}s
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Resend Code
              </>
            )}
          </Button>

        </div>
      </div>


    </div>
  );
}
