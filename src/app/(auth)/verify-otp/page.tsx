"use client";

import { Suspense } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { OtpVerificationForm } from "./otp-verification-form";

export default function VerifyOtpPage() {
  return (
    <Suspense fallback={
      <div className="flex w-full flex-col items-center justify-center flex-grow">
        <div className="w-full max-w-md space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Email Verification</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-center py-4">
                <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-primary"></div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    }>
      <div className="flex w-full flex-col items-center justify-center flex-grow">
        <div className="w-full max-w-md space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-center">Verify Your Email</CardTitle>
            </CardHeader>
            <CardContent>
              <OtpVerificationForm />
            </CardContent>
          </Card>
        </div>
      </div>
    </Suspense>
  );
}
