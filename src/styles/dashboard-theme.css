/* Dashboard Theme - Consistent styling across all dashboards */

/* Card Styling */
.dashboard-card {
  @apply bg-white border border-gray-200 rounded-lg shadow-sm;
}

.dashboard-card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.dashboard-card-title {
  @apply text-sm font-bold text-gray-900;
}

.dashboard-card-description {
  @apply text-xs font-normal text-muted-foreground mt-1;
}

.dashboard-card-content {
  @apply px-6 py-4;
}

/* Stats Cards */
.stats-card {
  @apply dashboard-card p-6;
}

.stats-card-title {
  @apply text-sm font-normal text-gray-600;
}

.stats-card-value {
  @apply text-xl font-bold text-gray-900 mt-1;
}

.stats-card-description {
  @apply text-xs font-normal text-muted-foreground mt-1;
}

/* Navigation Styling */
.dashboard-nav {
  @apply bg-white border-r border-gray-200;
}

.dashboard-nav-item {
  @apply flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors;
}

.dashboard-nav-item-active {
  @apply bg-gray-100 text-gray-900;
}

.dashboard-nav-item-inactive {
  @apply text-gray-600 hover:bg-gray-50 hover:text-gray-900;
}

/* Button Styling */
.dashboard-button-primary {
  @apply bg-blue-600 hover:bg-blue-700 text-white font-medium px-4 py-2 rounded-md text-sm transition-colors;
}

.dashboard-button-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-900 font-medium px-4 py-2 rounded-md text-sm transition-colors;
}

.dashboard-button-outline {
  @apply border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium px-4 py-2 rounded-md text-sm transition-colors;
}

/* Table Styling */
.dashboard-table {
  @apply w-full border-collapse;
}

.dashboard-table-header {
  @apply bg-gray-50 border-b border-gray-200;
}

.dashboard-table-header-cell {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.dashboard-table-row {
  @apply border-b border-gray-200 hover:bg-gray-50;
}

.dashboard-table-cell {
  @apply px-6 py-4 text-sm text-gray-900;
}

/* Badge Styling */
.dashboard-badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.dashboard-badge-success {
  @apply dashboard-badge bg-green-100 text-green-800;
}

.dashboard-badge-warning {
  @apply dashboard-badge bg-yellow-100 text-yellow-800;
}

.dashboard-badge-error {
  @apply dashboard-badge bg-red-100 text-red-800;
}

.dashboard-badge-info {
  @apply dashboard-badge bg-blue-100 text-blue-800;
}

.dashboard-badge-neutral {
  @apply dashboard-badge bg-gray-100 text-gray-800;
}

/* Form Styling */
.dashboard-form-group {
  @apply space-y-2;
}

.dashboard-form-label {
  @apply block text-sm font-medium text-gray-700;
}

.dashboard-form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm;
}

.dashboard-form-select {
  @apply dashboard-form-input;
}

.dashboard-form-textarea {
  @apply dashboard-form-input resize-vertical;
}

/* Layout Styling */
.dashboard-container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.dashboard-header {
  @apply flex items-center justify-between mb-6;
}

.dashboard-title {
  @apply text-2xl font-bold text-gray-900;
}

.dashboard-subtitle {
  @apply text-sm text-gray-600 mt-1;
}

.dashboard-content {
  @apply space-y-6;
}

.dashboard-grid {
  @apply grid gap-6;
}

.dashboard-grid-cols-1 {
  @apply dashboard-grid grid-cols-1;
}

.dashboard-grid-cols-2 {
  @apply dashboard-grid grid-cols-1 lg:grid-cols-2;
}

.dashboard-grid-cols-3 {
  @apply dashboard-grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3;
}

.dashboard-grid-cols-4 {
  @apply dashboard-grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-card {
    @apply mx-4;
  }
  
  .dashboard-header {
    @apply flex-col items-start space-y-4;
  }
  
  .dashboard-grid-cols-2,
  .dashboard-grid-cols-3,
  .dashboard-grid-cols-4 {
    @apply grid-cols-1;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .dashboard-card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dashboard-card-title {
    @apply text-white;
  }
  
  .dashboard-card-description {
    @apply text-gray-400;
  }
  
  .stats-card-title {
    @apply text-gray-400;
  }
  
  .stats-card-value {
    @apply text-white;
  }
  
  .dashboard-nav {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dashboard-nav-item-active {
    @apply bg-gray-700 text-white;
  }
  
  .dashboard-nav-item-inactive {
    @apply text-gray-300 hover:bg-gray-700 hover:text-white;
  }
  
  .dashboard-table-header {
    @apply bg-gray-700;
  }
  
  .dashboard-table-header-cell {
    @apply text-gray-300;
  }
  
  .dashboard-table-row {
    @apply border-gray-700 hover:bg-gray-700;
  }
  
  .dashboard-table-cell {
    @apply text-gray-300;
  }
  
  .dashboard-title {
    @apply text-white;
  }
  
  .dashboard-subtitle {
    @apply text-gray-400;
  }
}

/* Animation Classes */
.dashboard-fade-in {
  @apply animate-in fade-in duration-200;
}

.dashboard-slide-in {
  @apply animate-in slide-in-from-bottom-4 duration-300;
}

.dashboard-scale-in {
  @apply animate-in zoom-in-95 duration-200;
}

/* Loading States */
.dashboard-skeleton {
  @apply animate-pulse bg-gray-200 rounded;
}

.dashboard-spinner {
  @apply animate-spin rounded-full border-2 border-gray-300 border-t-blue-600;
}
