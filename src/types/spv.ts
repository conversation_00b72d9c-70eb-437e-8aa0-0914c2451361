/**
 * SPV (Special Purpose Vehicle) related TypeScript interfaces and types
 */

// SPV Status enum
export type SPVStatus = "ACTIVE" | "INACTIVE" | "PENDING" | "DISSOLVED";

// SPV Verification Status enum
export type SPVVerificationStatus =
  | "PENDING_VERIFICATION"
  | "IN_REVIEW"
  | "VERIFIED"
  | "REJECTED"
  | "NEEDS_MORE_INFO"
  | "SUSPENDED";

// SPV Document Type enum
export type SPVDocumentType =
  | "CERTIFICATE_OF_INCORPORATION"
  | "PAN_CARD"
  | "GST_REGISTRATION"
  | "BOARD_RESOLUTION"
  | "BANK_PROOF"
  | "AUTHORIZED_SIGNATORY_ID"
  | "MOA_AOA"
  | "CIN_CERTIFICATE"
  | "INCORPORATION_CERTIFICATE"
  | "PAN_DOCUMENT"
  | "GST_CERTIFICATE"
  | "ADDRESS_PROOF"
  | "SPV_LOGO"
  | "OTHER";

// Base SPV interface
export interface SPV {
  id: string;
  name: string;
  purpose: string | null;
  jurisdiction: string | null;
  status: SPVStatus;
  establishedDate: string | null;
  legalStructure: string | null;
  registrationNumber: string | null;
  taxId: string | null;
  address: string | null;
  description: string | null;
  // New fields from PRD
  country: string | null;
  legalEntityId: string | null;
  contact: string | null;
  projectCategories: string[];
  metadata: Record<string, any> | null;

  // Basic Information - New comprehensive fields
  parentCompany: string | null;
  spvType: string | null;
  dateOfIncorporation: string | null;
  stateOfIncorporation: string | null;
  spvLogoUrl: string | null;

  // Ownership & Stakeholders
  promoterName: string | null;
  equityHolders: Array<{
    name: string;
    shareholding: number;
  }> | null;
  boardMembers: Array<{
    name: string;
    designation: string;
    din?: string;
  }> | null;
  authorizedSignatory: string | null;
  registeredRepresentative: string | null;

  // Financial Details
  authorizedCapital: number | null;
  paidUpCapital: number | null;
  bankAccountDetails: {
    accountNumber?: string;
    bankName?: string;
    ifscCode?: string;
    accountType?: string;
  } | null;
  debtProvider: string | null;
  debtEquityRatio: string | null;
  fundingSource: string | null;
  ppaSignedWith: string | null;

  // Legacy SPV Verification Fields
  gstNumber: string | null;
  cinNumber: string | null;
  panNumber: string | null;
  incorporationDate: string | null;
  registeredAddress: string | null;
  contactPersonName: string | null;
  contactPersonEmail: string | null;
  contactPersonMobile: string | null;
  bankAccountNumber: string | null;
  ifscCode: string | null;
  verificationStatus: SPVVerificationStatus;
  verificationNotes: string | null;
  verifiedBy: string | null;
  verifiedAt: string | null;
  rejectionReason: string | null;

  // Custom fields support
  customFields: Record<string, any> | null;

  createdAt: string;
  updatedAt: string;
  organizationId: string;
}

// SPV Document interface
export interface SPVDocument {
  id: string;
  spvId: string;
  documentType: SPVDocumentType;
  fileName: string;
  fileUrl: string;
  fileSize: number | null;
  mimeType: string | null;
  uploadedBy: string;
  uploadedAt: string;
  verified: boolean;
  verifiedBy: string | null;
  verifiedAt: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
}

// SPV with documents
export interface SPVWithDocuments extends SPV {
  documents: SPVDocument[];
}

// SPV creation data
export interface SPVCreateData {
  name: string;
  purpose?: string;
  legalStructure?: string;
  registrationNumber?: string;
  taxId?: string;
  address?: string;
  description?: string;
  establishedDate?: string;
  jurisdiction?: string;
  country?: string;
  legalEntityId?: string;
  contact?: string;
  projectCategories?: string;
  adminEmail: string;
}

// SPV verification data
export interface SPVVerificationData {
  name: string;
  purpose?: string;
  legalStructure: string;
  registrationNumber: string;
  jurisdiction: string;
  country: string;
  gstNumber?: string;
  cinNumber?: string;
  panNumber?: string;
  incorporationDate?: string;
  establishedDate?: string;
  registeredAddress: string;
  address?: string;
  contactPersonName: string;
  contactPersonEmail: string;
  contactPersonMobile: string;
  contact?: string;
  bankAccountNumber: string;
  ifscCode: string;
  projectCategories: string;
  description?: string;
  taxId?: string;
  legalEntityId?: string;
}

// SPV verification action data
export interface SPVVerificationAction {
  action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO' | 'SUBMIT_FOR_REVIEW';
  notes?: string;
  rejectionReason?: string;
}

// Document upload data
export interface SPVDocumentUpload {
  documentType: SPVDocumentType;
  file: File;
  notes?: string;
}

// SPV with organization details
export interface SPVWithOrganization extends SPV {
  organization: {
    id: string;
    name: string;
    legalName: string | null;
    status: string;
    verificationStatus: string;
    country: string | null;
    industry: string | null;
    phoneNumber?: string | null;
  };
}

// SPV with projects
export interface SPVWithProjects extends SPV {
  projects: {
    id: string;
    name: string;
    status: string;
    type?: string;
    createdAt: string;
    estimatedReductions?: number | null;
  }[];
  _count: {
    projects: number;
  };
}

// SPV with full details (organization + projects + users + documents)
export interface SPVWithDetails extends SPVWithOrganization {
  projects: {
    id: string;
    name: string;
    status: string;
    type?: string;
    createdAt: string;
    estimatedReductions?: number | null;
  }[];
  spvUsers: {
    id: string;
    role: string;
    isActive: boolean;
    createdAt: string;
    user: {
      id: string;
      name: string;
      email: string;
    };
  }[];
  documents: SPVDocument[];
  _count: {
    projects: number;
    spvUsers: number;
    documents: number;
  };
}

// Comprehensive SPV creation form data
export interface ComprehensiveSPVCreateData {
  // Basic Information
  name: string;
  parentCompany?: string;
  spvType?: "PRIVATE_LIMITED" | "LLP" | "TRUST" | "PARTNERSHIP";
  dateOfIncorporation?: string;
  cinNumber?: string;
  registeredAddress?: string;
  stateOfIncorporation?: string;
  panNumber?: string;
  gstNumber?: string;
  spvLogoUrl?: string;

  description?: string;

  // Ownership & Stakeholders
  promoterName?: string;
  equityHolders?: Array<{
    name: string;
    shareholding: number;
  }>;
  boardMembers?: Array<{
    name: string;
    designation: string;
    din?: string;
  }>;
  authorizedSignatory?: string;
  registeredRepresentative?: string;

  // Financial Details
  authorizedCapital?: number;
  paidUpCapital?: number;
  bankAccountDetails?: {
    accountNumber?: string;
    bankName?: string;
    ifscCode?: string;
    accountType?: string;
  };
  debtProvider?: string;
  debtEquityRatio?: string;
  fundingSource?: "EQUITY" | "DEBT" | "GRANT" | "MIXED" | "OTHER";
  ppaSignedWith?: string;

  // Legacy fields for backward compatibility
  legalStructure?: string;
  registrationNumber?: string;
  taxId?: string;
  address?: string;
  establishedDate?: string;
  jurisdiction?: string;
  country?: string;
  legalEntityId?: string;
  contact?: string;
  projectCategories?: string;

  // Admin details
  adminName?: string;
  adminEmail?: string;

  // Custom fields
  customFields?: Record<string, any>;
}

// Legacy SPV creation form data - updated with PRD requirements
export interface SPVCreateData {
  name: string;
  purpose?: string;
  legalStructure?: string;
  registrationNumber?: string;
  taxId?: string;
  address?: string;
  description?: string;
  establishedDate?: string;
  // New required fields from PRD (legacy interface)
  countryLegacy?: string;
  legalEntityIdLegacy?: string;
  contactLegacy?: string;
  projectCategoriesLegacy?: string;
}

// Admin SPV creation form data (includes organizationId)
export interface AdminSPVCreateData extends SPVCreateData {
  organizationId: string;
}

// SPV update form data
export interface SPVUpdateData extends Partial<SPVCreateData> {
  status?: SPVStatus;
}

// SPV list response
export interface SPVListResponse {
  spvs: SPVWithOrganization[];
  organizations?: {
    id: string;
    name: string;
    legalName: string | null;
  }[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    organizationId?: string;
    status?: SPVStatus;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
}

// SPV details response
export interface SPVDetailsResponse {
  spv: SPVWithDetails;
}

// SPV creation response
export interface SPVCreateResponse {
  spv: SPVWithOrganization;
  message: string;
}

// SPV update response
export interface SPVUpdateResponse {
  spv: SPVWithOrganization;
  message: string;
}

// SPV delete response
export interface SPVDeleteResponse {
  message: string;
}

// SPV query parameters
export interface SPVQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  organizationId?: string;
  status?: SPVStatus;
  sortBy?: "name" | "createdAt" | "status" | "organization";
  sortOrder?: "asc" | "desc";
}

// Organization data for SPV management
export interface OrganizationForSPV {
  id: string;
  name: string;
  legalName: string | null;
  country: string | null;
  industry: string | null;
  status: string;
  verificationStatus: string;
}

// Organization SPV response (for dashboard)
export interface OrganizationSPVResponse {
  organization: OrganizationForSPV;
  spvs: SPV[];
}

// SPV bulk operation request
export interface SPVBulkOperationRequest {
  spvIds: string[];
  operation: "DELETE" | "UPDATE_STATUS" | "EXPORT";
  data?: {
    status?: SPVStatus;
  };
}

// SPV bulk operation response
export interface SPVBulkOperationResponse {
  success: boolean;
  processedCount: number;
  failedCount: number;
  errors?: string[];
  message: string;
}
