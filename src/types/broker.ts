/**
 * Broker Type Definitions
 * 
 * This module provides comprehensive type definitions for broker entities,
 * API responses, form data, and query parameters following the SPV patterns.
 */

// Base Broker interface
export interface Broker {
  id: string;
  name: string;
  legalName?: string | null;
  description?: string | null;
  website?: string | null;
  logo?: string | null;
  status: BrokerStatus;
  verificationStatus: VerificationStatus;
  
  // Contact Information
  email: string;
  phoneNumber?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  postalCode?: string | null;
  country?: string | null;
  
  // License and Registration
  licenseNumber?: string | null;
  licenseType?: string | null;
  licenseIssuer?: string | null;
  licenseExpiryDate?: string | null;
  registrationNumber?: string | null;
  taxId?: string | null;
  
  // Operating Model and Commission
  operatingModel: BrokerOperatingModel;
  commissionRate: number;
  minimumCommission?: number | null;
  maximumCommission?: number | null;
  
  // Specialization and Services
  specializations?: string[] | null;
  servicesOffered?: string[] | null;
  targetMarkets?: string[] | null;
  
  // Financial Information
  bondAmount?: number | null;
  insuranceAmount?: number | null;
  creditRating?: string | null;
  
  // Metadata and Settings
  metadata?: Record<string, any> | null;
  settings?: Record<string, any> | null;
  
  // Timestamps
  establishedDate?: string | null;
  createdAt: string;
  updatedAt: string;
  
  // Relations
  userId?: string | null;
  organizationId?: string | null;
}

// Broker with organization details
export interface BrokerWithOrganization extends Broker {
  organization?: {
    id: string;
    name: string;
    legalName?: string | null;
  } | null;
  user?: {
    id: string;
    name?: string | null;
    email: string;
  } | null;
}

// Broker with full details (organization + clients + transactions)
export interface BrokerWithDetails extends BrokerWithOrganization {
  clients: {
    id: string;
    clientName: string;
    clientType: BrokerClientType;
    status: BrokerClientStatus;
    createdAt: string;
  }[];
  transactions: {
    id: string;
    transactionType: BrokerTransactionType;
    amount: number;
    commissionAmount: number;
    status: BrokerTransactionStatus;
    transactionDate: string;
  }[];
  _count: {
    clients: number;
    transactions: number;
  };
}

// Broker creation form data
export interface BrokerCreateData {
  name: string;
  legalName?: string;
  description?: string;
  website?: string;
  email: string;
  phoneNumber?: string;
  address?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  licenseNumber?: string;
  licenseType?: string;
  licenseIssuer?: string;
  licenseExpiryDate?: string;
  registrationNumber?: string;
  taxId?: string;
  operatingModel?: BrokerOperatingModel;
  commissionRate?: number;
  minimumCommission?: number;
  maximumCommission?: number;
  specializations?: string[];
  servicesOffered?: string[];
  targetMarkets?: string[];
  bondAmount?: number;
  insuranceAmount?: number;
  creditRating?: string;
  establishedDate?: string;
}

// Admin broker creation form data (includes organizationId and userId)
export interface AdminBrokerCreateData extends BrokerCreateData {
  organizationId?: string;
  userId?: string;
}

// Broker update form data
export interface BrokerUpdateData extends Partial<BrokerCreateData> {
  status?: BrokerStatus;
  verificationStatus?: VerificationStatus;
}

// Broker list response
export interface BrokerListResponse {
  brokers: BrokerWithOrganization[];
  organizations?: {
    id: string;
    name: string;
    legalName: string | null;
  }[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: {
    search?: string;
    organizationId?: string;
    status?: BrokerStatus;
    operatingModel?: BrokerOperatingModel;
    sortBy: string;
    sortOrder: "asc" | "desc";
  };
}

// Broker details response
export interface BrokerDetailsResponse {
  broker: BrokerWithDetails;
}

// Broker creation response
export interface BrokerCreateResponse {
  broker: BrokerWithOrganization;
  message: string;
}

// Broker update response
export interface BrokerUpdateResponse {
  broker: BrokerWithOrganization;
  message: string;
}

// Broker delete response
export interface BrokerDeleteResponse {
  message: string;
}

// Broker query parameters
export interface BrokerQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  organizationId?: string;
  status?: BrokerStatus;
  operatingModel?: BrokerOperatingModel;
  verificationStatus?: VerificationStatus;
  sortBy?: "name" | "createdAt" | "status" | "organization" | "commissionRate";
  sortOrder?: "asc" | "desc";
}

// Organization data for broker management
export interface OrganizationForBroker {
  id: string;
  name: string;
  legalName: string | null;
  country: string | null;
  industry: string | null;
  status: string;
  verificationStatus: string;
}

// Organization broker response
export interface OrganizationBrokerResponse {
  organization: OrganizationForBroker;
  brokers: Broker[];
}

// Broker bulk operation request
export interface BrokerBulkOperationRequest {
  brokerIds: string[];
  operation: "activate" | "deactivate" | "suspend" | "delete";
  reason?: string;
}

// Broker bulk operation response
export interface BrokerBulkOperationResponse {
  success: boolean;
  message: string;
  processedCount: number;
  failedCount: number;
  errors?: string[];
}

// Enums
export type BrokerStatus = "ACTIVE" | "INACTIVE" | "SUSPENDED" | "TERMINATED";
export type BrokerOperatingModel = "FULL_SERVICE" | "EXECUTION_ONLY" | "ADVISORY_ONLY" | "HYBRID";
export type VerificationStatus = "PENDING" | "IN_REVIEW" | "VERIFIED" | "REJECTED";
export type BrokerClientType = "ORGANIZATION" | "INDIVIDUAL";
export type BrokerClientStatus = "ACTIVE" | "INACTIVE" | "SUSPENDED" | "TERMINATED";
export type BrokerTransactionType = "CARBON_CREDIT_SALE" | "CARBON_CREDIT_PURCHASE" | "PROJECT_INVESTMENT" | "CONSULTATION" | "OTHER";
export type BrokerTransactionStatus = "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED" | "CANCELLED";
export type BrokerCommissionStatus = "PENDING" | "CALCULATED" | "APPROVED" | "PAID" | "DISPUTED";
export type BrokerDocumentType = "LICENSE" | "REGISTRATION" | "INSURANCE" | "BOND" | "AGREEMENT" | "COMPLIANCE" | "FINANCIAL" | "OTHER";

// Broker Client interfaces
export interface BrokerClient {
  id: string;
  brokerId: string;
  clientType: BrokerClientType;
  clientName: string;
  clientEmail: string;
  clientPhone?: string | null;
  organizationId?: string | null;
  userId?: string | null;
  status: BrokerClientStatus;
  relationshipType?: string | null;
  commissionRate?: number | null;
  contractStartDate?: string | null;
  contractEndDate?: string | null;
  agreementDocument?: string | null;
  notes?: string | null;
  metadata?: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
}

// Broker Client with related data
export interface BrokerClientWithDetails extends BrokerClient {
  broker: {
    id: string;
    name: string;
    email: string;
  };
  organization?: {
    id: string;
    name: string;
    legalName?: string | null;
  } | null;
  user?: {
    id: string;
    name?: string | null;
    email: string;
  } | null;
}

// Broker Client creation data
export interface BrokerClientCreateData {
  clientType: BrokerClientType;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  organizationId?: string;
  userId?: string;
  relationshipType?: string;
  commissionRate?: number;
  contractStartDate?: string;
  contractEndDate?: string;
  notes?: string;
}

// Broker Client update data
export interface BrokerClientUpdateData extends Partial<BrokerClientCreateData> {
  status?: BrokerClientStatus;
}

// Broker Transaction interfaces
export interface BrokerTransaction {
  id: string;
  brokerId: string;
  transactionType: BrokerTransactionType;
  amount: number;
  currency: string;
  commissionAmount: number;
  commissionRate: number;
  carbonCreditId?: string | null;
  orderId?: string | null;
  projectId?: string | null;
  clientType: BrokerClientType;
  clientOrganizationId?: string | null;
  clientUserId?: string | null;
  status: BrokerTransactionStatus;
  description?: string | null;
  notes?: string | null;
  externalTransactionId?: string | null;
  externalReference?: string | null;
  transactionDate: string;
  settlementDate?: string | null;
  createdAt: string;
  updatedAt: string;
}

// Broker Transaction with related data
export interface BrokerTransactionWithDetails extends BrokerTransaction {
  broker: {
    id: string;
    name: string;
    email: string;
  };
  carbonCredit?: {
    id: string;
    name: string;
    quantity: number;
  } | null;
  project?: {
    id: string;
    name: string;
    type: string;
  } | null;
  clientOrganization?: {
    id: string;
    name: string;
  } | null;
  clientUser?: {
    id: string;
    name?: string | null;
    email: string;
  } | null;
}

// Broker Commission interfaces
export interface BrokerCommission {
  id: string;
  brokerId: string;
  amount: number;
  currency: string;
  rate: number;
  baseAmount: number;
  transactionId?: string | null;
  periodStart: string;
  periodEnd: string;
  status: BrokerCommissionStatus;
  paymentDate?: string | null;
  paymentReference?: string | null;
  description?: string | null;
  notes?: string | null;
  metadata?: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
}

// Broker Document interfaces
export interface BrokerDocument {
  id: string;
  brokerId: string;
  documentType: BrokerDocumentType;
  title: string;
  description?: string | null;
  fileName: string;
  fileUrl: string;
  fileSize?: number | null;
  mimeType?: string | null;
  documentNumber?: string | null;
  issueDate?: string | null;
  expiryDate?: string | null;
  issuingAuthority?: string | null;
  status: DocumentStatus;
  verificationStatus: VerificationStatus;
  verifiedAt?: string | null;
  verifiedBy?: string | null;
  tags?: string[] | null;
  metadata?: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
}

export type DocumentStatus = "PENDING" | "APPROVED" | "REJECTED" | "EXPIRED";
