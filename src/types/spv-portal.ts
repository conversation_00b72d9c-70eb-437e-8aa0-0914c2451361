/**
 * SPV Portal related TypeScript interfaces and types
 */

import { SPVUserRole, DataVerificationStatus } from "@prisma/client";

// SPV User interfaces
export interface SPVUser {
  id: string;
  userId: string;
  spvId: string;
  role: SPVUserRole;
  permissions: Record<string, any> | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SPVUserWithDetails extends SPVUser {
  user: {
    id: string;
    email: string;
    name: string | null;
    jobTitle: string | null;
    phoneNumber: string | null;
  };
  spv: {
    id: string;
    name: string;
    purpose: string | null;
    jurisdiction: string | null;
    status: string;
  };
  projectAssignments: ProjectAssignment[];
}

// Project Assignment interfaces
export interface ProjectAssignment {
  id: string;
  projectId: string;
  spvUserId: string;
  assignedBy: string;
  assignedAt: string;
  isActive: boolean;
  permissions: Record<string, any> | null;
}

export interface ProjectAssignmentWithDetails extends ProjectAssignment {
  project: {
    id: string;
    name: string;
    description: string | null;
    type: string;
    status: string;
    location: string | null;
    country: string | null;
  };
  spvUser: {
    id: string;
    role: SPVUserRole;
    user: {
      id: string;
      name: string | null;
      email: string;
    };
  };
  assignedByUser: {
    id: string;
    name: string | null;
    email: string;
  };
}

// Data Verification interfaces
export interface DataVerificationLog {
  id: string;
  unitLogId: string;
  fromStatus: DataVerificationStatus;
  toStatus: DataVerificationStatus;
  verifiedBy: string;
  verificationNotes: string | null;
  metadata: Record<string, any> | null;
  createdAt: string;
}

export interface DataVerificationLogWithDetails extends DataVerificationLog {
  verifier: {
    id: string;
    name: string | null;
    email: string;
    role: string;
  };
  unitLog: {
    id: string;
    logDate: string;
    unitType: string;
    quantity: number;
    dataSource: string;
  };
}

// Enhanced UnitLog with verification workflow
export interface UnitLogWithVerification {
  id: string;
  projectId: string;
  logDate: string;
  frequency: string;
  unitType: string;
  quantity: number;
  dataSource: string;
  verificationStatus: DataVerificationStatus;
  loggedBy: string;
  verifiedBy: string | null;
  verifiedAt: string | null;
  verificationNotes: string | null;
  spvApprovedBy: string | null;
  spvApprovedAt: string | null;
  spvApprovalNotes: string | null;
  orgApprovedBy: string | null;
  orgApprovedAt: string | null;
  orgApprovalNotes: string | null;
  notes: string | null;
  metadata: Record<string, any> | null;
  createdAt: string;
  updatedAt: string;
  logger: {
    id: string;
    name: string | null;
    email: string;
  };
  verifier?: {
    id: string;
    name: string | null;
    email: string;
  };
  verificationLogs: DataVerificationLogWithDetails[];
}

// SPV Portal Dashboard data
export interface SPVDashboardData {
  assignedProjects: {
    id: string;
    name: string;
    status: string;
    type: string;
    dataEntryProgress: {
      total: number;
      draft: number;
      submitted: number;
      verified: number;
      approved: number;
    };
  }[];
  verificationQueue: {
    pendingVerification: number;
    pendingSpvApproval: number;
    pendingOrgApproval: number;
  };
  recentActivity: {
    id: string;
    type: 'DATA_ENTRY' | 'VERIFICATION' | 'APPROVAL' | 'REJECTION';
    description: string;
    timestamp: string;
    projectName: string;
  }[];
  userStats: {
    totalDataEntries: number;
    verificationsCompleted: number;
    approvalsGiven: number;
    rejectionsMade: number;
  };
}

// Form data types
export interface SPVUserCreateData {
  email: string;
  name: string;
  spvId: string;
  role: SPVUserRole;
  jobTitle?: string;
  phoneNumber?: string;
  permissions?: Record<string, any>;
}

export interface ProjectAssignmentCreateData {
  projectId: string;
  spvUserId: string;
  permissions?: Record<string, any>;
}

export interface DataVerificationActionData {
  action: 'VERIFY' | 'REJECT' | 'SPV_APPROVE' | 'SPV_REJECT' | 'ORG_APPROVE' | 'ORG_REJECT';
  notes?: string;
  metadata?: Record<string, any>;
}

// API Response types
export interface SPVPortalResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface SPVUserListResponse {
  spvUsers: SPVUserWithDetails[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface ProjectAssignmentListResponse {
  assignments: ProjectAssignmentWithDetails[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Query parameters
export interface SPVUserQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  spvId?: string;
  role?: SPVUserRole;
  isActive?: boolean;
  sortBy?: 'name' | 'email' | 'role' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

export interface ProjectAssignmentQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  spvUserId?: string;
  projectId?: string;
  isActive?: boolean;
  sortBy?: 'assignedAt' | 'projectName' | 'spvUserName';
  sortOrder?: 'asc' | 'desc';
}

export interface VerificationQueueQueryParams {
  page?: number;
  limit?: number;
  status?: DataVerificationStatus[];
  projectId?: string;
  loggedBy?: string;
  dateFrom?: string;
  dateTo?: string;
  sortBy?: 'logDate' | 'createdAt' | 'verificationStatus';
  sortOrder?: 'asc' | 'desc';
}

// Permission constants
export const SPV_PERMISSIONS = {
  // Base permissions
  READ_ASSIGNED_PROJECTS: 'read:assigned_projects',
  READ_PROJECT_DATA: 'read:project_data',
  VIEW_SPV_DASHBOARD: 'view:spv_dashboard',
  
  // Data entry permissions
  CREATE_UNIT_LOG: 'create:unit_log',
  UPDATE_UNIT_LOG_OWN: 'update:unit_log:own',
  UPDATE_UNIT_LOG_ANY: 'update:unit_log:any',
  UPLOAD_DATA_FILES: 'upload:data_files',
  SUBMIT_DATA_FOR_VERIFICATION: 'submit:data_for_verification',
  
  // Verification permissions
  VERIFY_UNIT_LOG: 'verify:unit_log',
  REJECT_UNIT_LOG: 'reject:unit_log',
  APPROVE_DATA_ENTRY: 'approve:data_entry',
  MANAGE_PROJECT_DATA: 'manage:project_data',
  VIEW_VERIFICATION_QUEUE: 'view:verification_queue',
  
  // SPV Admin permissions
  REVIEW_VERIFIED_DATA: 'review:verified_data',
  APPROVE_SPV_LEVEL: 'approve:spv_level',
  MANAGE_SPV_USERS: 'manage:spv_users',
  VIEW_SPV_ANALYTICS: 'view:spv_analytics',
  EXPORT_PROJECT_REPORTS: 'export:project_reports',
  
  // Organization Admin permissions
  CREATE_SPV_USER: 'create:spv_user',
  ASSIGN_PROJECT_TO_SPV: 'assign:project_to_spv',
  MANAGE_SPV_PERMISSIONS: 'manage:spv_permissions',
  APPROVE_ORG_LEVEL: 'approve:org_level',
  SUBMIT_TO_VVB: 'submit:to_vvb',
} as const;

export type SPVPermission = typeof SPV_PERMISSIONS[keyof typeof SPV_PERMISSIONS];
