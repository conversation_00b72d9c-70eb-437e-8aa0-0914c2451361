/**
 * RBAC Initialization Script
 * 
 * This script initializes the RBAC system by:
 * 1. Syncing all permissions to the database
 * 2. Creating default system roles
 * 3. Setting up role-permission mappings
 */

import { db } from '@/lib/db';
import { ALL_PERMISSIONS } from './permissions';
import { SYSTEM_ROLES, PREDEFINED_CUSTOM_ROLES } from './roles';
import { logger } from '@/lib/logger';

export async function initializeRbacSystem() {
  try {
    logger.info('Starting RBAC system initialization...');

    // 1. Sync permissions to database
    await syncPermissions();

    // 2. Create system roles (if they don't exist)
    await createSystemRoles();

    // 3. Create predefined custom roles
    await createPredefinedCustomRoles();

    logger.info('RBAC system initialization completed successfully');
  } catch (error) {
    logger.error('Failed to initialize RBAC system:', error);
    throw error;
  }
}

async function syncPermissions() {
  logger.info('Syncing permissions to database...');
  
  let created = 0;
  let updated = 0;

  for (const permission of ALL_PERMISSIONS) {
    try {
      const result = await db.permission.upsert({
        where: { name: permission.name },
        update: {
          displayName: permission.displayName,
          description: permission.description,
          category: permission.category,
        },
        create: {
          name: permission.name,
          displayName: permission.displayName,
          description: permission.description,
          category: permission.category,
        },
      });

      // Check if it was created or updated
      const existingPermission = await db.permission.findUnique({
        where: { name: permission.name },
        select: { createdAt: true, updatedAt: true },
      });

      if (existingPermission) {
        if (existingPermission.createdAt.getTime() === existingPermission.updatedAt.getTime()) {
          created++;
        } else {
          updated++;
        }
      }
    } catch (error) {
      logger.error(`Failed to sync permission ${permission.name}:`, error);
    }
  }

  logger.info(`Permissions synced: ${created} created, ${updated} updated`);
}

async function createSystemRoles() {
  logger.info('Creating system roles...');

  for (const [roleName, roleData] of Object.entries(SYSTEM_ROLES)) {
    try {
      // Check if role already exists
      const existingRole = await db.customRole.findFirst({
        where: {
          name: roleName,
          isSystemRole: true,
        },
      });

      if (existingRole) {
        logger.info(`System role ${roleName} already exists, skipping...`);
        continue;
      }

      // Get permission IDs
      const permissions = await db.permission.findMany({
        where: {
          name: { in: roleData.permissions },
        },
        select: { id: true, name: true },
      });

      const permissionIds = permissions.map(p => p.id);

      // Create role in transaction
      await db.$transaction(async (tx) => {
        // Create the role
        const newRole = await tx.customRole.create({
          data: {
            name: roleName,
            description: roleData.description,
            isSystemRole: true,
            organizationId: null, // System roles are global
          },
        });

        // Add permissions
        if (permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: permissionIds.map(permissionId => ({
              roleId: newRole.id,
              permissionId,
            })),
          });
        }

        logger.info(`Created system role: ${roleName} with ${permissionIds.length} permissions`);
      });
    } catch (error) {
      logger.error(`Failed to create system role ${roleName}:`, error);
    }
  }
}

async function createPredefinedCustomRoles() {
  logger.info('Creating predefined custom roles...');

  for (const [roleName, roleData] of Object.entries(PREDEFINED_CUSTOM_ROLES)) {
    try {
      // Check if role template already exists
      const existingRole = await db.customRole.findFirst({
        where: {
          name: roleName,
          organizationId: null, // Templates have no organization
        },
      });

      if (existingRole) {
        logger.info(`Predefined role ${roleName} already exists, skipping...`);
        continue;
      }

      // Get permission IDs
      const permissions = await db.permission.findMany({
        where: {
          name: { in: roleData.permissions },
        },
        select: { id: true, name: true },
      });

      const permissionIds = permissions.map(p => p.id);

      // Create role template in transaction
      await db.$transaction(async (tx) => {
        // Create the role template
        const newRole = await tx.customRole.create({
          data: {
            name: roleName,
            description: roleData.description,
            isSystemRole: false,
            organizationId: null, // Templates have no organization
          },
        });

        // Add permissions
        if (permissionIds.length > 0) {
          await tx.rolePermission.createMany({
            data: permissionIds.map(permissionId => ({
              roleId: newRole.id,
              permissionId,
            })),
          });
        }

        logger.info(`Created predefined role template: ${roleName} with ${permissionIds.length} permissions`);
      });
    } catch (error) {
      logger.error(`Failed to create predefined role ${roleName}:`, error);
    }
  }
}

/**
 * Create organization-specific roles from templates
 */
export async function createOrganizationRolesFromTemplates(organizationId: string) {
  try {
    logger.info(`Creating organization roles for organization: ${organizationId}`);

    // Get all role templates (roles with no organization)
    const roleTemplates = await db.customRole.findMany({
      where: {
        organizationId: null,
        isSystemRole: false,
      },
      include: {
        permissions: {
          include: {
            permission: true,
          },
        },
      },
    });

    for (const template of roleTemplates) {
      // Check if organization already has this role
      const existingRole = await db.customRole.findFirst({
        where: {
          name: template.name,
          organizationId,
        },
      });

      if (existingRole) {
        continue;
      }

      // Create organization-specific role
      await db.$transaction(async (tx) => {
        const newRole = await tx.customRole.create({
          data: {
            name: template.name,
            description: template.description,
            isSystemRole: false,
            organizationId,
          },
        });

        // Copy permissions
        if (template.permissions.length > 0) {
          await tx.rolePermission.createMany({
            data: template.permissions.map(rp => ({
              roleId: newRole.id,
              permissionId: rp.permissionId,
            })),
          });
        }

        logger.info(`Created organization role: ${template.name} for organization: ${organizationId}`);
      });
    }
  } catch (error) {
    logger.error(`Failed to create organization roles for ${organizationId}:`, error);
    throw error;
  }
}

/**
 * Check if RBAC system is initialized
 */
export async function isRbacInitialized(): Promise<boolean> {
  try {
    const permissionCount = await db.permission.count();
    const systemRoleCount = await db.customRole.count({
      where: { isSystemRole: true },
    });

    return permissionCount > 0 && systemRoleCount > 0;
  } catch (error) {
    logger.error('Failed to check RBAC initialization status:', error);
    return false;
  }
}
