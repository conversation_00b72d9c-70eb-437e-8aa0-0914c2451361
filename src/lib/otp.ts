import { db } from "@/lib/db";
import { logger } from "@/lib/logger";
import { emailService } from "@/lib/email";

// OTP configuration
const OTP_LENGTH = 6;
const OTP_EXPIRES_IN = 10 * 60 * 1000; // 10 minutes in milliseconds
const MAX_ATTEMPTS = 5;

/**
 * Generate a random OTP
 * @param length Length of the OTP (default: 6)
 * @returns Random OTP string
 */
export function generateOTP(length: number = OTP_LENGTH): string {
  const digits = '0123456789';
  let otp = '';
  
  for (let i = 0; i < length; i++) {
    otp += digits[Math.floor(Math.random() * digits.length)];
  }
  
  return otp;
}

/**
 * Generate and store OTP for email verification
 * @param email User's email address
 * @param userId Optional user ID (for registered users)
 * @returns Generated OTP
 */
export async function generateEmailOTP(email: string, userId?: string): Promise<string> {
  try {
    // Generate OTP
    const otp = generateOTP();
    const expires = new Date(Date.now() + OTP_EXPIRES_IN);

    // Delete any existing OTP for this email
    await db.otpVerification.deleteMany({
      where: {
        email,
        verified: false,
      },
    });

    // Store the new OTP
    await db.otpVerification.create({
      data: {
        email,
        otp,
        expires,
        userId,
      },
    });

    logger.info(`OTP generated for email: ${email}`);
    return otp;
  } catch (error) {
    logger.error(`Error generating OTP for email ${email}:`, error);
    throw new Error("Failed to generate OTP");
  }
}

/**
 * Send OTP via email
 * @param email User's email address
 * @param name User's name
 * @param otp OTP code
 */
export async function sendOTPEmail(email: string, name: string, otp: string): Promise<void> {
  try {
    const subject = "Your CarbonX Verification Code";
    
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Email Verification - CarbonX</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .otp-box { 
            background: #f8f9fa; 
            border: 2px solid #e9ecef; 
            border-radius: 8px; 
            padding: 20px; 
            text-align: center; 
            margin: 20px 0; 
          }
          .otp-code { 
            font-size: 32px; 
            font-weight: bold; 
            color: #007bff; 
            letter-spacing: 8px; 
            margin: 10px 0; 
          }
          .footer { margin-top: 30px; font-size: 14px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Email Verification</h1>
          </div>
          
          <p>Hello ${name},</p>
          
          <p>Thank you for registering with CarbonX. To complete your registration, please enter the verification code below:</p>
          
          <div class="otp-box">
            <p>Your verification code is:</p>
            <div class="otp-code">${otp}</div>
            <p><small>This code will expire in 10 minutes</small></p>
          </div>
          
          <p>If you didn't request this verification code, please ignore this email.</p>
          
          <div class="footer">
            <p>Best regards,<br>The CarbonX Team</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
Hello ${name},

Thank you for registering with CarbonX. To complete your registration, please enter the verification code below:

Your verification code is: ${otp}

This code will expire in 10 minutes.

If you didn't request this verification code, please ignore this email.

Best regards,
The CarbonX Team
    `;

    await emailService.sendEmail({
      to: email,
      subject,
      html,
      text,
    });

    logger.info(`OTP email sent to: ${email}`);
  } catch (error) {
    logger.error(`Error sending OTP email to ${email}:`, error);
    throw new Error("Failed to send OTP email");
  }
}

/**
 * Verify OTP
 * @param email User's email address
 * @param otp OTP code to verify
 * @returns Verification result with user ID if successful
 */
export async function verifyOTP(email: string, otp: string): Promise<{ success: boolean; userId?: string; message: string }> {
  try {
    // Find the OTP record
    const otpRecord = await db.otpVerification.findFirst({
      where: {
        email,
        otp,
        verified: false,
        expires: {
          gt: new Date(),
        },
      },
    });

    if (!otpRecord) {
      // Check if there's an expired or already verified OTP
      const expiredOtp = await db.otpVerification.findFirst({
        where: {
          email,
          otp,
        },
      });

      if (expiredOtp) {
        if (expiredOtp.verified) {
          return { success: false, message: "OTP has already been used" };
        }
        if (expiredOtp.expires < new Date()) {
          return { success: false, message: "OTP has expired. Please request a new one" };
        }
      }

      // Increment attempts for any existing OTP for this email
      await db.otpVerification.updateMany({
        where: {
          email,
          verified: false,
        },
        data: {
          attempts: {
            increment: 1,
          },
        },
      });

      return { success: false, message: "Invalid OTP code" };
    }

    // Check if max attempts exceeded
    if (otpRecord.attempts >= MAX_ATTEMPTS) {
      return { success: false, message: "Maximum verification attempts exceeded. Please request a new OTP" };
    }

    // Mark OTP as verified
    await db.otpVerification.update({
      where: {
        id: otpRecord.id,
      },
      data: {
        verified: true,
      },
    });

    logger.info(`OTP verified successfully for email: ${email}`);
    return { 
      success: true, 
      userId: otpRecord.userId || undefined,
      message: "OTP verified successfully" 
    };
  } catch (error) {
    logger.error(`Error verifying OTP for email ${email}:`, error);
    return { success: false, message: "An error occurred during verification" };
  }
}

/**
 * Resend OTP
 * @param email User's email address
 * @param name User's name
 * @returns Success status
 */
export async function resendOTP(email: string, name: string): Promise<{ success: boolean; message: string }> {
  try {
    // Check if there's a recent OTP (within 1 minute)
    const recentOtp = await db.otpVerification.findFirst({
      where: {
        email,
        createdAt: {
          gt: new Date(Date.now() - 60 * 1000), // 1 minute ago
        },
      },
    });

    if (recentOtp) {
      return { 
        success: false, 
        message: "Please wait at least 1 minute before requesting a new OTP" 
      };
    }

    // Generate and send new OTP
    const otp = await generateEmailOTP(email);
    await sendOTPEmail(email, name, otp);

    return { success: true, message: "New OTP sent successfully" };
  } catch (error) {
    logger.error(`Error resending OTP for email ${email}:`, error);
    return { success: false, message: "Failed to resend OTP" };
  }
}

/**
 * Clean up expired OTPs (should be called periodically)
 */
export async function cleanupExpiredOTPs(): Promise<void> {
  try {
    const result = await db.otpVerification.deleteMany({
      where: {
        expires: {
          lt: new Date(),
        },
      },
    });

    logger.info(`Cleaned up ${result.count} expired OTP records`);
  } catch (error) {
    logger.error("Error cleaning up expired OTPs:", error);
  }
}
