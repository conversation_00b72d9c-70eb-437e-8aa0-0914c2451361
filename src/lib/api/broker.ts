/**
 * Broker API client functions
 * 
 * This module provides API client functions for broker management,
 * following the same patterns as SPV API client.
 */

import { API_ENDPOINTS } from "@/lib/api-endpoints";
import {
  Broker,
  BrokerWithOrganization,
  BrokerWithDetails,
  BrokerCreateData,
  AdminBrokerCreateData,
  BrokerUpdateData,
  BrokerListResponse,
  BrokerDetailsResponse,
  BrokerCreateResponse,
  BrokerUpdateResponse,
  BrokerDeleteResponse,
  OrganizationBrokerResponse,
  BrokerQueryParams,
  BrokerBulkOperationRequest,
  BrokerBulkOperationResponse,
  BrokerClient,
  BrokerTransaction,
  BrokerCommission,
} from "@/types/broker";

// Base API error class
export class BrokerApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string
  ) {
    super(message);
    this.name = "BrokerApiError";
  }
}

// Helper function to handle API responses
async function handleApiResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new BrokerApiError(
      errorData.error || `HTTP ${response.status}`,
      response.status,
      errorData.code
    );
  }
  return response.json();
}

// Helper function to build query strings
function buildQueryString(params: Record<string, any>): string {
  const searchParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      searchParams.append(key, String(value));
    }
  });
  
  const queryString = searchParams.toString();
  return queryString ? `?${queryString}` : "";
}

/**
 * Organization Broker API functions (for organization dashboard)
 */
export const organizationBrokerApi = {
  /**
   * Get all brokers for the current organization
   */
  async getOrganizationBrokers(params: BrokerQueryParams = {}): Promise<BrokerListResponse> {
    const queryString = buildQueryString(params);
    const response = await fetch(`${API_ENDPOINTS.ORGANIZATION.ORGANIZATION_BROKERS}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<BrokerListResponse>(response);
  },

  /**
   * Get a specific broker by ID within the organization
   */
  async getBrokerById(brokerId: string): Promise<BrokerDetailsResponse> {
    const response = await fetch(`${API_ENDPOINTS.ORGANIZATION.ORGANIZATION_BROKERS}/${brokerId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<BrokerDetailsResponse>(response);
  },

  /**
   * Create a new broker for the organization
   */
  async createBroker(data: BrokerCreateData): Promise<BrokerCreateResponse> {
    const response = await fetch(API_ENDPOINTS.ORGANIZATION.ORGANIZATION_BROKERS, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    return handleApiResponse<BrokerCreateResponse>(response);
  },

  /**
   * Update a broker within the organization
   */
  async updateBroker(brokerId: string, data: BrokerUpdateData): Promise<BrokerUpdateResponse> {
    const response = await fetch(`${API_ENDPOINTS.ORGANIZATION.ORGANIZATION_BROKERS}/${brokerId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    return handleApiResponse<BrokerUpdateResponse>(response);
  },

  /**
   * Delete a broker within the organization
   */
  async deleteBroker(brokerId: string): Promise<BrokerDeleteResponse> {
    const response = await fetch(`${API_ENDPOINTS.ORGANIZATION.ORGANIZATION_BROKERS}/${brokerId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<BrokerDeleteResponse>(response);
  },
};

/**
 * Admin Broker API functions (for admin panel)
 */
export const adminBrokerApi = {
  /**
   * Get all brokers across organizations with filtering and pagination
   */
  async getAllBrokers(params: BrokerQueryParams = {}): Promise<BrokerListResponse> {
    const queryString = buildQueryString(params);
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_BROKERS}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<BrokerListResponse>(response);
  },

  /**
   * Get a specific broker by ID (admin access)
   */
  async getBrokerById(brokerId: string): Promise<BrokerDetailsResponse> {
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_BROKERS}/${brokerId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<BrokerDetailsResponse>(response);
  },

  /**
   * Create a new broker (admin)
   */
  async createBroker(data: AdminBrokerCreateData): Promise<BrokerCreateResponse> {
    const response = await fetch(API_ENDPOINTS.ADMIN.ADMIN_BROKERS, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    return handleApiResponse<BrokerCreateResponse>(response);
  },

  /**
   * Update a broker (admin)
   */
  async updateBroker(brokerId: string, data: BrokerUpdateData): Promise<BrokerUpdateResponse> {
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_BROKERS}/${brokerId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    return handleApiResponse<BrokerUpdateResponse>(response);
  },

  /**
   * Delete a broker (admin)
   */
  async deleteBroker(brokerId: string): Promise<BrokerDeleteResponse> {
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_BROKERS}/${brokerId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });
    
    return handleApiResponse<BrokerDeleteResponse>(response);
  },

  /**
   * Perform bulk operations on brokers (admin)
   */
  async bulkOperation(data: BrokerBulkOperationRequest): Promise<BrokerBulkOperationResponse> {
    const response = await fetch(`${API_ENDPOINTS.ADMIN.ADMIN_BROKERS}/bulk`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    return handleApiResponse<BrokerBulkOperationResponse>(response);
  },
};

/**
 * Broker-specific API functions (for broker dashboard)
 */
export const brokerApi = {
  /**
   * Get broker dashboard statistics
   */
  async getDashboardStats(): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_DASHBOARD_STATS, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Get broker profile
   */
  async getProfile(): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_PROFILE, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Update broker profile
   */
  async updateProfile(data: any): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_PROFILE, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Get broker clients
   */
  async getClients(params: any = {}): Promise<any> {
    const queryString = buildQueryString(params);
    const response = await fetch(`${API_ENDPOINTS.BROKER.BROKER_CLIENTS}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Create new broker client
   */
  async createClient(data: any): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_CLIENTS, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Get broker client by ID
   */
  async getClientById(clientId: string): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_CLIENT_BY_ID.replace("{id}", clientId), {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Update broker client
   */
  async updateClient(clientId: string, data: any): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_CLIENT_BY_ID.replace("{id}", clientId), {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Delete broker client
   */
  async deleteClient(clientId: string): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_CLIENT_BY_ID.replace("{id}", clientId), {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Get broker transactions
   */
  async getTransactions(params: any = {}): Promise<any> {
    const queryString = buildQueryString(params);
    const response = await fetch(`${API_ENDPOINTS.BROKER.BROKER_TRANSACTIONS}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Create new broker transaction
   */
  async createTransaction(data: any): Promise<any> {
    const response = await fetch(API_ENDPOINTS.BROKER.BROKER_TRANSACTIONS, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Get broker commissions
   */
  async getCommissions(params: any = {}): Promise<any> {
    const queryString = buildQueryString(params);
    const response = await fetch(`${API_ENDPOINTS.BROKER.BROKER_COMMISSIONS}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },

  /**
   * Get broker earnings summary
   */
  async getEarnings(params: any = {}): Promise<any> {
    const queryString = buildQueryString(params);
    const response = await fetch(`${API_ENDPOINTS.BROKER.BROKER_EARNINGS}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    return handleApiResponse<any>(response);
  },
};

/**
 * Broker utility functions
 */
export const brokerUtils = {
  /**
   * Format broker status for display
   */
  formatStatus(status: string): string {
    const statusMap: Record<string, string> = {
      PENDING: "Pending",
      ACTIVE: "Active",
      INACTIVE: "Inactive",
      SUSPENDED: "Suspended",
      TERMINATED: "Terminated",
    };
    return statusMap[status] || status;
  },

  /**
   * Format verification status for display
   */
  formatVerificationStatus(status: string): string {
    const statusMap: Record<string, string> = {
      PENDING: "Pending",
      IN_REVIEW: "In Review",
      VERIFIED: "Verified",
      REJECTED: "Rejected",
    };
    return statusMap[status] || status;
  },

  /**
   * Format operating model for display
   */
  formatOperatingModel(model: string): string {
    const modelMap: Record<string, string> = {
      INDEPENDENT: "Independent",
      ONIX_MANAGED: "Onix Managed",
      HYBRID: "Hybrid",
    };
    return modelMap[model] || model;
  },

  /**
   * Format commission rate as percentage
   */
  formatCommissionRate(rate: number): string {
    return `${(rate * 100).toFixed(2)}%`;
  },

  /**
   * Get status color for UI components
   */
  getStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      PENDING: "yellow",
      ACTIVE: "green",
      INACTIVE: "gray",
      SUSPENDED: "orange",
      TERMINATED: "red",
    };
    return colorMap[status] || "gray";
  },

  /**
   * Get verification status color for UI components
   */
  getVerificationStatusColor(status: string): string {
    const colorMap: Record<string, string> = {
      PENDING: "yellow",
      IN_REVIEW: "blue",
      VERIFIED: "green",
      REJECTED: "red",
    };
    return colorMap[status] || "gray";
  },

  /**
   * Validate broker data before submission
   */
  validateBrokerData(data: Partial<BrokerCreateData>): string[] {
    const errors: string[] = [];

    if (!data.name?.trim()) {
      errors.push("Broker name is required");
    }

    if (!data.email?.trim()) {
      errors.push("Email is required");
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push("Invalid email format");
    }

    if (data.commissionRate !== undefined && (data.commissionRate < 0 || data.commissionRate > 1)) {
      errors.push("Commission rate must be between 0% and 100%");
    }

    if (data.minimumCommission !== undefined && data.maximumCommission !== undefined) {
      if (data.minimumCommission > data.maximumCommission) {
        errors.push("Minimum commission cannot be greater than maximum commission");
      }
    }

    return errors;
  },
};
