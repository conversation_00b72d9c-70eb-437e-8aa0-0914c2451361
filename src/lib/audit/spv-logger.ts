import { AuditService } from './service';
import { AuditLogType } from '@prisma/client';
import { logger } from '@/lib/logger';

interface SPVAuditContext {
  userId: string;
  organizationId: string;
  spvId?: string;
  userRole?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
}

/**
 * SPV management audit logging utilities
 */
export class SPVAuditLogger {
  /**
   * Log SPV creation
   */
  static async logSPVCreated(context: SPVAuditContext, spvData: any) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_CREATED,
        description: `SPV created: ${spvData.name}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: spvData.id,
        resourceType: 'spv',
        resourceId: spvData.id,
        metadata: {
          spvName: spvData.name,
          spvType: spvData.type,
          createdBy: context.userId,
          userRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'SPV',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV creation:', error);
    }
  }

  /**
   * Log SPV update
   */
  static async logSPVUpdated(context: SPVAuditContext, spvId: string, oldData: any, newData: any) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_UPDATED,
        description: `SPV updated: ${newData.name || oldData.name}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'spv',
        resourceId: spvId,
        oldValue: oldData,
        newValue: newData,
        metadata: {
          spvName: newData.name || oldData.name,
          updatedBy: context.userId,
          userRole: context.userRole,
          changedFields: Object.keys(newData),
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'SPV',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV update:', error);
    }
  }

  /**
   * Log SPV deletion
   */
  static async logSPVDeleted(context: SPVAuditContext, spvId: string, spvName: string) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_DELETED,
        description: `SPV deleted: ${spvName}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'spv',
        resourceId: spvId,
        metadata: {
          spvName,
          deletedBy: context.userId,
          userRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'SPV',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV deletion:', error);
    }
  }

  /**
   * Log SPV verification
   */
  static async logSPVVerified(context: SPVAuditContext, spvId: string, spvName: string) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_VERIFIED,
        description: `SPV verified: ${spvName}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'spv',
        resourceId: spvId,
        metadata: {
          spvName,
          verifiedBy: context.userId,
          userRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'SPV',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV verification:', error);
    }
  }

  /**
   * Log SPV rejection
   */
  static async logSPVRejected(context: SPVAuditContext, spvId: string, spvName: string, reason: string) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_REJECTED,
        description: `SPV rejected: ${spvName} - ${reason}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'spv',
        resourceId: spvId,
        metadata: {
          spvName,
          rejectedBy: context.userId,
          userRole: context.userRole,
          rejectionReason: reason,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'SPV',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV rejection:', error);
    }
  }

  /**
   * Log SPV document upload
   */
  static async logSPVDocumentUploaded(
    context: SPVAuditContext, 
    spvId: string, 
    documentType: string, 
    fileName: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_DOCUMENT_UPLOADED,
        description: `SPV document uploaded: ${documentType} - ${fileName}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'document',
        resourceId: `${spvId}-${documentType}`,
        metadata: {
          documentType,
          fileName,
          uploadedBy: context.userId,
          userRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'DOCUMENT',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV document upload:', error);
    }
  }

  /**
   * Log SPV document verification
   */
  static async logSPVDocumentVerified(
    context: SPVAuditContext, 
    spvId: string, 
    documentType: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_DOCUMENT_VERIFIED,
        description: `SPV document verified: ${documentType}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'document',
        resourceId: `${spvId}-${documentType}`,
        metadata: {
          documentType,
          verifiedBy: context.userId,
          userRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'DOCUMENT',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV document verification:', error);
    }
  }

  /**
   * Log SPV document rejection
   */
  static async logSPVDocumentRejected(
    context: SPVAuditContext, 
    spvId: string, 
    documentType: string, 
    reason: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_DOCUMENT_REJECTED,
        description: `SPV document rejected: ${documentType} - ${reason}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'document',
        resourceId: `${spvId}-${documentType}`,
        metadata: {
          documentType,
          rejectedBy: context.userId,
          userRole: context.userRole,
          rejectionReason: reason,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'DOCUMENT',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV document rejection:', error);
    }
  }

  /**
   * Log SPV user creation
   */
  static async logSPVUserCreated(
    context: SPVAuditContext, 
    spvId: string, 
    newUserId: string, 
    userEmail: string, 
    userRole: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_USER_CREATED,
        description: `SPV user created: ${userEmail} with role ${userRole}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'user',
        resourceId: newUserId,
        metadata: {
          newUserEmail: userEmail,
          newUserRole: userRole,
          createdBy: context.userId,
          creatorRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'ROLE',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV user creation:', error);
    }
  }

  /**
   * Log SPV user role change
   */
  static async logSPVUserRoleChanged(
    context: SPVAuditContext, 
    spvId: string, 
    targetUserId: string, 
    oldRole: string, 
    newRole: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_USER_ROLE_CHANGED,
        description: `SPV user role changed from ${oldRole} to ${newRole}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'user_role',
        resourceId: targetUserId,
        oldValue: { role: oldRole },
        newValue: { role: newRole },
        metadata: {
          targetUserId,
          oldRole,
          newRole,
          changedBy: context.userId,
          changerRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'ROLE',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV user role change:', error);
    }
  }

  /**
   * Log SPV user deletion
   */
  static async logSPVUserDeleted(
    context: SPVAuditContext, 
    spvId: string, 
    deletedUserId: string, 
    userEmail: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SPV_USER_DELETED,
        description: `SPV user deleted: ${userEmail}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId,
        resourceType: 'user',
        resourceId: deletedUserId,
        metadata: {
          deletedUserEmail: userEmail,
          deletedBy: context.userId,
          deleterRole: context.userRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'ROLE',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging SPV user deletion:', error);
    }
  }
}
