import { AuditService } from './service';
import { AuditLogType } from '@prisma/client';
import { logger } from '@/lib/logger';

interface DataAuditContext {
  userId: string;
  organizationId: string;
  spvId?: string;
  projectId?: string;
  userRole?: string;
  spvRole?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
}

/**
 * Data entry audit logging utilities
 */
export class DataAuditLogger {
  /**
   * Log data entry creation
   */
  static async logDataEntryCreated(
    context: DataAuditContext, 
    entryType: string, 
    entryId: string, 
    entryData: any
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.DATA_ENTRY_CREATED,
        description: `${entryType} data entry created`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_entry',
        resourceId: entryId,
        newValue: entryData,
        metadata: {
          entryType,
          entryId,
          createdBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'DATA',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging data entry creation:', error);
    }
  }

  /**
   * Log data entry update
   */
  static async logDataEntryUpdated(
    context: DataAuditContext, 
    entryType: string, 
    entryId: string, 
    oldData: any, 
    newData: any
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.DATA_ENTRY_UPDATED,
        description: `${entryType} data entry updated`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_entry',
        resourceId: entryId,
        oldValue: oldData,
        newValue: newData,
        metadata: {
          entryType,
          entryId,
          updatedBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          changedFields: Object.keys(newData),
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'DATA',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging data entry update:', error);
    }
  }

  /**
   * Log data entry deletion
   */
  static async logDataEntryDeleted(
    context: DataAuditContext, 
    entryType: string, 
    entryId: string, 
    entryData: any
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.DATA_ENTRY_DELETED,
        description: `${entryType} data entry deleted`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_entry',
        resourceId: entryId,
        oldValue: entryData,
        metadata: {
          entryType,
          entryId,
          deletedBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'DATA',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging data entry deletion:', error);
    }
  }

  /**
   * Log data entry verification
   */
  static async logDataEntryVerified(
    context: DataAuditContext, 
    entryType: string, 
    entryId: string, 
    verificationLevel: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.DATA_ENTRY_VERIFIED,
        description: `${entryType} data entry verified at ${verificationLevel} level`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_entry',
        resourceId: entryId,
        metadata: {
          entryType,
          entryId,
          verificationLevel,
          verifiedBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'DATA',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging data entry verification:', error);
    }
  }

  /**
   * Log data entry rejection
   */
  static async logDataEntryRejected(
    context: DataAuditContext, 
    entryType: string, 
    entryId: string, 
    reason: string, 
    verificationLevel: string
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.DATA_ENTRY_REJECTED,
        description: `${entryType} data entry rejected at ${verificationLevel} level - ${reason}`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_entry',
        resourceId: entryId,
        metadata: {
          entryType,
          entryId,
          verificationLevel,
          rejectionReason: reason,
          rejectedBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'DATA',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging data entry rejection:', error);
    }
  }

  /**
   * Log bulk data operation
   */
  static async logBulkDataOperation(
    context: DataAuditContext, 
    operation: string, 
    entryType: string, 
    count: number, 
    details?: any
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.BULK_DATA_OPERATION,
        description: `Bulk ${operation} operation on ${count} ${entryType} entries`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_entry',
        resourceId: `bulk-${operation}-${Date.now()}`,
        metadata: {
          operation,
          entryType,
          count,
          details,
          performedBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'DATA',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging bulk data operation:', error);
    }
  }

  /**
   * Log data export
   */
  static async logDataExport(
    context: DataAuditContext, 
    exportType: string, 
    format: string, 
    filters?: any
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.DATA_EXPORTED,
        description: `Data exported: ${exportType} in ${format} format`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_export',
        resourceId: `export-${Date.now()}`,
        metadata: {
          exportType,
          format,
          filters,
          exportedBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'DATA',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging data export:', error);
    }
  }

  /**
   * Log data import
   */
  static async logDataImport(
    context: DataAuditContext, 
    importType: string, 
    fileName: string, 
    recordCount: number, 
    success: boolean, 
    errors?: string[]
  ) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.DATA_IMPORTED,
        description: `Data imported: ${importType} from ${fileName} (${recordCount} records)`,
        userId: context.userId,
        organizationId: context.organizationId,
        spvId: context.spvId,
        projectId: context.projectId,
        resourceType: 'data_import',
        resourceId: `import-${Date.now()}`,
        metadata: {
          importType,
          fileName,
          recordCount,
          errors,
          importedBy: context.userId,
          userRole: context.userRole,
          spvRole: context.spvRole,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: success ? 'INFO' : 'ERROR',
        category: 'DATA',
        source: 'WEB',
        success,
        errorCode: success ? undefined : 'IMPORT_FAILED'
      });
    } catch (error) {
      logger.error('Error logging data import:', error);
    }
  }
}
