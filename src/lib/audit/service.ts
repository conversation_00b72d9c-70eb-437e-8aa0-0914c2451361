import { AuditLogType, Prisma } from '@prisma/client';
import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import {
  AuditLog,
  AuditLogCreationData,
  AuditLogFilter,
  AuditLogResponse,
  AuditSummary
} from './types';

// Enhanced audit log creation data with new fields
interface EnhancedAuditLogCreationData extends AuditLogCreationData {
  sessionId?: string;
  requestId?: string;
  severity?: 'INFO' | 'WARN' | 'ERROR' | 'CRITICAL';
  category?: 'AUTHENTICATION' | 'AUTHORIZATION' | 'DATA' | 'DATA_ENTRY' | 'SYSTEM' | 'SPV' | 'DOCUMENT' | 'ROLE' | 'API' | 'TRANSACTION' | 'WALLET';
  source?: 'WEB' | 'API' | 'MOBILE' | 'SYSTEM' | 'CLI';
  duration?: number;
  success?: boolean;
  errorCode?: string;
  tags?: string[];
}

// Enhanced filter interface
interface EnhancedAuditLogFilter extends AuditLogFilter {
  severity?: string;
  category?: string;
  source?: string;
  success?: boolean;
  sessionId?: string;
  requestId?: string;
  tags?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  excludeTypes?: AuditLogType[];
  includeMetadata?: boolean;
  spvFilter?: string; // 'SPV' | 'NON_SPV' | undefined
}

/**
 * Audit service for logging and retrieving audit logs
 */
export class AuditService {
  /**
   * Create an enhanced audit log with comprehensive metadata
   * @param data Enhanced audit log creation data
   * @returns Created audit log
   */
  static async createAuditLog(data: EnhancedAuditLogCreationData): Promise<AuditLog> {
    try {
      // Enhanced logging with context
      const contextLogger = logger.withContext({
        userId: data.userId,
        organizationId: data.organizationId,
        spvId: data.spvId,
        projectId: data.projectId,
        action: data.type,
        resource: data.resourceType,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
        sessionId: data.sessionId,
        requestId: data.requestId,
        severity: data.severity,
        category: data.category
      });

      contextLogger.audit(`Creating audit log: ${data.type} - ${data.description}`, {
        resourceId: data.resourceId,
        oldValue: data.oldValue,
        newValue: data.newValue,
        severity: data.severity,
        category: data.category,
        success: data.success
      });

      const auditLog = await db.auditLog.create({
        data: {
          type: data.type,
          description: data.description,
          ...(data.userId && {
            user: {
              connect: { id: data.userId }
            }
          }),
          ...(data.organizationId && {
            organization: {
              connect: { id: data.organizationId }
            }
          }),
          ...(data.spvId && {
            spv: {
              connect: { id: data.spvId }
            }
          }),
          ...(data.projectId && {
            project: {
              connect: { id: data.projectId }
            }
          }),
          resourceType: data.resourceType,
          resourceId: data.resourceId,
          oldValue: data.oldValue as Prisma.JsonObject,
          newValue: data.newValue as Prisma.JsonObject,
          metadata: data.metadata as Prisma.JsonObject,
          ipAddress: data.ipAddress,
          userAgent: data.userAgent,

          // Enhanced fields
          sessionId: data.sessionId,
          requestId: data.requestId,
          severity: data.severity || 'INFO',
          category: data.category,
          source: data.source,
          duration: data.duration,
          success: data.success,
          errorCode: data.errorCode,
          tags: data.tags || [],
        },
      });

      contextLogger.audit(`Audit log created: ${auditLog.id}`);
      return auditLog;
    } catch (error) {
      logger.error(`Error creating audit log: ${data.type} - ${data.description}`, error);
      throw new Error(`Failed to create audit log: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get audit log by ID
   * @param id Audit log ID
   * @returns Audit log
   */
  static async getAuditLogById(id: string): Promise<AuditLog | null> {
    try {
      return await db.auditLog.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
    } catch (error) {
      logger.error(`Error getting audit log ${id}:`, error);
      throw new Error(`Failed to get audit log: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get audit logs (legacy method - use role-specific methods for better filtering)
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getAuditLogs(filter: AuditLogFilter = {}): Promise<AuditLogResponse> {
    return this.getAuditLogsWithRoleFilter(filter);
  }

  /**
   * Get audit logs for a user
   * @param userId User ID
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getUserAuditLogs(userId: string, filter: Omit<AuditLogFilter, 'userId'> = {}): Promise<AuditLogResponse> {
    return this.getAuditLogs({ ...filter, userId });
  }

  /**
   * Get audit logs for an organization
   * @param organizationId Organization ID
   * @param filter Filter options
   * @returns Audit logs and pagination
   */
  static async getOrganizationAuditLogs(organizationId: string, filter: Omit<AuditLogFilter, 'organizationId'> = {}): Promise<AuditLogResponse> {
    return this.getAuditLogs({ ...filter, organizationId });
  }

  /**
   * Get audit summary
   * @param filter Filter options
   * @returns Audit summary
   */
  static async getAuditSummary(filter: Omit<AuditLogFilter, 'limit' | 'offset' | 'sortBy' | 'sortOrder'> = {}): Promise<AuditSummary> {
    try {
      const {
        userId,
        organizationId,
        type,
        startDate,
        endDate,
        search,
      } = filter;
      
      // Build query
      const where: Prisma.AuditLogWhereInput = {
        ...(userId && { userId }),
        ...(organizationId && { organizationId }),
        ...(type && { type }),
        ...(startDate && { createdAt: { gte: startDate } }),
        ...(endDate && { createdAt: { lte: endDate } }),
        ...(search && {
          OR: [
            { description: { contains: search, mode: "insensitive" } },
            { type: { contains: search, mode: "insensitive" } },
          ],
        }),
      };
      
      // Get total count
      const totalLogs = await db.auditLog.count({ where });
      
      // Get logs by type
      const logsByTypeResult = await db.auditLog.groupBy({
        by: ['type'],
        where,
        _count: {
          id: true,
        },
      });
      
      const logsByType = logsByTypeResult.reduce((acc, curr) => {
        acc[curr.type as AuditLogType] = curr._count.id;
        return acc;
      }, {} as Record<AuditLogType, number>);
      
      // Get recent activity
      const recentActivity = await db.$queryRaw<{ date: string; count: number }[]>`
        SELECT 
          DATE_TRUNC('day', "createdAt") as date,
          COUNT(*) as count
        FROM "AuditLog"
        WHERE ${where ? Prisma.sql`${Prisma.join(Object.entries(where).map(([key, value]) => Prisma.sql`${Prisma.raw(key)} = ${value}`), ' AND ')}` : Prisma.sql`1=1`}
        GROUP BY DATE_TRUNC('day', "createdAt")
        ORDER BY date DESC
        LIMIT 30
      `;
      
      // Get top users
      const topUsers = await db.auditLog.groupBy({
        by: ['userId'],
        where: {
          ...where,
          userId: { not: null },
        },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 10,
      });
      
      // Get user names
      const userIds = topUsers.map((user) => user.userId).filter((id): id is string => id !== null);
      const users = await db.user.findMany({
        where: {
          id: { in: userIds },
        },
        select: {
          id: true,
          name: true,
        },
      });
      
      const userMap = users.reduce((acc, user) => {
        acc[user.id] = user.name;
        return acc;
      }, {} as Record<string, string | undefined>);
      
      // Get top organizations
      const topOrganizations = await db.auditLog.groupBy({
        by: ['organizationId'],
        where: {
          ...where,
          organizationId: { not: null },
        },
        _count: {
          id: true,
        },
        orderBy: {
          _count: {
            id: 'desc',
          },
        },
        take: 10,
      });
      
      // Get organization names
      const organizationIds = topOrganizations.map((org) => org.organizationId).filter((id): id is string => id !== null);
      const organizations = await db.organization.findMany({
        where: {
          id: { in: organizationIds },
        },
        select: {
          id: true,
          name: true,
        },
      });
      
      const organizationMap = organizations.reduce((acc, org) => {
        acc[org.id] = org.name;
        return acc;
      }, {} as Record<string, string | undefined>);
      
      return {
        totalLogs,
        logsByType,
        recentActivity,
        topUsers: topUsers.map((user) => ({
          userId: user.userId!,
          userName: userMap[user.userId!],
          count: user._count.id,
        })),
        topOrganizations: topOrganizations.map((org) => ({
          organizationId: org.organizationId!,
          organizationName: organizationMap[org.organizationId!],
          count: org._count.id,
        })),
      };
    } catch (error) {
      logger.error("Error getting audit summary:", error);
      throw new Error(`Failed to get audit summary: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }



  /**
   * Get audit logs for Organization Admin (all org data)
   */
  static async getAuditLogsForOrgAdmin(
    organizationId: string,
    filter: Omit<AuditLogFilter, 'organizationId'> = {}
  ): Promise<AuditLogResponse> {
    return this.getAuditLogsWithRoleFilter({
      ...filter,
      organizationId
    });
  }

  /**
   * Get audit logs for SPV Admin (SPV and assigned projects)
   */
  static async getAuditLogsForSPVAdmin(
    spvId: string,
    filter: Omit<AuditLogFilter, 'spvId'> = {}
  ): Promise<AuditLogResponse> {
    return this.getAuditLogsWithRoleFilter({
      ...filter,
      spvId
    });
  }

  /**
   * Get audit logs for Project Manager (assigned projects only)
   */
  static async getAuditLogsForProjectManager(
    projectIds: string[],
    filter: Omit<AuditLogFilter, 'projectId'> = {}
  ): Promise<AuditLogResponse> {
    const {
      limit = 10,
      offset = 0,
      page,
      sortBy = "createdAt",
      sortOrder = "desc",
      ...otherFilters
    } = filter;

    const actualOffset = page ? (page - 1) * limit : offset;

    const where: Prisma.AuditLogWhereInput = {
      projectId: { in: projectIds },
      ...this.buildWhereClause(otherFilters)
    };

    return this.executeAuditLogQuery(where, limit, actualOffset, sortBy, sortOrder);
  }

  /**
   * Get audit logs for Site Worker (own data entries only)
   */
  static async getAuditLogsForSiteWorker(
    userId: string,
    filter: Omit<AuditLogFilter, 'userId'> = {}
  ): Promise<AuditLogResponse> {
    return this.getAuditLogsWithRoleFilter({
      ...filter,
      userId
    });
  }

  /**
   * Get audit logs for Super Admin (all system logs)
   */
  static async getAuditLogsForSuperAdmin(
    filter: EnhancedAuditLogFilter = {}
  ): Promise<AuditLogResponse> {
    return this.getAuditLogsWithRoleFilter(filter);
  }

  /**
   * Real-time audit log streaming for organization admins
   */
  static async getRealtimeAuditLogs(
    organizationId: string,
    lastTimestamp?: Date,
    limit: number = 50
  ): Promise<AuditLogResponse> {
    try {
      const where: Prisma.AuditLogWhereInput = {
        organizationId,
        ...(lastTimestamp && {
          createdAt: {
            gt: lastTimestamp
          }
        })
      };

      const auditLogs = await db.auditLog.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true
            }
          },
          organization: {
            select: {
              id: true,
              name: true
            }
          },
          spv: {
            select: {
              id: true,
              name: true
            }
          },
          project: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      const total = await db.auditLog.count({ where });

      return {
        auditLogs,
        pagination: {
          total,
          limit,
          offset: 0,
          hasMore: auditLogs.length === limit
        }
      };
    } catch (error) {
      logger.error('Error fetching realtime audit logs:', error);
      throw new Error(`Failed to fetch realtime audit logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get audit log analytics and insights
   */
  static async getAuditAnalytics(
    organizationId: string | undefined,
    timeRange: { start: Date; end: Date }
  ): Promise<{
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    eventsByCategory: Record<string, number>;
    topUsers: Array<{ userId: string; userName: string; count: number }>;
    errorRate: number;
    averageResponseTime: number;
    securityEvents: number;
  }> {
    try {
      console.log('Analytics service called with:', { organizationId, timeRange });
      const where: Prisma.AuditLogWhereInput = {
        ...(organizationId && { organizationId }),
        createdAt: {
          gte: timeRange.start,
          lte: timeRange.end
        }
      };

      // Get total events
      const totalEvents = await db.auditLog.count({ where });
      console.log('Total events found:', totalEvents, 'for organization:', organizationId);

      // Get events by type
      const eventsByTypeRaw = await db.auditLog.groupBy({
        by: ['type'],
        where,
        _count: { type: true }
      });
      const eventsByType = eventsByTypeRaw.reduce((acc, item) => {
        acc[item.type] = item._count.type;
        return acc;
      }, {} as Record<string, number>);

      // Get events by severity (handle missing severity field gracefully)
      let eventsBySeverity: Record<string, number> = {};
      try {
        const eventsBySeverityRaw = await db.auditLog.groupBy({
          by: ['severity'],
          where: { ...where, severity: { not: null } },
          _count: { severity: true }
        });
        eventsBySeverity = eventsBySeverityRaw.reduce((acc, item) => {
          if (item.severity) acc[item.severity] = item._count.severity;
          return acc;
        }, {} as Record<string, number>);
      } catch (error) {
        console.warn('Severity field not available:', error);
        eventsBySeverity = { 'INFO': totalEvents }; // Default fallback
      }

      // Get events by category (handle missing category field gracefully)
      let eventsByCategory: Record<string, number> = {};
      try {
        const eventsByCategoryRaw = await db.auditLog.groupBy({
          by: ['category'],
          where: { ...where, category: { not: null } },
          _count: { category: true }
        });
        eventsByCategory = eventsByCategoryRaw.reduce((acc, item) => {
          if (item.category) acc[item.category] = item._count.category;
          return acc;
        }, {} as Record<string, number>);
      } catch (error) {
        console.warn('Category field not available:', error);
        eventsByCategory = { 'SYSTEM': totalEvents }; // Default fallback
      }

      // Get top users
      const topUsersRaw = await db.auditLog.groupBy({
        by: ['userId'],
        where: { ...where, userId: { not: null } },
        _count: { userId: true },
        orderBy: { _count: { userId: 'desc' } },
        take: 10
      });

      const userIds = topUsersRaw.map(item => item.userId).filter(Boolean) as string[];
      const users = await db.user.findMany({
        where: { id: { in: userIds } },
        select: { id: true, name: true }
      });

      const topUsers = topUsersRaw.map(item => {
        const user = users.find(u => u.id === item.userId);
        return {
          userId: item.userId || 'unknown',
          userName: user?.name || 'Unknown User',
          count: item._count.userId
        };
      });

      // Calculate error rate (handle missing success field gracefully)
      let errorRate = 0;
      try {
        const errorEvents = await db.auditLog.count({
          where: { ...where, success: false }
        });
        errorRate = totalEvents > 0 ? (errorEvents / totalEvents) * 100 : 0;
      } catch (error) {
        console.warn('Success field not available:', error);
        errorRate = 0; // Default fallback
      }

      // Calculate average response time (handle missing duration field gracefully)
      let averageResponseTime = 0;
      try {
        const avgDurationResult = await db.auditLog.aggregate({
          where: { ...where, duration: { not: null } },
          _avg: { duration: true }
        });
        averageResponseTime = avgDurationResult._avg.duration || 0;
      } catch (error) {
        console.warn('Duration field not available:', error);
        averageResponseTime = 0; // Default fallback
      }

      // Count security events (handle missing category field gracefully)
      let securityEvents = 0;
      try {
        securityEvents = await db.auditLog.count({
          where: {
            ...where,
            OR: [
              { category: 'AUTHENTICATION' },
              { category: 'AUTHORIZATION' },
              { type: { in: ['LOGIN_FAILED', 'ACCOUNT_LOCKED', 'PERMISSION_DENIED'] } }
            ]
          }
        });
      } catch (error) {
        console.warn('Category field not available for security events:', error);
        // Fallback to counting by type only
        securityEvents = await db.auditLog.count({
          where: {
            ...where,
            type: { in: ['LOGIN_FAILED', 'ACCOUNT_LOCKED', 'PERMISSION_DENIED', 'LOGIN_SUCCESS'] }
          }
        });
      }

      return {
        totalEvents,
        eventsByType,
        eventsBySeverity,
        eventsByCategory,
        topUsers,
        errorRate,
        averageResponseTime,
        securityEvents
      };
    } catch (error) {
      logger.error('Error fetching audit analytics:', error);
      throw new Error(`Failed to fetch audit analytics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Enhanced audit log retrieval with role-based filtering
   */
  private static async getAuditLogsWithRoleFilter(filter: EnhancedAuditLogFilter): Promise<AuditLogResponse> {
    const {
      limit = 10,
      offset = 0,
      page,
      sortBy = "createdAt",
      sortOrder = "desc",
      ...whereFilters
    } = filter;

    const actualOffset = page ? (page - 1) * limit : offset;
    const where = this.buildEnhancedWhereClause(whereFilters);

    return this.executeAuditLogQuery(where, limit, actualOffset, sortBy, sortOrder);
  }

  /**
   * Advanced search with full-text capabilities
   */
  static async searchAuditLogs(
    organizationId: string,
    searchQuery: string,
    filters: Partial<EnhancedAuditLogFilter> = {}
  ): Promise<AuditLogResponse> {
    try {
      const {
        limit = 20,
        offset = 0,
        sortBy = "createdAt",
        sortOrder = "desc"
      } = filters;

      const where: Prisma.AuditLogWhereInput = {
        organizationId,
        AND: [
          {
            OR: [
              { description: { contains: searchQuery, mode: 'insensitive' } },
              { resourceType: { contains: searchQuery, mode: 'insensitive' } },
              { resourceId: { contains: searchQuery, mode: 'insensitive' } },
              { user: { name: { contains: searchQuery, mode: 'insensitive' } } },
              { user: { email: { contains: searchQuery, mode: 'insensitive' } } },
              { tags: { has: searchQuery } }
            ]
          },
          this.buildEnhancedWhereClause(filters)
        ]
      };

      return this.executeAuditLogQuery(where, limit, offset, sortBy, sortOrder);
    } catch (error) {
      logger.error('Error searching audit logs:', error);
      throw new Error(`Failed to search audit logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get audit logs by session for tracking user activity
   */
  static async getAuditLogsBySession(
    sessionId: string,
    organizationId?: string
  ): Promise<AuditLogResponse> {
    try {
      const where: Prisma.AuditLogWhereInput = {
        sessionId,
        ...(organizationId && { organizationId })
      };

      return this.executeAuditLogQuery(where, 100, 0, 'createdAt', 'asc');
    } catch (error) {
      logger.error('Error fetching audit logs by session:', error);
      throw new Error(`Failed to fetch audit logs by session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get security-related audit logs
   */
  static async getSecurityAuditLogs(
    organizationId: string,
    timeRange?: { start: Date; end: Date },
    limit: number = 50
  ): Promise<AuditLogResponse> {
    try {
      const where: Prisma.AuditLogWhereInput = {
        organizationId,
        OR: [
          { category: 'AUTHENTICATION' },
          { category: 'AUTHORIZATION' },
          { severity: { in: ['ERROR', 'CRITICAL'] } },
          { success: false },
          {
            type: {
              in: [
                'LOGIN_FAILED',
                'ACCOUNT_LOCKED',
                'PERMISSION_DENIED',
                'UNAUTHORIZED_ACCESS',
                'SECURITY_POLICY_UPDATED'
              ]
            }
          }
        ],
        ...(timeRange && {
          createdAt: {
            gte: timeRange.start,
            lte: timeRange.end
          }
        })
      };

      return this.executeAuditLogQuery(where, limit, 0, 'createdAt', 'desc');
    } catch (error) {
      logger.error('Error fetching security audit logs:', error);
      throw new Error(`Failed to fetch security audit logs: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build enhanced where clause for audit log queries
   */
  private static buildEnhancedWhereClause(filter: Partial<EnhancedAuditLogFilter>): Prisma.AuditLogWhereInput {
    const {
      userId,
      organizationId,
      spvId,
      projectId,
      resourceType,
      resourceId,
      type,
      types,
      startDate,
      endDate,
      search,
      severity,
      category,
      source,
      success,
      sessionId,
      requestId,
      tags,
      dateRange,
      excludeTypes,
      spvFilter
    } = filter;

    const where: Prisma.AuditLogWhereInput = {
      ...(userId && { userId }),
      ...(organizationId && { organizationId }),
      ...(spvId && { spvId }),
      ...(projectId && { projectId }),
      ...(resourceType && { resourceType }),
      ...(resourceId && { resourceId }),
      ...(type && { type }),
      ...(types && { type: { in: types } }),
      ...(excludeTypes && { type: { notIn: excludeTypes } }),
      ...(severity && { severity }),
      ...(category && category !== 'DATA_ENTRY' && { category }),
      ...(source && { source }),
      ...(success !== undefined && { success }),
      ...(sessionId && { sessionId }),
      ...(requestId && { requestId }),
      ...(tags && tags.length > 0 && { tags: { hasEvery: tags } }),

      // Enhanced date filtering
      ...((startDate || endDate || dateRange) && {
        createdAt: {
          ...(startDate && { gte: startDate }),
          ...(endDate && { lte: endDate }),
          ...(dateRange && { gte: dateRange.start, lte: dateRange.end })
        }
      }),

      // Enhanced search capabilities
      ...(search && {
        OR: [
          { description: { contains: search, mode: "insensitive" } },
          { type: { contains: search, mode: "insensitive" } },
          { resourceType: { contains: search, mode: "insensitive" } },
          { resourceId: { contains: search, mode: "insensitive" } },
          { user: { name: { contains: search, mode: "insensitive" } } },
          { user: { email: { contains: search, mode: "insensitive" } } },
          { tags: { has: search } }
        ]
      }),

      // SPV filter
      ...(spvFilter === 'SPV' && { spvId: { not: null } }),
      ...(spvFilter === 'NON_SPV' && { spvId: null }),

      // Data Entry filter - includes all data entry types
      ...(category === 'DATA_ENTRY' && {
        type: {
          in: [
            'DATA_ENTRY_MANUAL',
            'DATA_ENTRY_CSV',
            'DATA_ENTRY_API',
            'DATA_ENTRY_IOT',
            'DATA_EDITED',
            'DATA_DELETED',
            'DATA_APPROVED',
            'DATA_REJECTED',
            'DATA_VERIFIED',
            'DATA_CORRECTION_REQUESTED',
            'DATA_CORRECTION_APPROVED',
            'DATA_CORRECTION_REJECTED',
            'MONITORING_DATA_CREATED',
            'MONITORING_DATA_UPDATED',
            'MONITORING_DATA_DELETED',
            'MONITORING_DATA_VERIFIED',
            'MONITORING_DATA_REJECTED',
            'DATA_SUBMITTED_FOR_VERIFICATION',
            'DATA_PM_VERIFIED',
            'DATA_PM_REJECTED',
            'DATA_SPV_ADMIN_VERIFIED',
            'DATA_SPV_ADMIN_REJECTED',
            'DATA_SUBMITTED_TO_ORG_ADMIN',
            'DATA_ORG_APPROVED',
            'DATA_ORG_REJECTED',
            'DATA_SUBMITTED_TO_VVB',
            'DATA_VVB_VERIFIED',
            'DATA_VVB_REJECTED',
            'DATA_SENT_BACK_FOR_CORRECTION',
            'DATA_EDITED_AND_RESUBMITTED'
          ]
        }
      })
    };

    return where;
  }

  /**
   * Execute audit log query with pagination
   */
  private static async executeAuditLogQuery(
    where: Prisma.AuditLogWhereInput,
    limit: number,
    offset: number,
    sortBy: string,
    sortOrder: 'asc' | 'desc'
  ): Promise<AuditLogResponse> {
    try {
      const [auditLogs, total] = await Promise.all([
        db.auditLog.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            organization: {
              select: {
                id: true,
                name: true,
              },
            },
            spv: {
              select: {
                id: true,
                name: true,
              },
            },
            project: {
              select: {
                id: true,
                name: true,
              },
            },
          },
          orderBy: { [sortBy]: sortOrder },
          take: limit,
          skip: offset,
        }),
        db.auditLog.count({ where })
      ]);

      return {
        auditLogs,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total,
        },
      };
    } catch (error) {
      logger.error("Error executing audit log query:", error);
      throw new Error(`Failed to get audit logs: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Get a single audit log by ID
   */
  static async getAuditLogById(id: string): Promise<AuditLog | null> {
    try {
      const auditLog = await db.auditLog.findUnique({
        where: { id },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          organization: {
            select: {
              id: true,
              name: true,
            },
          },
          spv: {
            select: {
              id: true,
              name: true,
            },
          },
          project: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      return auditLog;
    } catch (error) {
      logger.error(`Error getting audit log by ID ${id}:`, error);
      throw new Error(`Failed to get audit log: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  /**
   * Export audit logs with enhanced metadata
   * @param filter Enhanced filter options
   * @param format Export format (csv, json)
   * @returns Exported data string
   */
  static async exportAuditLogs(
    filter: EnhancedAuditLogFilter = {},
    format: 'csv' | 'json' = 'csv'
  ): Promise<string> {
    try {
      // Get all audit logs without pagination for export
      const { auditLogs } = await this.getAuditLogsWithRoleFilter({
        ...filter,
        limit: 50000, // Large limit for export
        offset: 0
      });

      if (format === 'json') {
        return JSON.stringify({
          exportedAt: new Date().toISOString(),
          totalRecords: auditLogs.length,
          filters: filter,
          data: auditLogs
        }, null, 2);
      }

      // CSV Export with enhanced fields
      const headers = [
        "ID",
        "Type",
        "Description",
        "User Name",
        "User Email",
        "Organization",
        "SPV",
        "Project",
        "Resource Type",
        "Resource ID",
        "Severity",
        "Category",
        "Source",
        "Success",
        "Duration (ms)",
        "Session ID",
        "Request ID",
        "IP Address",
        "User Agent",
        "Tags",
        "Error Code",
        "Created At",
        "Metadata"
      ];

      // Convert audit logs to CSV rows with enhanced data
      const rows = auditLogs.map(log => [
        log.id,
        log.type,
        `"${log.description.replace(/"/g, '""')}"`, // Escape quotes
        log.user?.name || "",
        log.user?.email || "",
        log.organization?.name || "",
        log.spv?.name || "",
        log.project?.name || "",
        log.resourceType || "",
        log.resourceId || "",
        (log as any).severity || "",
        (log as any).category || "",
        (log as any).source || "",
        (log as any).success !== undefined ? (log as any).success.toString() : "",
        (log as any).duration || "",
        (log as any).sessionId || "",
        (log as any).requestId || "",
        log.ipAddress || "",
        log.userAgent || "",
        (log as any).tags ? (log as any).tags.join(';') : "",
        (log as any).errorCode || "",
        log.createdAt.toISOString(),
        log.metadata ? `"${JSON.stringify(log.metadata).replace(/"/g, '""')}"` : ""
      ]);

      // Combine header and rows
      return [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
    } catch (error) {
      logger.error("Error exporting audit logs:", error);
      throw new Error(`Failed to export audit logs: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
