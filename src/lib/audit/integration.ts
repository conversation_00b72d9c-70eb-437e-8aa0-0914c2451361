import { NextRequest } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { AuthAuditLogger } from './auth-logger';
import { SPVAuditLogger } from './spv-logger';
import { DataAuditLogger } from './data-logger';
import { logger } from '@/lib/logger';

/**
 * Generate a unique request ID for tracking
 */
export function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Extract audit context from request
 */
export async function getAuditContextFromRequest(req: NextRequest) {
  try {
    const session = await auth();
    const requestId = generateRequestId();
    
    if (!session?.user) {
      return {
        ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
        userAgent: req.headers.get('user-agent') || 'unknown',
        requestId
      };
    }

    // Get user details with role information
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        spvUser: {
          include: {
            projectAssignments: {
              where: { isActive: true },
              select: { projectId: true }
            }
          }
        }
      }
    });

    return {
      userId: user?.id,
      organizationId: user?.organizationId || undefined,
      spvId: user?.spvUser?.spvId,
      userRole: user?.role,
      spvRole: user?.spvUser?.role,
      email: user?.email,
      ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
      sessionId: session.user.id,
      requestId
    };
  } catch (error) {
    logger.error('Error getting audit context from request:', error);
    return {
      ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
      requestId: generateRequestId()
    };
  }
}

/**
 * Audit middleware wrapper for API routes
 */
export function withAuditLogging<T extends any[]>(
  handler: (...args: T) => Promise<Response>,
  options?: {
    logRequest?: boolean;
    logResponse?: boolean;
    logErrors?: boolean;
    excludePaths?: string[];
  }
) {
  return async (...args: T): Promise<Response> => {
    const req = args[0] as NextRequest;
    const startTime = Date.now();
    
    // Skip logging for excluded paths
    if (options?.excludePaths?.some(path => req.url.includes(path))) {
      return handler(...args);
    }

    const context = await getAuditContextFromRequest(req);
    
    try {
      // Log request if enabled
      if (options?.logRequest) {
        logger.info('API Request', {
          method: req.method,
          url: req.url,
          userId: context.userId,
          requestId: context.requestId
        });
      }

      const response = await handler(...args);
      const duration = Date.now() - startTime;

      // Log successful response if enabled
      if (options?.logResponse) {
        logger.info('API Response', {
          method: req.method,
          url: req.url,
          status: response.status,
          duration,
          userId: context.userId,
          requestId: context.requestId
        });
      }

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      // Log error if enabled
      if (options?.logErrors) {
        logger.error('API Error', {
          method: req.method,
          url: req.url,
          error: error instanceof Error ? error.message : 'Unknown error',
          duration,
          userId: context.userId,
          requestId: context.requestId
        });
      }

      throw error;
    }
  };
}

/**
 * Audit logging hooks for common operations
 */
export const AuditHooks = {
  // Authentication hooks
  auth: {
    onLoginSuccess: AuthAuditLogger.logLoginSuccess,
    onLoginFailure: AuthAuditLogger.logLoginFailure,
    onLogout: AuthAuditLogger.logLogout,
    onSessionExpired: AuthAuditLogger.logSessionExpired,
    onPasswordChange: AuthAuditLogger.logPasswordChange,
    onPasswordReset: AuthAuditLogger.logPasswordResetRequest,
    onEmailVerification: AuthAuditLogger.logEmailVerification,
    onAccountLocked: AuthAuditLogger.logAccountLocked,
    onAccountUnlocked: AuthAuditLogger.logAccountUnlocked,
    onTwoFactorEnabled: AuthAuditLogger.logTwoFactorEnabled,
    onTwoFactorDisabled: AuthAuditLogger.logTwoFactorDisabled
  },

  // SPV management hooks
  spv: {
    onSPVCreated: SPVAuditLogger.logSPVCreated,
    onSPVUpdated: SPVAuditLogger.logSPVUpdated,
    onSPVDeleted: SPVAuditLogger.logSPVDeleted,
    onSPVVerified: SPVAuditLogger.logSPVVerified,
    onSPVRejected: SPVAuditLogger.logSPVRejected,
    onDocumentUploaded: SPVAuditLogger.logSPVDocumentUploaded,
    onDocumentVerified: SPVAuditLogger.logSPVDocumentVerified,
    onDocumentRejected: SPVAuditLogger.logSPVDocumentRejected,
    onUserCreated: SPVAuditLogger.logSPVUserCreated,
    onUserRoleChanged: SPVAuditLogger.logSPVUserRoleChanged,
    onUserDeleted: SPVAuditLogger.logSPVUserDeleted
  },

  // Data entry hooks
  data: {
    onEntryCreated: DataAuditLogger.logDataEntryCreated,
    onEntryUpdated: DataAuditLogger.logDataEntryUpdated,
    onEntryDeleted: DataAuditLogger.logDataEntryDeleted,
    onEntryVerified: DataAuditLogger.logDataEntryVerified,
    onEntryRejected: DataAuditLogger.logDataEntryRejected,
    onBulkOperation: DataAuditLogger.logBulkDataOperation,
    onDataExport: DataAuditLogger.logDataExport,
    onDataImport: DataAuditLogger.logDataImport
  }
};

/**
 * Utility function to create audit context for server-side operations
 */
export async function createServerAuditContext(userId?: string) {
  if (!userId) {
    return {
      ipAddress: 'server',
      userAgent: 'system',
      requestId: generateRequestId(),
      source: 'SYSTEM'
    };
  }

  try {
    const user = await db.user.findUnique({
      where: { id: userId },
      include: {
        spvUser: true
      }
    });

    return {
      userId: user?.id,
      organizationId: user?.organizationId || undefined,
      spvId: user?.spvUser?.spvId,
      userRole: user?.role,
      spvRole: user?.spvUser?.role,
      email: user?.email,
      ipAddress: 'server',
      userAgent: 'system',
      sessionId: userId,
      requestId: generateRequestId()
    };
  } catch (error) {
    logger.error('Error creating server audit context:', error);
    return {
      ipAddress: 'server',
      userAgent: 'system',
      requestId: generateRequestId(),
      source: 'SYSTEM'
    };
  }
}

/**
 * Batch audit logging for multiple operations
 */
export class BatchAuditLogger {
  private operations: Array<() => Promise<void>> = [];

  add(operation: () => Promise<void>) {
    this.operations.push(operation);
  }

  async flush() {
    try {
      await Promise.allSettled(this.operations.map(op => op()));
      this.operations = [];
    } catch (error) {
      logger.error('Error flushing batch audit logs:', error);
    }
  }

  clear() {
    this.operations = [];
  }
}

/**
 * Audit logging decorator for class methods
 */
export function AuditLog(options: {
  type: string;
  description: string;
  category?: string;
  severity?: string;
}) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      
      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;

        // Log successful operation
        logger.info(`${options.type}: ${options.description}`, {
          method: propertyName,
          duration,
          success: true
        });

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;

        // Log failed operation
        logger.error(`${options.type}: ${options.description} failed`, {
          method: propertyName,
          duration,
          error: error instanceof Error ? error.message : 'Unknown error',
          success: false
        });

        throw error;
      }
    };

    return descriptor;
  };
}

/**
 * Export all audit loggers for easy access
 */
export {
  AuthAuditLogger,
  SPVAuditLogger,
  DataAuditLogger
};
