import { AuditService } from './service';
import { AuditLogType } from '@prisma/client';
import { logger } from '@/lib/logger';

interface AuthAuditContext {
  userId?: string;
  organizationId?: string;
  email?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
}

/**
 * Authentication audit logging utilities
 */
export class AuthAuditLogger {
  /**
   * Log successful login
   */
  static async logLoginSuccess(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.LOGIN_SUCCESS,
        description: `User successfully logged in: ${context.email}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          loginMethod: 'credentials',
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging login success:', error);
    }
  }

  /**
   * Log failed login attempt
   */
  static async logLoginFailure(context: AuthAuditContext, reason: string) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.LOGIN_FAILED,
        description: `Failed login attempt for: ${context.email} - ${reason}`,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.email || 'unknown',
        metadata: {
          email: context.email,
          failureReason: reason,
          loginMethod: 'credentials',
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: false,
        errorCode: 'AUTH_FAILED'
      });
    } catch (error) {
      logger.error('Error logging login failure:', error);
    }
  }

  /**
   * Log logout
   */
  static async logLogout(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.LOGOUT_SUCCESS,
        description: `User logged out: ${context.email}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging logout:', error);
    }
  }

  /**
   * Log session expiration
   */
  static async logSessionExpired(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.SESSION_EXPIRED,
        description: `User session expired: ${context.email}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'SYSTEM',
        success: true
      });
    } catch (error) {
      logger.error('Error logging session expiration:', error);
    }
  }

  /**
   * Log password change
   */
  static async logPasswordChange(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.PASSWORD_CHANGED,
        description: `User changed password: ${context.email}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging password change:', error);
    }
  }

  /**
   * Log password reset request
   */
  static async logPasswordResetRequest(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.PASSWORD_RESET,
        description: `Password reset requested for: ${context.email}`,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.email || 'unknown',
        metadata: {
          email: context.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging password reset request:', error);
    }
  }

  /**
   * Log email verification
   */
  static async logEmailVerification(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.EMAIL_VERIFIED,
        description: `Email verified for user: ${context.email}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging email verification:', error);
    }
  }

  /**
   * Log account lockout
   */
  static async logAccountLocked(context: AuthAuditContext, reason: string) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.ACCOUNT_LOCKED,
        description: `Account locked for user: ${context.email} - ${reason}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          lockReason: reason,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'ERROR',
        category: 'AUTHENTICATION',
        source: 'SYSTEM',
        success: false,
        errorCode: 'ACCOUNT_LOCKED'
      });
    } catch (error) {
      logger.error('Error logging account lockout:', error);
    }
  }

  /**
   * Log account unlock
   */
  static async logAccountUnlocked(context: AuthAuditContext, unlockedBy: string) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.ACCOUNT_UNLOCKED,
        description: `Account unlocked for user: ${context.email} by ${unlockedBy}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          unlockedBy,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging account unlock:', error);
    }
  }

  /**
   * Log two-factor authentication events
   */
  static async logTwoFactorEnabled(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.TWO_FACTOR_ENABLED,
        description: `Two-factor authentication enabled for user: ${context.email}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'INFO',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging 2FA enabled:', error);
    }
  }

  static async logTwoFactorDisabled(context: AuthAuditContext) {
    try {
      await AuditService.createAuditLog({
        type: AuditLogType.TWO_FACTOR_DISABLED,
        description: `Two-factor authentication disabled for user: ${context.email}`,
        userId: context.userId,
        organizationId: context.organizationId,
        resourceType: 'authentication',
        resourceId: context.userId || 'unknown',
        metadata: {
          email: context.email,
          timestamp: new Date().toISOString()
        },
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        sessionId: context.sessionId,
        requestId: context.requestId,
        severity: 'WARN',
        category: 'AUTHENTICATION',
        source: 'WEB',
        success: true
      });
    } catch (error) {
      logger.error('Error logging 2FA disabled:', error);
    }
  }
}
