import { db } from '@/lib/db';
import { logger } from '@/lib/logger';
import { AuditLogType } from '@prisma/client';

/**
 * Performance optimization utilities for audit logging
 */
export class AuditPerformanceOptimizer {
  private static readonly BATCH_SIZE = 100;
  private static readonly RETENTION_DAYS = 365;
  private static readonly ARCHIVE_DAYS = 90;

  /**
   * Clean up old audit logs based on retention policy
   */
  static async cleanupOldLogs(retentionDays: number = this.RETENTION_DAYS): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      const result = await db.auditLog.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate
          },
          // Keep critical security events longer
          NOT: {
            severity: {
              in: ['CRITICAL', 'ERROR']
            }
          }
        }
      });

      logger.info(`Cleaned up ${result.count} old audit logs older than ${retentionDays} days`);
      return result.count;
    } catch (error) {
      logger.error('Error cleaning up old audit logs:', error);
      throw error;
    }
  }

  /**
   * Archive old audit logs to separate table or storage
   */
  static async archiveOldLogs(archiveDays: number = this.ARCHIVE_DAYS): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - archiveDays);

      // Get logs to archive
      const logsToArchive = await db.auditLog.findMany({
        where: {
          createdAt: {
            lt: cutoffDate
          }
        },
        take: this.BATCH_SIZE
      });

      if (logsToArchive.length === 0) {
        return 0;
      }

      // In a real implementation, you would move these to an archive table or external storage
      // For now, we'll just mark them as archived in metadata
      const archivePromises = logsToArchive.map(log =>
        db.auditLog.update({
          where: { id: log.id },
          data: {
            metadata: {
              ...(log.metadata as any),
              archived: true,
              archivedAt: new Date().toISOString()
            }
          }
        })
      );

      await Promise.all(archivePromises);

      logger.info(`Archived ${logsToArchive.length} audit logs`);
      return logsToArchive.length;
    } catch (error) {
      logger.error('Error archiving audit logs:', error);
      throw error;
    }
  }

  /**
   * Optimize database indexes for better query performance
   */
  static async optimizeIndexes(): Promise<void> {
    try {
      // This would typically be done via migrations, but we can analyze query performance
      const indexAnalysis = await db.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          indexname,
          idx_tup_read,
          idx_tup_fetch
        FROM pg_stat_user_indexes 
        WHERE tablename = 'AuditLog'
        ORDER BY idx_tup_read DESC;
      `;

      logger.info('Audit log index analysis:', indexAnalysis);
    } catch (error) {
      logger.error('Error analyzing indexes:', error);
    }
  }

  /**
   * Get audit log statistics for monitoring
   */
  static async getStatistics(): Promise<{
    totalLogs: number;
    logsLast24h: number;
    logsLast7d: number;
    errorRate: number;
    topEventTypes: Array<{ type: string; count: number }>;
    averageResponseTime: number;
    databaseSize: string;
  }> {
    try {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

      const [
        totalLogs,
        logsLast24h,
        logsLast7d,
        errorLogs,
        topEventTypes,
        avgDuration
      ] = await Promise.all([
        db.auditLog.count(),
        db.auditLog.count({
          where: { createdAt: { gte: yesterday } }
        }),
        db.auditLog.count({
          where: { createdAt: { gte: weekAgo } }
        }),
        db.auditLog.count({
          where: { success: false }
        }),
        db.auditLog.groupBy({
          by: ['type'],
          _count: { type: true },
          orderBy: { _count: { type: 'desc' } },
          take: 10
        }),
        db.auditLog.aggregate({
          _avg: { duration: true },
          where: { duration: { not: null } }
        })
      ]);

      const errorRate = totalLogs > 0 ? (errorLogs / totalLogs) * 100 : 0;
      const averageResponseTime = avgDuration._avg.duration || 0;

      // Get database size (PostgreSQL specific)
      let databaseSize = 'Unknown';
      try {
        const sizeResult = await db.$queryRaw<Array<{ size: string }>>`
          SELECT pg_size_pretty(pg_total_relation_size('AuditLog')) as size;
        `;
        databaseSize = sizeResult[0]?.size || 'Unknown';
      } catch (error) {
        logger.warn('Could not get database size:', error);
      }

      return {
        totalLogs,
        logsLast24h,
        logsLast7d,
        errorRate,
        topEventTypes: topEventTypes.map(item => ({
          type: item.type,
          count: item._count.type
        })),
        averageResponseTime,
        databaseSize
      };
    } catch (error) {
      logger.error('Error getting audit statistics:', error);
      throw error;
    }
  }

  /**
   * Monitor audit log performance and alert on issues
   */
  static async monitorPerformance(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    issues: string[];
    recommendations: string[];
  }> {
    try {
      const stats = await this.getStatistics();
      const issues: string[] = [];
      const recommendations: string[] = [];
      let status: 'healthy' | 'warning' | 'critical' = 'healthy';

      // Check error rate
      if (stats.errorRate > 10) {
        issues.push(`High error rate: ${stats.errorRate.toFixed(1)}%`);
        status = 'critical';
        recommendations.push('Investigate failed audit log operations');
      } else if (stats.errorRate > 5) {
        issues.push(`Elevated error rate: ${stats.errorRate.toFixed(1)}%`);
        status = 'warning';
      }

      // Check response time
      if (stats.averageResponseTime > 1000) {
        issues.push(`Slow response time: ${stats.averageResponseTime.toFixed(0)}ms`);
        status = status === 'critical' ? 'critical' : 'warning';
        recommendations.push('Consider optimizing database queries or adding indexes');
      }

      // Check growth rate
      const dailyGrowthRate = stats.logsLast24h;
      if (dailyGrowthRate > 10000) {
        issues.push(`High log volume: ${dailyGrowthRate} logs in 24h`);
        status = status === 'critical' ? 'critical' : 'warning';
        recommendations.push('Consider implementing log retention policies');
      }

      // Check database size
      if (stats.databaseSize.includes('GB')) {
        const sizeMatch = stats.databaseSize.match(/(\d+(?:\.\d+)?)\s*GB/);
        if (sizeMatch && parseFloat(sizeMatch[1]) > 5) {
          issues.push(`Large database size: ${stats.databaseSize}`);
          recommendations.push('Consider archiving old audit logs');
        }
      }

      return {
        status,
        issues,
        recommendations
      };
    } catch (error) {
      logger.error('Error monitoring audit performance:', error);
      return {
        status: 'critical',
        issues: ['Failed to monitor audit performance'],
        recommendations: ['Check audit system health']
      };
    }
  }

  /**
   * Batch insert audit logs for better performance
   */
  static async batchInsertLogs(logs: Array<{
    type: AuditLogType;
    description: string;
    userId?: string;
    organizationId?: string;
    spvId?: string;
    projectId?: string;
    resourceType?: string;
    resourceId?: string;
    metadata?: any;
    ipAddress?: string;
    userAgent?: string;
    severity?: string;
    category?: string;
    source?: string;
    success?: boolean;
    duration?: number;
    sessionId?: string;
    requestId?: string;
  }>): Promise<number> {
    try {
      const result = await db.auditLog.createMany({
        data: logs.map(log => ({
          ...log,
          createdAt: new Date()
        })),
        skipDuplicates: true
      });

      logger.info(`Batch inserted ${result.count} audit logs`);
      return result.count;
    } catch (error) {
      logger.error('Error batch inserting audit logs:', error);
      throw error;
    }
  }

  /**
   * Create maintenance job for regular cleanup
   */
  static async runMaintenanceJob(): Promise<void> {
    try {
      logger.info('Starting audit log maintenance job');

      // Archive old logs
      const archivedCount = await this.archiveOldLogs();
      
      // Clean up very old logs
      const cleanedCount = await this.cleanupOldLogs();
      
      // Get performance stats
      const performance = await this.monitorPerformance();
      
      logger.info('Audit log maintenance completed', {
        archivedLogs: archivedCount,
        cleanedLogs: cleanedCount,
        performanceStatus: performance.status,
        issues: performance.issues
      });

      // Alert if there are critical issues
      if (performance.status === 'critical') {
        logger.error('Critical audit system issues detected', {
          issues: performance.issues,
          recommendations: performance.recommendations
        });
      }
    } catch (error) {
      logger.error('Error running audit maintenance job:', error);
      throw error;
    }
  }
}
