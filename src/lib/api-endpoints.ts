/**
 * Centralized API Endpoints Configuration
 * 
 * This file contains all API endpoints used throughout the application.
 * All API calls should use these constants instead of hardcoded URLs.
 */

// Base URL configuration with better fallback handling
const getBaseUrl = (): string => {
  // In browser environment, check for public API URL first
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL || window.location.origin;
  }

  // In server environment, use configured URLs
  return process.env.NEXT_PUBLIC_API_URL || process.env.NEXTAUTH_URL || '';
};

const BASE_URL = getBaseUrl();

// Helper function to build API URLs
const buildApiUrl = (endpoint: string): string => {
  // Always use relative URLs for same-origin requests in browser
  if (typeof window !== 'undefined' && (!BASE_URL || BASE_URL === window.location.origin)) {
    return `/api${endpoint}`;
  }

  // For server-side or different origins, use full URL
  if (!BASE_URL) {
    return `/api${endpoint}`;
  }

  return `${BASE_URL}/api${endpoint}`;
};

// Helper function to build API URLs with dynamic parameters
const buildApiUrlWithParams = (endpoint: string) => (params: Record<string, string | number>): string => {
  let url = endpoint;
  Object.entries(params).forEach(([key, value]) => {
    url = url.replace(`{${key}}`, String(value));
  });
  return buildApiUrl(url);
};

// Authentication & User Management
const AUTH_ENDPOINTS = {
  // NextAuth endpoints
  NEXTAUTH: buildApiUrl('/auth/[...nextauth]'),
  
  // User registration and verification
  REGISTER: buildApiUrl('/register'),
  VERIFY_EMAIL: buildApiUrl('/verify-email'),
  RESET_PASSWORD: buildApiUrl('/auth/reset-password'),
  
  // User profile
  ME: buildApiUrl('/me'),
  USER_PROFILE: buildApiUrl('/user/profile'),
  USER_CHANGE_PASSWORD: buildApiUrl('/user/change-password'),
  USER_ORGANIZATION: buildApiUrl('/user/organization'),
  USER_NOTIFICATION_PREFERENCES: buildApiUrl('/user/notification-preferences'),
  
  // Onboarding
  ONBOARDING: buildApiUrl('/onboarding'),
} as const;

// Organizations
const ORGANIZATION_ENDPOINTS = {
  // Organization CRUD
  ORGANIZATIONS: buildApiUrl('/organizations'),
  ORGANIZATION_BY_ID: buildApiUrlWithParams('/organizations/{id}'),
  ORGANIZATION_DRAFT: buildApiUrl('/organizations/draft'),
  ORGANIZATION_VERIFICATION: buildApiUrl('/organizations/verification'),
  ORGANIZATION_VERIFICATION_STATUS: buildApiUrl('/organizations/verification/status'),
  ORGANIZATION_VERIFICATION_DOCUMENTS: buildApiUrl('/organizations/verification/documents'),
  ORGANIZATION_VERIFY: buildApiUrl('/organizations/verify'),

  // SPVs
  ORGANIZATION_SPVS: buildApiUrl('/organizations/spvs'),

  // Brokers
  ORGANIZATION_BROKERS: buildApiUrl('/organizations/brokers'),

  // Team management
  ORGANIZATION_TEAM: buildApiUrl('/organizations/team'),
  ORGANIZATION_ANALYTICS: buildApiUrl('/organization/analytics'),
  ORGANIZATION_AUDIT_LOGS: buildApiUrl('/organization/audit-logs'),
  ORGANIZATION_TEAM_MANAGEMENT: buildApiUrl('/organization/team'),
} as const;

// Projects
  const PROJECT_ENDPOINTS = {
  // Project CRUD
  PROJECTS: buildApiUrl('/projects'),
  PROJECT_BY_ID: buildApiUrlWithParams('/projects/{id}'),
  
  // Project monitoring and data
  PROJECT_UNIT_LOGS: buildApiUrlWithParams('/projects/{id}/unit-logs'),
  PROJECT_UNIT_LOGS_EXTERNAL: buildApiUrlWithParams('/projects/{id}/unit-logs/external'),
  PROJECT_CARBON_CREDITS_ESTIMATE: buildApiUrlWithParams('/projects/{id}/carbon-credits/estimate'),
  
  // API integrations
  PROJECT_API_INTEGRATIONS: buildApiUrlWithParams('/projects/{id}/api-integrations'),
  PROJECT_INTEGRATIONS: buildApiUrlWithParams('/projects/{id}/integrations'),
} as const;

// Carbon Credits
  const CARBON_CREDIT_ENDPOINTS = {
  // Carbon credit CRUD
  CARBON_CREDITS: buildApiUrl('/carbon-credits'),
  CARBON_CREDIT_BY_ID: buildApiUrlWithParams('/carbon-credits/{id}'),
  CARBON_CREDIT_CREATE: buildApiUrl('/carbon-credits/create'),
  CARBON_CREDIT_ORGANIZATION: buildApiUrl('/carbon-credits/organization'),
  
  // Batch operations
  CARBON_CREDITS_BATCH: buildApiUrl('/carbon-credits/batch'),
  CARBON_CREDITS_BULK_IMPORT: buildApiUrl('/carbon-credits/bulk-import'),
  
  // Tokenization and transactions
  CARBON_CREDIT_TOKENIZE: buildApiUrlWithParams('/carbon-credits/{id}/tokenize'),
  CARBON_CREDIT_TRANSFER: buildApiUrlWithParams('/carbon-credits/{id}/transfer'),
  CARBON_CREDIT_RETIRE: buildApiUrlWithParams('/carbon-credits/{id}/retire'),
  CARBON_CREDIT_TRANSACTIONS: buildApiUrlWithParams('/carbon-credits/{id}/transactions'),
} as const;

// Marketplace
const MARKETPLACE_ENDPOINTS = {
  // Marketplace listings
  MARKETPLACE_LISTINGS: buildApiUrl('/marketplace/listings'),
  MARKETPLACE_LISTING_BY_ID: buildApiUrlWithParams('/marketplace/listings/{id}'),

  // Watchlist
  MARKETPLACE_WATCHLIST: buildApiUrl('/marketplace/watchlist'),
  MARKETPLACE_WATCHLIST_ITEMS: buildApiUrl('/marketplace/watchlist/items'),
  MARKETPLACE_WATCHLIST_ITEM_BY_ID: buildApiUrlWithParams('/marketplace/watchlist/items/{id}'),
  MARKETPLACE_WATCHLIST_ALERTS: buildApiUrl('/marketplace/watchlist/alerts'),

  // Preferences
  MARKETPLACE_PREFERENCES: buildApiUrl('/marketplace/preferences'),
} as const;

// Orders
const ORDER_ENDPOINTS = {
  // Order management
  ORDERS: buildApiUrl('/orders'),
  ORDER_BY_ID: buildApiUrlWithParams('/orders/{id}'),
  MY_ORDERS: buildApiUrl('/orders/my-orders'),
  ORDER_MATCH: buildApiUrl('/orders/match'),
} as const;

// Wallet & Blockchain
const WALLET_ENDPOINTS = {
  // Wallet management
  WALLET: buildApiUrl('/wallet'),
  WALLET_BY_ID: buildApiUrlWithParams('/wallet/{id}'),
  WALLET_CREATE: buildApiUrl('/wallet/create'),
  WALLET_LIST: buildApiUrl('/wallet/list'),
  WALLET_ALL: buildApiUrl('/wallet/all'),

  // Smart wallet
  WALLET_SMART_WALLET: buildApiUrl('/wallet/smart-wallet'),

  // Wallet operations
  WALLET_SEND: buildApiUrl('/wallet/send'),
  WALLET_SEND_TOKENS: buildApiUrl('/wallet/send-tokens'),
  WALLET_PORTFOLIO: buildApiUrl('/wallet/portfolio'),
  WALLET_TRANSACTIONS: buildApiUrl('/wallet/transactions'),

  // Cross-chain and multi-chain
  WALLET_CROSS_CHAIN: buildApiUrl('/wallet/cross-chain'),
  WALLET_MULTI_CHAIN: buildApiUrl('/wallet/multi-chain'),
  WALLET_BRIDGE: buildApiUrl('/wallet/bridge'),
  WALLET_BRIDGE_TRANSACTIONS: buildApiUrl('/wallet/bridge/transactions'),

  // Network-specific endpoints
  WALLET_NETWORK_ETHEREUM: buildApiUrl('/wallet/network/ethereum'),
  WALLET_NETWORK_POLYGON: buildApiUrl('/wallet/network/polygon'),

  // Gas and security
  WALLET_GAS_ESTIMATE: buildApiUrl('/wallet/gas-estimate'),
  WALLET_GAS_SETTINGS: buildApiUrl('/wallet/gas-settings'),
  WALLET_SECURITY: buildApiUrl('/wallet/security'),
  WALLET_ACCESS: buildApiUrl('/wallet/access'),
  WALLET_AUDIT: buildApiUrl('/wallet/audit'),
  WALLET_TEST: buildApiUrl('/wallet/test'),

  // QR Code generation
  WALLET_QR_CODE: buildApiUrlWithParams('/wallet/{id}/qr-code'),

  // Gas prices
  WALLET_GAS_PRICES: buildApiUrl('/wallet/gas-prices'),
} as const;

// Blockchain
  const BLOCKCHAIN_ENDPOINTS = {
  // Transaction status
  BLOCKCHAIN_TRANSACTION_STATUS: buildApiUrl('/blockchain/transaction-status'),
  
  // Gas estimation
  GAS_ESTIMATION_TOKENIZE: buildApiUrl('/gas-estimation/tokenize'),
  GAS_ESTIMATION_TRANSFER: buildApiUrl('/gas-estimation/transfer'),
  GAS_ESTIMATION_RETIRE: buildApiUrl('/gas-estimation/retire'),
} as const;

// Notifications
const NOTIFICATION_ENDPOINTS = {
  // Notification management
  NOTIFICATIONS: buildApiUrl('/notifications'),
  NOTIFICATION_BY_ID: buildApiUrlWithParams('/notifications/{id}'),
  NOTIFICATIONS_READ_ALL: buildApiUrl('/notifications/read-all'),
  NOTIFICATIONS_ANNOUNCEMENTS: buildApiUrl('/notifications/announcements'),
  NOTIFICATIONS_ORGANIZATION: buildApiUrl('/notifications/organization'),
  NOTIFICATIONS_PREFERENCES: buildApiUrl('/notifications/preferences'),
} as const;

// Analytics
  const ANALYTICS_ENDPOINTS = {
  // Platform analytics
  ANALYTICS_PLATFORM: buildApiUrl('/analytics/platform'),
  ANALYTICS_USER: buildApiUrl('/analytics/user'),
  ANALYTICS_ORGANIZATION: buildApiUrl('/analytics/organization'),
  ANALYTICS_MARKET: buildApiUrl('/analytics/market'),
  ANALYTICS_FINANCIAL: buildApiUrl('/analytics/financial'),
  ANALYTICS_ASSET_VALUATION: buildApiUrl('/analytics/asset-valuation'),
  ANALYTICS_TRANSACTION_AUDIT: buildApiUrl('/analytics/transaction-audit'),
  ANALYTICS_AUDIT: buildApiUrl('/analytics/audit'),
  
  // Trading analytics
  ANALYTICS_TRADING_METRICS: buildApiUrl('/analytics/trading/metrics'),
  ANALYTICS_TRADING_ORDERS: buildApiUrl('/analytics/trading/orders'),
  ANALYTICS_TRANSACTIONS: buildApiUrl('/analytics/transactions'),
} as const;

// Admin
const ADMIN_ENDPOINTS = {
  // Admin dashboard
  ADMIN_STATS: buildApiUrl('/admin/stats'),
  ADMIN_USERS: buildApiUrl('/admin/users'),
  ADMIN_ORGANIZATIONS: buildApiUrl('/admin/organizations'),
  ADMIN_PROJECTS: buildApiUrl('/admin/projects'),
  ADMIN_DOCUMENTS: buildApiUrl('/admin/documents'),
  ADMIN_SETTINGS: buildApiUrl('/admin/settings'),
  ADMIN_SETTINGS_FEES: buildApiUrl('/admin/settings/fees'),
  ADMIN_SETTINGS_FEES_TOKENIZATION: buildApiUrl('/admin/settings/fees/tokenization'),
  ADMIN_SETTINGS_FEES_TRANSFER: buildApiUrl('/admin/settings/fees/transfer'),
  ADMIN_SETTINGS_FEES_RETIREMENT: buildApiUrl('/admin/settings/fees/retirement'),

  // Admin SPV management
  ADMIN_SPVS: buildApiUrl('/admin/spv'),
  ADMIN_SPV_BY_ID: buildApiUrlWithParams('/admin/spv/{id}'),

  // Admin Broker management
  ADMIN_BROKERS: buildApiUrl('/admin/brokers'),
  ADMIN_BROKER_BY_ID: buildApiUrlWithParams('/admin/brokers/{id}'),
} as const;

// Compliance
const COMPLIANCE_ENDPOINTS = {
  // KYC/AML
  COMPLIANCE_KYC: buildApiUrl('/compliance/kyc'),
  COMPLIANCE_KYC_VERIFY: buildApiUrl('/compliance/kyc/verify'),
  COMPLIANCE_KYC_STATUS: buildApiUrl('/compliance/kyc/status'),
  COMPLIANCE_AML: buildApiUrl('/compliance/aml'),
  COMPLIANCE_AML_STATUS: buildApiUrl('/compliance/aml/status'),
  COMPLIANCE_AUDIT_LOGS: buildApiUrl('/compliance/audit-logs'),
  COMPLIANCE_REPORTING: buildApiUrl('/compliance/reporting'),
  COMPLIANCE_RISK_ASSESSMENT: buildApiUrl('/compliance/risk-assessment'),
  COMPLIANCE_CARBON_VERIFICATION: buildApiUrl('/compliance/carbon-verification'),
  COMPLIANCE_STATUS: buildApiUrl('/compliance/status'),
  COMPLIANCE_DOCUMENTS: buildApiUrl('/compliance/documents'),
  COMPLIANCE_ACTIVITIES: buildApiUrl('/compliance/activities'),
} as const;

// Verifications
  const VERIFICATION_ENDPOINTS = {
  VERIFICATIONS: buildApiUrl('/verifications'),
  VERIFICATION_BY_ID: buildApiUrlWithParams('/verifications/{id}'),
  VERIFICATIONS_PENDING: buildApiUrl('/verifications/pending'),
} as const;

// Payments & Subscriptions
  const PAYMENT_ENDPOINTS = {
  // Payment methods
  PAYMENT_METHODS: buildApiUrl('/payments/methods'),
  PAYMENT_PROCESS: buildApiUrl('/payments/process'),
  PAYMENT_HISTORY: buildApiUrl('/payments/history'),
  
  // Subscriptions
  SUBSCRIPTIONS: buildApiUrl('/subscriptions'),
  SUBSCRIPTION_BY_ID: buildApiUrlWithParams('/subscriptions/{id}'),
} as const;

// Tax Reports
  const TAX_REPORT_ENDPOINTS = {
  TAX_REPORTS: buildApiUrl('/tax-reports'),
  TAX_REPORT_BY_ID: buildApiUrlWithParams('/tax-reports/{id}'),
  TAX_REPORT_GENERATE: buildApiUrl('/tax-reports/generate'),
} as const;

// RBAC (Role-Based Access Control)
  const RBAC_ENDPOINTS = {
  RBAC_CHECK_PERMISSION: buildApiUrl('/rbac/check-permission'),
  RBAC_USER_PERMISSIONS: buildApiUrl('/rbac/user-permissions'),
  RBAC_USER_ROLES: buildApiUrl('/rbac/user-roles'),
} as const;

// Invitations
  const INVITATION_ENDPOINTS = {
  INVITATION_ACCEPT: buildApiUrl('/invitation/accept'),
  INVITATION_VERIFY: buildApiUrl('/invitation/verify'),
} as const;

// Dashboard
  const DASHBOARD_ENDPOINTS = {
  DASHBOARD_STATS: buildApiUrl('/dashboard/stats'),
} as const;

// Broker
const BROKER_ENDPOINTS = {
  // Broker profile and authentication
  BROKER_PROFILE: buildApiUrl('/broker/profile'),
  BROKER_ONBOARDING: buildApiUrl('/broker/onboarding'),

  // Broker dashboard and stats
  BROKER_DASHBOARD_STATS: buildApiUrl('/broker/dashboard/stats'),

  // Broker client management
  BROKER_CLIENTS: buildApiUrl('/broker/clients'),
  BROKER_CLIENT_BY_ID: buildApiUrlWithParams('/broker/clients/{id}'),
  BROKER_CLIENT_INVITE: buildApiUrl('/broker/clients/invite'),

  // Broker transaction management
  BROKER_TRANSACTIONS: buildApiUrl('/broker/transactions'),
  BROKER_TRANSACTION_BY_ID: buildApiUrlWithParams('/broker/transactions/{id}'),

  // Broker commission management
  BROKER_COMMISSIONS: buildApiUrl('/broker/commissions'),
  BROKER_COMMISSION_BY_ID: buildApiUrlWithParams('/broker/commissions/{id}'),
  BROKER_EARNINGS: buildApiUrl('/broker/earnings'),

  // Broker document management
  BROKER_DOCUMENTS: buildApiUrl('/broker/documents'),
  BROKER_DOCUMENT_BY_ID: buildApiUrlWithParams('/broker/documents/{id}'),
  BROKER_KYC_DOCUMENTS: buildApiUrl('/broker/kyc-documents'),
  BROKER_KYC_STATUS: buildApiUrl('/broker/kyc-status'),

  // Broker settings
  BROKER_SETTINGS: buildApiUrl('/broker/settings'),
  BROKER_OPERATING_MODEL: buildApiUrl('/broker/operating-model'),
} as const;

// SPV Portal
const SPV_ENDPOINTS = {
  // SPV management
  SPV_DASHBOARD: buildApiUrl('/spv/dashboard'),
  SPV_PROJECTS: buildApiUrl('/spv/projects'),
  SPV_PROJECT_BY_ID: buildApiUrlWithParams('/spv/projects/{id}'),
  SPV_PROJECT_UNIT_LOGS: buildApiUrlWithParams('/spv/projects/{id}/unit-logs'),
  SPV_VERIFICATION_QUEUE: buildApiUrl('/spv/verification'),
  SPV_UNIT_LOGS: buildApiUrl('/spv/unit-logs'),
  SPV_UNIT_LOG_BY_ID: buildApiUrlWithParams('/spv/unit-logs/{id}'),
  SPV_UNIT_LOGS_BULK: buildApiUrl('/spv/unit-logs/bulk'),
  SPV_UNIT_LOG_VERIFY: buildApiUrlWithParams('/spv/unit-logs/{id}/verify'),
  SPV_UNIT_LOG_SUBMIT: buildApiUrlWithParams('/spv/unit-logs/{id}/submit'),
  SPV_PERMISSIONS: buildApiUrl('/spv/permissions'),
  SPV_PROJECT_AUDIT: buildApiUrlWithParams('/spv/projects/{id}/audit'),
} as const;

// Utility endpoints
  const UTILITY_ENDPOINTS = {
  HEALTH: buildApiUrl('/health'),
  TEST_ENV: buildApiUrl('/test-env'),
  TEST_UNIT_LOGS: buildApiUrl('/test-unit-logs'),
  UPLOAD: buildApiUrl('/upload'),
} as const;

// Export all endpoints as a single object for convenience
export const API_ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  ORGANIZATION: ORGANIZATION_ENDPOINTS,
  PROJECT: PROJECT_ENDPOINTS,
  CARBON_CREDIT: CARBON_CREDIT_ENDPOINTS,
  MARKETPLACE: MARKETPLACE_ENDPOINTS,
  ORDER: ORDER_ENDPOINTS,
  WALLET: WALLET_ENDPOINTS,
  BLOCKCHAIN: BLOCKCHAIN_ENDPOINTS,
  NOTIFICATION: NOTIFICATION_ENDPOINTS,
  ANALYTICS: ANALYTICS_ENDPOINTS,
  ADMIN: ADMIN_ENDPOINTS,
  COMPLIANCE: COMPLIANCE_ENDPOINTS,
  VERIFICATION: VERIFICATION_ENDPOINTS,
  PAYMENT: PAYMENT_ENDPOINTS,
  TAX_REPORT: TAX_REPORT_ENDPOINTS,
  RBAC: RBAC_ENDPOINTS,
  INVITATION: INVITATION_ENDPOINTS,
  DASHBOARD: DASHBOARD_ENDPOINTS,
  BROKER: BROKER_ENDPOINTS,
  SPV: SPV_ENDPOINTS,
  UTILITY: UTILITY_ENDPOINTS,
} as const;

// Export individual endpoint groups for direct import
export {
  AUTH_ENDPOINTS,
  ORGANIZATION_ENDPOINTS,
  PROJECT_ENDPOINTS,
  CARBON_CREDIT_ENDPOINTS,
  MARKETPLACE_ENDPOINTS,
  ORDER_ENDPOINTS,
  WALLET_ENDPOINTS,
  BLOCKCHAIN_ENDPOINTS,
  NOTIFICATION_ENDPOINTS,
  ANALYTICS_ENDPOINTS,
  ADMIN_ENDPOINTS,
  COMPLIANCE_ENDPOINTS,
  VERIFICATION_ENDPOINTS,
  PAYMENT_ENDPOINTS,
  TAX_REPORT_ENDPOINTS,
  RBAC_ENDPOINTS,
  INVITATION_ENDPOINTS,
  DASHBOARD_ENDPOINTS,
  BROKER_ENDPOINTS,
  SPV_ENDPOINTS,
  UTILITY_ENDPOINTS,
};
