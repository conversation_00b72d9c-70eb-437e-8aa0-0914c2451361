/**
 * Password generation utilities for automatic user creation
 */

/**
 * Generate a secure random password
 * @param length Password length (default: 12)
 * @param includeSymbols Whether to include symbols (default: true)
 * @returns Generated password
 */
export function generatePassword(length: number = 12, includeSymbols: boolean = true): string {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';
  
  let charset = lowercase + uppercase + numbers;
  if (includeSymbols) {
    charset += symbols;
  }
  
  let password = '';
  
  // Ensure at least one character from each required set
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  
  if (includeSymbols) {
    password += symbols[Math.floor(Math.random() * symbols.length)];
  }
  
  // Fill the rest with random characters
  const remainingLength = length - password.length;
  for (let i = 0; i < remainingLength; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }
  
  // Shuffle the password to avoid predictable patterns
  return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Generate SPV admin credentials
 * @param spvName SPV name for email generation
 * @param organizationName Organization name for context
 * @returns Object with email and password
 */
export function generateSPVAdminCredentials(spvName: string, organizationName: string) {
  // Create a clean email-friendly version of the SPV name
  const cleanSPVName = spvName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .substring(0, 20); // Limit length
  
  // Generate a unique suffix to avoid conflicts
  const timestamp = Date.now().toString().slice(-4);
  
  const email = `${cleanSPVName}-admin-${timestamp}@spv.carbonx.com`;
  const password = generatePassword(12, true);
  
  return {
    email,
    password,
    displayName: `${spvName} Administrator`
  };
}

/**
 * Validate password strength
 * @param password Password to validate
 * @returns Object with validation result and feedback
 */
export function validatePasswordStrength(password: string) {
  const minLength = 8;
  const hasLowercase = /[a-z]/.test(password);
  const hasUppercase = /[A-Z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSymbols = /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password);
  
  const isValid = password.length >= minLength && hasLowercase && hasUppercase && hasNumbers;
  
  const feedback = [];
  if (password.length < minLength) {
    feedback.push(`Password must be at least ${minLength} characters long`);
  }
  if (!hasLowercase) {
    feedback.push('Password must contain at least one lowercase letter');
  }
  if (!hasUppercase) {
    feedback.push('Password must contain at least one uppercase letter');
  }
  if (!hasNumbers) {
    feedback.push('Password must contain at least one number');
  }
  
  let strength = 'weak';
  if (isValid) {
    if (hasSymbols && password.length >= 12) {
      strength = 'strong';
    } else {
      strength = 'medium';
    }
  }
  
  return {
    isValid,
    strength,
    feedback
  };
}
