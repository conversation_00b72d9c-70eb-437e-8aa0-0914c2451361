import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { AuditService } from '@/lib/audit/service';
import { AuditLogType } from '@prisma/client';
import { logger } from '@/lib/logger';
import { db } from '@/lib/db';

interface AuditContext {
  userId?: string;
  organizationId?: string;
  spvId?: string;
  projectId?: string;
  role?: string;
  spvRole?: string;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  requestId?: string;
  permissions?: string[];
}

interface ApiAuditOptions {
  logRequest?: boolean;
  logResponse?: boolean;
  logErrors?: boolean;
  excludePaths?: string[];
  includeBody?: boolean;
  sensitiveFields?: string[];
  logLevel?: 'minimal' | 'standard' | 'detailed';
  enableRealTime?: boolean;
  retentionDays?: number;
}

const DEFAULT_OPTIONS: ApiAuditOptions = {
  logRequest: true,
  logResponse: false,
  logErrors: true,
  excludePaths: ['/api/health', '/api/ping', '/api/auth/session'],
  includeBody: false,
  sensitiveFields: ['password', 'token', 'secret', 'key', 'authorization', 'otp', 'pin'],
  logLevel: 'standard',
  enableRealTime: true,
  retentionDays: 365
};

/**
 * Enhanced API middleware for comprehensive audit logging
 */
export function withAuditLogging(
  handler: (req: NextRequest, context?: any) => Promise<NextResponse>,
  options: ApiAuditOptions = {}
) {
  const config = { ...DEFAULT_OPTIONS, ...options };

  return async (req: NextRequest, context?: any): Promise<NextResponse> => {
    const startTime = Date.now();
    const path = req.nextUrl.pathname;
    const method = req.method;

    // Skip excluded paths
    if (config.excludePaths?.some(excludePath => path.startsWith(excludePath))) {
      return handler(req, context);
    }

    // Get audit context
    const auditContext = await getAuditContext(req);
    
    // Create context logger
    const contextLogger = logger.withContext({
      ...auditContext,
      path,
      method,
      requestId: generateRequestId()
    });

    let requestBody: any = null;
    let response: NextResponse;
    let error: Error | null = null;

    try {
      // Log request if enabled
      if (config.logRequest) {
        if (config.includeBody && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
          try {
            const clonedRequest = req.clone();
            requestBody = await clonedRequest.json();
            requestBody = sanitizeData(requestBody, config.sensitiveFields || []);
          } catch {
            // Body might not be JSON, ignore
          }
        }

        await logApiRequest(auditContext, {
          method,
          path,
          query: Object.fromEntries(req.nextUrl.searchParams.entries()),
          body: requestBody,
          headers: sanitizeHeaders(req.headers)
        });

        contextLogger.audit(`API Request: ${method} ${path}`, {
          query: Object.fromEntries(req.nextUrl.searchParams.entries()),
          hasBody: !!requestBody
        });
      }

      // Execute the handler
      response = await handler(req, context);

      // Log successful response if enabled
      if (config.logResponse && response.ok) {
        const duration = Date.now() - startTime;
        
        await logApiResponse(auditContext, {
          method,
          path,
          statusCode: response.status,
          duration,
          success: true
        });

        contextLogger.audit(`API Response: ${method} ${path} - ${response.status}`, {
          duration,
          success: true
        });
      }

    } catch (err) {
      error = err as Error;
      
      // Log error if enabled
      if (config.logErrors) {
        const duration = Date.now() - startTime;
        
        await logApiError(auditContext, {
          method,
          path,
          error: error.message,
          stack: error.stack,
          duration
        });

        contextLogger.audit(`API Error: ${method} ${path} - ${error.message}`, {
          duration,
          error: error.message,
          stack: error.stack
        });
      }

      throw error;
    }

    return response;
  };
}

/**
 * Get audit context from request
 */
async function getAuditContext(req: NextRequest): Promise<AuditContext> {
  try {
    const session = await auth();
    const requestId = generateRequestId();

    if (!session?.user) {
      return {
        ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
        userAgent: req.headers.get('user-agent') || 'unknown',
        requestId
      };
    }

    // Get user details with role information and permissions
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      include: {
        spvUser: {
          include: {
            projectAssignments: {
              where: { isActive: true },
              select: { projectId: true }
            }
          }
        }
      }
    });

    // Get project IDs for SPV users
    const projectIds = user?.spvUser?.projectAssignments?.map(pa => pa.projectId) || [];

    return {
      userId: user?.id,
      organizationId: user?.organizationId || undefined,
      spvId: user?.spvUser?.spvId,
      projectId: projectIds.length > 0 ? projectIds[0] : undefined, // Primary project
      role: user?.role,
      spvRole: user?.spvUser?.role,
      ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
      sessionId: session.user.id, // Use user ID as session identifier
      requestId,
      permissions: [] // TODO: Implement permission fetching
    };
  } catch (error) {
    logger.error('Error getting audit context:', error);
    return {
      ipAddress: req.headers.get('x-forwarded-for') || req.ip || 'unknown',
      userAgent: req.headers.get('user-agent') || 'unknown',
      requestId: generateRequestId()
    };
  }
}

/**
 * Log API request
 */
async function logApiRequest(
  context: AuditContext,
  requestData: {
    method: string;
    path: string;
    query: Record<string, string>;
    body?: any;
    headers: Record<string, string>;
  }
) {
  try {
    if (!context.userId) return; // Only log for authenticated users

    await AuditService.createAuditLog({
      type: AuditLogType.API_REQUEST,
      description: `API Request: ${requestData.method} ${requestData.path}`,
      userId: context.userId,
      organizationId: context.organizationId,
      spvId: context.spvId,
      resourceType: 'api_endpoint',
      resourceId: `${requestData.method}:${requestData.path}`,
      metadata: {
        method: requestData.method,
        path: requestData.path,
        query: requestData.query,
        body: requestData.body,
        headers: requestData.headers,
        role: context.role,
        spvRole: context.spvRole
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  } catch (error) {
    logger.error('Error logging API request:', error);
  }
}

/**
 * Log API response
 */
async function logApiResponse(
  context: AuditContext,
  responseData: {
    method: string;
    path: string;
    statusCode: number;
    duration: number;
    success: boolean;
  }
) {
  try {
    if (!context.userId) return; // Only log for authenticated users

    await AuditService.createAuditLog({
      type: AuditLogType.API_RESPONSE,
      description: `API Response: ${responseData.method} ${responseData.path} - ${responseData.statusCode}`,
      userId: context.userId,
      organizationId: context.organizationId,
      spvId: context.spvId,
      resourceType: 'api_endpoint',
      resourceId: `${responseData.method}:${responseData.path}`,
      metadata: {
        method: responseData.method,
        path: responseData.path,
        statusCode: responseData.statusCode,
        duration: responseData.duration,
        success: responseData.success,
        role: context.role,
        spvRole: context.spvRole
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  } catch (error) {
    logger.error('Error logging API response:', error);
  }
}

/**
 * Log API error
 */
async function logApiError(
  context: AuditContext,
  errorData: {
    method: string;
    path: string;
    error: string;
    stack?: string;
    duration: number;
  }
) {
  try {
    if (!context.userId) return; // Only log for authenticated users

    await AuditService.createAuditLog({
      type: AuditLogType.API_ERROR,
      description: `API Error: ${errorData.method} ${errorData.path} - ${errorData.error}`,
      userId: context.userId,
      organizationId: context.organizationId,
      spvId: context.spvId,
      resourceType: 'api_endpoint',
      resourceId: `${errorData.method}:${errorData.path}`,
      metadata: {
        method: errorData.method,
        path: errorData.path,
        error: errorData.error,
        stack: errorData.stack,
        duration: errorData.duration,
        role: context.role,
        spvRole: context.spvRole
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  } catch (error) {
    logger.error('Error logging API error:', error);
  }
}

/**
 * Sanitize sensitive data
 */
function sanitizeData(data: any, sensitiveFields: string[]): any {
  if (!data || typeof data !== 'object') return data;

  const sanitized = { ...data };
  
  for (const field of sensitiveFields) {
    if (field in sanitized) {
      sanitized[field] = '[REDACTED]';
    }
  }

  // Recursively sanitize nested objects
  for (const key in sanitized) {
    if (typeof sanitized[key] === 'object' && sanitized[key] !== null) {
      sanitized[key] = sanitizeData(sanitized[key], sensitiveFields);
    }
  }

  return sanitized;
}

/**
 * Sanitize headers
 */
function sanitizeHeaders(headers: Headers): Record<string, string> {
  const sanitized: Record<string, string> = {};
  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];

  headers.forEach((value, key) => {
    if (sensitiveHeaders.includes(key.toLowerCase())) {
      sanitized[key] = '[REDACTED]';
    } else {
      sanitized[key] = value;
    }
  });

  return sanitized;
}

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Authentication event logging
 */
export async function logAuthenticationEvent(
  eventType: AuditLogType,
  context: Partial<AuditContext>,
  metadata?: Record<string, any>
) {
  try {
    await AuditService.createAuditLog({
      type: eventType,
      description: getAuthEventDescription(eventType),
      userId: context.userId,
      organizationId: context.organizationId,
      resourceType: 'authentication',
      resourceId: context.userId || 'anonymous',
      metadata: {
        ...metadata,
        sessionId: context.sessionId,
        role: context.role
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  } catch (error) {
    logger.error('Error logging authentication event:', error);
  }
}

/**
 * SPV management event logging
 */
export async function logSPVEvent(
  eventType: AuditLogType,
  context: AuditContext,
  spvId: string,
  metadata?: Record<string, any>
) {
  try {
    await AuditService.createAuditLog({
      type: eventType,
      description: getSPVEventDescription(eventType),
      userId: context.userId,
      organizationId: context.organizationId,
      spvId,
      resourceType: 'spv',
      resourceId: spvId,
      metadata: {
        ...metadata,
        role: context.role,
        spvRole: context.spvRole
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  } catch (error) {
    logger.error('Error logging SPV event:', error);
  }
}

/**
 * Document management event logging
 */
export async function logDocumentEvent(
  eventType: AuditLogType,
  context: AuditContext,
  documentId: string,
  documentType: string,
  metadata?: Record<string, any>
) {
  try {
    await AuditService.createAuditLog({
      type: eventType,
      description: getDocumentEventDescription(eventType, documentType),
      userId: context.userId,
      organizationId: context.organizationId,
      spvId: context.spvId,
      projectId: context.projectId,
      resourceType: 'document',
      resourceId: documentId,
      metadata: {
        ...metadata,
        documentType,
        role: context.role
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  } catch (error) {
    logger.error('Error logging document event:', error);
  }
}

/**
 * Role and permission event logging
 */
export async function logRolePermissionEvent(
  eventType: AuditLogType,
  context: AuditContext,
  targetUserId: string,
  metadata?: Record<string, any>
) {
  try {
    await AuditService.createAuditLog({
      type: eventType,
      description: getRolePermissionEventDescription(eventType),
      userId: context.userId,
      organizationId: context.organizationId,
      resourceType: 'user_role',
      resourceId: targetUserId,
      metadata: {
        ...metadata,
        targetUserId,
        adminRole: context.role
      },
      ipAddress: context.ipAddress,
      userAgent: context.userAgent
    });
  } catch (error) {
    logger.error('Error logging role/permission event:', error);
  }
}

// Helper functions for event descriptions
function getAuthEventDescription(eventType: AuditLogType): string {
  const descriptions: Record<string, string> = {
    LOGIN_SUCCESS: 'User successfully logged in',
    LOGIN_FAILED: 'User login attempt failed',
    LOGIN_ATTEMPT: 'User attempted to log in',
    LOGOUT_SUCCESS: 'User successfully logged out',
    SESSION_EXPIRED: 'User session expired',
    PASSWORD_CHANGED: 'User changed password',
    PASSWORD_RESET: 'User reset password',
    TWO_FACTOR_ENABLED: 'User enabled two-factor authentication',
    TWO_FACTOR_DISABLED: 'User disabled two-factor authentication',
    ACCOUNT_LOCKED: 'User account was locked',
    ACCOUNT_UNLOCKED: 'User account was unlocked'
  };
  return descriptions[eventType] || `Authentication event: ${eventType}`;
}

function getSPVEventDescription(eventType: AuditLogType): string {
  const descriptions: Record<string, string> = {
    SPV_CREATED: 'SPV was created',
    SPV_UPDATED: 'SPV details were updated',
    SPV_DELETED: 'SPV was deleted',
    SPV_VERIFIED: 'SPV was verified',
    SPV_REJECTED: 'SPV verification was rejected',
    SPV_DOCUMENT_UPLOADED: 'SPV document was uploaded',
    SPV_DOCUMENT_VERIFIED: 'SPV document was verified',
    SPV_DOCUMENT_REJECTED: 'SPV document was rejected',
    SPV_USER_CREATED: 'SPV user was created',
    SPV_USER_UPDATED: 'SPV user was updated',
    SPV_USER_DELETED: 'SPV user was deleted',
    SPV_USER_ROLE_CHANGED: 'SPV user role was changed'
  };
  return descriptions[eventType] || `SPV event: ${eventType}`;
}

function getDocumentEventDescription(eventType: AuditLogType, documentType: string): string {
  const descriptions: Record<string, string> = {
    DOCUMENT_UPLOADED: `${documentType} document was uploaded`,
    DOCUMENT_VERIFIED: `${documentType} document was verified`,
    DOCUMENT_DOWNLOADED: `${documentType} document was downloaded`,
    DOCUMENT_DELETED: `${documentType} document was deleted`,
    DOCUMENT_ACCESS_GRANTED: `Access granted to ${documentType} document`,
    DOCUMENT_ACCESS_REVOKED: `Access revoked from ${documentType} document`
  };
  return descriptions[eventType] || `Document event: ${eventType}`;
}

function getRolePermissionEventDescription(eventType: AuditLogType): string {
  const descriptions: Record<string, string> = {
    USER_ROLE_CHANGED: 'User role was changed',
    USER_PERMISSIONS_UPDATED: 'User permissions were updated',
    ORGANIZATION_ROLE_ASSIGNED: 'Organization role was assigned',
    ORGANIZATION_ROLE_REMOVED: 'Organization role was removed',
    PERMISSION_GRANTED: 'Permission was granted',
    PERMISSION_REVOKED: 'Permission was revoked'
  };
  return descriptions[eventType] || `Role/Permission event: ${eventType}`;
}

// Export audit log types for use in other modules
export const COMPREHENSIVE_AUDIT_TYPES = {
  // API Events
  API_REQUEST: 'API_REQUEST' as AuditLogType,
  API_RESPONSE: 'API_RESPONSE' as AuditLogType,
  API_ERROR: 'API_ERROR' as AuditLogType,

  // Authentication Events
  LOGIN_SUCCESS: 'LOGIN_SUCCESS' as AuditLogType,
  LOGIN_FAILED: 'LOGIN_FAILED' as AuditLogType,
  LOGIN_ATTEMPT: 'LOGIN_ATTEMPT' as AuditLogType,
  LOGOUT_SUCCESS: 'LOGOUT_SUCCESS' as AuditLogType,
  SESSION_EXPIRED: 'SESSION_EXPIRED' as AuditLogType,
  PASSWORD_CHANGED: 'PASSWORD_CHANGED' as AuditLogType,

  // SPV Events
  SPV_CREATED: 'SPV_CREATED' as AuditLogType,
  SPV_UPDATED: 'SPV_UPDATED' as AuditLogType,
  SPV_DELETED: 'SPV_DELETED' as AuditLogType,
  SPV_USER_CREATED: 'SPV_USER_CREATED' as AuditLogType,

  // Document Events
  DOCUMENT_UPLOADED: 'DOCUMENT_UPLOADED' as AuditLogType,
  DOCUMENT_VERIFIED: 'DOCUMENT_VERIFIED' as AuditLogType,
  DOCUMENT_DOWNLOADED: 'DOCUMENT_DOWNLOADED' as AuditLogType,

  // Role Events
  USER_ROLE_CHANGED: 'USER_ROLE_CHANGED' as AuditLogType,
  PERMISSION_GRANTED: 'PERMISSION_GRANTED' as AuditLogType,
  PERMISSION_REVOKED: 'PERMISSION_REVOKED' as AuditLogType
} as const;
